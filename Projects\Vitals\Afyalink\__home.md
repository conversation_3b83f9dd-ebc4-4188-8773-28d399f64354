# Afyalink - Healthcare Platform

## Project Overview
A simple care worker booking system designed to attract potential leads with smartly placed guides on Zambian health facilities, health insurance and costs not commonly found online, with the hopes to boost SEO.

## Status
- **Current**: #mvpcomplete #strategic #cofounder
- **Co-Founder**: Simon
- **MVP Location**: C:\Users\<USER>\afyalink-zambia
- **Priority**: Strategic (long-term growth)

## Technical Stack (MVP Completed)
- **Framework**: Next.js (version 14.2.x, App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS (version 3.4.x)
- **CMS**: Sanity.io (version 3.x)
- **Deployment**: Vercel

## Core Features (MVP)
- Care worker booking system (<PERSON>'s profile)
- NHIMA facility directory with programmatic SEO
- Medication price guide
- Health insurance information
- Zambian healthcare cost guides

## SEO Strategy
Programmatic content targeting specific user questions:
- "NHIMA hospitals in Lusaka"
- "NHIMA clinics in Kitwe"
- "Pharmacies that accept NHIMA"
- Medication pricing guides
- Healthcare facility directories

## Business Model
- Lead generation for care workers
- SEO-driven traffic through healthcare guides
- Potential expansion to agency model for multiple care workers

## Next Phase Planning
- [ ] Review MVP performance and analytics
- [ ] Plan expansion features with <PERSON>
- [ ] Develop marketing and content strategy
- [ ] Consider additional care worker onboarding
- [ ] Explore monetization opportunities

## Team
- **Mwila**: Lead Developer, Technical Architecture
- **Simon**: Co-Founder, Business Strategy, Care Worker Profile

#afyalink #healthcare #mvp #seo #cofounder #strategic
