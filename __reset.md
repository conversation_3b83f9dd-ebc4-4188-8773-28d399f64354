# Task Master Reset Command

## Reset Instructions
When user requests `__reset`, execute the following steps:

### 1. Create Backup
```
Backup Location: ../Backups/DaVAWT-{YYYY-MM-DD-HHMMSS}/
```
- Copy entire DaVAWT folder to backup location
- Preserve all project data, tasks, archives, and customizations
- Include timestamp in backup folder name

### 2. Reset to Template
- Clear all project-specific content
- Reset to clean template structure
- Preserve `__start.md` customizations
- Maintain global rules and tag system

### 3. Template Structure to Restore
```
DaVAWT/
├── README.md                    # Task Master documentation
├── __start.md                   # Project definitions (preserve user customizations)
├── __rules.md                   # Global rules and automation
├── __memo.md                    # Empty daily memo template
├── __logs.md                    # Fresh activity log
├── __reset.md                   # This reset instruction file
├── Projects/                    # Empty project structure
│   ├── __home.md               # Template project overview
│   ├── __rules.md              # Project rules template
│   └── __logs.md               # Empty project logs
├── Tasks/                       # Clean task structure
│   ├── __home.md               # Task management overview
│   ├── __rules.md              # Task rules template
│   ├── __logs.md               # Empty task logs
│   └── archive/                # Empty archive structure
│       ├── __home.md           # Archive overview
│       └── __rules.md          # Archive rules
├── Performance/                 # Clean performance tracking
│   ├── __home.md               # Performance dashboard template
│   ├── __rules.md              # Performance rules
│   └── __logs.md               # Empty performance logs
├── Team/                        # Clean team structure
│   ├── __home.md               # Team overview template
│   ├── __rules.md              # Team rules
│   └── __logs.md               # Empty team logs
└── Clients/                     # Clean client structure
    ├── __home.md               # Client overview template
    ├── __rules.md              # Client rules
    └── __logs.md               # Empty client logs
```

### 4. Post-Reset Actions
- Run first-time setup to populate projects from `__start.md`
- Initialize daily task file for current date
- Update performance and team dashboards
- Log reset action in `__logs.md`

### 5. Backup Verification
- Confirm backup was created successfully
- Verify all original data is preserved
- Provide backup location to user

## Usage
User can trigger reset by saying:
- `__reset`
- "Reset the workspace"
- "Create backup and reset to template"

## Recovery
To restore from backup:
1. Navigate to backup folder in `../Backups/`
2. Copy desired backup back to `DaVAWT/`
3. Rename if necessary

#reset #backup #template #taskmaster
