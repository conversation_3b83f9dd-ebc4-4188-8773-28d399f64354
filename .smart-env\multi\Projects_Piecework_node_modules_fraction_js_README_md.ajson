
"smart_sources:Projects/Piecework/node_modules/fraction.js/README.md": {"path":"Projects/Piecework/node_modules/fraction.js/README.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07228205,-0.0473911,-0.03271838,-0.05720969,-0.03009492,-0.02324555,-0.08907983,0.01362219,0.03478772,0.03968774,-0.01547393,-0.05793266,0.00473472,0.05945603,0.02688937,0.01320242,0.03649449,0.06145253,-0.04672922,0.04732343,0.06041664,-0.0480366,-0.01944751,-0.04117469,0.04520221,0.07146318,0.01765152,-0.03960216,-0.00833237,-0.21630451,0.000815,0.00659224,0.04242979,0.01235938,-0.03835245,-0.00921284,-0.00320744,0.01851158,0.01423889,0.02552924,0.03970846,-0.00303907,0.02526433,-0.02616491,-0.01850137,0.00299669,-0.0510583,-0.02516074,0.01486862,-0.02368377,-0.00417614,0.00204947,0.04635879,-0.0100891,-0.00299102,0.01384228,0.03592027,0.06477512,0.04399597,0.05804707,0.02792737,-0.0073821,-0.16904171,0.03477439,0.05198102,-0.0307829,-0.05402917,-0.03457766,-0.01047993,0.02277405,-0.00275933,0.00295797,-0.01607259,0.07300612,0.02370201,-0.04684552,0.0025195,-0.05301676,0.01085121,0.02859418,-0.07386471,0.00109338,-0.02139046,0.0175555,0.01292775,-0.0484721,0.02073712,0.02714678,0.0710277,-0.00490814,-0.00490035,-0.04679822,0.00104742,0.0710919,0.03304224,0.04489665,0.08113497,0.02453854,-0.00873447,0.10536139,0.0439212,0.0567041,-0.00501927,-0.01878062,0.01684675,-0.02918241,-0.03076431,-0.04633958,-0.01348022,-0.07216001,-0.04869123,0.05752032,0.0327531,-0.06675759,-0.00851841,-0.00820945,-0.0243988,0.0227622,0.05913011,0.02471062,-0.0057285,0.05088103,0.06332228,-0.03428365,0.03784113,0.02343541,-0.00573405,0.07986379,0.01831486,0.13907759,0.0310003,-0.02027283,-0.07930259,-0.06139591,-0.01790833,0.00340708,-0.00353579,0.00629288,-0.00232579,0.01412213,0.00837172,-0.05469501,-0.01890262,-0.04142392,-0.02265845,0.09463351,-0.07382326,0.00272525,0.03382272,-0.02825727,-0.02544477,0.05086998,-0.04846531,-0.01409554,-0.01100304,-0.03184219,0.05482134,0.05463531,-0.06742497,-0.00207681,-0.04127236,0.00681708,-0.01384635,0.08358163,0.01114856,-0.01475012,-0.01124003,0.05042347,0.00775395,-0.08852767,-0.01390568,0.009892,0.00469253,-0.02854289,0.1082931,-0.03691096,-0.0422033,-0.05333589,0.0437924,-0.01632487,0.00991853,-0.0843159,-0.04841731,0.00943562,0.02256012,-0.02566909,0.03957404,-0.03372879,-0.03225672,0.00460751,0.006192,0.04077737,-0.08193625,0.00584281,0.01197287,-0.04517775,-0.03703041,0.02568878,0.02766313,-0.04971639,0.05824677,0.00165329,0.02788736,0.0376662,-0.00933669,0.03898115,0.05059379,-0.08698341,0.0614154,0.03704846,-0.08723962,-0.03028265,0.03974383,0.055245,-0.00289614,-0.00587481,0.01826292,0.07075691,0.04316295,0.02561284,-0.0398511,-0.07673498,-0.12745911,-0.18437916,-0.0223616,0.06262373,-0.06841637,0.03153062,0.01833743,0.01884846,-0.0361051,-0.05104403,0.06568943,0.07408278,-0.03882276,-0.10350925,-0.05437408,-0.02351745,-0.01327921,-0.01568989,0.00373034,-0.03262224,-0.03031002,0.02146386,0.00905189,-0.07113728,-0.05413018,0.03936158,-0.08576509,0.13554366,0.02675312,-0.02342136,-0.03339362,-0.01599588,-0.06457677,0.00651683,-0.02599227,-0.01855688,-0.01954656,-0.03742839,0.00757534,0.01163416,0.02474808,-0.04549661,-0.03521809,-0.02133893,-0.06372581,0.00079904,0.01903323,-0.06707312,0.01294906,0.02325138,0.06015135,-0.00643856,0.00528959,0.04731666,0.04991025,0.01895124,-0.00522848,-0.03015441,-0.01097817,0.03020397,0.04824366,0.00751902,-0.08187161,-0.00504006,0.01511235,0.04523988,0.08517985,0.02303986,-0.01740823,0.02620967,0.03740348,0.00842933,0.11543989,0.04769745,0.03886293,0.00691269,0.02302907,-0.00296574,0.04610848,0.03383449,0.07200989,0.03662596,-0.00880266,0.03900567,0.02682118,-0.03119471,-0.05845391,0.00643914,0.03080187,0.00408637,-0.03625325,-0.07178741,0.01285987,0.02629276,0.03318678,0.01360398,-0.0602387,-0.23379555,0.02563761,-0.06158656,0.00510306,0.01778829,-0.03923266,0.06430483,0.00473715,-0.06989585,0.02556785,-0.04923741,0.02813109,0.03390025,-0.06494125,-0.0104049,0.02179995,0.00073972,0.01291545,0.10167269,-0.0635859,0.1062208,0.05652026,0.25657749,-0.00481243,-0.04607565,0.0749875,0.00990388,0.01935153,0.07048924,0.02893595,-0.01385682,-0.00485863,0.14472836,-0.04856306,-0.0392812,0.00028176,-0.03860446,-0.01408992,0.04959014,0.02018037,0.01281438,-0.03865124,-0.09141032,0.00010763,0.09423104,-0.09074797,-0.01049249,-0.08806352,0.07234826,0.04175804,-0.05179265,0.05975792,-0.00708037,-0.03513289,-0.01562107,0.00899989,-0.02156854,0.01542276,-0.06366913,-0.02707923,-0.05821613,-0.02804758,0.09179725,0.11341415,-0.03490078],"last_embed":{"hash":"thjbnn","tokens":428}}},"last_read":{"hash":"thjbnn","at":1751288788854},"class_name":"SmartSource","last_import":{"mtime":1751244532331,"size":14999,"at":1751288765474,"hash":"thjbnn"},"blocks":{"#Fraction.js - ℚ in JavaScript":[1,467],"#Fraction.js - ℚ in JavaScript#{1}":[3,467],"#---frontmatter---":[149,156]},"outlinks":[{"title":"![NPM Package","target":"https://img.shields.io/npm/v/fraction.js.svg?style=flat","line":3},{"title":"![MIT license","target":"http://img.shields.io/badge/license-MIT-brightgreen.svg","line":4},{"title":"Polynomial.js","target":"https://github.com/infusion/Polynomial.js","line":27},{"title":"Math.js","target":"https://github.com/josdejong/mathjs","line":27},{"title":"WolframAlpha","target":"http://www.wolframalpha.com/input/?i=sqrt%285%29-2+binary","line":142},{"title":"blog","target":"http://www.xarg.org/2014/03/precise-calculations-in-javascript/","line":145},{"title":"php.js","target":"http://phpjs.org/functions/fmod/","line":172},{"title":"WolframAlpha","target":"http://www.wolframalpha.com/input/?i=123.32+%2F+%2812453%2F370%29","line":232},{"title":"fmod()","target":"#fmod-impreciseness-circumvented","line":324},{"title":"here","target":"#mathematical-correct-modulo","line":324},{"title":"live demo","target":"http://www.xarg.org/2014/03/precise-calculations-in-javascript/","line":391},{"title":"Robert Eisele","target":"https://raw.org/","line":465}],"last_embed":{"hash":"thjbnn","at":1751288788479}},"smart_blocks:Projects/Piecework/node_modules/fraction.js/README.md#Fraction.js - ℚ in JavaScript": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07575593,-0.04656999,-0.02803501,-0.05544605,-0.03085856,-0.02479537,-0.08890596,0.01617779,0.03700944,0.03242774,-0.01922597,-0.04977721,-0.00166152,0.06013647,0.02607886,0.01267817,0.03423816,0.06091377,-0.05290712,0.04631517,0.06248296,-0.04235033,-0.01745326,-0.04015342,0.04159408,0.06904257,0.01927629,-0.03784869,-0.0001631,-0.20952322,-0.00003557,0.00315483,0.03908833,0.01369223,-0.03602903,-0.01151847,-0.00766904,0.01888541,0.00903842,0.02562035,0.04046509,0.00027951,0.02249994,-0.02626066,-0.02162288,0.00161457,-0.05032931,-0.03139341,0.01222342,-0.02126067,-0.00870778,0.00163115,0.04392692,-0.01441033,-0.00364142,0.01525655,0.0354488,0.066212,0.04103029,0.05921853,0.03067513,-0.0069855,-0.16865842,0.03556077,0.05298652,-0.02645273,-0.05924806,-0.03686186,-0.00873499,0.02582815,-0.00326337,0.00615782,-0.01244688,0.0781724,0.01973615,-0.04597049,0.00197279,-0.06010549,0.00487526,0.0314864,-0.06902333,0.0038798,-0.01674065,0.01550066,0.01148368,-0.04892873,0.02278851,0.02268239,0.07330173,-0.00340071,-0.00647136,-0.04837173,-0.00314367,0.06862658,0.02800786,0.03922308,0.08448416,0.02543711,-0.0084959,0.09492752,0.03971689,0.0586099,-0.00456339,-0.0230347,0.01939,-0.03042284,-0.0270014,-0.05073059,-0.01325236,-0.07527551,-0.05105692,0.06079791,0.03394472,-0.06408461,-0.00898376,-0.01178184,-0.02551162,0.01582793,0.0609551,0.02138607,-0.00052692,0.05353554,0.06272841,-0.03302978,0.03868281,0.02426634,-0.00240051,0.07967394,0.01453998,0.13927662,0.03277149,-0.02429068,-0.07610112,-0.05411463,-0.01772357,0.00419344,-0.00850114,0.00708571,0.00152166,0.01148904,0.01339663,-0.0515646,-0.02078894,-0.05017331,-0.0194458,0.09651884,-0.06996892,0.00177144,0.03124549,-0.02513113,-0.02150922,0.05117035,-0.0461089,-0.01173243,-0.01116753,-0.02215467,0.05619603,0.05455032,-0.0597185,-0.00354143,-0.04121674,0.00479352,-0.01874525,0.07823519,0.01650431,-0.01944282,-0.01173233,0.04881125,0.01158186,-0.08642563,-0.01077365,0.00738762,0.0049038,-0.0278491,0.10386035,-0.02970622,-0.04192932,-0.05631203,0.04811017,-0.01379477,0.01114279,-0.08941659,-0.053529,0.01060695,0.02443314,-0.0233725,0.04443908,-0.03925928,-0.02731607,0.00807955,0.00621993,0.04017001,-0.07243684,0.00901478,0.00752442,-0.04490116,-0.04014197,0.01651179,0.02616969,-0.05151154,0.05649004,0.00716083,0.02882384,0.03641101,-0.01092852,0.03889507,0.04981679,-0.08777927,0.06830806,0.03775903,-0.09091165,-0.02813267,0.03948759,0.0553357,-0.00363345,-0.00692501,0.01369852,0.06714381,0.04508952,0.03196082,-0.03545576,-0.07905979,-0.12671608,-0.18802495,-0.02036401,0.05785672,-0.0715741,0.03252273,0.01747328,0.01989413,-0.03589372,-0.05412469,0.06127741,0.07223596,-0.04148302,-0.10228483,-0.05650313,-0.0212967,-0.01159552,-0.02242014,0.00190891,-0.02926775,-0.02876646,0.02151248,0.0171879,-0.06555975,-0.06124471,0.03913324,-0.08681592,0.13395384,0.02492529,-0.02141935,-0.03648619,-0.01492085,-0.07074976,0.01368288,-0.03018985,-0.02001651,-0.02162186,-0.03726907,0.00879042,0.01056383,0.02697554,-0.04149912,-0.03460177,-0.01653533,-0.05747098,0.0005452,0.01913008,-0.06724475,0.01528184,0.01956899,0.0617701,-0.010161,0.00750997,0.04660574,0.0497539,0.02025412,-0.00692442,-0.03086524,-0.01283098,0.02851629,0.04686507,0.00424909,-0.08187629,-0.0044717,0.01352801,0.05403171,0.09102793,0.02284774,-0.01987185,0.02361479,0.03744835,0.00291333,0.12188584,0.05435567,0.03804453,0.00692013,0.02010125,-0.00783067,0.04187443,0.02743252,0.06963,0.03581477,-0.00065953,0.03252687,0.03078802,-0.02722705,-0.05750546,0.01204572,0.03105279,0.00950595,-0.03635238,-0.07708803,0.00998932,0.02729204,0.03256049,0.01289934,-0.0581793,-0.23762499,0.02208132,-0.05352642,0.00674884,0.01285596,-0.03453635,0.05867096,-0.00111497,-0.06719278,0.02397166,-0.05801225,0.02360869,0.03400126,-0.06353583,-0.0130705,0.02729688,-0.00339548,0.01512654,0.09718315,-0.0698495,0.10818659,0.05557008,0.25753343,-0.01024248,-0.04104942,0.08195821,0.01137992,0.02005785,0.07167441,0.03132055,-0.00793685,-0.00664739,0.15322708,-0.04884446,-0.03951633,-0.00153232,-0.0376711,-0.01684012,0.05179871,0.01489941,0.01328299,-0.03327334,-0.09507053,0.00329323,0.09946393,-0.0878316,-0.00752176,-0.09155791,0.06525037,0.04422867,-0.05250542,0.06235873,-0.00135011,-0.03447902,-0.01324108,0.00890702,-0.02072757,0.01331516,-0.06117772,-0.02937621,-0.05598095,-0.0269715,0.09432507,0.11074613,-0.03521795],"last_embed":{"hash":"thjbnn","tokens":499}}},"text":null,"length":0,"last_read":{"hash":"thjbnn","at":1751288788629},"key":"Projects/Piecework/node_modules/fraction.js/README.md#Fraction.js - ℚ in JavaScript","lines":[1,467],"size":14996,"outlinks":[{"title":"![NPM Package","target":"https://img.shields.io/npm/v/fraction.js.svg?style=flat","line":3},{"title":"![MIT license","target":"http://img.shields.io/badge/license-MIT-brightgreen.svg","line":4},{"title":"Polynomial.js","target":"https://github.com/infusion/Polynomial.js","line":27},{"title":"Math.js","target":"https://github.com/josdejong/mathjs","line":27},{"title":"WolframAlpha","target":"http://www.wolframalpha.com/input/?i=sqrt%285%29-2+binary","line":142},{"title":"blog","target":"http://www.xarg.org/2014/03/precise-calculations-in-javascript/","line":145},{"title":"php.js","target":"http://phpjs.org/functions/fmod/","line":172},{"title":"WolframAlpha","target":"http://www.wolframalpha.com/input/?i=123.32+%2F+%2812453%2F370%29","line":232},{"title":"fmod()","target":"#fmod-impreciseness-circumvented","line":324},{"title":"here","target":"#mathematical-correct-modulo","line":324},{"title":"live demo","target":"http://www.xarg.org/2014/03/precise-calculations-in-javascript/","line":391},{"title":"Robert Eisele","target":"https://raw.org/","line":465}],"class_name":"SmartBlock","last_embed":{"hash":"thjbnn","at":1751288788629}},
"smart_blocks:Projects/Piecework/node_modules/fraction.js/README.md#Fraction.js - ℚ in JavaScript#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07878378,-0.0464043,-0.0323195,-0.05571391,-0.0326689,-0.0219443,-0.08881125,0.01625774,0.03810737,0.03439927,-0.02234117,-0.04814931,-0.00098245,0.0582021,0.02740322,0.01610975,0.03069689,0.06110933,-0.05498931,0.04422783,0.06391954,-0.04463812,-0.02224136,-0.04411502,0.04368082,0.06839035,0.01909914,-0.03896795,0.00186131,-0.20650117,0.00286806,0.00271223,0.04169342,0.01001119,-0.0392227,-0.01391605,-0.01147005,0.0229614,0.00764064,0.02604688,0.04192632,-0.00400232,0.02691007,-0.0307076,-0.02130557,0.0017231,-0.05302119,-0.0303119,0.0122237,-0.01679176,-0.0018819,0.00003166,0.04063652,-0.01273018,-0.00290972,0.0132081,0.03820245,0.06868928,0.04207677,0.06326759,0.03156133,-0.01221488,-0.16706654,0.03560822,0.05241556,-0.0277319,-0.05646935,-0.03521834,-0.0074114,0.02945353,-0.00839277,0.00496851,-0.0093787,0.07968637,0.017562,-0.0449405,0.00274819,-0.0606526,0.00767573,0.03101746,-0.06525208,0.00057674,-0.01978493,0.01880484,0.01434861,-0.05242855,0.01868365,0.02035907,0.06871608,-0.00458798,-0.00673266,-0.04833927,-0.0054597,0.06489078,0.02421529,0.03992568,0.08260664,0.0244147,-0.01265813,0.09728935,0.04189256,0.05817099,0.00037642,-0.02483639,0.01918707,-0.03428134,-0.0291773,-0.04896795,-0.01118814,-0.07425807,-0.05165551,0.06454575,0.0377169,-0.06627871,-0.01136868,-0.01644735,-0.02795544,0.01196543,0.0629639,0.01601013,-0.00100638,0.05177065,0.06567568,-0.03252187,0.03982349,0.02673428,-0.00821845,0.07743668,0.01614068,0.13640761,0.03237202,-0.02713375,-0.07247759,-0.05298942,-0.01791506,0.00226075,-0.00709401,0.01527852,0.00350909,0.01874647,0.01592974,-0.05309253,-0.02384356,-0.04925235,-0.01913421,0.09871558,-0.07177044,0.00374793,0.03507125,-0.02549022,-0.02448916,0.0524651,-0.04775194,-0.01107379,-0.00835768,-0.01978523,0.05054527,0.05683355,-0.06007193,-0.00597527,-0.04414989,0.00600349,-0.015148,0.08137403,0.01400764,-0.01989019,-0.00799216,0.04870692,0.01413827,-0.08458031,-0.01641113,0.00550742,0.00125588,-0.03048532,0.10679377,-0.034198,-0.03947802,-0.05108146,0.04988732,-0.01001307,0.01328138,-0.08502565,-0.05193915,0.00904959,0.02715018,-0.02870612,0.04427114,-0.03603966,-0.02944212,0.00771786,0.00013008,0.04386481,-0.07252011,0.00369289,0.01011744,-0.04541293,-0.03628168,0.01452535,0.0298896,-0.04853196,0.05751825,0.00700587,0.03473049,0.03382917,-0.00986795,0.03870006,0.05117042,-0.087582,0.0662955,0.04086953,-0.09329875,-0.02413632,0.04253587,0.0543125,-0.00156988,-0.00259133,0.0141815,0.06617402,0.04270385,0.03403854,-0.03518669,-0.07715741,-0.12338085,-0.18435973,-0.02155432,0.05828904,-0.07227765,0.03670306,0.01554608,0.02025716,-0.03875733,-0.05785265,0.0599312,0.07022804,-0.04192255,-0.10031085,-0.05512396,-0.02365621,-0.01514259,-0.01219554,0.00079342,-0.02213156,-0.03064026,0.01908526,0.01581756,-0.06061453,-0.06174327,0.0375769,-0.08921088,0.13646314,0.02463559,-0.02313153,-0.0370411,-0.01910983,-0.07095979,0.00856234,-0.03066898,-0.01512536,-0.02680153,-0.04157844,0.00189169,0.01156857,0.0266727,-0.04583742,-0.03350196,-0.01555754,-0.0552853,0.00011225,0.02018788,-0.06807779,0.01492497,0.01802018,0.061692,-0.00968039,0.0082718,0.04764502,0.04760026,0.02243199,-0.00847988,-0.02819531,-0.01126274,0.02893373,0.04880311,0.00310335,-0.08684067,-0.00680827,0.0144123,0.05238468,0.08889071,0.02595941,-0.01956696,0.02396495,0.03840107,0.00478288,0.12593587,0.05335178,0.03733298,0.00976334,0.01799714,-0.00912535,0.04423233,0.02905229,0.0672177,0.03416598,-0.00313101,0.0345189,0.03048216,-0.02590793,-0.05828157,0.0186826,0.0328144,0.01065012,-0.03575311,-0.07770102,0.007302,0.03131385,0.03608792,0.01111252,-0.05631988,-0.23842129,0.02210864,-0.0537729,0.00596812,0.01280034,-0.03516651,0.05935435,0.00189545,-0.06878663,0.02475232,-0.05983564,0.02295911,0.04206831,-0.05830935,-0.01029034,0.02412908,-0.00342398,0.0146799,0.09986249,-0.06888054,0.10310148,0.05674509,0.25399706,-0.0102373,-0.04100408,0.07902295,0.01180052,0.02017238,0.06875404,0.02510951,-0.01040062,-0.00981899,0.15190925,-0.04957997,-0.03811757,-0.00031909,-0.04074226,-0.02160012,0.04840862,0.015792,0.00980237,-0.03865388,-0.09296962,0.00303406,0.10103806,-0.08550356,-0.00159538,-0.08831581,0.06542219,0.03941157,-0.0560385,0.06216057,-0.00458584,-0.03933934,-0.01344489,0.01270563,-0.0219858,0.01690255,-0.06363065,-0.03061637,-0.05715842,-0.02203218,0.09425356,0.11062817,-0.03451264],"last_embed":{"hash":"1bp215u","tokens":499}}},"text":null,"length":0,"last_read":{"hash":"1bp215u","at":1751288788854},"key":"Projects/Piecework/node_modules/fraction.js/README.md#Fraction.js - ℚ in JavaScript#{1}","lines":[3,467],"size":14963,"outlinks":[{"title":"![NPM Package","target":"https://img.shields.io/npm/v/fraction.js.svg?style=flat","line":1},{"title":"![MIT license","target":"http://img.shields.io/badge/license-MIT-brightgreen.svg","line":2},{"title":"Polynomial.js","target":"https://github.com/infusion/Polynomial.js","line":25},{"title":"Math.js","target":"https://github.com/josdejong/mathjs","line":25},{"title":"WolframAlpha","target":"http://www.wolframalpha.com/input/?i=sqrt%285%29-2+binary","line":140},{"title":"blog","target":"http://www.xarg.org/2014/03/precise-calculations-in-javascript/","line":143},{"title":"php.js","target":"http://phpjs.org/functions/fmod/","line":170},{"title":"WolframAlpha","target":"http://www.wolframalpha.com/input/?i=123.32+%2F+%2812453%2F370%29","line":230},{"title":"fmod()","target":"#fmod-impreciseness-circumvented","line":322},{"title":"here","target":"#mathematical-correct-modulo","line":322},{"title":"live demo","target":"http://www.xarg.org/2014/03/precise-calculations-in-javascript/","line":389},{"title":"Robert Eisele","target":"https://raw.org/","line":463}],"class_name":"SmartBlock","last_embed":{"hash":"1bp215u","at":1751288788854}},
