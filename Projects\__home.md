# Projects - Template

## Overview
Project structure will be auto-populated from __start.md on first run.

## Auto-Population Process
1. System reads project definitions from __start.md
2. Creates folder structure with proper hierarchy
3. Generates project home pages with links
4. Sets up task files with linking system
5. Creates archive structures

## Project Categories
Will be populated based on your __start.md definitions.

## Features
- ✅ Automatic project structure creation
- ✅ Task linking between daily and project tasks
- ✅ Archive system for completed work
- ✅ Status tracking and team coordination
- ✅ Performance metrics integration

#projects #template #autopopulation
