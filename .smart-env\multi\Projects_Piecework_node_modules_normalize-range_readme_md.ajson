
"smart_sources:Projects/Piecework/node_modules/normalize-range/readme.md": {"path":"Projects/Piecework/node_modules/normalize-range/readme.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04838229,-0.03189951,0.06464512,-0.078927,0.03282069,-0.01056271,-0.0632064,-0.02258035,-0.01732156,-0.01416255,0.05936135,-0.05056553,0.04125215,0.07066876,0.02063181,0.01149117,-0.0039958,0.08559741,-0.04464626,0.03594233,0.06439147,0.03359703,0.0616627,-0.04292825,0.09452131,0.06926552,-0.04798826,-0.0262734,-0.01184193,-0.22663523,0.03689515,-0.03278851,0.03118602,-0.02246384,-0.02414105,-0.00757785,-0.03976409,0.01098892,-0.04645849,0.04006408,0.02707832,0.06234379,-0.0222483,-0.03919991,-0.03515876,0.00666915,-0.03366967,-0.01138213,-0.03758792,-0.03680758,-0.06476498,-0.03303022,0.00584575,0.0326845,0.06221939,0.07654683,0.02548861,0.01900569,0.04181498,0.04967598,-0.01326884,0.06708185,-0.19855334,0.04137599,0.06378765,-0.00980393,-0.09216604,0.0161427,-0.00055114,0.01636529,0.02326354,0.01811163,-0.01112922,0.05496211,0.02208433,-0.07217731,-0.01763409,-0.00952551,-0.01993453,-0.01887676,-0.07007954,-0.00227542,0.03665765,-0.03730176,0.02529575,0.00873261,0.05988003,-0.01222851,0.04604071,-0.02420648,-0.00489871,-0.05959194,-0.03566439,0.06192033,-0.02127706,-0.00608109,0.08512342,0.00609672,-0.02076253,0.12540814,0.01208025,0.02761634,-0.01110612,0.04339829,0.04701423,-0.00755166,-0.06888702,-0.0498184,-0.04264796,-0.03397853,-0.00486656,-0.03751844,-0.04150869,-0.10822841,-0.00406603,0.0183937,0.00285855,0.0441184,0.00899266,0.03527844,0.00296753,0.08678511,0.00188528,-0.00455623,0.07328263,0.03503651,0.07349939,0.067852,-0.01636428,0.09519437,0.03005366,0.0398136,-0.08870421,-0.05744804,0.04030026,0.01466665,-0.01754452,-0.01908921,-0.00626223,0.01033361,-0.02680793,0.01238414,-0.0145827,-0.04505875,0.01090842,0.08611797,-0.10625437,0.04133474,-0.0530181,-0.04475466,0.01917067,-0.02424169,-0.03957908,-0.01295681,-0.02275932,-0.03555706,0.02741517,-0.01476582,-0.07196219,-0.01837627,0.04693835,-0.05470676,-0.01030343,0.06350251,0.03038897,-0.06175116,0.01669095,0.06060013,0.02757567,0.0014318,0.01046429,0.01802909,0.03658088,0.00476989,0.05602229,0.00161187,-0.06454051,-0.03281083,0.02006014,-0.00433309,-0.02199284,-0.0388397,-0.03074639,0.03452657,0.02434487,0.00161399,0.04421632,0.00389499,0.04202835,-0.02727972,0.00408367,0.01478698,-0.05619497,0.01833409,-0.03664813,0.03122782,-0.05746947,0.02992337,0.02831836,-0.03446643,0.14645346,0.037871,-0.02596319,0.03045816,-0.03445403,0.03941207,0.03272823,-0.06090844,-0.0101642,0.03997143,-0.08245727,-0.04908654,0.00518447,0.0346216,-0.04570202,0.00463277,0.0111059,0.05491591,0.03655226,0.06318219,0.0140891,0.00235466,-0.13440038,-0.24025644,0.01895766,0.00080713,-0.01121123,0.02325463,-0.01835582,0.03482724,-0.02818144,-0.02036749,0.04871809,0.12048871,-0.00124301,-0.02708845,-0.02321219,-0.03730465,0.01413747,-0.0290256,-0.02473113,-0.07307509,-0.00525635,-0.0170013,0.06265224,-0.11200844,-0.04771291,0.0314607,-0.01481469,0.1593067,-0.02296374,0.05360425,-0.03824752,0.02711163,-0.0332902,0.00070674,-0.06824043,0.02127629,0.00688018,-0.05453257,0.02669592,-0.03807373,0.02476566,0.0048402,0.06420702,0.00156211,-0.06498104,0.03554691,0.00334504,-0.0134271,0.02467986,0.02104179,-0.01075438,-0.01796317,-0.00963703,-0.01136618,0.0260286,0.01501974,-0.03646676,-0.09601723,-0.03858555,0.01629244,0.01948072,0.03322572,-0.03489846,0.00925647,-0.06545345,0.03370468,0.05698977,0.04678174,0.00582931,0.04902459,-0.04211017,-0.03724476,0.05424105,0.04300624,0.05128681,-0.00969996,0.01981081,-0.02851422,0.04070785,0.00630591,-0.04008221,0.06886213,0.03192637,0.02509554,-0.01047532,-0.04479499,-0.03319256,-0.02955722,-0.01399543,0.06104145,0.01490842,-0.01342839,-0.02452288,-0.01838721,-0.01604015,0.09475135,-0.02798157,-0.2465151,-0.0074858,0.05267883,-0.06541394,-0.02267123,0.01891024,-0.00142624,-0.01651957,-0.07662622,-0.00223482,-0.03712541,0.07138035,0.00015576,0.00883246,-0.00683949,0.00139968,0.00653699,0.01180041,0.05514872,-0.06222351,0.05559124,-0.01904313,0.25613138,0.00629514,-0.01463122,0.02522556,-0.03323338,0.01716875,0.03487626,0.05492704,0.00885268,0.05899953,0.04645815,0.0084674,-0.00737464,0.03241602,0.04949415,-0.0063753,-0.00713358,-0.0403203,0.02361575,0.0335728,-0.03431533,-0.04012861,0.12593962,-0.07631051,-0.06055981,-0.04431751,0.05003997,0.01149547,-0.07764018,0.04102903,-0.035553,0.01191491,0.07106245,0.01630603,0.02231797,0.01584889,-0.04729511,-0.04106369,-0.01561655,-0.07223053,0.01198803,0.04100209,0.01500488],"last_embed":{"hash":"17lzpgu","tokens":492}}},"last_read":{"hash":"17lzpgu","at":1751288797831},"class_name":"SmartSource","last_import":{"mtime":1751244529124,"size":4013,"at":1751288765474,"hash":"17lzpgu"},"blocks":{"#normalize-range":[1,149],"#normalize-range#{1}":[3,12],"#normalize-range#Usage":[13,36],"#normalize-range#Usage#{1}":[15,36],"#normalize-range#API":[37,137],"#normalize-range#API#wrap(min, max, value)":[39,64],"#normalize-range#API#wrap(min, max, value)#{1}":[41,64],"#normalize-range#API#limit(min, max, value)":[65,72],"#normalize-range#API#limit(min, max, value)#{1}":[67,72],"#normalize-range#API#test(min, max, value, [minExclusive], [maxExclusive])":[73,78],"#normalize-range#API#test(min, max, value, [minExclusive], [maxExclusive])#{1}":[75,78],"#normalize-range#API#validate(min, max, value, [minExclusive], [maxExclusive])":[79,82],"#normalize-range#API#validate(min, max, value, [minExclusive], [maxExclusive])#{1}":[81,82],"#normalize-range#API#name(min, max, value, [minExclusive], [maxExclusive])":[83,87],"#normalize-range#API#name(min, max, value, [minExclusive], [maxExclusive])#{1}":[85,87],"#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])":[88,137],"#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])#{1}":[90,110],"#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])#min":[111,117],"#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])#min#{1}":[113,117],"#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])#max":[118,124],"#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])#max#{1}":[120,124],"#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])#value":[125,131],"#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])#value#{1}":[127,131],"#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])#returns":[132,137],"#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])#returns#{1}":[134,137],"#normalize-range#Building and Releasing":[138,145],"#normalize-range#Building and Releasing#{1}":[140,140],"#normalize-range#Building and Releasing#{2}":[141,141],"#normalize-range#Building and Releasing#{3}":[142,143],"#normalize-range#Building and Releasing#{4}":[144,145],"#normalize-range#License":[146,149],"#normalize-range#License#{1}":[148,149]},"outlinks":[{"title":"![Build Status","target":"https://travis-ci.org/jamestalmage/normalize-range.svg?branch=master","line":5},{"title":"![Coverage Status","target":"https://coveralls.io/repos/jamestalmage/normalize-range/badge.svg?branch=master&service=github","line":6},{"title":"![Code Climate","target":"https://codeclimate.com/github/jamestalmage/normalize-range/badges/gpa.svg","line":7},{"title":"![Dependency Status","target":"https://david-dm.org/jamestalmage/normalize-range.svg","line":8},{"title":"![devDependency Status","target":"https://david-dm.org/jamestalmage/normalize-range/dev-status.svg","line":9},{"title":"![NPM","target":"https://nodei.co/npm/normalize-range.png","line":11},{"title":"range notation","target":"https://en.wikipedia.org/wiki/Interval_(mathematics","line":86},{"title":"-180,180)\"\n```\n\n#### min\n\n*Required*  \nType: `number`\n\nThe minimum value (inclusive) of the range.\n\n#### max\n\n*Required*  \nType: `number`\n\nThe maximum value (exclusive) of the range.\n\n#### value\n\n*Required*  \nType: `number`\n\nThe value to be normalized.\n\n#### returns\n\nType: `number`\n\nThe normalized value.\n\n## Building and Releasing\n\n- `npm test`: tests, linting, coverage and style checks.\n- `npm run watch`: autotest mode for active development.\n- `npm run debug`: run tests without coverage (istanbul can obscure line #'s) \n\nRelease via `cut-release` tool.\n\n## License\n\nMIT © [James Talmage","target":"http://github.com/jamestalmage","line":108}],"last_embed":{"hash":"17lzpgu","at":1751288795593}},"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05124684,-0.03347227,0.06353005,-0.07827707,0.03316673,-0.01570814,-0.06046592,-0.02045469,-0.01890193,-0.01602384,0.06105748,-0.0498938,0.03884412,0.06700642,0.01886317,0.00964157,-0.00378215,0.08637901,-0.0434973,0.03499153,0.06308959,0.03419833,0.06153848,-0.03915172,0.09425227,0.07032555,-0.04689428,-0.02613586,-0.01209805,-0.22372459,0.0351763,-0.03721201,0.0315682,-0.0196459,-0.0238147,-0.01159999,-0.04016658,0.01112318,-0.04848215,0.03823475,0.03099916,0.06089059,-0.02285113,-0.03643504,-0.03633035,0.00455582,-0.0351149,-0.01308285,-0.03732922,-0.03594662,-0.06457473,-0.03038185,0.00661702,0.03051368,0.06081574,0.07989972,0.02670412,0.01989701,0.04190175,0.04554235,-0.01345115,0.069261,-0.19934127,0.04189308,0.06533878,-0.00953921,-0.08937043,0.01475886,-0.00131273,0.0127257,0.02204315,0.01712699,-0.0116616,0.05499615,0.02297488,-0.07490598,-0.01610121,-0.01190678,-0.0217978,-0.01892919,-0.07290378,0.00264856,0.04055989,-0.03681,0.02590513,0.01218877,0.05680265,-0.01075652,0.05128999,-0.02432049,-0.00681645,-0.06043337,-0.0365018,0.06610332,-0.02041125,-0.00931862,0.08448033,0.00461599,-0.018859,0.1262055,0.01122546,0.03114202,-0.0139851,0.04077064,0.05070123,-0.01072563,-0.0684813,-0.05021444,-0.04378745,-0.03445839,-0.00470692,-0.03742635,-0.04022718,-0.10668907,-0.00422763,0.02210552,0.00123135,0.04493413,0.00838047,0.03717923,0.00200045,0.08462721,-0.00240198,-0.00318704,0.07460535,0.03304143,0.07325425,0.06793733,-0.01868336,0.09714507,0.02964397,0.03948096,-0.09085545,-0.05897111,0.04047619,0.019504,-0.01866111,-0.01994277,-0.00738627,0.0123841,-0.02502035,0.01063603,-0.01836418,-0.03984676,0.00922135,0.08329815,-0.10758857,0.04331353,-0.05348038,-0.04486644,0.0200549,-0.02472776,-0.03921875,-0.01372959,-0.02272996,-0.03791945,0.02404749,-0.01766701,-0.07202139,-0.01969058,0.04614805,-0.05294987,-0.01390421,0.06471087,0.02617167,-0.05957246,0.01730904,0.05831022,0.03191463,-0.00052807,0.01360656,0.0190229,0.03385842,0.00499085,0.05287136,-0.00054902,-0.06301329,-0.03589147,0.02042053,-0.00768835,-0.02044668,-0.04100444,-0.02893692,0.0359152,0.02636701,0.00457783,0.04469084,0.00202103,0.04516334,-0.02411151,0.00277734,0.01338343,-0.05927437,0.01407669,-0.03665166,0.03305245,-0.06084673,0.03287646,0.02653084,-0.03510641,0.14590244,0.03940891,-0.02535386,0.03066142,-0.03329115,0.03890429,0.03199532,-0.0598797,-0.01023105,0.04064542,-0.08042413,-0.04544931,0.00823562,0.03258918,-0.04475117,0.00070481,0.01527472,0.05498131,0.03423113,0.06324632,0.00672708,0.00049228,-0.13523164,-0.23719397,0.02077095,0.00025686,-0.01157121,0.01890762,-0.01883094,0.03661015,-0.02612186,-0.02015449,0.04795133,0.12146254,0.00022727,-0.02729879,-0.01974595,-0.03739582,0.01859975,-0.02964617,-0.02605532,-0.07334182,-0.00628782,-0.01665415,0.06176559,-0.11003429,-0.04747445,0.03309646,-0.01211733,0.15730266,-0.018281,0.05508823,-0.03695441,0.02711068,-0.03334301,0.00203767,-0.06979798,0.01964665,0.00546571,-0.05389182,0.03019163,-0.03846641,0.02231089,0.00606662,0.06196123,0.0040116,-0.06757086,0.03722294,0.00259659,-0.01275254,0.02310298,0.02185919,-0.01021342,-0.01688159,-0.00956759,-0.00740501,0.02778874,0.01547314,-0.03427156,-0.09507219,-0.03469536,0.01394444,0.01811072,0.03441318,-0.03273374,0.00651963,-0.06994478,0.0352994,0.05729993,0.04389898,0.00670321,0.05102124,-0.03868868,-0.03801683,0.05307198,0.04251348,0.05213367,-0.01237961,0.02252135,-0.02880302,0.03752122,0.00620282,-0.0410375,0.07247837,0.03363442,0.0260989,-0.00917485,-0.04268644,-0.03603065,-0.0295558,-0.01248726,0.05980918,0.01346948,-0.01659424,-0.02508791,-0.01626856,-0.01951712,0.09408884,-0.02838082,-0.2455727,-0.00536208,0.05576974,-0.06791129,-0.0236675,0.01882673,-0.00034152,-0.0173097,-0.07518614,-0.0000061,-0.03484855,0.07111824,-0.00164461,0.00415096,-0.00524696,0.00454057,0.01374131,0.00869033,0.05388302,-0.06514761,0.06024621,-0.01779211,0.2569612,0.0065897,-0.01440928,0.02223161,-0.03637615,0.01869158,0.03543683,0.05490733,0.00917577,0.05979441,0.04689949,0.0106214,-0.00724437,0.03508423,0.05038525,-0.00623184,-0.00846417,-0.03513415,0.02225344,0.03598853,-0.03895868,-0.04646494,0.1258543,-0.07472,-0.06409302,-0.04291791,0.04871568,0.01240882,-0.07612109,0.04244304,-0.03600453,0.00941485,0.07285838,0.01563662,0.01998593,0.01185192,-0.04735939,-0.03872902,-0.01282043,-0.0742785,0.01554062,0.03981959,0.01323526],"last_embed":{"hash":"17lzpgu","tokens":463}}},"text":null,"length":0,"last_read":{"hash":"17lzpgu","at":1751288795858},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range","lines":[1,149],"size":4003,"outlinks":[{"title":"![Build Status","target":"https://travis-ci.org/jamestalmage/normalize-range.svg?branch=master","line":5},{"title":"![Coverage Status","target":"https://coveralls.io/repos/jamestalmage/normalize-range/badge.svg?branch=master&service=github","line":6},{"title":"![Code Climate","target":"https://codeclimate.com/github/jamestalmage/normalize-range/badges/gpa.svg","line":7},{"title":"![Dependency Status","target":"https://david-dm.org/jamestalmage/normalize-range.svg","line":8},{"title":"![devDependency Status","target":"https://david-dm.org/jamestalmage/normalize-range/dev-status.svg","line":9},{"title":"![NPM","target":"https://nodei.co/npm/normalize-range.png","line":11},{"title":"range notation","target":"https://en.wikipedia.org/wiki/Interval_(mathematics","line":86},{"title":"-180,180)\"\n```\n\n#### min\n\n*Required*  \nType: `number`\n\nThe minimum value (inclusive) of the range.\n\n#### max\n\n*Required*  \nType: `number`\n\nThe maximum value (exclusive) of the range.\n\n#### value\n\n*Required*  \nType: `number`\n\nThe value to be normalized.\n\n#### returns\n\nType: `number`\n\nThe normalized value.\n\n## Building and Releasing\n\n- `npm test`: tests, linting, coverage and style checks.\n- `npm run watch`: autotest mode for active development.\n- `npm run debug`: run tests without coverage (istanbul can obscure line #'s) \n\nRelease via `cut-release` tool.\n\n## License\n\nMIT © [James Talmage","target":"http://github.com/jamestalmage","line":108}],"class_name":"SmartBlock","last_embed":{"hash":"17lzpgu","at":1751288795858}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05621659,-0.0362987,0.06267609,-0.07988324,0.0312327,-0.01460646,-0.05729809,-0.02015553,-0.02283177,-0.01157995,0.06211053,-0.0509803,0.04591788,0.06392743,0.01420174,0.01666717,-0.00946394,0.09182408,-0.04745993,0.03466555,0.0643484,0.03212889,0.05235564,-0.04489269,0.10431947,0.07054302,-0.04638786,-0.02103419,-0.01273653,-0.22350995,0.03801944,-0.03499703,0.03717056,-0.03017011,-0.02392156,-0.0114915,-0.04139354,0.01866651,-0.05344098,0.0327805,0.03848277,0.05703748,-0.02180108,-0.03852854,-0.03389562,-0.00256137,-0.03814631,-0.01157019,-0.04232749,-0.03802822,-0.05600862,-0.03139381,0.00077966,0.03636136,0.06056975,0.07684273,0.03076191,0.01793554,0.04623352,0.04710401,-0.01255986,0.06807102,-0.19835135,0.04327071,0.06656275,-0.01628025,-0.08432934,0.01239604,0.00016336,0.01631197,0.01760368,0.01754236,-0.01287148,0.04942395,0.02375607,-0.07550331,-0.01388138,-0.00614427,-0.01863752,-0.01572465,-0.06604211,0.00397514,0.03446693,-0.03271386,0.02894335,0.00884108,0.05301007,-0.01364084,0.04541487,-0.0246656,-0.0029009,-0.06026078,-0.03823863,0.06443099,-0.0163141,-0.00816794,0.08189835,-0.00604035,-0.02069609,0.13501222,0.01905745,0.0281272,-0.00410042,0.04118073,0.05268551,-0.01702264,-0.07518109,-0.04717082,-0.04770863,-0.03185842,-0.00080968,-0.03327464,-0.03777806,-0.11206647,-0.0080426,0.0167771,-0.0030772,0.05005339,0.00907835,0.030503,-0.0091038,0.08263096,0.00275,-0.00012169,0.07315923,0.02767339,0.06768269,0.06944598,-0.01950459,0.09305464,0.0313652,0.03747452,-0.08667782,-0.06217162,0.04382896,0.02184093,-0.02113238,-0.01542096,-0.01000534,0.02448665,-0.02448114,0.0081077,-0.01933985,-0.03591079,0.00954701,0.08440181,-0.11333376,0.04625879,-0.05115714,-0.04569933,0.01404662,-0.02913527,-0.04045666,-0.01529294,-0.019748,-0.03443001,0.01723889,-0.01891421,-0.06971822,-0.02576127,0.04640067,-0.0502711,-0.01301564,0.07264619,0.02243931,-0.06406463,0.022881,0.05590113,0.04217461,0.00454841,0.00875413,0.01918287,0.03088994,0.00819288,0.05653661,-0.00561792,-0.06400833,-0.03296104,0.02524892,-0.0061771,-0.01578267,-0.03522419,-0.02319307,0.03027708,0.02589934,0.00185574,0.04336242,0.0091513,0.0438494,-0.02781961,-0.00421883,0.01552337,-0.06177521,0.00827905,-0.03778469,0.02988597,-0.06000845,0.02837261,0.02654231,-0.02753394,0.13545437,0.03478435,-0.01581843,0.02554683,-0.03227783,0.03342045,0.03164684,-0.0597198,-0.01602861,0.04573854,-0.0831525,-0.04059588,0.01355299,0.02911665,-0.04228099,0.00628736,0.02182592,0.05252756,0.03179935,0.06311011,0.00790986,0.0066891,-0.13275258,-0.23068875,0.01720056,-0.00528096,-0.0124066,0.02349171,-0.03023564,0.04505839,-0.02647407,-0.02777403,0.04467151,0.12002435,0.00021353,-0.02072727,-0.01815896,-0.03808903,0.01387991,-0.01543018,-0.02693081,-0.06713106,-0.01002448,-0.01916231,0.0663333,-0.10657735,-0.04490281,0.03162032,-0.01176436,0.15968537,-0.01603816,0.0597733,-0.03705721,0.01900918,-0.03121566,-0.00683793,-0.0708067,0.03199169,0.00210218,-0.05447127,0.02395741,-0.04474859,0.02120067,0.00752606,0.06868292,0.00177096,-0.06351169,0.04104742,0.00588117,-0.01022268,0.02739563,0.02244194,-0.01693642,-0.02175106,-0.00931132,-0.00936845,0.01938529,0.02236112,-0.03641731,-0.09844441,-0.0297821,0.0140375,0.02116691,0.04358573,-0.03410891,0.01018878,-0.06893415,0.03868851,0.05120364,0.04378015,-0.00079259,0.05349352,-0.03907841,-0.03442631,0.05274014,0.04084342,0.05201413,-0.01294495,0.02523895,-0.03182922,0.03913363,0.00605692,-0.04777563,0.069042,0.02667714,0.02662092,-0.01300388,-0.04068419,-0.04006682,-0.02031902,-0.01519115,0.06114946,0.01744272,-0.01679692,-0.02904723,-0.01065228,-0.01613951,0.09782792,-0.02263246,-0.24649414,-0.00770425,0.06006839,-0.07768273,-0.02809613,0.01909047,0.00105714,-0.01433496,-0.07071881,-0.00309798,-0.03419571,0.07006484,0.00660201,0.0095159,-0.00291707,-0.00004807,0.01922125,0.00844031,0.05566163,-0.05901688,0.0595158,-0.01703623,0.25853726,0.00818513,-0.01439936,0.01383519,-0.03525665,0.01413592,0.0213767,0.05399402,0.01558968,0.0597239,0.03399673,0.01195229,-0.00648139,0.0417446,0.05465853,-0.01515004,-0.01737529,-0.03412786,0.01869454,0.03197942,-0.03886779,-0.048701,0.13184263,-0.06784155,-0.06286686,-0.03553263,0.04685606,0.00874507,-0.07894804,0.04236735,-0.04482696,0.00439373,0.07277257,0.01594709,0.0151995,0.01310677,-0.05182396,-0.03866085,-0.01130182,-0.07296083,0.01952534,0.03925708,0.01573089],"last_embed":{"hash":"1xg261v","tokens":398}}},"text":null,"length":0,"last_read":{"hash":"1xg261v","at":1751288796160},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#{1}","lines":[3,12],"size":965,"outlinks":[{"title":"![Build Status","target":"https://travis-ci.org/jamestalmage/normalize-range.svg?branch=master","line":3},{"title":"![Coverage Status","target":"https://coveralls.io/repos/jamestalmage/normalize-range/badge.svg?branch=master&service=github","line":4},{"title":"![Code Climate","target":"https://codeclimate.com/github/jamestalmage/normalize-range/badges/gpa.svg","line":5},{"title":"![Dependency Status","target":"https://david-dm.org/jamestalmage/normalize-range.svg","line":6},{"title":"![devDependency Status","target":"https://david-dm.org/jamestalmage/normalize-range/dev-status.svg","line":7},{"title":"![NPM","target":"https://nodei.co/npm/normalize-range.png","line":9}],"class_name":"SmartBlock","last_embed":{"hash":"1xg261v","at":1751288796160}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#Usage": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01884932,-0.02893956,0.05969536,-0.0566305,-0.00198717,-0.01776659,-0.03275096,-0.02264975,0.03641815,-0.02544162,0.00217767,-0.03814246,0.00231762,0.10465903,0.04534482,-0.0117701,0.0014269,0.07954497,-0.07016214,0.00501029,0.08164011,0.03334165,0.02071444,-0.05504908,0.04952461,0.02693769,-0.02582005,-0.03387826,-0.00203229,-0.21144125,0.00559286,-0.03645458,0.00916157,0.00510166,-0.015616,-0.0387672,-0.03664241,-0.04639397,0.01436347,0.07801332,-0.00531491,0.06288783,0.00142575,-0.01548202,-0.03007336,0.03928247,-0.05355133,-0.00341615,0.00831669,-0.0255085,-0.06549906,-0.04342618,0.03079225,-0.01600771,0.08562517,0.0373935,0.04379752,0.07044547,0.04787277,0.0849072,-0.00472838,0.04143878,-0.16387165,0.03334598,0.04871235,-0.01433719,-0.0439783,-0.00720633,0.0123479,0.06972992,0.01815565,0.02045851,-0.00261516,0.08804255,0.0105231,-0.04206444,-0.03227431,-0.03044033,-0.0037893,0.00454521,-0.03272615,-0.04091926,0.01483574,-0.01740265,-0.00554776,-0.00163225,0.02706762,-0.00591718,0.0465597,-0.03008661,-0.03122708,-0.03356195,-0.02132853,0.05194128,-0.03733109,0.01316416,0.04750955,0.00693289,-0.04140897,0.1207173,-0.04416981,0.02358481,0.00965376,0.01057618,-0.0160491,0.00690095,-0.08462241,-0.04977888,-0.01361488,-0.02984754,-0.03846406,-0.02642018,-0.02737352,-0.08700912,0.01376528,0.00530276,0.00591314,-0.01429559,-0.00462782,0.03587725,0.04225909,0.09727697,-0.00845205,-0.02932481,0.05335914,0.07304271,0.06976333,0.0670986,0.00072001,0.11917073,0.01477697,0.05216011,-0.07240642,-0.01300217,0.01221659,-0.00208337,0.00911103,0.02266332,0.03461553,0.00509672,-0.00783547,-0.02253945,-0.0174478,-0.09685684,0.02889151,0.10036708,-0.07514841,0.03456265,-0.00898597,-0.0581064,0.04325666,0.00938087,-0.05704088,-0.05936743,-0.02095754,-0.02319262,0.00963681,-0.00607001,-0.05232372,-0.01964698,-0.01530539,-0.04380032,-0.01883376,0.04585125,0.01017384,-0.06240892,0.01866675,0.04754188,0.02697225,-0.02033048,0.00446176,0.05006918,-0.0088173,0.00853279,0.06911725,0.02586195,-0.04660128,-0.0236282,0.00301361,0.04078703,0.00112346,-0.07337748,-0.05589388,0.04504287,0.03460527,-0.05485813,0.00968422,-0.02024605,0.03568846,0.00749811,-0.01915062,0.01142471,-0.06286124,-0.00399257,-0.01980598,0.00310221,-0.00642409,0.04711634,0.03198316,-0.00412595,0.18758506,0.07241992,-0.04391523,0.05752987,-0.01658883,0.05546754,0.01793312,-0.06794732,-0.01461519,0.01955117,-0.07445402,-0.07456639,-0.01310653,0.1009141,-0.0605335,0.00009447,0.00894445,0.05385553,0.03845518,0.05405053,-0.0061691,-0.00621673,-0.09455083,-0.24269399,0.02947976,0.04095778,-0.04296086,0.03914049,0.021103,-0.01217549,-0.03407869,-0.02962318,0.04827083,0.10841744,0.00789363,-0.02196988,-0.0249119,-0.02105136,0.03519284,-0.02651582,-0.04386121,-0.07285931,0.03859458,0.01609928,0.01494381,-0.06559077,-0.09751303,0.07203709,-0.01515796,0.1545141,-0.05586413,0.04611732,-0.03938231,0.06327982,-0.01877642,-0.0193154,-0.06711575,-0.01303527,0.02025541,-0.05321932,0.03783721,-0.01307667,0.01106238,0.0107834,-0.00411507,-0.02306409,-0.09478684,0.03142236,0.01519491,-0.00979517,-0.02712459,0.03538851,0.02081494,-0.00660275,0.01615082,0.00467249,0.07775625,0.00955087,-0.03483247,-0.04840733,-0.02614112,-0.01330885,0.05272313,-0.03295914,-0.06709504,-0.01675406,-0.04691222,0.01583872,0.06333902,0.07656105,0.04065866,0.01467457,-0.00680598,-0.05888157,0.02267862,0.01738554,0.03396615,-0.02723286,0.00429758,-0.06245779,0.02491185,0.05759684,0.02665776,0.06277265,0.01256378,0.02515755,0.01481039,-0.04070143,-0.01649137,-0.00580452,-0.00446277,0.06561624,-0.00902489,-0.03848689,-0.01453453,-0.05333323,0.03048397,0.07928126,-0.07764789,-0.26808751,-0.00744032,0.02417912,-0.0293278,-0.00056677,0.02944071,0.01672924,-0.03810569,-0.10382766,-0.0035223,-0.04295536,0.07417656,0.00193602,-0.00003068,0.02541814,-0.00458154,0.01369221,0.04127411,0.06830702,-0.06562264,0.06196165,0.00091586,0.24922706,-0.0091715,0.00382625,0.06310926,-0.04179782,0.01561716,0.02649275,0.00378763,-0.03278336,0.03906739,0.10347082,-0.02103699,0.00437838,-0.02037706,0.03525358,0.01790562,0.00084804,-0.02744177,0.00484175,-0.03475298,-0.04421956,-0.00134308,0.11542202,-0.05397126,-0.06535402,-0.08156803,0.02571909,-0.01148714,-0.05591803,0.04320994,0.01647434,0.02313433,0.04725812,0.0295572,0.01675929,-0.00129463,-0.05362431,-0.02550601,0.0059718,-0.04098625,0.01589127,0.0237805,-0.00906099],"last_embed":{"hash":"13qh6yv","tokens":167}}},"text":null,"length":0,"last_read":{"hash":"13qh6yv","at":1751288796394},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#Usage","lines":[13,36],"size":331,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"13qh6yv","at":1751288796394}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#Usage#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02011227,-0.02778742,0.05933695,-0.05493454,-0.00150907,-0.01667262,-0.02891825,-0.02261361,0.03709335,-0.02680229,-0.00001045,-0.03637507,0.00406071,0.10477956,0.0479448,-0.01216892,-0.00124843,0.07888667,-0.07067434,0.00229847,0.08369887,0.03073744,0.01566293,-0.05658925,0.04731322,0.02664437,-0.0264076,-0.03239115,-0.00058717,-0.20951121,0.00639056,-0.03863671,0.00856701,0.00837979,-0.01732093,-0.0397924,-0.0368031,-0.04521971,0.01153827,0.07912146,-0.00383164,0.06333503,0.00042989,-0.01407114,-0.02981354,0.03893269,-0.0556339,-0.00617975,0.00986942,-0.02294569,-0.06300925,-0.04437323,0.03387281,-0.01729959,0.08552036,0.03626914,0.04408854,0.07209151,0.04838431,0.08573861,-0.00633677,0.03970673,-0.16144484,0.03231643,0.04598122,-0.01266595,-0.04441902,-0.00767262,0.01300432,0.07115211,0.01710008,0.02094197,-0.00174081,0.08896329,0.01147963,-0.04036298,-0.03156166,-0.03327486,-0.00710024,0.00295638,-0.03132489,-0.04176519,0.01853672,-0.01673651,-0.00483532,-0.00096826,0.0252351,-0.00912523,0.05039167,-0.03039406,-0.03041486,-0.02908755,-0.01780359,0.05138022,-0.0387965,0.01659205,0.04715038,0.00812679,-0.04407199,0.11893588,-0.04595548,0.02262409,0.01251017,0.00966341,-0.02015838,0.0060026,-0.08592287,-0.05022012,-0.01376726,-0.03099615,-0.03956314,-0.02554971,-0.02771182,-0.08423483,0.01468502,0.00630807,0.00400932,-0.01412669,-0.00637777,0.03534355,0.04638093,0.09726436,-0.00816054,-0.02696273,0.05033212,0.07338736,0.06759294,0.06728369,0.00030092,0.11783201,0.01558978,0.05279148,-0.07173882,-0.01227907,0.01491141,-0.00157587,0.01198808,0.0237863,0.03566342,0.00681014,-0.00516923,-0.02031982,-0.01795829,-0.09879296,0.02765945,0.10134961,-0.07470953,0.03385109,-0.00712652,-0.05754277,0.04450185,0.01222084,-0.05883877,-0.05882516,-0.01994158,-0.0223753,0.00901387,-0.0041192,-0.05047018,-0.01819974,-0.01968487,-0.04289226,-0.01764,0.04700261,0.00589575,-0.06173509,0.01957633,0.04854991,0.02572267,-0.02053188,0.00315223,0.04932322,-0.01158352,0.00674919,0.06674026,0.02596485,-0.04633756,-0.02320941,0.00249253,0.04183691,0.00162015,-0.07370723,-0.05863193,0.04614908,0.03342919,-0.0579857,0.00925572,-0.02218196,0.03366848,0.00795399,-0.02030268,0.01220464,-0.06207029,-0.00401337,-0.0179171,0.00593933,-0.00686281,0.04565273,0.03192815,-0.00511362,0.18878523,0.07004187,-0.04104866,0.05708126,-0.01542069,0.05759747,0.01623739,-0.06908643,-0.01451748,0.02051267,-0.07431914,-0.07193343,-0.01320577,0.10284106,-0.05835202,0.00178547,0.0073127,0.05427399,0.03823783,0.05232728,-0.00432052,-0.0079929,-0.09256431,-0.24206422,0.02857113,0.04269288,-0.04164072,0.04062788,0.01916584,-0.01186658,-0.03529444,-0.03064166,0.04632214,0.10353325,0.0061779,-0.02133268,-0.02685499,-0.01917365,0.03537904,-0.02557803,-0.04575087,-0.07243136,0.04017135,0.01698839,0.01080327,-0.06169229,-0.09824486,0.07514247,-0.01599544,0.15615571,-0.05510848,0.04702348,-0.04112305,0.06325608,-0.01891186,-0.01857272,-0.06805921,-0.01397081,0.01717765,-0.05427406,0.0361304,-0.01136959,0.0074991,0.00967938,-0.00512234,-0.0259028,-0.09682917,0.03522998,0.0159003,-0.01110254,-0.03452089,0.03347977,0.02231242,-0.00506999,0.01845033,0.00611553,0.07651874,0.01079915,-0.0367313,-0.04660331,-0.02772546,-0.01631314,0.05112895,-0.03418371,-0.07130828,-0.01689438,-0.04897409,0.01739141,0.06372797,0.07788239,0.04151899,0.01482266,-0.00764559,-0.06122929,0.02260234,0.01886457,0.0338054,-0.02815884,0.0040575,-0.06228457,0.02264828,0.05887948,0.02736021,0.06178862,0.01433397,0.0246422,0.01661478,-0.0395014,-0.01727508,-0.00193411,-0.00328434,0.06601501,-0.00989651,-0.04136675,-0.01507847,-0.05391422,0.030712,0.07726276,-0.07875764,-0.26759237,-0.00567176,0.02334281,-0.02636071,0.00144393,0.0305452,0.01752172,-0.03740194,-0.10206815,-0.00022341,-0.04120725,0.07424948,0.0075069,-0.00092254,0.02918918,-0.00677977,0.01685116,0.04051549,0.06921205,-0.06527303,0.06212756,-0.00003866,0.24708532,-0.01106345,0.0041567,0.06618629,-0.0404467,0.01480639,0.02648049,0.00041354,-0.03586151,0.03674202,0.10600628,-0.020684,0.00650621,-0.02383518,0.03301812,0.01602955,-0.00088071,-0.02813577,0.00791361,-0.03694391,-0.04654237,-0.00200155,0.11533399,-0.05286257,-0.06324965,-0.0837797,0.02609587,-0.01469154,-0.05463703,0.04321798,0.01493641,0.02018506,0.04838444,0.028737,0.01820669,0.00164268,-0.05499108,-0.02735971,0.00566707,-0.03857231,0.01632849,0.02391285,-0.01000618],"last_embed":{"hash":"n8f6fj","tokens":166}}},"text":null,"length":0,"last_read":{"hash":"n8f6fj","at":1751288796484},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#Usage#{1}","lines":[15,36],"size":321,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"n8f6fj","at":1751288796484}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03186765,-0.04058035,0.01725915,-0.05523081,0.01215403,-0.02122064,-0.03758885,0.00054555,0.04313133,-0.03436005,0.05366687,-0.03043935,0.01680423,0.09190282,-0.00256896,0.03696399,0.01956048,0.1020613,-0.1342407,0.01006494,0.08763552,-0.00942678,0.00548058,-0.05194726,0.10799052,0.05552694,-0.02468309,-0.06395239,0.01665405,-0.2229262,0.03144566,-0.08218527,-0.03311075,-0.04790035,-0.01896835,-0.01997448,-0.0455096,-0.03683889,-0.03582473,0.0669786,0.01763878,0.05031691,0.00748581,-0.0153187,-0.04660033,0.01039536,-0.07899809,-0.03645065,-0.06472591,-0.00594694,-0.04141914,0.03781675,-0.00976349,0.00280844,0.05125761,0.04292796,0.04970242,0.04873166,-0.00478264,0.07661431,0.03301923,0.03895892,-0.14216697,0.04420771,0.06532659,0.01428566,-0.05097499,-0.03354684,-0.02062455,0.03916408,0.01838866,-0.00361218,0.00376481,0.07635075,0.04375453,-0.05531017,-0.02627698,-0.00722518,-0.00231634,0.01378273,-0.06925485,-0.04143932,0.02913954,-0.02418695,0.07935182,-0.00147753,0.06685222,-0.00626367,0.02398065,-0.03677556,-0.0133131,-0.0321758,-0.04762498,0.0384361,-0.02100172,-0.00413211,0.07922964,0.03691544,-0.04810011,0.12420062,0.01144288,0.01795923,-0.0512255,0.00909182,0.02511848,0.00049234,-0.07298413,-0.05262887,-0.01587308,-0.05189596,-0.05800111,0.00407684,-0.06313543,-0.07563193,-0.03007943,0.02895449,0.050646,-0.01815525,0.04870107,-0.0073187,0.01637254,0.08751705,0.01155496,-0.03127231,0.00225504,0.07503562,0.07476294,0.05198125,-0.01675401,0.09074135,0.04542173,-0.00951763,-0.03739733,-0.03154406,0.0290497,0.02711127,0.00356041,0.03886016,0.04167266,0.01800504,-0.01639633,-0.0671317,-0.04108671,-0.08331216,-0.00311197,0.11194106,-0.11563801,0.04056744,-0.02042427,-0.04852465,0.03306679,-0.05392857,-0.06429263,-0.0428783,-0.01883239,-0.01122429,0.03196003,-0.034254,-0.03037806,-0.02255945,0.0103429,0.03152388,-0.04555188,0.07826211,0.00055849,-0.04546604,0.06829897,0.02886975,0.04508782,-0.02914977,0.01224529,0.02046067,0.02234389,-0.0070584,0.07983289,0.01102669,-0.07393776,-0.01425139,-0.02605847,0.04677961,0.00284462,-0.05252071,-0.02387919,0.00968884,0.02317403,-0.03687241,0.02794741,0.01424585,0.0398583,-0.03295608,-0.03225733,-0.02352978,-0.03316604,0.01548552,-0.03080751,0.02862423,-0.02649366,0.03776683,-0.01365124,0.01388137,0.14300151,0.03868911,-0.01849479,0.02491218,-0.01326875,0.031782,0.06425057,-0.08364413,0.03539437,0.05974775,-0.06105361,-0.07048631,-0.00667872,0.04202042,-0.0233118,0.01212473,0.03980762,0.05892789,0.02010989,0.05421836,-0.02730872,-0.01463255,-0.1111918,-0.21087573,0.02972614,0.02556062,-0.01618676,0.01874856,-0.03192693,0.01809956,-0.0112794,-0.00583177,0.0513654,0.1005545,-0.01148502,-0.05066506,0.00395749,-0.04535613,-0.01649442,-0.03665692,0.00847486,-0.05870438,0.0432987,-0.00591177,-0.01026214,-0.05344489,-0.06505696,0.02809,-0.04210793,0.1699685,-0.03472539,0.06068574,-0.02150127,0.02260374,-0.02658658,-0.01707546,0.00964584,-0.00151799,0.00390994,-0.05901289,0.00680673,-0.03555888,0.01976465,-0.0052532,0.01214501,-0.00952783,-0.07778867,0.01414812,-0.00859006,0.01985624,-0.03614995,0.02439658,0.0294012,-0.0493581,-0.04613084,0.00300664,0.0262739,0.02245036,-0.02025552,-0.03293734,-0.05038829,0.00013879,0.02270943,-0.01519002,-0.04307035,-0.00583212,-0.05090887,0.0224421,0.05420507,0.08210524,0.03221695,0.02951473,-0.03564541,-0.00980198,0.08728708,-0.01506808,0.00236627,-0.01967453,0.0519399,-0.00093373,0.06337081,0.04398957,0.04087931,0.06120576,0.03034643,0.03146308,-0.00927056,-0.00845482,-0.02049837,-0.01623927,-0.01746668,0.02627569,0.00392336,-0.01056159,0.01419147,-0.01361763,-0.0485359,0.059685,-0.09577401,-0.25527179,-0.00405367,0.01575993,-0.04078206,-0.04395268,0.01149542,0.01091112,0.00834174,-0.14292316,0.00902097,-0.02636654,0.06303953,-0.00063087,0.00814941,0.00170913,0.0178458,0.02561302,0.03464894,0.05285896,-0.03496252,0.05009649,0.03244012,0.24826242,-0.04310842,0.00582974,0.05941013,-0.04348108,0.02447459,0.04682092,0.01668472,0.03369684,0.06397437,0.05833101,0.00458104,0.01782077,0.00678981,0.00195133,-0.01090869,0.01078106,-0.02998712,0.03771347,-0.04937022,-0.02223003,-0.01687239,0.14486983,-0.03384189,-0.07316776,-0.08814646,0.01301019,0.00402741,-0.07012733,0.04679227,0.00720327,-0.01625551,0.06623226,-0.00810021,0.03946732,-0.00044724,-0.01798406,-0.03641543,0.01837807,-0.05509806,0.01026692,0.03705481,0.04425365],"last_embed":{"hash":"g8rp8a","tokens":474}}},"text":null,"length":0,"last_read":{"hash":"g8rp8a","at":1751288796575},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API","lines":[37,137],"size":2364,"outlinks":[{"title":"range notation","target":"https://en.wikipedia.org/wiki/Interval_(mathematics","line":50}],"class_name":"SmartBlock","last_embed":{"hash":"g8rp8a","at":1751288796575}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#wrap(min, max, value)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03924557,-0.0374806,0.01657157,-0.05850972,0.00005863,-0.01241666,-0.02702797,-0.00236352,0.03404352,-0.01981578,0.05224779,-0.03221699,0.02138306,0.09568879,-0.00226706,0.03214828,0.0251187,0.11523117,-0.12116655,0.00799109,0.10034205,-0.01307274,0.00268572,-0.04703115,0.09476058,0.07215831,-0.02644373,-0.06129226,0.02332309,-0.21109435,0.03886325,-0.07868419,-0.03621498,-0.03714124,-0.01781743,-0.01781506,-0.04621628,-0.02542555,-0.05756767,0.0649285,0.01326771,0.04981443,0.00284445,-0.01673298,-0.03934795,0.01626963,-0.08596751,-0.03732217,-0.06372923,-0.00830633,-0.0499595,0.03386788,0.00114701,-0.00837836,0.05304751,0.02933753,0.05386186,0.05430409,-0.01182194,0.07622787,0.02832945,0.03450409,-0.13709716,0.05181026,0.05521796,0.01908483,-0.05498296,-0.02746004,-0.018046,0.05433298,0.01056287,-0.00019515,-0.00045246,0.06820849,0.04623029,-0.05650099,-0.03565242,-0.01278022,0.00454597,0.00576696,-0.06768466,-0.03735253,0.03983857,-0.01651553,0.0596242,0.00388928,0.05638562,-0.00478843,0.02514935,-0.03158774,-0.01838379,-0.03338609,-0.02696864,0.03738746,-0.02696087,-0.00154998,0.07442103,0.04372916,-0.05249403,0.13178048,0.00923165,0.00359188,-0.0436792,0.0097252,0.01742912,0.00171704,-0.06925212,-0.05448452,-0.02333726,-0.0546951,-0.0662048,0.00906663,-0.07737881,-0.08219947,-0.02674603,0.01357796,0.0530755,0.0016145,0.03572354,0.00774678,0.02120473,0.09899293,0.01858412,-0.01637872,0.00947772,0.06237587,0.05960242,0.04486596,-0.01270759,0.1010969,0.04413555,0.01519179,-0.03014215,-0.03603578,0.03576123,0.02676529,0.00659533,0.03129855,0.03390914,0.00153222,-0.01731325,-0.05340505,-0.02304724,-0.08231768,-0.00220661,0.11202145,-0.11098226,0.05311738,-0.030592,-0.02759961,0.04662471,-0.0449153,-0.06349839,-0.0509003,-0.02557644,-0.02270097,0.03430138,-0.02658554,-0.02642256,-0.01258039,0.00633367,0.02892004,-0.03274689,0.08275719,0.00438565,-0.04911162,0.08326305,0.03434107,0.03998739,-0.0188506,0.0158391,0.0207453,0.01842466,0.00221729,0.0901669,0.02326889,-0.06100707,-0.01858974,-0.0224116,0.05041371,0.00480172,-0.06821357,-0.02570965,0.00332181,0.0148165,-0.03105322,0.03265794,0.00151123,0.04194082,-0.04650116,-0.03609317,-0.01255924,-0.02778395,0.01973501,-0.04024084,0.03952453,-0.01876075,0.03035284,-0.02425792,0.02330892,0.14428473,0.03286231,-0.02615956,0.02720121,0.00008139,0.01755298,0.06857987,-0.08296262,0.03547765,0.07767999,-0.06465089,-0.07115742,-0.01128754,0.04490699,-0.01942435,0.02238331,0.02841402,0.05917411,0.01480517,0.0540454,-0.02706727,-0.01530299,-0.11384918,-0.21217622,0.02300622,0.01910648,-0.03456075,0.02033964,-0.03175208,0.02301986,-0.02004901,-0.01019773,0.05709552,0.10837095,-0.0160937,-0.05744447,-0.00480817,-0.05102398,-0.01747067,-0.03566151,0.00205436,-0.05035168,0.0340903,-0.00591216,-0.00340449,-0.04843355,-0.06834284,0.03178094,-0.03805187,0.17113183,-0.03736958,0.06174992,-0.02003151,0.03665057,-0.03932659,-0.02141402,0.00620189,0.00133917,0.00289458,-0.04783822,-0.00230805,-0.03050491,0.01408674,-0.01179612,0.01914943,-0.01318257,-0.06704822,0.01615715,-0.01484743,0.01444454,-0.05135439,0.02053363,0.02270001,-0.05624004,-0.04646964,0.01381636,0.02124456,0.01382372,-0.01328279,-0.02491612,-0.05732805,0.00369307,0.03067422,-0.02275711,-0.04110354,0.00035959,-0.05895457,0.01668585,0.06527997,0.06902656,0.03638546,0.0242473,-0.03351163,-0.01925757,0.09290374,-0.01959835,-0.00072416,-0.01104891,0.05159028,0.00539317,0.05882568,0.0431543,0.03130827,0.05034509,0.0246975,0.02857593,-0.01811351,-0.01804731,-0.01682413,-0.0128471,-0.01966306,0.02897033,0.01221954,-0.01108642,0.02765895,-0.00951468,-0.03256324,0.06015806,-0.09380308,-0.26526031,0.00484129,0.01920339,-0.04122902,-0.04427175,0.02284045,0.00361486,-0.00044692,-0.13422668,0.01823188,-0.02359426,0.06036203,0.00749076,0.00439306,0.01208876,0.02392137,0.02135549,0.02761702,0.04886611,-0.03616,0.05275157,0.02387156,0.25233552,-0.04409648,0.00310923,0.05042699,-0.04753497,0.02540046,0.03759567,0.01526576,0.01987229,0.0701696,0.04557361,0.01149095,0.01240093,-0.0022545,0.00849177,-0.00694719,0.01457737,-0.02754752,0.0273397,-0.05722723,-0.01537405,-0.02347842,0.13485005,-0.03392655,-0.09374638,-0.09786953,0.0049214,0.00231133,-0.0549906,0.04052352,0.00081057,-0.02044244,0.06262428,-0.00818622,0.03566065,-0.0124199,-0.02123436,-0.02084716,0.01629012,-0.06245187,0.00361934,0.04568713,0.04831776],"last_embed":{"hash":"beu50w","tokens":317}}},"text":null,"length":0,"last_read":{"hash":"beu50w","at":1751288796886},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#wrap(min, max, value)","lines":[39,64],"size":790,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"beu50w","at":1751288796886}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#wrap(min, max, value)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03717138,-0.03721899,0.0160746,-0.05931491,0.00008944,-0.00825534,-0.02529986,0.00034474,0.0339064,-0.02249348,0.05533945,-0.03321963,0.02320861,0.09471145,-0.0005651,0.03642053,0.02106351,0.11502118,-0.12256704,0.00737464,0.10027901,-0.01644681,0.00200541,-0.04728343,0.09587833,0.06909971,-0.02590551,-0.06355247,0.02213557,-0.21003635,0.03996623,-0.08063634,-0.04029366,-0.03731551,-0.0170801,-0.0143007,-0.04798914,-0.02416032,-0.05861151,0.06382906,0.01409459,0.05161409,0.00382197,-0.01579487,-0.03780026,0.0161971,-0.08490054,-0.0425395,-0.06376621,-0.00807305,-0.04826206,0.03817609,-0.001757,-0.00705108,0.04862672,0.02724196,0.05349421,0.04686796,-0.01703891,0.07751943,0.03006561,0.03477673,-0.13472322,0.05190298,0.05425546,0.02056942,-0.05472388,-0.02692582,-0.02181461,0.05500468,0.01029177,-0.00234854,-0.00154797,0.06621924,0.0475644,-0.05534573,-0.03639594,-0.01340128,0.00309886,0.00503742,-0.06549282,-0.03479831,0.04245632,-0.01798856,0.06083316,0.00642432,0.05632355,-0.00967596,0.02246841,-0.02846927,-0.01512772,-0.02988373,-0.02741308,0.03781588,-0.027545,-0.00196687,0.07517852,0.04338488,-0.05343639,0.13364123,0.01028932,0.00410245,-0.04100381,0.00858938,0.01861688,0.00027135,-0.07013166,-0.05244923,-0.02486967,-0.05507356,-0.06634445,0.00909016,-0.07918215,-0.08063897,-0.02896227,0.01585073,0.05215894,-0.00046242,0.03731119,0.00959737,0.0213993,0.10122024,0.02084892,-0.01512925,0.00821795,0.0606035,0.0578009,0.04526503,-0.01470794,0.09978545,0.04393465,0.01452728,-0.02678959,-0.03649364,0.03733124,0.02527122,0.00859007,0.03213288,0.03314701,-0.00129023,-0.01705049,-0.0536706,-0.02417101,-0.08348529,-0.00389511,0.1092567,-0.11309945,0.05027333,-0.03220199,-0.02432303,0.04933708,-0.04714071,-0.06526925,-0.05291101,-0.02784339,-0.02415308,0.03431443,-0.0260557,-0.0274015,-0.01319285,0.00591355,0.03341074,-0.03518598,0.08539228,0.00223736,-0.04802204,0.0851882,0.03348568,0.04080068,-0.01646164,0.01651963,0.02025846,0.0178169,-0.00138281,0.09139923,0.02286485,-0.0626144,-0.01682932,-0.02291399,0.04847429,0.01075876,-0.06655803,-0.02268102,0.00324326,0.01448059,-0.03361724,0.03067488,0.00350325,0.04077085,-0.04640123,-0.03567932,-0.01442437,-0.02633959,0.02090963,-0.03893369,0.04030042,-0.01801238,0.03001481,-0.02528624,0.02332235,0.14103949,0.03261186,-0.02620586,0.02498255,-0.00174986,0.01892427,0.06779701,-0.086169,0.03501544,0.07496063,-0.06404251,-0.06723402,-0.01220207,0.04276734,-0.01677224,0.02839464,0.0261539,0.05948319,0.01559583,0.05162684,-0.02735173,-0.01652977,-0.10744188,-0.21129267,0.02209319,0.01928732,-0.03489704,0.02235355,-0.02923499,0.02450558,-0.01868884,-0.00978328,0.05527575,0.10666794,-0.0196655,-0.05887438,-0.00704435,-0.05120252,-0.01889113,-0.03451706,0.00054528,-0.04930331,0.03651899,-0.0060275,-0.00493656,-0.04727846,-0.06692555,0.03491357,-0.03922657,0.17183237,-0.03818772,0.06414383,-0.01773907,0.03537102,-0.0365331,-0.02334703,0.00603074,0.00530125,-0.00207138,-0.04577398,-0.00335326,-0.02997008,0.01275042,-0.01116713,0.01691303,-0.01450919,-0.0674577,0.01751145,-0.01308956,0.01670394,-0.05130862,0.02101645,0.02062158,-0.05884814,-0.04709333,0.01454577,0.02137971,0.01724727,-0.01295493,-0.02486689,-0.05811901,0.002023,0.02984776,-0.01994441,-0.04370305,0.00226743,-0.06248925,0.01471558,0.06829253,0.06970309,0.03524332,0.02193716,-0.03715692,-0.01644469,0.09485042,-0.01890069,-0.00397177,-0.01221339,0.05220536,0.00519777,0.05903374,0.04240093,0.03229912,0.05321564,0.02678707,0.02995884,-0.01807321,-0.01736802,-0.0205762,-0.0137177,-0.0198275,0.02992164,0.0144275,-0.01337327,0.02735317,-0.00640665,-0.03247984,0.06284823,-0.09807181,-0.26337513,0.00545704,0.02240255,-0.0403026,-0.04359098,0.02064821,0.00318982,0.00206338,-0.13379437,0.01853177,-0.0210187,0.05539622,0.00811218,0.00102084,0.01325292,0.01989505,0.02354047,0.02456404,0.05092416,-0.03521448,0.0524441,0.02390655,0.25132972,-0.04577345,0.00453525,0.04943682,-0.0469663,0.02325633,0.03683968,0.01800346,0.02420649,0.07233187,0.04210455,0.01132657,0.00943566,0.00099342,0.00251295,-0.0081057,0.01482077,-0.02814536,0.03354426,-0.05879349,-0.01803008,-0.02597638,0.13389032,-0.03208062,-0.09123486,-0.09843984,0.00683727,0.00152132,-0.05332448,0.04623115,-0.00070979,-0.02550336,0.06445675,-0.01014659,0.0368492,-0.01232384,-0.02019833,-0.0230528,0.0160338,-0.06182422,0.00744219,0.0435805,0.05239584],"last_embed":{"hash":"5rf1wr","tokens":315}}},"text":null,"length":0,"last_read":{"hash":"5rf1wr","at":1751288797056},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#wrap(min, max, value)#{1}","lines":[41,64],"size":763,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"5rf1wr","at":1751288797056}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#limit(min, max, value)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04490147,-0.03762628,0.04670263,-0.05803316,0.0167929,-0.03119997,-0.03594112,0.0168059,0.06414367,-0.02054951,0.04138672,-0.01944458,0.02420258,0.08056319,-0.00615311,-0.01968059,0.03431226,0.05974578,-0.14534441,0.03334002,0.07599505,0.01304059,0.03720701,-0.05887307,0.10488132,0.02196551,-0.04584792,-0.03617685,0.03537576,-0.22927448,0.01270421,-0.02959851,0.005631,-0.03713313,0.02743055,-0.01481481,-0.01420296,-0.06599327,-0.00974451,0.07706405,0.01825752,0.08262217,0.00178718,-0.0190617,-0.05541499,-0.01579653,-0.0431014,-0.02867565,-0.0582407,-0.00353941,-0.06172458,-0.00532321,0.01387317,0.03314982,0.05788509,0.07385084,0.02613895,0.06610348,0.04105713,0.0616196,0.01187898,0.04436368,-0.1404409,-0.01098709,0.06577108,0.01257622,-0.04568307,-0.03983698,-0.03522525,0.0115323,0.04912495,0.01823599,-0.00347659,0.07417243,0.06307936,-0.03828694,-0.02838247,-0.0084349,0.01782618,0.03089831,-0.08866885,-0.07689471,-0.00603085,-0.02306075,0.04556717,-0.04863955,0.03554652,0.01842983,0.03712498,-0.03114692,-0.02388711,-0.04993169,-0.05226182,0.06744849,-0.00374967,-0.01700728,0.09124796,0.01925236,-0.02284058,0.13449472,-0.00325011,0.05905322,-0.05745263,0.00284425,0.031335,0.03778742,-0.05187231,-0.01060016,-0.00247919,-0.02468679,-0.03407254,-0.00944566,-0.02522695,-0.07877944,-0.01023504,0.03349766,0.03453311,0.00353959,0.01376356,0.00267058,0.01908201,0.0368863,0.00446122,-0.06289369,0.0271483,0.07063023,0.07204258,0.0546019,-0.05107114,0.09431609,0.02402457,-0.05614401,-0.08457826,-0.03329178,0.03130659,-0.0210134,0.0138549,-0.02552545,0.05066645,0.03521632,-0.01015955,-0.06967937,-0.03732439,-0.08485929,-0.02340603,0.12915339,-0.06738228,0.01522069,-0.01212377,-0.0825785,-0.01051338,-0.04431014,-0.05956477,-0.05024991,-0.00210423,-0.00944756,0.02884633,-0.03772364,-0.04119222,-0.0390658,0.02379453,0.00160438,-0.06975171,0.07215049,0.00407921,-0.01578014,0.01696497,0.0236458,0.00988444,-0.02865367,0.00906504,0.04742211,0.00645575,-0.00067949,0.08067699,0.00945952,-0.08835953,-0.04852089,-0.04194526,0.0331318,-0.02048757,-0.00368485,-0.02956039,0.00754494,0.05471659,0.00189146,0.04013237,0.02544852,0.02277956,0.00065258,-0.02510946,-0.01037067,-0.05497094,-0.01109988,-0.01752633,-0.00810939,-0.00215675,0.04261672,-0.01491228,-0.00443278,0.13954306,0.05935108,-0.01085515,0.05175854,-0.01381062,0.04201608,0.01907123,-0.06518103,0.04070456,0.03991853,-0.06544495,-0.10164828,0.04195875,0.04910991,-0.05526219,-0.04217359,0.04084019,0.05040543,0.0068639,0.01517359,-0.02065174,0.01178398,-0.1079986,-0.20709583,0.05861975,-0.00260779,0.01417614,0.03385985,-0.01926718,0.02090127,-0.02002823,-0.02318716,0.0570964,0.06897312,-0.00535582,-0.04165144,0.00314051,-0.02931219,-0.00927068,-0.04517466,0.0182762,-0.06408408,0.05923986,0.03057382,-0.00593578,-0.08320138,-0.06926121,0.00794018,-0.01294806,0.15634783,-0.04073248,0.05127313,-0.036941,0.01906537,-0.03478749,-0.00576888,-0.03015,-0.0156197,0.04942035,-0.06731423,0.02202745,-0.02488602,0.03160121,0.00515133,0.00508681,0.01516998,-0.07182464,0.02131557,0.0045321,-0.0154786,0.03368667,0.01885644,0.02923324,-0.02549491,-0.0028107,-0.03636663,0.06485839,0.04078427,-0.02205116,-0.06321997,-0.01126937,0.00705076,0.02360316,0.00385982,-0.05886003,-0.03513552,-0.05355865,0.05632044,0.00222588,0.07706587,0.01694714,0.00438836,-0.01816055,0.00700826,0.04378821,-0.01229947,0.02144946,-0.02617501,0.03686132,-0.02937914,0.0559236,0.03250785,-0.01889494,0.06883407,-0.01414749,0.02061655,-0.00740282,0.01654294,-0.01051987,0.01495783,-0.00632376,0.00205745,-0.01902777,0.02026441,-0.00667079,-0.05371501,-0.03275296,0.0352665,-0.05662051,-0.25608727,-0.01269711,0.02526547,-0.02752342,-0.01737584,0.06603199,0.01573533,-0.00626314,-0.10040492,0.00098894,-0.00367025,0.07923959,0.00582939,0.02800467,-0.02175801,0.00259972,0.03022589,0.03126603,0.04868154,-0.04386551,0.0248559,0.00930563,0.24282032,-0.03538209,0.00806825,0.05520898,-0.03530347,0.04672404,0.03766823,0.01293815,0.03380711,0.03801938,0.14516725,-0.01973472,0.02268859,0.02532813,0.00561273,-0.02463767,0.02114678,-0.01163233,0.00225322,-0.04654776,-0.03513986,0.02600243,0.16456863,-0.07097083,-0.0735923,-0.07960191,0.01240691,0.02458895,-0.10898335,0.03896054,0.03606346,-0.01360543,0.06169847,0.01285554,0.03545663,0.0043909,-0.02387769,-0.02706929,-0.00075066,-0.00411719,0.02050602,0.00400103,-0.00720675],"last_embed":{"hash":"eof1ds","tokens":104}}},"text":null,"length":0,"last_read":{"hash":"eof1ds","at":1751288797213},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#limit(min, max, value)","lines":[65,72],"size":277,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"eof1ds","at":1751288797213}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#limit(min, max, value)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04260421,-0.0375097,0.04668323,-0.05618484,0.01513602,-0.02310704,-0.03636047,0.0182742,0.06631897,-0.02168071,0.0451373,-0.01563009,0.0245027,0.08116213,-0.00385069,-0.01521995,0.0317431,0.06140239,-0.14329645,0.03255041,0.07388119,0.00799228,0.03430366,-0.0607623,0.10982909,0.01758362,-0.04199924,-0.04012693,0.03511058,-0.23214683,0.01093934,-0.0288879,-0.00001237,-0.03353665,0.02517355,-0.01088059,-0.01076283,-0.06563685,-0.00848285,0.08076901,0.01509441,0.08577999,0.00006383,-0.01815044,-0.05717822,-0.02168957,-0.04176868,-0.0338584,-0.05807519,-0.00257755,-0.05809139,-0.00241345,0.01723491,0.03488665,0.05480365,0.07087306,0.02449733,0.06422112,0.03968204,0.06085031,0.01433022,0.04285771,-0.13864808,-0.0155448,0.06262542,0.01598516,-0.0500282,-0.03666995,-0.04236559,0.02031652,0.05163144,0.02470469,-0.00696068,0.07512818,0.06291249,-0.03051364,-0.03163241,-0.00802559,0.01717521,0.02909565,-0.08458677,-0.08117118,-0.00553378,-0.02722342,0.04412315,-0.04887276,0.03225625,0.01008302,0.0383891,-0.0323339,-0.02403331,-0.05011078,-0.05008956,0.06749861,-0.00493579,-0.01467207,0.09528115,0.01774748,-0.02747777,0.13489921,-0.00043257,0.05949338,-0.05232524,0.0072279,0.02949152,0.04137375,-0.0503706,-0.01029761,-0.00354899,-0.02775665,-0.02880586,-0.01219616,-0.02561947,-0.07528234,-0.0123516,0.03382127,0.03387237,-0.00365049,0.01489674,0.0028224,0.0209101,0.03953412,0.01180955,-0.06611501,0.0247901,0.07004733,0.06653693,0.05482953,-0.04936196,0.08686442,0.02433017,-0.06165879,-0.07720872,-0.03266849,0.03538212,-0.024629,0.02041117,-0.02593283,0.05293948,0.0304503,-0.00668532,-0.07091339,-0.03266441,-0.08693597,-0.02680642,0.13159703,-0.06640215,0.01117405,-0.01178351,-0.08414419,-0.0083269,-0.04485516,-0.06144057,-0.05491809,-0.00392238,-0.00635985,0.02911346,-0.03252368,-0.04341488,-0.03971494,0.0210463,0.00429538,-0.07067195,0.07372352,0.00069349,-0.01233318,0.01638844,0.02589975,0.01139528,-0.03028266,0.00725983,0.04350701,0.00443459,-0.00182225,0.0823994,0.00668129,-0.09043161,-0.04992361,-0.04812827,0.0390368,-0.0210586,-0.00025711,-0.03220338,0.00975469,0.05765927,0.00249282,0.03579608,0.02465251,0.0209505,-0.00018306,-0.03104411,-0.0089717,-0.04897907,-0.01131535,-0.01675544,-0.00891172,0.00124795,0.04568081,-0.01541954,-0.00586683,0.13428353,0.05763513,-0.01382538,0.05672128,-0.01340001,0.04440429,0.01614144,-0.07001342,0.03778926,0.03908045,-0.06396454,-0.10296004,0.0437089,0.04841986,-0.05500796,-0.03672789,0.03343714,0.0542501,0.00689358,0.00804467,-0.01855965,0.01395893,-0.10203469,-0.20816129,0.05687617,-0.00237125,0.01529454,0.03806217,-0.01546498,0.02231233,-0.0233827,-0.02376914,0.05264183,0.06582997,-0.0103863,-0.04517389,0.00117669,-0.0287478,-0.01372267,-0.04633811,0.01714044,-0.06156638,0.06182148,0.03128333,-0.00674251,-0.08298433,-0.06865363,0.00982937,-0.01574528,0.15411097,-0.04259937,0.05545829,-0.03576179,0.01810584,-0.03139838,-0.00757309,-0.02781137,-0.01290557,0.04687198,-0.07097419,0.0182964,-0.02450895,0.03391786,0.00659649,0.00330662,0.01500351,-0.07501563,0.02102426,0.00384652,-0.01281059,0.03570554,0.01704986,0.03307603,-0.02130418,-0.00274753,-0.03881335,0.06585129,0.04510608,-0.02091435,-0.06630796,-0.01207639,0.00364514,0.01851076,0.00809125,-0.06287058,-0.03205939,-0.0495589,0.05698611,0.00332472,0.07769828,0.01790204,0.00276719,-0.01771749,0.01093527,0.04662985,-0.01356675,0.02042983,-0.02884596,0.03828861,-0.02763035,0.05536399,0.02783878,-0.01753823,0.0681797,-0.01956789,0.02246183,-0.00359125,0.01328074,-0.01005452,0.01577902,-0.00111093,0.00180636,-0.01631896,0.01556793,-0.0031254,-0.05327395,-0.02871727,0.0333408,-0.06514727,-0.25443891,-0.01264837,0.02533488,-0.02490497,-0.01546905,0.06392531,0.01944565,-0.00391991,-0.10173752,0.00014109,-0.00264441,0.07879698,0.00788691,0.03262045,-0.02180928,-0.00143035,0.03067468,0.03043146,0.04861418,-0.0469876,0.02382519,0.00735478,0.2404356,-0.03767239,0.00871847,0.05559169,-0.02935862,0.04323684,0.03218258,0.01108409,0.03272304,0.0396211,0.14719379,-0.02541452,0.02178629,0.02891232,-0.00388411,-0.02061034,0.02313497,-0.00889739,0.00622327,-0.05080262,-0.03781311,0.02983905,0.16634765,-0.07434496,-0.07058524,-0.07506857,0.0095298,0.02067224,-0.10276211,0.04456514,0.03871998,-0.01610079,0.06458164,0.01297074,0.03984845,0.00474226,-0.02301664,-0.02899796,0.00137706,-0.00063977,0.02031212,0.00143887,-0.00701944],"last_embed":{"hash":"uyi41","tokens":102}}},"text":null,"length":0,"last_read":{"hash":"uyi41","at":1751288797267},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#limit(min, max, value)#{1}","lines":[67,72],"size":249,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"uyi41","at":1751288797267}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#test(min, max, value, [minExclusive], [maxExclusive])": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02808206,-0.04067066,0.06021153,-0.03439052,0.04706182,-0.03196543,-0.0442894,-0.00802255,0.02367417,-0.04542191,0.0395441,-0.04458404,0.0162211,0.06485847,0.00102932,0.01918478,0.02462705,0.05899234,-0.11534371,0.01910326,0.06704168,0.01641843,0.02554478,-0.04420708,0.11009332,0.02411186,-0.02668051,-0.02810024,0.02004127,-0.23764999,0.00904692,-0.04707466,-0.01658809,-0.06708341,0.04284952,-0.02296693,-0.02462762,-0.07277855,0.00981732,0.06345013,0.03322887,0.05658312,0.00644639,-0.00274026,-0.04780988,-0.0023486,-0.02357103,0.00926756,-0.03535558,-0.02996896,-0.01653763,-0.00845336,0.00456252,0.02735512,0.03481192,0.08803079,0.01817541,0.05409925,0.0544802,0.05636328,0.02196013,0.05544849,-0.15523036,0.00980998,0.08698059,0.02335234,-0.03982186,-0.07751955,-0.01726508,0.00741431,0.0380429,0.00443552,-0.00868269,0.1172246,0.01666584,-0.04166203,0.01406513,0.00362763,-0.00037649,0.04433751,-0.11787674,-0.07218701,-0.01024575,-0.01653142,0.05616342,-0.00804618,0.06872819,0.02448152,0.02639761,-0.04159683,-0.01371816,-0.02899208,-0.04556087,0.04971443,0.0215925,0.01084081,0.0634813,0.02485969,0.00848915,0.12423083,-0.02330486,0.04505079,-0.08279646,0.01015015,0.02960252,0.00185536,-0.03479946,-0.04307197,0.01490459,-0.03289832,-0.01137345,-0.01799718,0.00633702,-0.03858895,-0.02415641,0.04583491,0.03816862,-0.01351228,0.01315226,-0.02660427,0.0038598,0.03877986,0.00908225,-0.06332799,-0.00524498,0.05422427,0.11185585,0.05015441,-0.05605005,0.0409832,0.02184747,-0.08143458,-0.08867273,-0.04101096,0.00033251,0.02490461,-0.01979635,0.02701377,0.04933507,0.07395744,-0.02199527,-0.07606823,-0.04482064,-0.06856468,-0.00785611,0.08673203,-0.08739006,0.01237801,0.01979965,-0.07272934,-0.06405463,-0.0299991,-0.04426951,-0.01516683,0.01127078,-0.01665203,0.02673399,-0.04110457,-0.05549495,-0.02784355,0.02690629,0.00400781,-0.073077,0.08661494,-0.02190313,-0.01631321,-0.01703054,0.03306605,0.0392907,-0.06146383,0.01130998,0.02681428,0.00924925,-0.02771738,-0.00189248,-0.01767311,-0.06916974,0.00825204,-0.05170572,0.01026407,-0.01077364,-0.02046218,-0.03071804,0.05093724,0.03062553,-0.02729725,0.01427881,0.00780064,0.04880669,-0.01438037,0.00491803,-0.00629929,-0.06317338,0.00436189,-0.02516625,-0.04708721,-0.01772491,0.0311548,0.00322241,-0.02078726,0.1241011,0.05842591,0.00076765,0.04570746,-0.05354393,0.0462297,0.010663,-0.05686908,0.05555786,0.00511441,-0.04843295,-0.0988925,0.02723384,0.04026004,-0.05605797,-0.01971796,0.05409832,0.02000215,0.00744165,0.04581909,-0.01808057,0.00257907,-0.11635619,-0.22772606,0.01903181,0.02786175,0.02867886,0.01305982,-0.04636389,0.0099334,0.00546416,-0.00061818,0.07321524,0.08494296,0.02540971,-0.02890093,0.01684985,-0.01108507,-0.00567862,-0.03021915,0.0021065,-0.07073533,0.05183623,-0.00308612,-0.04015426,-0.1191515,-0.06780796,0.0184914,-0.01232613,0.15439987,0.00521249,0.04419868,-0.02881777,0.00213799,-0.02348727,-0.0039502,-0.06633645,-0.02714648,0.04806044,-0.07694089,0.00901269,-0.00923103,0.01181728,0.01383011,0.0153176,0.00642366,-0.09694659,0.04288736,-0.00103579,0.01049224,0.05055332,0.0341866,0.04153159,-0.02688354,-0.00598955,-0.01787879,0.07111783,0.00165531,-0.04481828,-0.07966448,-0.03011305,-0.00779081,0.02537047,-0.00700445,-0.02206362,-0.0260784,-0.02040375,0.05611635,0.01842606,0.0764712,-0.0445184,0.05522216,-0.01924123,0.01602622,0.0678595,0.01540341,0.00512754,-0.0161716,0.04998426,-0.01990834,0.0256586,0.05099088,0.02030401,0.08284172,-0.00398646,0.03660625,0.00716502,0.01675446,0.02004986,-0.01675028,-0.00435351,0.0321539,-0.03248339,0.01474234,0.00497441,-0.03065583,-0.06882539,0.06068467,-0.07309627,-0.21716341,-0.02006353,-0.00215725,-0.04438065,-0.06263429,0.00829868,0.03324027,0.00219457,-0.14116085,-0.01334616,-0.03244576,0.0790552,-0.01372023,-0.00005335,-0.06689832,0.0383667,0.04512544,0.02703515,0.08165627,-0.06061471,0.0218501,0.02880473,0.23464203,-0.02228782,0.01772188,0.10497169,-0.01017067,0.04998874,0.03017744,0.04221934,0.0799425,0.0289338,0.07101325,0.00375738,0.02213669,0.0322888,0.01407783,-0.01045851,0.04362895,-0.00701491,0.00565631,-0.03652972,-0.05402872,0.01602332,0.12472309,-0.05719453,-0.04070169,-0.05218367,0.01348529,0.03176988,-0.09989091,0.02576788,0.03410341,0.01175431,0.03305193,0.01301904,0.01963422,0.01906883,0.00041669,-0.03970851,-0.01979231,0.01121592,0.06153685,0.024575,-0.01305],"last_embed":{"hash":"c6npwq","tokens":106}}},"text":null,"length":0,"last_read":{"hash":"c6npwq","at":1751288797327},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#test(min, max, value, [minExclusive], [maxExclusive])","lines":[73,78],"size":270,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"c6npwq","at":1751288797327}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#test(min, max, value, [minExclusive], [maxExclusive])#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0236071,-0.03652995,0.06156033,-0.03346141,0.05191885,-0.02420755,-0.0463425,-0.0049758,0.02153579,-0.04873035,0.04108483,-0.04088564,0.01804677,0.06687927,0.00419339,0.02305727,0.02482779,0.05699322,-0.11501204,0.01700139,0.06183225,0.01246548,0.0205982,-0.04475589,0.11781022,0.02171909,-0.02262967,-0.03285006,0.01816969,-0.24066646,0.01091862,-0.04650851,-0.01671086,-0.06584983,0.04130664,-0.02424994,-0.02620257,-0.07411132,0.01248105,0.06096132,0.03186953,0.05544296,0.00516701,-0.00294818,-0.04933347,-0.00634096,-0.02172567,0.00134896,-0.03448767,-0.02499272,-0.01457597,-0.01017592,0.00731418,0.02558823,0.03644216,0.08149302,0.01779847,0.05167505,0.05199492,0.05654894,0.02380817,0.05776246,-0.15381698,0.00558684,0.08681855,0.02409813,-0.03817181,-0.08031232,-0.02252194,0.01448928,0.03815175,0.00747406,-0.008071,0.11732888,0.01433473,-0.03592364,0.01082416,0.00365471,-0.00082323,0.04294256,-0.12139843,-0.07228768,-0.00852682,-0.01660297,0.06068121,-0.00662074,0.06387524,0.01676079,0.02493038,-0.04257704,-0.01170919,-0.03054499,-0.04452362,0.04757149,0.02079019,0.00791081,0.06848373,0.02317426,0.00719794,0.12797129,-0.01938212,0.04969434,-0.0794678,0.01170284,0.02937149,0.00610541,-0.03579452,-0.04178145,0.01236466,-0.03401887,-0.0090267,-0.02187176,0.00554683,-0.03528311,-0.02673394,0.04975217,0.03924565,-0.02039225,0.01352027,-0.02050152,0.00038431,0.03847785,0.01142205,-0.0692507,-0.00987937,0.05407607,0.11000706,0.05243916,-0.05899605,0.0352675,0.02298911,-0.08672171,-0.08599401,-0.04079256,0.0031145,0.02391743,-0.01576818,0.02852789,0.04907049,0.07278486,-0.01782688,-0.07791779,-0.041346,-0.06918457,-0.01105269,0.08934388,-0.0900461,0.00999102,0.02389624,-0.07580085,-0.06114034,-0.0313721,-0.0451533,-0.01797092,0.01163736,-0.01752059,0.02896356,-0.04137466,-0.05554283,-0.02736163,0.02476238,0.01229756,-0.07491455,0.0854304,-0.02117066,-0.01128187,-0.01774427,0.03478924,0.04100521,-0.06166086,0.01270726,0.02461458,0.00349271,-0.03378043,-0.00498112,-0.0196482,-0.0715588,0.00951473,-0.05473655,0.01245478,-0.00859646,-0.01803603,-0.0306832,0.05183756,0.0294613,-0.0248806,0.01438181,0.00698986,0.04621588,-0.0118892,0.0067175,-0.00768247,-0.05916059,0.00590361,-0.0258809,-0.04651598,-0.01587501,0.02723759,0.00033121,-0.02129628,0.12469225,0.05687446,0.00463237,0.04587101,-0.05352659,0.05151815,0.0108512,-0.06256875,0.05690496,0.00392674,-0.05014142,-0.1002112,0.02826791,0.03815795,-0.05417714,-0.01530286,0.05019372,0.01921847,0.00566113,0.04580922,-0.01691471,0.00291923,-0.11364397,-0.22532117,0.01721652,0.02818263,0.02978941,0.01838916,-0.04709337,0.01039439,0.00938997,0.00302701,0.06627742,0.08064376,0.02399672,-0.02798168,0.01723116,-0.01040013,-0.01236857,-0.02721765,0.00405876,-0.06780282,0.04984516,-0.0000975,-0.04419361,-0.11680932,-0.06434537,0.02219023,-0.01510338,0.15518491,0.00610972,0.04696644,-0.02726498,0.00017628,-0.01839297,-0.00736504,-0.06376828,-0.02754351,0.04455433,-0.07785024,0.00710021,-0.00925589,0.01011604,0.01372916,0.01322731,0.00679145,-0.09879586,0.04213981,-0.00170641,0.01403535,0.05373985,0.03509069,0.04451933,-0.02778976,-0.00981783,-0.01804446,0.07071278,0.00907859,-0.04431834,-0.0793431,-0.0309263,-0.00911707,0.02275142,-0.00123677,-0.01965429,-0.02539765,-0.02296447,0.05507599,0.01871116,0.07837622,-0.04374275,0.0574586,-0.02188547,0.01975578,0.06607457,0.01248842,0.0034673,-0.01831538,0.05149411,-0.01671172,0.02677613,0.05057854,0.02181197,0.08438569,-0.00695529,0.0374404,0.00583046,0.01374469,0.02206561,-0.01474469,-0.00421527,0.03330389,-0.02944102,0.01458398,0.00909773,-0.02686061,-0.06975172,0.05892915,-0.0818611,-0.21386713,-0.01959046,-0.00614218,-0.04480983,-0.06249462,0.00636578,0.0312363,0.00687047,-0.13747862,-0.01149109,-0.03131001,0.07360716,-0.01814294,-0.00339555,-0.0671585,0.03605099,0.04860464,0.02748666,0.08187214,-0.06339428,0.0210396,0.02680044,0.23375659,-0.02978656,0.01695316,0.10230497,-0.00719722,0.04514195,0.02977809,0.04159683,0.08294827,0.02966277,0.07117528,-0.00247298,0.01834413,0.03808867,0.00662226,-0.01192401,0.04396366,-0.00504956,0.00966732,-0.03986769,-0.05766673,0.01292392,0.12508824,-0.05917279,-0.03799729,-0.05117183,0.01104811,0.02883646,-0.09841859,0.02606761,0.03637472,0.00870847,0.03615021,0.01167015,0.02235103,0.01786997,0.00392686,-0.04073915,-0.01721886,0.01634503,0.06649605,0.02185038,-0.01132657],"last_embed":{"hash":"2vtru4","tokens":104}}},"text":null,"length":0,"last_read":{"hash":"2vtru4","at":1751288797495},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#test(min, max, value, [minExclusive], [maxExclusive])#{1}","lines":[75,78],"size":211,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"2vtru4","at":1751288797495}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#name(min, max, value, [minExclusive], [maxExclusive])": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04775279,-0.058982,0.03157242,-0.0592121,0.00440226,-0.0318183,-0.03093037,-0.02238992,0.02038966,-0.0374792,0.04785921,-0.05334042,0.02581322,0.04911602,-0.03300838,0.01840819,0.02261749,0.09821979,-0.11740711,-0.01303488,0.11929978,0.05955255,0.01683918,-0.04579282,0.11469765,0.05865266,-0.03709465,-0.03169586,0.01304208,-0.20172891,0.02185625,-0.0294883,0.0215742,-0.04602789,0.01627756,-0.04164147,-0.03708025,-0.01826875,-0.00821177,0.07190216,0.0327822,0.04921014,-0.00096701,-0.00381669,-0.04996588,-0.02561347,-0.02991382,-0.01558196,-0.03175884,-0.01716739,-0.07585629,-0.01466719,0.01873974,0.03745818,0.06024712,0.0547714,0.03212773,0.03942189,0.03824212,0.03315915,0.05594067,0.03461508,-0.16532555,0.0198394,0.03799314,0.01357486,-0.01180681,-0.04981919,-0.01343163,0.01244143,0.05349121,-0.02305532,-0.0091397,0.09660763,0.02784611,-0.04926351,-0.00092461,-0.02105253,0.01394134,0.02444585,-0.11506139,-0.06032175,-0.01346412,-0.0185472,0.05206806,-0.00022258,0.05154276,0.01348218,0.04758414,-0.02872988,-0.05267848,-0.05915617,-0.02369934,0.0646546,-0.01714143,-0.03625675,0.05552546,0.05379995,0.01541978,0.12694585,-0.01908021,0.01890955,-0.04764882,0.02862399,0.02530624,-0.00219095,-0.06676785,-0.05914967,-0.00369601,-0.06337962,-0.03265008,-0.00820965,-0.00939319,-0.08458973,-0.00102462,0.01231749,0.02116158,-0.00395308,0.04242226,0.01172186,0.00048455,0.0511523,-0.00930932,-0.033291,0.01319065,0.07138891,0.07742683,0.04821607,-0.01621634,0.08052949,0.03544955,0.00497089,-0.08806243,-0.0346154,0.00538632,0.01176941,0.00093696,-0.01471942,0.00587122,0.02117196,0.01696569,-0.07493026,-0.03511225,-0.06945038,-0.0152999,0.14923574,-0.08934782,0.00869855,-0.01442599,-0.05158105,-0.03900506,-0.04697908,-0.02919552,-0.02749662,-0.00033701,-0.01108165,0.0819222,-0.0216533,-0.03880179,-0.01977402,0.02399274,-0.01087172,-0.04774754,0.10032064,0.03222019,-0.06155094,-0.01162407,0.05777323,0.01191458,-0.07994623,0.02769819,0.03938086,0.00017724,0.0369348,0.02222112,0.01116241,-0.07698562,-0.06867966,-0.00644567,0.03211622,-0.01071761,-0.05662731,-0.02333376,-0.00969936,0.04005347,-0.00073374,0.00123354,-0.00729642,0.05060343,-0.00631538,0.01222204,0.01157032,-0.03133567,-0.00348123,-0.04695005,-0.03964801,-0.02707992,0.05192396,0.00828675,-0.01116197,0.12463515,0.06838609,-0.00454731,0.08136696,-0.03931146,0.02177329,0.01439685,-0.03426227,0.01460308,0.00359932,-0.06015611,-0.08021713,0.01090472,0.06214072,-0.06321698,-0.02899229,0.04459959,0.02230426,-0.00598411,0.03949131,-0.02967545,-0.03280481,-0.1363157,-0.20563485,0.02887685,0.02965571,-0.00708566,-0.00604162,-0.01888264,0.00426104,-0.01620368,-0.01616179,0.07395393,0.09843667,0.01783338,-0.05488275,-0.002982,-0.04712456,0.02937924,-0.03816513,-0.00096634,-0.06716487,0.0655125,0.0476636,-0.02301033,-0.13966212,-0.02300266,-0.00003826,-0.01906675,0.17220454,0.02646095,0.04967599,-0.0199729,0.00602149,-0.02249597,-0.00142307,-0.01537407,-0.03409278,0.01134867,-0.06550595,0.03684678,-0.00520783,0.00219431,-0.04259313,0.02691024,0.03693563,-0.06219019,0.05235738,-0.0034684,-0.01911613,-0.00563492,0.05668062,0.03528378,-0.0243087,0.01741293,0.00851281,0.03741251,0.00255177,0.00331907,-0.0461272,-0.0141795,0.00100743,0.00896381,-0.00448039,-0.04765625,-0.00871795,-0.04370815,0.00504915,0.02492469,0.06602234,0.00708381,0.03370819,-0.04346613,-0.00299369,0.07624201,0.02451911,-0.04231928,0.00266896,0.01660015,0.00075805,0.0127445,0.06225015,0.01893622,0.06663578,-0.01310312,0.05255667,-0.00109151,0.01955215,0.02309675,-0.01355922,-0.01144401,0.03928381,-0.03688896,0.02873648,0.01784759,-0.00934379,-0.0596831,0.06723867,-0.05609133,-0.23402296,0.01451544,0.03349136,-0.02758317,-0.01764966,0.01944037,0.01997538,-0.04827329,-0.090662,-0.02246431,0.00292484,0.03863408,-0.02411437,-0.02358758,-0.06499641,0.03255594,0.04176191,0.02568491,0.08370472,-0.02556768,0.01401906,0.03587838,0.24833353,-0.03070422,0.02562922,0.05842796,-0.03733456,0.03024413,0.05011641,0.02792517,0.03485962,0.01424818,0.1220416,0.0076281,-0.00750742,0.01914639,0.05653038,-0.01587292,0.03866638,-0.01040083,0.01501652,-0.04857483,-0.08924388,0.01360475,0.11569677,-0.03144487,-0.07087629,-0.09177257,0.00984672,0.05258719,-0.11503231,0.02686661,-0.00456829,0.03053783,0.03420491,0.0184036,0.02660639,0.00215876,-0.00906126,-0.02722958,-0.03455007,-0.0203631,0.06238859,0.01048675,-0.00436584],"last_embed":{"hash":"1fgkhys","tokens":86}}},"text":null,"length":0,"last_read":{"hash":"1fgkhys","at":1751288797545},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#name(min, max, value, [minExclusive], [maxExclusive])","lines":[83,87],"size":204,"outlinks":[{"title":"range notation","target":"https://en.wikipedia.org/wiki/Interval_(mathematics","line":4}],"class_name":"SmartBlock","last_embed":{"hash":"1fgkhys","at":1751288797545}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03150955,-0.03136817,0.0428175,-0.05300669,-0.02543935,0.0051461,0.00097051,-0.02386576,0.02014423,-0.02292282,0.02060205,-0.05817537,0.01302997,0.07014432,0.0284589,0.00173944,0.00692692,0.05935694,-0.08460963,0.00176742,0.08628674,0.03751538,-0.0011971,-0.07394754,0.08637067,0.01180356,-0.01628394,-0.02745272,0.00599871,-0.21561339,-0.01967414,-0.02999888,0.02078889,-0.02192784,-0.02651902,-0.05468488,-0.04840105,-0.0596521,0.00632636,0.06307222,-0.02476521,0.03796694,0.02007833,-0.01585607,-0.04161705,0.03935018,-0.04577015,0.00025556,0.00856478,-0.01731691,-0.08668963,-0.00305408,0.02070607,-0.0467878,0.06653393,0.03834507,0.08297125,0.08030611,0.05129109,0.05733968,0.01511461,0.04998747,-0.15501605,0.05236079,0.04728171,-0.01647063,0.00475757,-0.02226817,0.02548555,0.03743693,0.03180078,0.02431487,0.00022013,0.07148705,-0.01150598,-0.0309127,-0.0208116,0.00786237,0.01644103,0.02548171,-0.06370283,-0.0300669,0.02729618,0.01347532,0.04723296,-0.0106838,0.02712814,0.00516656,0.0669476,-0.04669622,-0.01100203,-0.04270637,-0.05607286,0.03861771,0.03427446,-0.00904335,0.05410416,-0.03282671,-0.0668636,0.11925214,-0.02909092,0.00363147,-0.04899816,0.02732079,0.01485397,-0.04824,-0.11715659,-0.08642365,-0.01923736,-0.07402317,-0.05837462,-0.00697429,-0.01511029,-0.06971484,-0.01654302,0.04918335,0.03231801,0.00562152,0.00173257,-0.01480305,-0.00854083,0.10021348,0.0135511,-0.01322815,0.00921325,0.03124845,0.0633234,0.06119902,0.0073387,0.12965785,0.04177752,0.00285195,-0.10301229,-0.01294859,-0.00795649,0.0039822,0.02674869,0.01559324,0.03330862,0.00318254,-0.00545487,-0.07418299,-0.03229574,-0.08319417,0.01945787,0.09570026,-0.10271519,0.02510849,0.00124715,-0.06638768,0.02045805,-0.00719551,-0.06609134,-0.0372129,-0.00902683,0.00289251,-0.00030724,-0.02172813,-0.04006584,-0.02442978,-0.01774856,-0.02565843,-0.04652181,0.02279268,0.01046937,-0.0995536,0.01321387,0.04392935,0.02647674,0.00097717,0.0512273,0.06388186,0.02124536,0.03323319,0.0336377,0.02392267,-0.05869911,0.02448205,-0.03881313,0.07574565,0.03436521,-0.0472148,-0.0438756,0.05298942,0.04953295,-0.05097152,-0.00462141,-0.01899656,0.01462871,0.00471237,-0.01944327,-0.00410165,-0.12334189,-0.00619476,-0.01532311,-0.00451779,0.01290038,0.05287365,0.03457288,-0.0003337,0.13338785,0.08476308,-0.03853992,0.02017539,-0.00519547,0.02514261,0.03672395,-0.07434522,-0.003639,0.0475226,-0.05925775,-0.09220024,-0.01583715,0.10929502,-0.07804831,-0.00099193,0.0745846,0.06750438,0.01135562,0.06494901,0.00086466,-0.02841912,-0.079245,-0.23789841,0.00992672,0.02756267,-0.0306316,0.03164215,-0.03020046,0.00345137,-0.03212205,-0.01834582,0.0522669,0.09918095,0.04459284,-0.00665072,-0.03112339,0.02079591,0.03595401,-0.03256657,-0.00128982,-0.05871429,0.04963895,-0.01329253,0.01264659,-0.08138171,-0.07257314,0.0603169,-0.02025458,0.18493697,-0.03799286,0.04257659,-0.01322458,0.02114152,0.010407,-0.0263276,-0.0273392,0.00749399,0.00643852,-0.0448084,0.05565325,-0.01716325,-0.000118,0.00753916,0.00693099,0.01205566,-0.08351357,0.02904811,0.04548295,0.00055162,-0.00522563,0.04802387,0.03866005,-0.01188674,0.0026245,0.00764713,0.06668799,0.01177286,-0.03854762,-0.04795985,-0.03193953,0.01477564,0.02057368,0.00133112,-0.07209645,0.00246434,-0.02654459,-0.00983673,0.0339436,0.08263048,0.02773427,0.05009638,-0.01251994,-0.07065164,0.05339831,0.01757869,0.05745928,-0.03667756,0.01366712,-0.01088656,0.00632616,0.08089568,0.01712484,0.04073122,0.00143364,0.05999688,0.04311397,-0.0314813,0.02892496,-0.00079062,-0.02845006,0.06145463,-0.01361122,-0.00725245,-0.01475398,-0.04205903,0.02233474,0.05472054,-0.09297181,-0.23417772,-0.02818235,0.0045201,-0.00998795,-0.03543761,-0.00017326,0.02390409,-0.02165915,-0.12552452,-0.00038394,-0.0504326,0.06658985,0.03855898,-0.00223381,0.00359864,-0.02733245,0.00919194,0.03850327,0.07487359,-0.06223384,0.04487906,0.00464964,0.22272046,-0.01350251,0.00654737,0.04449675,-0.01776218,0.00775846,0.01919161,-0.00255755,-0.01939859,-0.02090122,0.09353288,-0.00192483,0.04699158,0.01180533,0.02965262,-0.03524594,0.00413777,-0.00567607,-0.0064551,-0.04631936,-0.04541454,0.00608779,0.10992026,-0.01248563,-0.05728264,-0.07848047,0.03123749,-0.01077217,-0.07679815,0.0188838,0.01225653,0.00893243,0.03934881,0.01610266,0.03157067,-0.02020866,-0.0469511,-0.02854141,0.03375892,-0.0378762,0.05447171,0.04355842,-0.01341818],"last_embed":{"hash":"1m1jdiw","tokens":248}}},"text":null,"length":0,"last_read":{"hash":"1m1jdiw","at":1751288797624},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])","lines":[88,137],"size":669,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1m1jdiw","at":1751288797624}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03467824,-0.02939624,0.05763955,-0.04829465,-0.01850197,-0.0041895,-0.01157946,-0.03219079,0.01434804,-0.01793532,0.01966761,-0.06246362,0.0103722,0.06884391,0.02233067,0.00470748,0.00049092,0.06866883,-0.0715455,-0.00290431,0.08305398,0.04673791,0.00655066,-0.06382955,0.0819825,0.02147156,-0.01069643,-0.03004824,-0.00059561,-0.20929411,-0.01928469,-0.02942265,-0.00689081,-0.011961,-0.01946336,-0.04406511,-0.0400434,-0.05392479,0.0097714,0.06609331,-0.02361721,0.03696287,0.01211328,-0.01069493,-0.03975873,0.03908733,-0.04216317,0.00250507,0.00963317,-0.01386704,-0.08585119,-0.015583,0.03142805,-0.05692993,0.06316551,0.04021818,0.07137002,0.08340814,0.05200673,0.05897903,0.01635975,0.05170707,-0.16594994,0.06276871,0.04424596,-0.01840481,0.00804424,-0.02456255,0.01916046,0.04940917,0.02870535,0.02776047,-0.00100511,0.07806149,-0.01873903,-0.02725792,-0.01444521,0.00417002,0.01081622,0.0263736,-0.06462316,-0.03928305,0.02168282,0.01749616,0.03413922,0.00104392,0.01655832,0.0008636,0.06900813,-0.03912871,-0.00887319,-0.06201354,-0.04390803,0.04010738,0.02437919,-0.00723793,0.04844657,-0.0303301,-0.07338613,0.1203355,-0.04446701,0.00409372,-0.04520012,0.02413199,0.0074034,-0.04555068,-0.1102998,-0.09154636,-0.01304329,-0.06597675,-0.0472835,-0.00945191,-0.02407184,-0.0672172,-0.01890596,0.0366447,0.03572822,0.00550959,-0.01838703,-0.0113638,-0.0080812,0.09999561,0.024344,-0.00586111,-0.00576161,0.02145424,0.06385434,0.06497065,0.01482808,0.11970221,0.03660909,0.00166417,-0.09435799,-0.01775353,-0.01082153,0.01116212,0.03495761,0.01636465,0.03748306,0.00986433,-0.00042819,-0.07736163,-0.03621291,-0.08271159,0.01878028,0.09212457,-0.08974691,0.03103979,-0.00089893,-0.07357673,0.0241756,0.00288511,-0.05051111,-0.02767537,-0.01296824,0.00011307,-0.00670834,-0.00845307,-0.03646188,-0.00574393,-0.02876549,-0.01369444,-0.04421184,0.03027288,-0.00133959,-0.09486727,0.00099854,0.04988005,0.03796808,0.00060312,0.04600955,0.05501495,0.02667623,0.02107295,0.0188778,0.02981314,-0.05182332,0.02093748,-0.04366288,0.06968758,0.04606252,-0.05848408,-0.04978509,0.06097924,0.04680613,-0.05674155,-0.01421058,-0.0245093,0.01428911,0.00407339,-0.03259354,-0.01054746,-0.12783562,-0.00902885,-0.02011959,-0.01927096,0.01285061,0.05540992,0.03185272,-0.00159184,0.13319007,0.0874242,-0.04537237,0.02614834,-0.01187791,0.02561955,0.02794594,-0.07303172,-0.00601752,0.04697453,-0.07355832,-0.09567349,-0.01730314,0.10791691,-0.0792878,0.00560441,0.05514774,0.0699388,0.00654337,0.06340877,-0.00642497,-0.02766863,-0.07251834,-0.24209782,0.00868965,0.02807075,-0.02871465,0.03095199,-0.03558438,-0.00119462,-0.02875186,-0.00868839,0.05767238,0.09993,0.04213079,0.00069717,-0.0322981,0.02040096,0.04131619,-0.03497294,0.00464539,-0.05096547,0.04028656,-0.0199136,0.00482659,-0.07470915,-0.08198291,0.06277341,-0.01745067,0.18522578,-0.02474004,0.04038131,-0.01774271,0.02452536,0.01506381,-0.02251697,-0.05347698,0.01135453,0.02193543,-0.0318879,0.05308091,-0.01480224,-0.00233831,0.0080985,0.01497441,0.02014408,-0.09578045,0.03062014,0.04311754,0.00323354,-0.00381796,0.04861879,0.03539195,-0.00574206,0.00001225,0.01083277,0.07008939,0.00827418,-0.02573069,-0.0346536,-0.02886927,0.02510328,0.01738658,0.00515631,-0.0623258,0.01476392,-0.02517989,-0.00310412,0.0453309,0.08544769,0.01453261,0.05050835,-0.00483485,-0.07879029,0.05669196,0.01453813,0.05203818,-0.03417018,0.01460035,-0.01491124,-0.00492598,0.07261361,0.02247101,0.04951632,0.00405472,0.05418938,0.04545164,-0.02946395,0.02799694,-0.02103265,-0.02956548,0.07662842,-0.02146828,-0.01482822,-0.00677806,-0.04646398,0.03825854,0.06031244,-0.09821544,-0.23465702,-0.02869788,0.00568603,-0.00965024,-0.04919482,0.00107613,0.03455912,-0.03327379,-0.1170783,-0.00006301,-0.06436652,0.06362311,0.02962781,-0.00081422,-0.00097874,-0.02152431,0.02334814,0.03817143,0.09257057,-0.07157136,0.04313965,0.0071963,0.23186946,-0.00681402,-0.00168468,0.05040048,-0.0203988,-0.00040128,0.01287433,0.0130169,-0.03139243,-0.01224133,0.07627884,-0.00165329,0.04434459,0.01510339,0.02161374,-0.03408607,0.00712366,-0.00577534,-0.00874902,-0.03186344,-0.05570535,0.00770643,0.09773175,-0.01410755,-0.05155748,-0.07183562,0.0327568,-0.01210541,-0.07037269,0.01069101,0.00584616,0.01416943,0.03378778,0.02620289,0.02710757,-0.02178632,-0.03975179,-0.02916978,0.03287921,-0.04076286,0.06673847,0.03723117,-0.00825712],"last_embed":{"hash":"f8sd7s","tokens":165}}},"text":null,"length":0,"last_read":{"hash":"f8sd7s","at":1751288797767},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#API#curry(min, max, [minExclusive], [maxExclusive])#{1}","lines":[90,110],"size":325,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"f8sd7s","at":1751288797767}},
"smart_blocks:Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#Building and Releasing": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04793342,0.0072636,0.08545443,-0.00918903,0.06461857,0.0147598,-0.06749437,0.00742512,-0.04598479,-0.05471775,0.04167331,-0.01601791,-0.00333591,0.05178511,0.01377877,-0.01512751,-0.02490757,0.05071608,-0.0021554,0.0267957,0.02455038,0.03771164,0.0198513,-0.0421913,0.04616049,0.08199617,0.02635986,-0.04318979,-0.00243229,-0.21431047,0.00518574,-0.03822771,0.00514109,-0.0089015,0.05279359,-0.01154951,-0.06565061,-0.02337605,-0.02112113,0.02737269,0.01641782,0.06999002,-0.01524242,-0.02467433,0.00345642,0.0103743,0.02878781,-0.04600822,-0.03299548,-0.0194732,-0.04073023,-0.05580023,0.0592614,-0.04964896,0.04317935,-0.01679359,0.02342321,0.02170626,0.04708133,0.06865793,-0.02307871,0.02525672,-0.17079757,0.01667594,0.08222806,0.04151382,-0.05993643,-0.01570758,0.03535283,0.03233995,-0.01033658,0.02585894,-0.01220454,0.10368743,-0.00760942,-0.02377575,-0.0498468,-0.01585965,0.00109151,-0.00300516,-0.07394028,-0.07456242,0.01432641,0.0256216,0.02844295,0.01161877,0.03717772,0.00579154,0.051031,0.00103806,-0.02518895,-0.06660936,-0.01296165,0.09361442,-0.01588368,-0.00203944,0.0568674,-0.01136302,-0.01502542,0.14714545,-0.03513989,0.01869068,-0.01391945,0.03080464,0.00177792,-0.00363192,-0.04255572,-0.07628527,-0.02856888,-0.02140257,0.05973933,0.00367188,0.01812232,-0.10490503,-0.01348829,0.02571514,-0.02999916,0.00163443,-0.04387562,0.03894833,0.05017138,0.02948889,0.02967691,-0.09931525,0.08620019,0.03654327,0.09143665,0.0638288,-0.02751749,0.03229017,-0.01363645,0.02849353,-0.08942745,-0.02960921,0.02592844,0.01915912,-0.07091101,0.01302415,0.00190086,0.02210395,-0.05464851,0.0065455,0.03301391,-0.07824584,0.02135128,0.03353905,-0.07572985,0.07704733,-0.03155751,-0.09732819,0.00549369,0.0200069,-0.05456586,-0.06760578,0.0157879,-0.02381117,0.00103249,0.04723525,-0.06319676,-0.00624775,-0.01448802,-0.01464946,-0.02277164,0.06652301,-0.04107931,-0.05380012,-0.04194784,0.02678256,0.04933513,-0.02625464,-0.00711564,0.02007793,-0.04182289,-0.0457061,-0.01288392,-0.02577855,-0.05844775,-0.00268383,-0.00917438,0.00794918,0.03263673,-0.0418409,-0.01301336,0.03931764,0.01639728,-0.0416166,0.01898054,-0.00901936,0.0555793,-0.0473793,-0.02316952,-0.00588277,-0.02497375,0.002009,-0.01902817,0.00176066,-0.01420758,0.02901552,0.03448436,-0.01742988,0.14407267,0.03912901,0.03852391,0.04351186,-0.06413494,0.05829278,-0.03777627,-0.06980492,0.07430263,0.03890058,-0.06869356,-0.04999768,0.03461992,0.07611796,-0.047118,-0.05080252,-0.00312751,0.04055091,0.02029489,0.1127905,0.02002038,0.06359158,-0.10940223,-0.20929508,-0.00736864,-0.01350708,0.03583888,0.03661587,0.00145689,0.01726256,-0.00461641,-0.0088018,0.05763788,0.08846832,0.01054794,-0.01757864,0.02396956,0.0054063,0.02862471,-0.05063698,-0.04081579,-0.093416,0.03542945,0.01885861,-0.04280232,-0.07565709,-0.09220568,0.00925017,-0.04696862,0.13361289,-0.03560906,0.06472363,-0.02538173,0.02202837,0.02697303,-0.03818402,-0.13598651,-0.00053365,0.02184794,-0.05174826,0.06121768,-0.01879723,0.00173944,0.01598608,0.00652383,0.01818291,-0.10146308,0.00133801,-0.02631027,0.02122405,0.01318306,-0.01230745,-0.03251848,-0.01946594,-0.0044111,0.03024415,0.03112253,-0.01431655,-0.03370187,-0.10799437,-0.03163639,-0.02361437,0.02361236,0.01643236,0.03327142,0.00570737,-0.09801666,0.07186651,0.02226548,0.02530646,0.00284886,0.06615721,-0.00581275,0.01263316,0.031062,0.02366806,0.00661318,0.00245884,0.00471799,-0.04929314,0.00545904,-0.02026985,-0.00073424,0.0967503,0.00856047,0.01336472,0.02507083,-0.02347781,0.016543,0.00528742,-0.03962691,0.09070193,-0.030902,-0.03116338,0.02161792,-0.01366414,-0.03826386,0.11264923,-0.02786714,-0.23249523,0.00249942,0.00872868,-0.03203452,-0.02863262,-0.03716515,0.01329812,0.00145182,-0.06693222,-0.02187549,-0.03371154,0.05629605,-0.01456754,-0.05303295,-0.00363846,0.03313635,0.04754042,-0.00223389,0.07762801,-0.09014789,0.03579135,0.00619969,0.25189555,-0.0128642,0.01698307,0.06956723,0.01578814,0.04417019,0.05625914,0.03584041,0.05623204,0.06426871,0.04339556,-0.01304359,-0.0070777,0.028775,0.04882108,0.02227389,0.04528968,-0.04076847,0.0113698,-0.04137483,-0.01530788,0.01454447,0.06708749,-0.09672721,-0.03266656,-0.05705928,0.01849481,0.02313972,-0.06377764,-0.03973968,0.06319632,0.0204598,0.09522016,0.03482117,0.0531486,0.02686781,-0.03991495,-0.02570249,0.0097587,-0.01999292,0.05151144,0.04384271,0.02629452],"last_embed":{"hash":"j7pc7z","tokens":92}}},"text":null,"length":0,"last_read":{"hash":"j7pc7z","at":1751288797832},"key":"Projects/Piecework/node_modules/normalize-range/readme.md#normalize-range#Building and Releasing","lines":[138,145],"size":253,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"j7pc7z","at":1751288797832}},
