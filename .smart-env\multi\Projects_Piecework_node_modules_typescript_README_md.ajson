
"smart_sources:Projects/Piecework/node_modules/typescript/README.md": {"path":"Projects/Piecework/node_modules/typescript/README.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05117408,-0.03243653,0.03391507,-0.05057479,0.00672308,-0.01042569,-0.11131723,0.01855208,-0.03673315,-0.03561082,0.00404662,-0.02753121,0.02545315,0.03177445,0.06037848,0.01358958,0.01643395,0.04764549,-0.00826473,0.01179485,0.05073017,0.00097678,0.05690201,-0.0091607,0.01670282,0.07215033,-0.00160776,-0.04266797,0.02111662,-0.17038187,-0.05524538,-0.01350972,-0.03227741,0.0646934,0.01797957,-0.00806613,-0.03073957,0.01588254,-0.03029132,0.02809632,0.02561565,0.03751079,-0.04677483,-0.00154271,-0.00134057,-0.08114148,-0.02107303,-0.04323971,-0.01406284,-0.03194173,-0.06870474,-0.04288761,0.03475504,0.00689654,0.020175,0.08377616,0.05183371,0.05908891,0.05746205,0.01096986,0.05770313,0.01753531,-0.18355334,0.08976641,0.04697525,0.06924807,-0.05969396,-0.00730813,0.03507799,-0.01308993,-0.01460363,0.04062583,-0.0025566,0.02845653,0.01455659,-0.01229738,0.00361593,-0.03444307,0.01845672,-0.00754579,-0.05603034,0.02254636,0.00006329,-0.01576386,0.00581174,-0.02059893,0.00832238,0.00123229,0.08160454,0.02845937,-0.01669241,-0.09207735,0.02445976,0.08775702,-0.03084113,-0.03771258,0.065111,0.0030232,-0.03441046,0.11200028,-0.01646451,0.01609829,-0.00434234,-0.00040854,0.04792382,0.01122971,-0.04798143,-0.07607008,0.00090427,-0.05399824,-0.03353403,-0.00815876,-0.03331555,-0.09112893,-0.00603553,0.00553262,0.00918979,0.02390363,-0.02187537,0.0771145,0.00165732,0.06710559,0.05902593,-0.05171917,0.01456263,0.04951938,0.02009191,0.03761576,0.03595433,0.07301969,-0.02186819,0.09231259,-0.07035465,-0.0324931,0.01383268,0.03554927,-0.04815525,-0.03441057,-0.01148592,-0.0514671,0.00108506,-0.00433391,0.05316763,-0.07242069,-0.02241476,0.06317829,-0.02944223,0.06698231,-0.04901432,-0.02674717,-0.00903646,0.07828268,-0.10139909,-0.00302417,-0.02046406,-0.06391333,0.03862654,0.02284475,-0.05693984,0.00192164,0.01967842,-0.03362581,-0.00355185,0.03573725,0.00252039,-0.09711351,-0.04150273,0.02730429,-0.02042693,-0.06999701,-0.00714677,0.04205425,-0.00297833,-0.0402667,0.03201485,-0.00898142,-0.05287573,-0.05266686,0.04664522,0.04686895,-0.00856082,-0.07804428,-0.03979307,0.00857129,0.01964132,0.01238738,0.06927602,-0.08956328,-0.0161305,0.02216622,0.03421176,0.0119227,0.01351053,-0.00948464,0.01237322,0.00563217,-0.04327923,-0.01278454,0.03999094,-0.03293879,0.08071526,0.03166983,0.01357405,0.02792957,-0.09039906,0.01076929,-0.04858922,-0.03703853,0.02401905,-0.00072921,-0.07395326,0.01525925,0.10789056,0.08349445,-0.00396034,0.01817009,0.05711338,0.05363981,0.02792978,0.05141298,-0.01378235,0.00153869,-0.09033053,-0.22091882,-0.01444426,0.018557,-0.04968802,-0.04308036,-0.05739896,0.03716137,-0.03905201,-0.05731388,0.11158216,0.17756984,0.01474837,-0.04096888,-0.0603429,0.01058966,0.0670462,-0.00799268,-0.0442962,-0.02029711,0.00865531,0.0251504,-0.01285914,-0.09925572,-0.06299926,0.03998672,-0.03946088,0.14322086,0.03562175,0.01346124,0.00386369,0.04385999,0.01176317,0.06141544,-0.12113628,0.00613077,0.0251175,-0.02162247,0.06513943,0.02954,0.03246257,-0.02475443,0.00680257,0.02891968,-0.07972434,0.02409944,-0.03769122,-0.02122128,-0.02118194,-0.02030001,0.04974033,0.01841457,0.03866645,0.0513672,0.07700715,-0.04225288,-0.00196267,-0.05254843,-0.03581273,0.02805976,0.02705617,0.01660787,-0.00841088,-0.00813281,-0.07229067,0.06749941,0.03416028,0.00556222,-0.0242467,0.04856727,-0.01690573,-0.06107791,0.06544326,0.00684283,0.01320753,-0.00219084,0.02570552,-0.05074883,0.02444577,0.0484116,0.00048238,0.02628478,-0.02088116,0.02345856,0.0172267,-0.05461158,0.00814132,-0.04253168,-0.01129475,0.03640481,0.01199709,-0.0393661,-0.01766042,-0.00759945,-0.04127749,0.04826966,0.00930462,-0.224067,-0.01925167,0.04084146,-0.05705344,-0.04679392,-0.04713649,0.03792215,-0.08939268,-0.03886142,-0.05299458,0.00400122,0.03772708,-0.01549633,-0.0199192,-0.00900399,0.01893089,0.05012305,0.01816814,0.0900175,-0.12358614,0.06496003,0.01979036,0.24548374,-0.02893018,0.00765819,0.03558751,-0.03439468,0.01225716,0.05725866,0.05658759,0.01692503,-0.00544341,0.12353978,0.0264393,-0.04956215,0.01443681,0.00969906,0.00753972,0.05812097,0.01414536,-0.00281225,0.03569193,-0.06702188,-0.03405405,0.07465263,-0.0998716,-0.06148234,-0.05804839,0.03731437,0.01116492,-0.06419595,0.01082249,-0.00003159,0.01773777,0.05881591,0.0389572,0.02750024,0.00758757,-0.04354277,0.01086514,-0.03702024,-0.03289696,0.03649277,0.05876438,0.00102173],"last_embed":{"hash":"t3iktq","tokens":473}}},"last_read":{"hash":"t3iktq","at":1751288835497},"class_name":"SmartSource","last_import":{"mtime":1751244567599,"size":2842,"at":1751288765735,"hash":"t3iktq"},"blocks":{"#TypeScript":[2,51],"#TypeScript#{1}":[4,13],"#TypeScript#Installing":[14,27],"#TypeScript#Installing#{1}":[16,27],"#TypeScript#Contribute":[28,41],"#TypeScript#Contribute#{1}":[30,41],"#TypeScript#Documentation":[42,47],"#TypeScript#Documentation#{1}":[44,47],"#TypeScript#Roadmap":[48,51],"#TypeScript#Roadmap#{1}":[50,51]},"outlinks":[{"title":"![CI","target":"https://github.com/microsoft/TypeScript/actions/workflows/ci.yml/badge.svg","line":4},{"title":"![npm version","target":"https://badge.fury.io/js/typescript.svg","line":5},{"title":"![Downloads","target":"https://img.shields.io/npm/dm/typescript.svg","line":6},{"title":"![OpenSSF Scorecard","target":"https://api.securityscorecards.dev/projects/github.com/microsoft/TypeScript/badge","line":7},{"title":"our blog","target":"https://blogs.msdn.microsoft.com/typescript","line":10},{"title":"Twitter account","target":"https://twitter.com/typescript","line":10},{"title":"TypeScript","target":"https://www.typescriptlang.org/","line":10},{"title":"playground","target":"https://www.typescriptlang.org/play/","line":10},{"title":"our community page","target":"https://www.typescriptlang.org/community/","line":12},{"title":"contribute","target":"https://github.com/microsoft/TypeScript/blob/main/CONTRIBUTING.md","line":30},{"title":"Submit bugs","target":"https://github.com/microsoft/TypeScript/issues","line":31},{"title":"source code changes","target":"https://github.com/microsoft/TypeScript/pulls","line":32},{"title":"StackOverflow","target":"https://stackoverflow.com/questions/tagged/typescript","line":33},{"title":"TypeScript Community Discord","target":"https://discord.gg/typescript","line":34},{"title":"#typescript","target":"https://twitter.com/search?q=%23TypeScript","line":35},{"title":"Contribute bug fixes","target":"https://github.com/microsoft/TypeScript/blob/main/CONTRIBUTING.md","line":36},{"title":"Microsoft Open Source Code of Conduct","target":"https://opensource.microsoft.com/codeofconduct/","line":38},{"title":"Code of Conduct FAQ","target":"https://opensource.microsoft.com/codeofconduct/faq/","line":39},{"title":"<EMAIL>","target":"mailto:<EMAIL>","line":39},{"title":"TypeScript in 5 minutes","target":"https://www.typescriptlang.org/docs/handbook/typescript-in-5-minutes.html","line":44},{"title":"Programming handbook","target":"https://www.typescriptlang.org/docs/handbook/intro.html","line":45},{"title":"Homepage","target":"https://www.typescriptlang.org/","line":46},{"title":"roadmap","target":"https://github.com/microsoft/TypeScript/wiki/Roadmap","line":50}],"last_embed":{"hash":"t3iktq","at":1751288834726}},"smart_blocks:Projects/Piecework/node_modules/typescript/README.md#TypeScript": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05343123,-0.03289625,0.0331244,-0.0511374,0.00680745,-0.0109608,-0.11025727,0.01886794,-0.03845569,-0.03643654,0.0033835,-0.02234766,0.02485157,0.03306032,0.06039886,0.01475837,0.01489081,0.04771642,-0.00984714,0.01017774,0.0513636,0.00435329,0.05755547,-0.00722676,0.01720063,0.07134974,-0.00146993,-0.04359443,0.02080461,-0.16904086,-0.05196216,-0.01252922,-0.03566819,0.06267932,0.01529735,-0.00780639,-0.02760991,0.01838513,-0.03173185,0.02979973,0.02538903,0.03894456,-0.04195873,-0.00692261,-0.00396682,-0.07983351,-0.0194968,-0.04327619,-0.01225849,-0.03048233,-0.07200781,-0.04264428,0.03377042,0.00263475,0.02300598,0.08552401,0.05108215,0.0599464,0.05857369,0.00737681,0.06057448,0.01846409,-0.18180265,0.09291239,0.04553485,0.06803266,-0.0570657,-0.00582808,0.03422286,-0.01207027,-0.01655825,0.04031232,-0.00169221,0.03108979,0.01520491,-0.01345081,0.00183516,-0.03400046,0.02220049,-0.01024225,-0.05818936,0.01983421,0.00189841,-0.01380842,0.00435557,-0.02209645,0.00739082,0.00128564,0.08189277,0.02502598,-0.01745308,-0.09452503,0.02379554,0.08684936,-0.03023386,-0.03623807,0.06676383,0.00291701,-0.03354695,0.10886241,-0.01749134,0.01672715,-0.00609164,-0.00135521,0.05014798,0.00963136,-0.0497195,-0.0731534,0.00380554,-0.05636413,-0.03257574,-0.00935975,-0.03639629,-0.08930452,-0.00519749,0.00237378,0.00948377,0.02172651,-0.02163823,0.07799272,0.00281318,0.06301392,0.05798981,-0.04746494,0.01694879,0.0474124,0.01684568,0.0357003,0.03586728,0.0724474,-0.0214127,0.09314357,-0.07260095,-0.03156328,0.01397065,0.03388962,-0.04736703,-0.03509493,-0.01005003,-0.04964105,0.00141499,-0.0003652,0.05433337,-0.07369681,-0.02211148,0.06453181,-0.02859977,0.06840252,-0.053377,-0.02646061,-0.01142061,0.08013129,-0.10263674,-0.00210569,-0.02248125,-0.06100453,0.04307089,0.02078915,-0.05623258,0.00389664,0.01947433,-0.0298522,-0.00433297,0.03090104,0.00033328,-0.09649112,-0.04713858,0.02624416,-0.01872298,-0.07264165,-0.00758512,0.04138993,-0.00365269,-0.04298333,0.03165655,-0.00818501,-0.05673094,-0.05047183,0.04672732,0.04950083,-0.005701,-0.07537641,-0.03866335,0.00692266,0.02296629,0.01350277,0.06922276,-0.08994896,-0.01404582,0.02335786,0.03432404,0.01218476,0.01114436,-0.01101962,0.01139429,0.00814168,-0.04320546,-0.01128976,0.04016071,-0.03279701,0.07821347,0.0322007,0.01082773,0.0280379,-0.09105258,0.01052604,-0.0467586,-0.03826068,0.02799203,-0.00195994,-0.07387864,0.01647223,0.10863534,0.08383303,-0.00691676,0.02073069,0.05685576,0.05694037,0.02969656,0.04766016,-0.01351728,-0.00255894,-0.09095984,-0.22000533,-0.0164922,0.02051124,-0.05146505,-0.04254738,-0.06183426,0.03624102,-0.04036203,-0.06005703,0.11325738,0.1729832,0.01079687,-0.03784338,-0.05823444,0.01046603,0.06753303,-0.01111492,-0.04544266,-0.02508493,0.00990736,0.02213602,-0.01588258,-0.09839132,-0.06122417,0.0416613,-0.03801222,0.14035241,0.03545958,0.01496854,0.0023157,0.04526101,0.01308817,0.0634746,-0.12228024,0.00507174,0.0255699,-0.02069927,0.06457736,0.03268401,0.03271761,-0.02208543,0.00751359,0.02901401,-0.08010057,0.02100083,-0.03586117,-0.01872982,-0.02223337,-0.02086245,0.0471796,0.01906203,0.04018188,0.05000677,0.07516026,-0.04134903,-0.00252208,-0.05080403,-0.03352137,0.0253714,0.02573362,0.01818652,-0.00678472,-0.00828451,-0.07276629,0.06609307,0.0340596,0.00130527,-0.02194252,0.04905801,-0.01430798,-0.06241628,0.06683512,0.00427932,0.02168229,-0.0060286,0.02650165,-0.05164513,0.02138298,0.04844848,-0.00244846,0.02776244,-0.02154672,0.02306642,0.02107002,-0.05391162,0.00998765,-0.0452712,-0.01168414,0.03524441,0.01557425,-0.03888265,-0.02148781,-0.00780673,-0.04603751,0.04322968,0.01087022,-0.22277355,-0.02008787,0.0389581,-0.05600716,-0.04567816,-0.04842119,0.03833032,-0.09262348,-0.03758017,-0.05053755,0.00610321,0.04007645,-0.01988579,-0.02057052,-0.00569812,0.02457934,0.05019053,0.02151293,0.08843442,-0.12617177,0.06827,0.01757147,0.24612597,-0.02707221,0.01104347,0.03700124,-0.03485446,0.01200693,0.05594331,0.05582449,0.01915762,-0.00790636,0.12495548,0.02434327,-0.04485752,0.01570875,0.01107451,0.00741754,0.05915233,0.0128521,-0.00090565,0.03388727,-0.06761893,-0.03430608,0.07375998,-0.10144475,-0.06089581,-0.06110658,0.0372354,0.01203348,-0.06482921,0.01126628,0.0030026,0.02000656,0.05942296,0.03911395,0.02811166,0.00972577,-0.04056358,0.01048445,-0.03615069,-0.03294684,0.0349521,0.06080062,0.00032084],"last_embed":{"hash":"sz5cey","tokens":476}}},"text":null,"length":0,"last_read":{"hash":"sz5cey","at":1751288834885},"key":"Projects/Piecework/node_modules/typescript/README.md#TypeScript","lines":[2,51],"size":2840,"outlinks":[{"title":"![CI","target":"https://github.com/microsoft/TypeScript/actions/workflows/ci.yml/badge.svg","line":3},{"title":"![npm version","target":"https://badge.fury.io/js/typescript.svg","line":4},{"title":"![Downloads","target":"https://img.shields.io/npm/dm/typescript.svg","line":5},{"title":"![OpenSSF Scorecard","target":"https://api.securityscorecards.dev/projects/github.com/microsoft/TypeScript/badge","line":6},{"title":"our blog","target":"https://blogs.msdn.microsoft.com/typescript","line":9},{"title":"Twitter account","target":"https://twitter.com/typescript","line":9},{"title":"TypeScript","target":"https://www.typescriptlang.org/","line":9},{"title":"playground","target":"https://www.typescriptlang.org/play/","line":9},{"title":"our community page","target":"https://www.typescriptlang.org/community/","line":11},{"title":"contribute","target":"https://github.com/microsoft/TypeScript/blob/main/CONTRIBUTING.md","line":29},{"title":"Submit bugs","target":"https://github.com/microsoft/TypeScript/issues","line":30},{"title":"source code changes","target":"https://github.com/microsoft/TypeScript/pulls","line":31},{"title":"StackOverflow","target":"https://stackoverflow.com/questions/tagged/typescript","line":32},{"title":"TypeScript Community Discord","target":"https://discord.gg/typescript","line":33},{"title":"#typescript","target":"https://twitter.com/search?q=%23TypeScript","line":34},{"title":"Contribute bug fixes","target":"https://github.com/microsoft/TypeScript/blob/main/CONTRIBUTING.md","line":35},{"title":"Microsoft Open Source Code of Conduct","target":"https://opensource.microsoft.com/codeofconduct/","line":37},{"title":"Code of Conduct FAQ","target":"https://opensource.microsoft.com/codeofconduct/faq/","line":38},{"title":"<EMAIL>","target":"mailto:<EMAIL>","line":38},{"title":"TypeScript in 5 minutes","target":"https://www.typescriptlang.org/docs/handbook/typescript-in-5-minutes.html","line":43},{"title":"Programming handbook","target":"https://www.typescriptlang.org/docs/handbook/intro.html","line":44},{"title":"Homepage","target":"https://www.typescriptlang.org/","line":45},{"title":"roadmap","target":"https://github.com/microsoft/TypeScript/wiki/Roadmap","line":49}],"class_name":"SmartBlock","last_embed":{"hash":"sz5cey","at":1751288834885}},
"smart_blocks:Projects/Piecework/node_modules/typescript/README.md#TypeScript#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04988435,-0.03851935,0.03175587,-0.04819721,-0.00517887,-0.00795028,-0.08294937,0.03150271,-0.03299171,-0.02589608,-0.0042459,-0.02059339,0.02215777,0.04433505,0.05707882,0.00087598,0.01137823,0.04853976,-0.02871534,0.01062767,0.06482459,0.0014724,0.04640963,-0.01889675,0.0344829,0.07022175,-0.01630525,-0.03742041,0.01577982,-0.15863439,-0.04820656,-0.02427539,-0.02550861,0.06133978,0.00791123,-0.02473313,-0.02856543,0.02981495,-0.04196626,0.02639551,0.02103092,0.04092607,-0.03937058,0.00216007,0.00194327,-0.08746779,-0.02128806,-0.04255577,-0.02979841,-0.0231422,-0.06522192,-0.04555621,0.03198558,0.02107176,0.01886444,0.07312713,0.0529383,0.04576577,0.05495737,0.00663877,0.06261933,0.01245838,-0.18395729,0.09253035,0.04374406,0.0746613,-0.05617844,0.0013931,0.02310808,-0.0097896,-0.01653752,0.04366262,0.00071383,0.03434686,0.01992204,-0.01873654,-0.01058132,-0.02902302,0.01436472,-0.00473095,-0.05554358,0.0171174,0.00106152,-0.01556875,0.01729948,-0.03014852,0.00455688,-0.00394665,0.08871514,0.01837956,-0.01246824,-0.1031568,0.01905099,0.08026621,-0.03258641,-0.04681895,0.057206,0.00517783,-0.03358587,0.11726434,-0.01288523,0.0152451,0.00525602,0.00202166,0.0500153,0.01130038,-0.04257707,-0.07881748,0.00436423,-0.04635949,-0.03391297,-0.0063611,-0.04556358,-0.08495818,-0.01221761,0.01233686,0.0110633,0.03053965,-0.02670207,0.06658337,-0.00653722,0.06142866,0.05000874,-0.05275042,0.00949105,0.04511096,0.01340463,0.04032954,0.03392711,0.07085077,-0.00951096,0.0855078,-0.08352213,-0.03417607,0.00270063,0.02000067,-0.05062132,-0.02090735,-0.00985922,-0.04484536,0.0057543,-0.00400353,0.03617813,-0.0748665,-0.01891088,0.07337067,-0.03145978,0.05208521,-0.04829727,-0.02956457,0.00067231,0.07666926,-0.11058516,0.00733024,-0.02633837,-0.07910621,0.0360349,0.01981807,-0.05764845,0.00338963,0.00874535,-0.02827722,0.00186378,0.04209023,0.00199691,-0.09918631,-0.03220943,0.03819864,-0.0187797,-0.05596248,-0.01607597,0.03432886,0.00301195,-0.0341409,0.02824822,-0.00902644,-0.06079362,-0.04831066,0.03744865,0.04886046,-0.01509259,-0.07047836,-0.01895992,0.00594741,0.02231557,0.02300682,0.08716863,-0.10164367,-0.01089194,0.03109669,0.03753303,0.01556317,0.00314834,-0.01475971,0.01013181,0.01188119,-0.03638657,-0.01288838,0.0374257,-0.0295997,0.08767942,0.03373443,0.01524334,0.04557944,-0.07984646,0.00955994,-0.04314228,-0.03561541,0.01645384,-0.00113106,-0.07270636,0.01415912,0.09959857,0.07927638,0.00945143,0.02744306,0.05916306,0.05104725,0.0177688,0.04562034,-0.02140355,-0.01347055,-0.08601711,-0.21834388,-0.031042,0.00407271,-0.04765689,-0.04444056,-0.06642962,0.0395082,-0.02980296,-0.06417928,0.11490253,0.17952563,0.03645125,-0.04819679,-0.05207676,0.01038004,0.07208046,-0.01161509,-0.04781929,-0.02435393,0.02392165,0.02194035,-0.00737449,-0.09005451,-0.06087776,0.03995948,-0.03953119,0.14383157,0.03971749,0.01915919,0.00547345,0.02360754,0.01064101,0.05862639,-0.09618825,0.00768203,0.02491083,-0.02460705,0.06163584,0.0341729,0.02449369,-0.03008953,0.00091428,0.02845257,-0.0780236,0.02732535,-0.03514623,-0.02481748,-0.02726023,-0.01471881,0.05531674,0.01964918,0.03985919,0.04502517,0.08414371,-0.03600828,0.00462331,-0.06000778,-0.03603059,0.02235364,0.04131557,0.01123163,-0.01237261,-0.01330401,-0.06886698,0.07570036,0.04596994,0.00665001,-0.01673995,0.05615271,-0.01221792,-0.06343078,0.07821576,0.01542957,-0.0040832,-0.00102064,0.03639146,-0.04829734,0.02527406,0.05099828,0.00217927,0.0170093,-0.013065,0.02447757,0.01893249,-0.06003227,0.0083516,-0.04226138,-0.01691624,0.04617018,-0.00110786,-0.04489901,-0.01834168,-0.00932161,-0.03067126,0.04938274,0.00984511,-0.23215155,-0.0293519,0.04824562,-0.06161161,-0.05326311,-0.04584737,0.02837848,-0.10703059,-0.03700045,-0.04376742,0.01031547,0.03158971,-0.0072742,-0.01667131,-0.01165569,0.0311902,0.04467634,0.02862628,0.10153489,-0.11003307,0.06462626,0.01757946,0.24898377,-0.02740496,0.01547494,0.02204672,-0.0348345,0.00057371,0.06607661,0.05244411,0.02400104,-0.01545578,0.13187204,0.0197182,-0.04702209,0.00959081,0.01535126,0.00206177,0.05113347,0.00963627,-0.0023547,0.02468336,-0.07408499,-0.06039084,0.0747363,-0.08453039,-0.07459075,-0.05201949,0.04240876,0.01481394,-0.06374441,0.03265657,-0.00668429,0.02472009,0.05557135,0.03183709,0.02622768,0.00306681,-0.04378996,0.00473717,-0.0330846,-0.01955931,0.02542516,0.05639415,0.01086449],"last_embed":{"hash":"1pu2r5y","tokens":419}}},"text":null,"length":0,"last_read":{"hash":"1pu2r5y","at":*************},"key":"Projects/Piecework/node_modules/typescript/README.md#TypeScript#{1}","lines":[4,13],"size":1159,"outlinks":[{"title":"![CI","target":"https://github.com/microsoft/TypeScript/actions/workflows/ci.yml/badge.svg","line":1},{"title":"![npm version","target":"https://badge.fury.io/js/typescript.svg","line":2},{"title":"![Downloads","target":"https://img.shields.io/npm/dm/typescript.svg","line":3},{"title":"![OpenSSF Scorecard","target":"https://api.securityscorecards.dev/projects/github.com/microsoft/TypeScript/badge","line":4},{"title":"our blog","target":"https://blogs.msdn.microsoft.com/typescript","line":7},{"title":"Twitter account","target":"https://twitter.com/typescript","line":7},{"title":"TypeScript","target":"https://www.typescriptlang.org/","line":7},{"title":"playground","target":"https://www.typescriptlang.org/play/","line":7},{"title":"our community page","target":"https://www.typescriptlang.org/community/","line":9}],"class_name":"SmartBlock","last_embed":{"hash":"1pu2r5y","at":*************}},
"smart_blocks:Projects/Piecework/node_modules/typescript/README.md#TypeScript#Contribute": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.********,-0.********,0.0063244,-0.********,-0.0073275,-0.********,-0.********,0.********,-0.********,-0.********,-0.0057393,-0.********,-0.********,0.********,0.********,0.********,-0.********,0.********,-0.********,-0.********,0.********,-0.********,0.********,0.********,-0.********,0.********,-0.********,-0.********,0.0078331,-0.********,-0.********,0.0198254,-0.********,0.********,0.********,-0.********,-0.********,-0.********,-0.********,0.********,0.01673263,0.04518501,-0.01260323,-0.01659882,0.01830305,-0.0358938,-0.02811495,-0.01244004,0.00699802,-0.02035889,-0.06513254,-0.07367231,0.02039917,0.01881989,0.04887844,0.10259958,0.01134722,0.09008219,0.06567905,0.01619009,0.07122498,0.0708511,-0.16622305,0.11003147,0.08955627,0.10208526,-0.03112269,0.0021429,0.01401837,-0.02496019,-0.02008012,0.00699761,-0.01134938,0.01987411,0.00621997,0.02807643,0.01394254,0.00399924,0.01627792,-0.04836325,-0.0700706,0.05633834,0.01940465,0.02044043,-0.02732842,-0.03950133,0.08175517,0.03952919,0.05315618,0.01219941,-0.00231503,-0.09887808,0.07065266,0.08010146,0.00431272,-0.01242259,0.02298023,0.01085281,-0.06152622,0.12164862,-0.05713191,0.04851028,-0.00059529,0.02779577,0.02461589,-0.0018279,0.02042862,-0.04041537,-0.00792249,-0.0009838,-0.01341972,-0.03600701,-0.06289911,-0.06293742,-0.00425588,0.01516169,0.01832111,0.01033522,-0.04182555,0.02906233,0.02240999,0.06258869,0.02615903,0.00074187,0.01735172,0.0335403,0.02464044,0.05030632,0.05101564,0.0788082,0.0027156,0.11157318,-0.07005103,-0.027167,-0.00821964,-0.01987469,-0.0328541,-0.05298519,0.0281645,-0.00548099,0.05929083,-0.0525448,0.03355383,-0.04068925,-0.05263627,0.07008634,-0.05875238,0.02646601,-0.01173928,-0.01438939,0.00773873,0.07685674,-0.13210253,-0.02158152,0.01626612,-0.00926244,0.08787782,0.02193157,-0.01291388,0.04585005,0.0111762,-0.00196308,-0.04098338,0.02361911,0.0164938,-0.08147975,-0.05538957,0.0011284,-0.03388778,-0.06691916,-0.01092139,0.03322676,-0.02010998,-0.049622,0.0809257,-0.00339404,-0.07130436,-0.03654175,0.06264212,0.04673055,-0.01655446,-0.01443002,-0.05105703,0.02253461,0.037675,-0.01478161,0.02110777,-0.07124261,-0.00602922,0.03065998,-0.00214962,-0.03091984,-0.03652416,0.03591579,0.00058939,-0.0290866,-0.0633638,-0.03551882,0.02153413,-0.00914054,0.00649877,0.01724317,-0.0516801,0.02740634,-0.07045715,0.04017145,0.01200395,-0.030958,0.1159909,-0.00848327,-0.04506447,0.00218425,0.04482678,0.07194148,-0.01518558,0.0274446,0.04597119,0.04583924,0.06480856,0.05235567,-0.02650077,0.0005518,-0.098836,-0.19210736,-0.01521039,-0.01142193,-0.02079536,-0.04492722,-0.08468705,0.01000609,-0.01088201,-0.0665281,0.08830357,0.155247,0.02428573,-0.01079519,-0.07858746,0.0239823,0.010441,-0.03214138,-0.06790467,-0.01317354,-0.02147512,-0.03963663,-0.01374506,-0.06998371,-0.05097955,0.02283903,-0.04182956,0.12770621,0.03948304,0.03117679,0.04679033,0.05117282,0.02491675,0.07581334,-0.16743828,-0.01198998,0.04615908,-0.00852017,0.0171316,0.03898751,0.02374143,-0.04778944,0.01784416,-0.02817933,-0.04421422,0.00270854,-0.01086376,-0.05611489,-0.01795127,-0.03215986,0.08506212,-0.02379239,0.03172768,0.02133844,0.12506439,-0.0498642,0.01680225,-0.03070123,-0.02262465,-0.01326475,0.03866626,0.01784022,0.02596863,-0.01024627,-0.0954919,0.05748938,0.02186649,-0.01780775,-0.01008394,0.07720762,-0.00233877,0.04137774,0.05945455,-0.02256514,0.01822203,-0.04610609,-0.02563712,-0.01313514,0.00012095,-0.00185769,-0.03805438,0.01997457,-0.08478007,0.02858015,0.02929855,-0.02308356,0.04978355,0.00126556,-0.02684244,0.05604682,-0.01115179,-0.01662684,-0.02289694,-0.00901241,-0.05421218,0.05517787,0.02456804,-0.20851856,-0.00710583,0.08377063,-0.05932601,-0.03698191,-0.03652605,0.0639287,-0.10086109,-0.02028219,-0.04832301,0.04152976,0.0870714,-0.01097954,-0.00925672,0.00312425,0.0429823,0.02398409,-0.04867027,0.04787468,-0.09869847,0.04697251,0.04934803,0.23289794,0.00465086,0.01861982,0.03843206,-0.01778851,0.02476502,0.06280755,0.01740019,0.00433937,-0.0115569,0.08692631,-0.04551139,-0.03999464,-0.01216957,-0.00065444,0.03121598,0.05896876,0.03557796,-0.02437364,0.00552478,-0.03126677,-0.05167555,0.10038207,-0.06318646,-0.02948891,-0.07342535,-0.01371525,0.02358711,-0.06425776,0.03032049,0.01874334,-0.0021423,-0.01703843,0.00180059,-0.0029018,0.00848987,-0.02627476,0.03918763,-0.01678601,-0.07022534,0.00324853,0.03679774,0.00688761],"last_embed":{"hash":"1tw6co9","tokens":345}}},"text":null,"length":0,"last_read":{"hash":"1tw6co9","at":1751288835154},"key":"Projects/Piecework/node_modules/typescript/README.md#TypeScript#Contribute","lines":[28,41],"size":1077,"outlinks":[{"title":"contribute","target":"https://github.com/microsoft/TypeScript/blob/main/CONTRIBUTING.md","line":3},{"title":"Submit bugs","target":"https://github.com/microsoft/TypeScript/issues","line":4},{"title":"source code changes","target":"https://github.com/microsoft/TypeScript/pulls","line":5},{"title":"StackOverflow","target":"https://stackoverflow.com/questions/tagged/typescript","line":6},{"title":"TypeScript Community Discord","target":"https://discord.gg/typescript","line":7},{"title":"#typescript","target":"https://twitter.com/search?q=%23TypeScript","line":8},{"title":"Contribute bug fixes","target":"https://github.com/microsoft/TypeScript/blob/main/CONTRIBUTING.md","line":9},{"title":"Microsoft Open Source Code of Conduct","target":"https://opensource.microsoft.com/codeofconduct/","line":11},{"title":"Code of Conduct FAQ","target":"https://opensource.microsoft.com/codeofconduct/faq/","line":12},{"title":"<EMAIL>","target":"mailto:<EMAIL>","line":12}],"class_name":"SmartBlock","last_embed":{"hash":"1tw6co9","at":1751288835154}},
"smart_blocks:Projects/Piecework/node_modules/typescript/README.md#TypeScript#Contribute#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05788983,-0.02953558,0.00278733,-0.07169007,-0.00453232,-0.03242149,-0.04819234,0.03762156,-0.09718969,-0.0261233,-0.00711316,-0.05186233,-0.01657583,0.0179154,0.05534993,0.03920367,-0.0203988,0.00590492,-0.02561476,-0.01860896,0.01696978,-0.0277785,0.04541201,0.02620479,-0.0043661,0.04066476,-0.04138836,-0.09516827,0.0099046,-0.16335291,-0.02317332,0.01921709,-0.01210687,0.03832954,0.06930008,-0.02089756,-0.02477928,-0.02878173,-0.05635166,0.02579277,0.01654955,0.04702828,-0.01053197,-0.01740424,0.01566205,-0.03564883,-0.03096009,-0.01398114,0.01055369,-0.01897381,-0.05782476,-0.07476876,0.01754927,0.02443251,0.05082124,0.09963073,0.00409621,0.09147493,0.06687238,0.01682034,0.06909861,0.07563487,-0.16425021,0.10149401,0.08580758,0.10538514,-0.03074539,0.00664901,0.01253469,-0.02768765,-0.02326741,0.0058184,-0.01230847,0.01843193,0.0074488,0.03144362,0.01548101,0.00721512,0.01334215,-0.05011042,-0.06444984,0.05798785,0.01736058,0.02671673,-0.02534484,-0.03619796,0.08816199,0.03537285,0.05144262,0.01416594,-0.00501578,-0.09468529,0.06658947,0.07930704,0.00294942,-0.01640223,0.0231055,0.00935911,-0.06651668,0.12415477,-0.0568031,0.05112393,0.0009996,0.0256916,0.02602195,-0.00070698,0.02502283,-0.03502444,-0.00713462,0.00512315,-0.0133297,-0.03666869,-0.07221227,-0.06686419,-0.00023931,0.01467828,0.01325928,0.00965167,-0.042124,0.0257498,0.02360471,0.05941094,0.02011796,0.00277453,0.01538036,0.03298153,0.02125462,0.05438649,0.05076912,0.07820097,0.00035307,0.11315807,-0.07377829,-0.02653902,-0.01034565,-0.02339974,-0.02784536,-0.05580261,0.02942971,0.00186207,0.0646268,-0.0526914,0.03574947,-0.03973793,-0.04835338,0.07357708,-0.05619056,0.02871503,-0.01012439,-0.01730129,0.00623955,0.0808413,-0.13303757,-0.02513672,0.01784404,-0.00355451,0.08929541,0.01567195,-0.00735212,0.04761301,0.00840533,-0.00477502,-0.03955351,0.02501054,0.01632873,-0.07989901,-0.05914963,0.00324059,-0.03246799,-0.0599588,-0.00911324,0.03041707,-0.0229434,-0.04882215,0.08670696,-0.0046098,-0.07657658,-0.03571714,0.06349571,0.05006609,-0.01675749,-0.01228352,-0.04818913,0.0200053,0.03847834,-0.01330134,0.01938359,-0.06769123,-0.01066918,0.02931827,-0.00789105,-0.02697325,-0.04039514,0.03419354,0.00300031,-0.02716859,-0.0668011,-0.03915951,0.02224249,-0.00616035,0.0040874,0.0157834,-0.05473761,0.02108895,-0.06930549,0.03557957,0.0160754,-0.02604573,0.1150532,-0.00947763,-0.04468376,0.00757949,0.04598916,0.07292108,-0.01749468,0.03645561,0.04331485,0.04664849,0.07009632,0.0503173,-0.02211739,-0.00435688,-0.09632305,-0.19009717,-0.01576023,-0.01141868,-0.02247844,-0.04523032,-0.08355116,0.01063108,-0.01001302,-0.07180862,0.08390478,0.15555832,0.02086386,-0.0086733,-0.07236394,0.02301388,0.01134647,-0.0306394,-0.06622985,-0.00964819,-0.02586973,-0.04071676,-0.01596143,-0.061866,-0.04787733,0.02164424,-0.03581041,0.12795496,0.04253768,0.02787734,0.04554261,0.04875633,0.02281848,0.07048325,-0.16703945,-0.01396236,0.04381555,-0.00936898,0.01181268,0.03749356,0.02478698,-0.04896848,0.01887481,-0.03035237,-0.04021757,0.00361455,-0.008294,-0.05995051,-0.0176758,-0.0318996,0.08594594,-0.02716579,0.03288066,0.0210593,0.1244158,-0.04472908,0.01492735,-0.0326707,-0.01752212,-0.01219864,0.0418741,0.01921204,0.02090186,-0.01498008,-0.09888074,0.05713976,0.01783487,-0.0226282,-0.01108489,0.07411833,-0.00164238,0.04400796,0.05592876,-0.02496029,0.02032959,-0.04207769,-0.02931949,-0.01314498,-0.00117286,-0.00475224,-0.03880759,0.01890882,-0.09019464,0.02573912,0.03329735,-0.02312137,0.05428841,0.00568432,-0.01864261,0.05778205,-0.01220127,-0.01789382,-0.02584361,-0.0086383,-0.04738098,0.05109267,0.02734333,-0.20996235,-0.00651036,0.08404439,-0.06044288,-0.03667385,-0.03585878,0.06704011,-0.09982199,-0.0224498,-0.04601238,0.04576867,0.09370657,-0.01002805,-0.00234057,0.00632045,0.04117362,0.02209303,-0.05056749,0.04779723,-0.10154492,0.0495795,0.05425185,0.23086855,0.00572553,0.01780564,0.03468341,-0.01813314,0.02226405,0.06143732,0.01186298,0.00750377,-0.01397729,0.08719146,-0.04934713,-0.03744187,-0.00763014,-0.00364166,0.0294113,0.05190295,0.03728875,-0.0245975,0.00428003,-0.03249933,-0.05640772,0.10657475,-0.06278349,-0.02352591,-0.07882086,-0.01489004,0.02239032,-0.06304434,0.03171023,0.01939635,-0.00361489,-0.0220368,-0.00321331,-0.00282617,0.01151515,-0.02360338,0.04049122,-0.01546382,-0.07055245,-0.00332841,0.03357157,0.00453278],"last_embed":{"hash":"1tbe24v","tokens":344}}},"text":null,"length":0,"last_read":{"hash":"1tbe24v","at":1751288835320},"key":"Projects/Piecework/node_modules/typescript/README.md#TypeScript#Contribute#{1}","lines":[30,41],"size":1060,"outlinks":[{"title":"contribute","target":"https://github.com/microsoft/TypeScript/blob/main/CONTRIBUTING.md","line":1},{"title":"Submit bugs","target":"https://github.com/microsoft/TypeScript/issues","line":2},{"title":"source code changes","target":"https://github.com/microsoft/TypeScript/pulls","line":3},{"title":"StackOverflow","target":"https://stackoverflow.com/questions/tagged/typescript","line":4},{"title":"TypeScript Community Discord","target":"https://discord.gg/typescript","line":5},{"title":"#typescript","target":"https://twitter.com/search?q=%23TypeScript","line":6},{"title":"Contribute bug fixes","target":"https://github.com/microsoft/TypeScript/blob/main/CONTRIBUTING.md","line":7},{"title":"Microsoft Open Source Code of Conduct","target":"https://opensource.microsoft.com/codeofconduct/","line":9},{"title":"Code of Conduct FAQ","target":"https://opensource.microsoft.com/codeofconduct/faq/","line":10},{"title":"<EMAIL>","target":"mailto:<EMAIL>","line":10}],"class_name":"SmartBlock","last_embed":{"hash":"1tbe24v","at":1751288835320}},
"smart_blocks:Projects/Piecework/node_modules/typescript/README.md#TypeScript#Documentation": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06351775,-0.01899656,0.07368207,-0.02199927,0.00245628,-0.01765743,-0.07286501,-0.01542637,-0.05744572,-0.02303821,0.01646471,-0.01614039,-0.00101099,0.05145147,-0.00102478,-0.0179981,-0.01172593,0.0225705,0.00058821,0.01771616,0.11070902,-0.01396128,0.00955751,0.00259353,0.00695962,0.05702239,0.0173318,-0.00950701,0.03062977,-0.15771863,-0.05795708,0.00958373,-0.05483284,0.04257247,0.04321211,-0.02987242,-0.03094002,-0.00783577,-0.04146176,0.05910503,0.00964182,0.06391505,-0.024148,-0.03371826,0.01239267,-0.08192141,-0.03217747,-0.04280272,0.02215033,-0.05340504,-0.08351523,-0.06678786,0.04699447,-0.01197827,0.03276988,0.09785546,0.08493579,0.05104621,0.07084747,0.01007947,0.01619771,0.0539669,-0.19860761,0.09630264,0.06299036,0.0771982,-0.04024199,-0.0027486,0.04376177,-0.02047195,-0.02071739,0.0383124,-0.03176053,0.05260719,0.00581646,-0.04172758,-0.00163999,-0.03681171,0.03759076,-0.04083113,-0.07491641,0.03083809,-0.01812014,-0.00811269,-0.040024,-0.01477541,0.05517602,0.02103888,0.05665744,0.04040319,-0.00269236,-0.07953377,0.02606128,0.10252562,-0.03157293,0.00960321,0.04890107,0.02293563,-0.00144012,0.11863492,-0.05328321,0.01893509,0.01785763,-0.01210148,0.02182196,-0.00519623,-0.02694499,-0.03664351,-0.01513779,-0.00012859,-0.03861931,0.00753334,-0.0597233,-0.09892222,-0.00605244,-0.01932573,0.04276109,0.02881176,0.00921606,0.07796355,0.00961068,0.04279681,0.01570792,-0.01751542,0.01209268,0.003036,0.04155761,0.01315766,0.03006246,0.11571209,0.04159397,0.11703821,-0.04012745,0.00306143,0.01920641,0.00411139,-0.00618852,-0.0535024,-0.00020599,-0.04493805,0.01888154,-0.0589769,0.01973185,-0.06759907,-0.0244347,0.11105226,-0.04501473,0.06401262,-0.04137909,-0.02402859,0.00270533,0.07095509,-0.05985682,-0.00894933,-0.02155664,-0.03924248,0.05379699,0.02973032,-0.01696298,-0.00751145,0.01232513,0.01034678,-0.02933602,0.05239731,0.02451721,-0.07284129,-0.04710508,0.04611766,0.00320316,-0.10246929,0.00411538,0.0332179,-0.02133608,-0.01096055,0.08681026,0.0076444,-0.01572734,-0.03462608,0.04133405,0.01072704,-0.00831767,-0.0685104,-0.00764077,0.03181638,0.0043091,-0.02760685,0.00643762,-0.04202995,0.00653441,0.06301035,0.01830861,-0.01840585,0.02718546,0.04740054,-0.01603535,-0.02100828,-0.0729311,-0.03311454,0.0201164,-0.02197019,0.08282299,0.02459722,-0.02799853,0.08575442,-0.04469472,0.02041162,-0.04670076,-0.00869261,0.0706111,0.00652253,-0.10395525,-0.01455805,0.02558084,0.04923144,0.01469239,0.00096493,0.0464534,0.06734983,-0.00191297,0.05379523,-0.04394329,-0.04914157,-0.11454158,-0.19226348,0.03027126,0.00297729,-0.01711252,-0.03275608,-0.05384831,0.01653324,-0.02875364,-0.04097969,0.08535441,0.14549129,0.00200244,-0.03404789,-0.04610269,-0.0069429,0.0173798,-0.01124036,-0.03703358,-0.03268364,0.02585639,0.04633832,-0.02887538,-0.12375817,-0.08440567,0.00708961,-0.02666282,0.12740603,-0.0448182,0.05327586,-0.0139184,0.03333695,0.02646378,0.03795994,-0.11393303,-0.04634981,0.0341257,-0.02647693,0.0180957,0.04801374,-0.0124144,-0.0418073,0.03409613,0.02209743,-0.03243242,0.00793354,0.01459506,-0.0715982,-0.00686579,-0.00152093,0.00864113,0.00049669,0.05038245,0.00761634,0.08018707,-0.0685896,-0.00356045,-0.02884304,-0.0401772,-0.02138817,0.00889692,0.00245934,0.01663901,-0.00788203,-0.06005128,0.05673971,0.01800261,0.00605261,-0.01916508,0.02643072,-0.00855181,-0.00286537,0.04677395,0.01651037,-0.02304845,-0.00918771,0.01511695,-0.02535686,0.00144051,0.01140109,-0.02074853,0.01353022,-0.04960557,0.01097222,0.02521757,-0.02933705,0.00287311,-0.0187889,-0.02655699,0.05164872,0.00381587,-0.00001295,0.03337564,-0.01152992,-0.01888555,0.07591429,-0.01154436,-0.25453883,0.0349745,0.06690411,0.00186308,-0.03081089,0.01201467,0.05234487,-0.09385635,-0.0204142,-0.04742439,0.02209621,0.01198784,0.01659389,-0.00680111,-0.04722068,0.05630243,0.06390456,-0.018462,0.04151805,-0.0482359,-0.00455449,0.01481928,0.24215695,-0.06400225,0.01547591,0.05466257,-0.04630141,0.00123067,0.07721642,0.04302485,0.02858832,0.00021174,0.11058643,0.00440091,-0.0727164,-0.0054349,0.00149341,0.05442119,0.07130272,0.03371653,-0.01463228,-0.01827256,-0.03310753,-0.00131328,0.07895833,-0.04283221,-0.08843663,-0.12849185,-0.02639616,0.05618357,-0.1099046,0.0117552,0.00790915,0.02196493,0.00770268,0.01540618,0.06926709,0.01491027,-0.0695646,-0.03154937,-0.02859732,-0.05162485,0.04840047,0.056445,0.03796152],"last_embed":{"hash":"1vq79lr","tokens":105}}},"text":null,"length":0,"last_read":{"hash":"1vq79lr","at":1751288835412},"key":"Projects/Piecework/node_modules/typescript/README.md#TypeScript#Documentation","lines":[42,47],"size":258,"outlinks":[{"title":"TypeScript in 5 minutes","target":"https://www.typescriptlang.org/docs/handbook/typescript-in-5-minutes.html","line":3},{"title":"Programming handbook","target":"https://www.typescriptlang.org/docs/handbook/intro.html","line":4},{"title":"Homepage","target":"https://www.typescriptlang.org/","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"1vq79lr","at":1751288835412}},
"smart_blocks:Projects/Piecework/node_modules/typescript/README.md#TypeScript#Documentation#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06335323,-0.02016121,0.073341,-0.02158389,0.00062701,-0.01451662,-0.07438761,-0.01908624,-0.05807362,-0.02495352,0.01783349,-0.00949008,-0.00017722,0.05115524,-0.00342387,-0.01941881,-0.01165656,0.02218586,-0.00171113,0.01997229,0.11284438,-0.01594304,0.00817203,0.00369489,0.00680026,0.05603366,0.01604153,-0.0096832,0.03108104,-0.15741074,-0.05836244,0.00739647,-0.05490148,0.04419978,0.03930608,-0.0321397,-0.03248782,-0.00376377,-0.04118533,0.05904313,0.00918239,0.06389309,-0.0218034,-0.03645699,0.0100946,-0.08413485,-0.03730768,-0.04200532,0.02412163,-0.05036949,-0.08257581,-0.06648982,0.04605843,-0.00912516,0.03075396,0.09462276,0.08686463,0.05266913,0.07235509,0.01255199,0.01419332,0.0529165,-0.19924764,0.09505362,0.05871176,0.07810313,-0.03907485,-0.0033,0.04714656,-0.0222856,-0.02464882,0.03770591,-0.0324361,0.05222173,0.00542588,-0.04267388,-0.00177749,-0.03523701,0.04182387,-0.04010886,-0.07212657,0.02868694,-0.02142932,-0.0061039,-0.04135455,-0.01708237,0.05473872,0.02036966,0.05477934,0.04254791,0.00025794,-0.08043604,0.02278565,0.1003412,-0.0337634,0.00696127,0.04876749,0.02511089,-0.00115907,0.11862126,-0.05211315,0.01865869,0.02067499,-0.01259598,0.02202065,-0.00590209,-0.02708491,-0.03283281,-0.01329336,0.00066618,-0.0397599,0.00732034,-0.06245771,-0.1001768,-0.00710321,-0.01878083,0.04191823,0.02622883,0.00819598,0.07895608,0.01171163,0.03897348,0.01496031,-0.01873276,0.01144758,0.00420818,0.03891456,0.01121922,0.02947308,0.11694665,0.04208209,0.1165508,-0.03974114,0.00411033,0.0205992,0.00397416,-0.0032582,-0.05054174,0.00072908,-0.04257442,0.01920363,-0.0600905,0.02264996,-0.06537306,-0.02257793,0.1142832,-0.04759593,0.06597788,-0.04094215,-0.02753267,0.00183503,0.07390831,-0.06045743,-0.00988631,-0.02006783,-0.03806752,0.05316826,0.02669607,-0.01324156,-0.00943571,0.01012218,0.01405658,-0.02800001,0.05384828,0.02540792,-0.06828126,-0.04592061,0.0461331,0.0037652,-0.10160913,0.00413095,0.03244041,-0.02149822,-0.0081519,0.08911156,0.00575601,-0.01719513,-0.02910472,0.04523316,0.01137901,-0.00944963,-0.06950127,-0.0089094,0.03215191,0.00583924,-0.02658186,0.00770034,-0.03877413,0.00418283,0.06389833,0.01486116,-0.01948296,0.02733786,0.04721375,-0.01584164,-0.02282294,-0.0745583,-0.03471611,0.02024193,-0.01963112,0.08408245,0.02192044,-0.0267058,0.08294503,-0.04338144,0.01739589,-0.04658617,-0.005188,0.07216513,0.00392542,-0.10570966,-0.01312461,0.02593159,0.04495947,0.01908924,0.00583217,0.04277894,0.06446808,-0.00264835,0.05155834,-0.04351223,-0.04797718,-0.11239761,-0.19060367,0.03091294,0.00277465,-0.01411693,-0.03003778,-0.05826756,0.01576216,-0.03037034,-0.04360899,0.08582813,0.14276133,0.00286815,-0.03340989,-0.04377322,-0.0079836,0.01800609,-0.00651731,-0.03426114,-0.02989259,0.0277576,0.0458112,-0.02972412,-0.12372652,-0.08308724,0.00709207,-0.02382214,0.12829657,-0.05186221,0.05181538,-0.01641322,0.03148062,0.02514271,0.03694378,-0.11180698,-0.04492718,0.03587132,-0.02816205,0.01369457,0.05008549,-0.01379185,-0.04157182,0.03565039,0.02420106,-0.03132339,0.00918381,0.01620445,-0.07422391,-0.00698268,0.00034096,0.00492828,-0.0002704,0.05290641,0.00909619,0.07730705,-0.06985341,-0.00970276,-0.02708763,-0.0389067,-0.0250573,0.00946102,0.00236674,0.0138572,-0.00869625,-0.05920273,0.05611499,0.01643384,0.00573797,-0.01943573,0.02729415,-0.00521069,-0.00297633,0.04855225,0.01655222,-0.02395735,-0.00509053,0.01513488,-0.02501884,0.00106641,0.01372693,-0.0229109,0.01206202,-0.05198965,0.01031175,0.02511568,-0.03090469,0.00061668,-0.01712432,-0.02562877,0.05101096,0.00434712,0.00130339,0.03272798,-0.01098876,-0.01405369,0.07426649,-0.00976654,-0.25624716,0.03340247,0.06734871,0.00279701,-0.03290287,0.01130275,0.05242909,-0.09299255,-0.02255089,-0.04647666,0.0210337,0.01249797,0.0199925,-0.00540777,-0.04692363,0.05860978,0.06350503,-0.01593905,0.04167995,-0.04941929,-0.00546595,0.01648874,0.24149378,-0.06732998,0.01374803,0.054467,-0.04739397,-0.00049244,0.07616226,0.03909413,0.03088156,-0.00161628,0.11278197,0.00573632,-0.07368147,-0.00163678,0.00002426,0.05257875,0.06788808,0.03477107,-0.01362534,-0.02278789,-0.03333869,-0.00270675,0.08007453,-0.04037699,-0.08728673,-0.12819961,-0.02821328,0.05710628,-0.109145,0.01210949,0.00518092,0.02378625,0.00928772,0.01654904,0.07388547,0.01809926,-0.07337442,-0.03586769,-0.02678182,-0.05186987,0.04520886,0.0571713,0.03959117],"last_embed":{"hash":"1ojr63l","tokens":104}}},"text":null,"length":0,"last_read":{"hash":"1ojr63l","at":1751288835497},"key":"Projects/Piecework/node_modules/typescript/README.md#TypeScript#Documentation#{1}","lines":[44,47],"size":238,"outlinks":[{"title":"TypeScript in 5 minutes","target":"https://www.typescriptlang.org/docs/handbook/typescript-in-5-minutes.html","line":1},{"title":"Programming handbook","target":"https://www.typescriptlang.org/docs/handbook/intro.html","line":2},{"title":"Homepage","target":"https://www.typescriptlang.org/","line":3}],"class_name":"SmartBlock","last_embed":{"hash":"1ojr63l","at":1751288835497}},
