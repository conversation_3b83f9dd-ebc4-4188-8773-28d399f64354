- `__rules`: This provides rules you'll need to follow in each workspace
- `__rules` in parent folders apply to their children!!
- `__logs` in each folder represent activities taken in the space and should be appended 
- `__memos`: These are notes taken from memos daily and should be refreshed in every run
- `__home`: These are the about of the folder, it should contain a brief and links to the child __home files with their folder names 
- If there are no `__rules` or `__logs` in the a folder then create them even if they are empty
- Any user correction should be appended to the `__rules` file as a new `__rule`
- The `__memo` will be created in the root each day to which it needs to be related to its corresponding project
- The `__memo` are unstructured notes related to real world feedback that feeds into both projects and tasks (they are also daily notes essentially)
- Only refer to latest results as the best option for a response
- Do not waffle in the response, just respond with meaningful output of what was done. The User does not care about the AI personality
- Use #tags as much as possible and add them to a separate section of this document to keep them in order. 
- #tags are global for now
- `__memos` populate the daily notes in a more structured way

## #tags

### Project Status Tags
- #started - Project has begun
- #notstarted - Project not yet initiated
- #completed - Project finished
- #blocked - Project has blocking issues
- #overdue - Project past deadline

### Payment Status Tags
- #paid - Fully paid
- #halfpaid - Partially paid
- #unpaid - No payment received

### Tracking Tags
- #tracked - Project is being tracked
- #untracked - Project not being tracked
- #gittracked - Version controlled

### Priority Tags
- #priority - High priority item
- #dailytasks - Daily task items

### Team Tags
- #team - Team-related activities
- #collaboration - Collaborative work
- #roles - Role definitions

### Organization Tags
- #workspace - Workspace organization
- #organization - General organization
- #taskmanagement - Task management activities
- #projects - Project-related items
- #performance - Performance tracking
- #kpi - Key Performance Indicators