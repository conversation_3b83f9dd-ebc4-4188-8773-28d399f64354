import React from 'react';

const GigWorkerOnboardingForm = () => {
  return (
    <div className="bg-gray-100 min-h-screen py-16">
      <div className="container mx-auto px-6">
        <div className="max-w-2xl mx-auto bg-white p-8 rounded-lg shadow">
          <h2 className="text-3xl font-bold text-gray-800 mb-8">Join Piecework as a Gig Worker</h2>
          <form>
            <div className="mb-6">
              <label htmlFor="name" className="block text-gray-800 font-bold mb-2">Full Name</label>
              <input type="text" id="name" className="w-full px-4 py-3 bg-gray-200 rounded-lg focus:outline-none focus:bg-white" />
            </div>
            <div className="mb-6">
              <label htmlFor="phone" className="block text-gray-800 font-bold mb-2">Phone Number</label>
              <input type="tel" id="phone" className="w-full px-4 py-3 bg-gray-200 rounded-lg focus:outline-none focus:bg-white" />
            </div>
            <div className="mb-6">
              <label htmlFor="category" className="block text-gray-800 font-bold mb-2">What kind of work do you do?</label>
              <select id="category" className="w-full px-4 py-3 bg-gray-200 rounded-lg focus:outline-none focus:bg-white">
                <option>Plumbing</option>
                <option>Painting</option>
                <option>Electrical</option>
                <option>Carpentry</option>
                <option>Other</option>
              </select>
            </div>
            <div className="mb-6">
              <label htmlFor="availability" className="block text-gray-800 font-bold mb-2">Availability</label>
              <p className="text-gray-600 mb-2">Are you open to being on a retainer for consistent work?</p>
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input type="radio" name="retainer" value="yes" className="mr-2" />
                  Yes
                </label>
                <label className="flex items-center">
                  <input type="radio" name="retainer" value="no" className="mr-2" />
                  No
                </label>
              </div>
            </div>
            <div className="text-center">
              <button type="submit" className="bg-blue-500 text-white font-bold py-4 px-8 rounded-lg hover:bg-blue-600 transition-colors">
                Join Piecework
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default GigWorkerOnboardingForm;

