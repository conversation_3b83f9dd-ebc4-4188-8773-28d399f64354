
"smart_sources:Projects/Piecework/node_modules/electron-to-chromium/README.md": {"path":"Projects/Piecework/node_modules/electron-to-chromium/README.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07078947,-0.00028156,0.01255313,-0.00540207,-0.04819756,-0.00256356,-0.08866439,0.0076039,-0.05311925,-0.02714715,-0.00990262,-0.06648181,0.05459747,0.04703183,0.07236551,0.01570633,0.04484123,0.01972351,-0.01945006,0.01384324,0.06985634,-0.01087985,0.09522896,-0.05278711,0.04029231,0.06114555,-0.00518877,-0.02453944,0.04783474,-0.20367834,0.00092774,0.00026521,-0.02670911,-0.01202364,0.038212,-0.01132693,-0.04039873,-0.02275971,-0.08077111,0.0293379,-0.00523648,0.03544142,0.0039711,0.00062584,0.05410066,0.02291678,0.00349814,-0.04451104,0.02430742,-0.05793111,-0.02629537,-0.00899952,0.07897697,-0.03169981,0.00749173,0.03546389,0.02432609,0.0894312,0.06914545,0.04972865,-0.00489131,0.08009791,-0.18316904,0.07903993,0.0511854,0.00137609,-0.04725828,-0.02579672,0.00715196,0.05783731,0.02263796,0.04021327,0.01343361,0.052451,0.01555432,-0.03548983,0.03781416,-0.05111872,-0.07208485,-0.02760656,-0.04792285,0.0433309,-0.06385468,0.00142948,-0.02394893,0.05293206,0.03616916,0.00303318,0.0558925,0.0227614,-0.02221481,-0.1348035,-0.00100154,0.03872985,-0.0369778,-0.0229535,0.06383394,0.03532282,-0.03046049,0.11892176,-0.03916852,0.04980204,0.01658537,-0.02593864,0.02712219,-0.00562251,-0.02280699,-0.04405805,-0.06912979,-0.02143374,0.02665599,-0.01424307,-0.10026994,-0.10352506,-0.02994049,0.00928481,0.01045076,0.02401012,0.04113152,0.07299767,0.0148916,0.05655851,0.04736949,-0.03356569,0.01209941,0.00813849,0.03874632,0.02083456,-0.03238359,0.09068985,-0.00366955,0.04267864,-0.01678663,-0.03503021,-0.08136414,0.01091589,0.01488295,-0.0472255,-0.0177844,-0.04384021,0.01423324,-0.00508018,0.0130264,-0.08528212,-0.03754338,0.01049082,-0.02115553,0.0203785,-0.03295469,-0.03135677,-0.01330095,0.01612805,-0.03895665,0.0126802,-0.02901604,-0.03187289,0.02818666,0.02689473,0.00108878,0.05458175,-0.00676135,-0.02440162,-0.00299005,0.08315437,0.00864383,-0.08108807,0.01558794,0.09645446,0.01927225,-0.08604659,0.00319684,0.10057815,0.00242802,0.03142757,0.03936981,0.00622803,-0.06275724,-0.06210148,0.04943189,0.05796907,0.02021573,-0.04263076,-0.0219152,-0.00011012,-0.03964891,-0.07974293,0.01644199,-0.02450552,-0.01410486,-0.02450833,0.00197445,0.06203909,-0.0097979,0.04268406,-0.01308141,-0.05360344,-0.04933056,-0.03075069,-0.00724894,-0.02594901,0.10232291,-0.00373405,-0.07915165,0.05578058,-0.0247116,0.01687301,0.02083896,-0.00925391,0.04047269,0.00996261,-0.05857982,-0.03686372,0.05475003,0.02228044,-0.05451414,-0.02500436,0.01167555,0.06368569,0.05074184,0.0716852,-0.03020917,-0.01409783,-0.10602923,-0.22661063,-0.0033437,-0.01407827,0.00445092,-0.00131885,0.0100056,0.00955776,-0.04418614,0.00250818,0.02241384,0.12169245,0.03552196,-0.05155207,-0.02894715,-0.05922263,0.00552706,-0.00307427,0.00790075,-0.05298078,-0.00509062,0.05501398,-0.02331609,-0.03787456,-0.0631514,0.01817248,0.02151435,0.18595502,0.03848342,-0.00200966,0.00308125,0.06392545,0.02566919,0.05682915,-0.11323805,-0.01030134,0.06035392,0.01983322,0.07273867,0.00382055,-0.04121653,-0.02961815,0.01734957,0.01662451,-0.11133507,0.00144301,-0.01182364,-0.06102369,-0.05604086,0.01822679,0.02433409,-0.00791818,-0.00105664,-0.00201837,0.05330703,-0.02499696,-0.0259811,-0.03684087,0.0304071,0.02896593,-0.00398323,0.02946971,-0.03646873,-0.01810108,-0.04722173,0.07017424,0.06007549,0.01895063,-0.01172881,0.05429859,-0.04857101,-0.0115103,0.08698552,0.02649822,0.03702114,0.00005703,-0.02268748,-0.03895025,0.05086979,-0.02909754,0.03741962,-0.00415965,-0.0304673,0.08494956,-0.03079263,-0.01945592,0.05061537,-0.02037384,-0.04089864,0.00169538,-0.00860988,-0.01991177,0.01993216,-0.05991963,0.00410845,0.03845569,-0.00847324,-0.23829798,0.00798204,0.01459598,0.00650856,-0.06659165,0.02136421,0.03447093,-0.04544982,-0.00801446,-0.01440773,-0.06019608,0.05100817,0.01223196,-0.05486773,0.00547192,0.06440318,0.07498205,-0.02649482,0.05323412,-0.06410497,0.08136293,0.01045437,0.24481557,0.01228805,0.02211472,0.03429631,-0.00952545,0.0074593,0.05970614,0.08635274,-0.00348249,0.0207275,0.05266542,-0.01274209,-0.07961186,-0.0001779,-0.05759234,-0.01071462,-0.00260701,-0.01022563,-0.02727891,0.04975667,-0.03513747,-0.02139879,0.08784609,-0.05450316,-0.07270687,-0.09119646,0.00230117,0.006167,-0.06501086,-0.00323317,-0.0247224,0.02399028,0.00443706,0.06517532,-0.01490769,0.02683466,-0.04148984,-0.00260904,-0.00174911,0.02018806,0.0487369,0.06910086,0.02224404],"last_embed":{"hash":"1irr8sf","tokens":447}}},"last_read":{"hash":"1irr8sf","at":1751288788300},"class_name":"SmartSource","last_import":{"mtime":1751244535588,"size":6460,"at":1751288765474,"hash":"1irr8sf"},"blocks":{"###Made by [@kilianvalkhof](https://twitter.com/kilianvalkhof)":[1,187],"###Made by [@kilianvalkhof](https://twitter.com/kilianvalkhof)#Other projects:":[3,187],"###Made by [@kilianvalkhof](https://twitter.com/kilianvalkhof)#Other projects:#{1}":[5,5],"###Made by [@kilianvalkhof](https://twitter.com/kilianvalkhof)#Other projects:#{2}":[6,6],"###Made by [@kilianvalkhof](https://twitter.com/kilianvalkhof)#Other projects:#{3}":[7,187],"#---frontmatter---":[9,null]},"outlinks":[{"title":"@kilianvalkhof","target":"https://twitter.com/kilianvalkhof","line":1},{"title":"Polypane","target":"https://polypane.app","line":5},{"title":"Superposition","target":"https://superposition.design","line":6},{"title":"FromScratch","target":"https://fromscratch.rocks","line":7},{"title":"![FOSSA Status","target":"https://app.fossa.io/api/projects/git%2Bgithub.com%2FKilian%2Felectron-to-chromium.svg?type=shield","line":11},{"title":"![codecov","target":"https://codecov.io/gh/Kilian/electron-to-chromium/branch/master/graph/badge.svg","line":11},{"title":"![npm-downloads","target":"https://img.shields.io/npm/dm/electron-to-chromium.svg","line":11},{"title":"![npm","target":"https://img.shields.io/npm/v/electron-to-chromium.svg","line":11},{"title":"![travis","target":"https://img.shields.io/travis/Kilian/electron-to-chromium/master.svg","line":11},{"title":"Browserslist","target":"https://github.com/ai/browserslist","line":15},{"title":"eslint-plugin-compat","target":"https://github.com/amilajack/eslint-plugin-compat","line":15},{"title":"babel-preset-env","target":"https://github.com/babel/babel-preset-env","line":15},{"title":"Autoprefixer","target":"https://github.com/postcss/autoprefixer","line":15},{"title":"Stylelint","target":"https://github.com/stylelint/stylelint","line":15},{"title":"Browserslist","target":"https://github.com/ai/browserslist","line":134},{"title":"![FOSSA Status","target":"https://app.fossa.io/api/projects/git%2Bgithub.com%2FKilian%2Felectron-to-chromium.svg?type=large","line":186}],"last_embed":{"hash":"1irr8sf","at":1751288787655}},"smart_blocks:Projects/Piecework/node_modules/electron-to-chromium/README.md###Made by [@kilianvalkhof](https://twitter.com/kilianvalkhof)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0771832,-0.00222793,0.00756568,-0.00771389,-0.0499059,-0.00016691,-0.09018709,0.00827464,-0.05272148,-0.02461687,-0.01800532,-0.06412537,0.05840974,0.04402148,0.07071226,0.02062787,0.03447194,0.02084286,-0.02602745,0.0101925,0.07292472,-0.02409421,0.08790145,-0.06878287,0.04625383,0.06080316,-0.01183479,-0.02578446,0.05676901,-0.20159347,0.00327325,0.00163878,-0.01917442,-0.02002184,0.03498811,-0.01982345,-0.04916047,-0.01146933,-0.08667339,0.02695168,-0.0069776,0.03417382,0.00804034,-0.00780705,0.05993129,0.02502034,-0.00509547,-0.03892584,0.0267853,-0.05288281,-0.01533116,-0.01693201,0.07214994,-0.03073324,0.0083568,0.03158828,0.02453525,0.09464497,0.07393584,0.0566325,-0.00720271,0.07419877,-0.18197271,0.07691533,0.05117598,0.00506802,-0.0379983,-0.02872123,0.00727812,0.0710192,0.01569497,0.0417197,0.02283519,0.0556983,0.01455232,-0.03742233,0.03737232,-0.05207978,-0.07477511,-0.0243222,-0.03890245,0.0409364,-0.0727922,0.01377973,-0.01964967,0.05789849,0.03004989,0.00311705,0.05210365,0.02967341,-0.02056049,-0.13516864,-0.00058541,0.02903689,-0.0415554,-0.0190868,0.0604329,0.03369744,-0.03905869,0.11688362,-0.03861447,0.04408129,0.02209133,-0.02415788,0.02810296,-0.00926925,-0.02106124,-0.04564814,-0.07019477,-0.01869862,0.03292003,-0.00337013,-0.10100596,-0.10897033,-0.03630111,0.00342457,0.0005528,0.01619303,0.0395624,0.05960473,0.01683015,0.05251858,0.05322506,-0.03264103,0.01165741,0.01181155,0.03151927,0.01834955,-0.0287191,0.08813656,-0.00738606,0.03671628,-0.00694415,-0.0287553,-0.08429883,0.00671615,0.01606127,-0.02795903,-0.00990234,-0.03539168,0.0206203,-0.00707858,0.01035328,-0.08250458,-0.03139558,0.01477111,-0.02507117,0.02144729,-0.02570598,-0.03207737,-0.01961908,0.01735707,-0.03966123,0.00608766,-0.02648538,-0.03193715,0.01381823,0.0302318,0.00878659,0.04889268,-0.00897494,-0.02255555,-0.00257872,0.08701733,0.0075898,-0.0866178,0.02380908,0.1036211,0.02448747,-0.08118449,-0.00978523,0.09740265,-0.00062049,0.03318491,0.04182969,0.00073167,-0.06051918,-0.05292996,0.05487743,0.07018054,0.02816808,-0.03366367,-0.01882656,-0.00340827,-0.03570411,-0.08800926,0.01477518,-0.01930305,-0.01916584,-0.02446037,-0.01680512,0.06872031,-0.01200329,0.03662282,-0.01052083,-0.06005079,-0.04184366,-0.02897311,-0.00279275,-0.01483645,0.10369257,-0.0042896,-0.07436448,0.05395425,-0.01820253,0.01827347,0.0270853,-0.00840726,0.03444649,0.01658859,-0.0611079,-0.03370734,0.05786602,0.01773072,-0.05540923,-0.0213517,0.00551451,0.06266574,0.04745814,0.07683767,-0.03690377,-0.00825873,-0.10275708,-0.21969718,-0.00481792,-0.01683751,0.00189621,0.00187236,0.00510159,0.00920151,-0.04858123,0.00040799,0.01435277,0.12110376,0.02935349,-0.05140137,-0.0272884,-0.06862868,-0.00022652,0.01234301,-0.00120691,-0.04458594,-0.00623937,0.05006021,-0.0182512,-0.02867282,-0.06565388,0.01377788,0.02192596,0.19355617,0.03682301,-0.00775795,0.00069254,0.05972435,0.0313967,0.04691606,-0.11338426,-0.00577033,0.05467959,0.01524049,0.06603883,0.00292079,-0.04046166,-0.03095598,0.02174199,0.01988685,-0.11327625,0.0031184,-0.01254035,-0.06304165,-0.06165642,0.01820716,0.02267972,-0.00968545,0.00104662,-0.00388607,0.04723919,-0.0245484,-0.02849692,-0.03112231,0.03203051,0.02720813,0.00542169,0.03151404,-0.04590181,-0.02146752,-0.04151249,0.07069889,0.06189474,0.01614819,-0.01485769,0.06064484,-0.0476527,-0.00899911,0.09573321,0.02737191,0.03503658,0.00808479,-0.02232677,-0.0428548,0.05319225,-0.02631879,0.03401085,-0.00752534,-0.03973637,0.08394232,-0.0340622,-0.01603507,0.04455282,-0.00362531,-0.03886102,0.00734505,-0.01565255,-0.02448571,0.01894584,-0.05241312,0.01060949,0.03601816,-0.00125789,-0.24106486,0.00622114,0.01805351,0.00109429,-0.07171112,0.02143518,0.03583214,-0.0406835,-0.00736341,-0.00860567,-0.06948096,0.05310082,0.02867734,-0.04165118,0.01233111,0.06812941,0.08016121,-0.03050516,0.05927258,-0.06768046,0.07288735,0.01186632,0.23978142,0.01386611,0.02545406,0.02765006,-0.00964859,0.00435854,0.05104882,0.07811403,-0.00944807,0.01915505,0.04708356,-0.01458549,-0.07548364,0.00141985,-0.0663425,-0.02060755,-0.0104426,-0.00297146,-0.0360513,0.03892101,-0.02666931,-0.0224205,0.09472645,-0.04905574,-0.06362722,-0.0899812,0.0066211,0.00240505,-0.06928556,-0.00194717,-0.02840162,0.02085928,-0.00347594,0.06929363,-0.02084378,0.03735061,-0.04624201,-0.00150622,-0.00227087,0.02880921,0.04973447,0.06860844,0.02160686],"last_embed":{"hash":"1irr8sf","tokens":461}}},"text":null,"length":0,"last_read":{"hash":"1irr8sf","at":1751288787876},"key":"Projects/Piecework/node_modules/electron-to-chromium/README.md###Made by [@kilianvalkhof](https://twitter.com/kilianvalkhof)","lines":[1,187],"size":6450,"outlinks":[{"title":"@kilianvalkhof","target":"https://twitter.com/kilianvalkhof","line":1},{"title":"Polypane","target":"https://polypane.app","line":5},{"title":"Superposition","target":"https://superposition.design","line":6},{"title":"FromScratch","target":"https://fromscratch.rocks","line":7},{"title":"![FOSSA Status","target":"https://app.fossa.io/api/projects/git%2Bgithub.com%2FKilian%2Felectron-to-chromium.svg?type=shield","line":11},{"title":"![codecov","target":"https://codecov.io/gh/Kilian/electron-to-chromium/branch/master/graph/badge.svg","line":11},{"title":"![npm-downloads","target":"https://img.shields.io/npm/dm/electron-to-chromium.svg","line":11},{"title":"![npm","target":"https://img.shields.io/npm/v/electron-to-chromium.svg","line":11},{"title":"![travis","target":"https://img.shields.io/travis/Kilian/electron-to-chromium/master.svg","line":11},{"title":"Browserslist","target":"https://github.com/ai/browserslist","line":15},{"title":"eslint-plugin-compat","target":"https://github.com/amilajack/eslint-plugin-compat","line":15},{"title":"babel-preset-env","target":"https://github.com/babel/babel-preset-env","line":15},{"title":"Autoprefixer","target":"https://github.com/postcss/autoprefixer","line":15},{"title":"Stylelint","target":"https://github.com/stylelint/stylelint","line":15},{"title":"Browserslist","target":"https://github.com/ai/browserslist","line":134},{"title":"![FOSSA Status","target":"https://app.fossa.io/api/projects/git%2Bgithub.com%2FKilian%2Felectron-to-chromium.svg?type=large","line":186}],"class_name":"SmartBlock","last_embed":{"hash":"1irr8sf","at":1751288787876}},
"smart_blocks:Projects/Piecework/node_modules/electron-to-chromium/README.md###Made by [@kilianvalkhof](https://twitter.com/kilianvalkhof)#Other projects:": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07609436,-0.00100151,0.00673098,-0.00680003,-0.05474862,-0.0014113,-0.08717439,0.01010991,-0.05956458,-0.02487515,-0.0177748,-0.06296269,0.06145302,0.04190499,0.07190026,0.01666837,0.03476358,0.02124548,-0.02401771,0.00910708,0.071238,-0.0251581,0.09061199,-0.06657872,0.0440807,0.06072604,-0.00949184,-0.02613582,0.05628339,-0.20332775,0.00448456,0.00009047,-0.01818018,-0.02000938,0.0307067,-0.01624673,-0.05149619,-0.0165787,-0.08506384,0.02628642,-0.00622197,0.03375927,0.00940502,-0.0048877,0.0562243,0.02188299,-0.00079729,-0.03594092,0.02426138,-0.05781936,-0.01515909,-0.01703632,0.07066797,-0.02959176,0.00579651,0.02864805,0.02869895,0.09493487,0.07821507,0.0550452,-0.00622046,0.07234356,-0.18596822,0.07996975,0.04591308,0.00475322,-0.03518965,-0.02961661,0.01050329,0.07052032,0.0154235,0.04069417,0.02229506,0.05119679,0.01355022,-0.03615944,0.04018405,-0.05579039,-0.07414158,-0.02334303,-0.03508767,0.03830715,-0.07606944,0.01488795,-0.02242956,0.05692678,0.02919744,0.00173594,0.05271783,0.03199918,-0.02056598,-0.13952154,0.00174046,0.02608546,-0.04290636,-0.01645106,0.05907252,0.0371216,-0.04142192,0.11653949,-0.03664377,0.04401174,0.01947673,-0.02532783,0.02798062,-0.01033614,-0.02433786,-0.0448748,-0.07169373,-0.0134111,0.03349523,-0.00838985,-0.10513224,-0.1040099,-0.03743201,0.00263207,-0.00225408,0.01725698,0.03969138,0.05878591,0.01342471,0.05270757,0.05775173,-0.03358854,0.01233759,0.01386632,0.02816907,0.01985229,-0.02860226,0.09100514,-0.00798781,0.03561391,-0.00952335,-0.02968617,-0.08327203,0.01108229,0.01555359,-0.03229968,-0.00936937,-0.03213781,0.01729769,-0.00633163,0.01105278,-0.08315957,-0.03291069,0.01366708,-0.02496837,0.01987027,-0.02722013,-0.03051852,-0.01714151,0.01789289,-0.04279085,0.00505513,-0.02564608,-0.02813142,0.0125554,0.03068427,0.00744768,0.04936106,-0.01475678,-0.02129822,0.00228199,0.08625958,-0.00014859,-0.08335908,0.02139539,0.10354141,0.02637801,-0.07795849,-0.00900537,0.09327798,0.00454452,0.03549316,0.04096413,0.00114057,-0.06012367,-0.04904951,0.0561632,0.07243357,0.0274746,-0.03336888,-0.01953602,-0.00261508,-0.03747796,-0.08948618,0.01609342,-0.0159998,-0.01529165,-0.02469243,-0.01134799,0.07153275,-0.01033741,0.03488808,-0.00922452,-0.06115215,-0.04336996,-0.02897613,-0.00167394,-0.01278061,0.09795388,-0.00305276,-0.0773045,0.05108707,-0.0189456,0.01459334,0.02715873,-0.00861948,0.03866009,0.01914789,-0.05550003,-0.03268179,0.0566402,0.02074069,-0.05512263,-0.01934956,0.00315797,0.06271626,0.04998024,0.07571108,-0.03531437,-0.00818103,-0.10010657,-0.21876223,-0.007515,-0.01645871,-0.00071221,-0.00008286,-0.00321344,0.00973385,-0.05107313,0.00127704,0.01516573,0.12182828,0.03284394,-0.04919642,-0.02407124,-0.06729148,-0.00180021,0.01399904,-0.00183826,-0.04534388,-0.01145531,0.04690084,-0.01898602,-0.02886379,-0.06659701,0.01365791,0.0229611,0.19088207,0.03174214,-0.00519431,0.0026179,0.06409722,0.02870307,0.05189808,-0.11790176,-0.00590886,0.05417363,0.02047168,0.06546336,0.00325996,-0.0415521,-0.02922506,0.02467926,0.01846469,-0.1136369,0.00091396,-0.01368406,-0.06096062,-0.06548245,0.0159352,0.02153066,-0.01173619,0.0024761,-0.00301445,0.04630784,-0.02209476,-0.02678672,-0.03418949,0.0308693,0.03065992,0.0054611,0.03194953,-0.04677532,-0.02364743,-0.03854892,0.06684583,0.05930194,0.01539586,-0.0105074,0.06100278,-0.04651495,-0.01252068,0.09659748,0.0244042,0.03920657,0.00912374,-0.02525475,-0.04235653,0.05106365,-0.02792478,0.0372346,-0.00324286,-0.03721504,0.08590961,-0.03168464,-0.01500361,0.0501606,-0.00724544,-0.03293239,0.0111899,-0.01427742,-0.02240889,0.01705807,-0.05157522,0.00961269,0.04136442,-0.00057652,-0.23866142,0.00682436,0.01325485,0.00033212,-0.07052301,0.0184051,0.0361022,-0.04141157,-0.0105988,-0.00598083,-0.07032573,0.05183028,0.02574213,-0.03929473,0.01384746,0.06533955,0.08047725,-0.02983736,0.06329993,-0.07186728,0.07305159,0.01044151,0.23914988,0.01263876,0.02489145,0.03078409,-0.00765945,0.00366273,0.04842375,0.07816,-0.00789181,0.0242046,0.04680488,-0.01309009,-0.07522878,0.00121644,-0.06991127,-0.02436341,-0.015534,-0.00108315,-0.03705145,0.03779167,-0.02689132,-0.02123851,0.09766291,-0.0494693,-0.06721328,-0.08848619,0.00462948,-0.00093249,-0.06640808,-0.00082672,-0.0296548,0.02444591,0.00404699,0.06994084,-0.01758415,0.03666805,-0.04759681,-0.00417528,-0.00353054,0.0304342,0.05131357,0.06729759,0.02128755],"last_embed":{"hash":"ic04ex","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"ic04ex","at":1751288788112},"key":"Projects/Piecework/node_modules/electron-to-chromium/README.md###Made by [@kilianvalkhof](https://twitter.com/kilianvalkhof)#Other projects:","lines":[3,187],"size":6385,"outlinks":[{"title":"Polypane","target":"https://polypane.app","line":3},{"title":"Superposition","target":"https://superposition.design","line":4},{"title":"FromScratch","target":"https://fromscratch.rocks","line":5},{"title":"![FOSSA Status","target":"https://app.fossa.io/api/projects/git%2Bgithub.com%2FKilian%2Felectron-to-chromium.svg?type=shield","line":9},{"title":"![codecov","target":"https://codecov.io/gh/Kilian/electron-to-chromium/branch/master/graph/badge.svg","line":9},{"title":"![npm-downloads","target":"https://img.shields.io/npm/dm/electron-to-chromium.svg","line":9},{"title":"![npm","target":"https://img.shields.io/npm/v/electron-to-chromium.svg","line":9},{"title":"![travis","target":"https://img.shields.io/travis/Kilian/electron-to-chromium/master.svg","line":9},{"title":"Browserslist","target":"https://github.com/ai/browserslist","line":13},{"title":"eslint-plugin-compat","target":"https://github.com/amilajack/eslint-plugin-compat","line":13},{"title":"babel-preset-env","target":"https://github.com/babel/babel-preset-env","line":13},{"title":"Autoprefixer","target":"https://github.com/postcss/autoprefixer","line":13},{"title":"Stylelint","target":"https://github.com/stylelint/stylelint","line":13},{"title":"Browserslist","target":"https://github.com/ai/browserslist","line":132},{"title":"![FOSSA Status","target":"https://app.fossa.io/api/projects/git%2Bgithub.com%2FKilian%2Felectron-to-chromium.svg?type=large","line":184}],"class_name":"SmartBlock","last_embed":{"hash":"ic04ex","at":1751288788112}},
"smart_blocks:Projects/Piecework/node_modules/electron-to-chromium/README.md###Made by [@kilianvalkhof](https://twitter.com/kilianvalkhof)#Other projects:#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07054857,0.00839918,0.01248768,-0.00048299,-0.02126561,-0.03718594,-0.08306412,0.00852373,-0.08188608,-0.04996537,0.01155913,-0.07662606,0.02464929,0.04962245,0.0717028,0.0148715,0.00055656,0.06893026,0.00500654,0.01564272,0.0278664,-0.01387562,0.08390375,-0.03986619,0.04487948,0.08960739,-0.00633047,-0.0323279,0.04150946,-0.20055757,-0.00231265,-0.03489275,-0.01937011,-0.02934832,0.03650215,-0.02274858,-0.04156078,-0.02804763,-0.10454702,0.0237747,0.04011878,0.00673539,-0.01303327,-0.02090432,0.05093345,0.01233689,0.00273672,-0.04455905,0.03752496,-0.07268407,-0.00234057,0.00090963,0.0662671,-0.01920265,-0.01447769,0.03688434,0.03286876,0.06192373,0.08749949,0.04305239,-0.01166509,0.07701567,-0.20171539,0.06368576,0.03778877,0.00879551,-0.03276179,-0.01818234,0.01405725,0.05216676,0.01703689,0.03951892,-0.00902417,0.03586273,0.02400595,-0.04325395,0.02721952,-0.03098107,-0.067068,-0.02513669,-0.08424307,0.04895334,-0.06767999,0.04549072,0.00177191,0.04759021,0.03685224,-0.01628321,0.05291111,0.01611074,-0.0192254,-0.126697,-0.00167714,0.06481425,-0.03806495,-0.0359845,0.04514901,0.04776568,-0.06527151,0.12190385,-0.03796071,0.03604234,-0.00669837,-0.03594075,0.0243037,0.00304803,-0.00880155,-0.05001987,-0.06974793,-0.0191232,0.0712117,-0.02701988,-0.09704828,-0.12178195,-0.0379658,0.00147977,0.01438296,0.0147459,0.03061144,0.03897663,0.00110889,0.06230838,0.03989362,-0.01465429,-0.00347972,0.02619761,0.04354471,0.03624908,0.03262334,0.06725073,-0.02386184,0.03633395,-0.03572641,-0.03838517,-0.08792391,0.04572962,0.01106645,-0.02417908,-0.03711108,-0.04301872,0.01548219,-0.02253254,0.0051984,-0.07096205,-0.0606593,-0.03045163,-0.05314084,-0.00466413,-0.0330609,-0.02081898,0.00478648,0.02271277,-0.04246452,0.0285088,-0.00782491,-0.05018798,-0.00209002,0.06964745,-0.02736887,0.05977781,0.00686604,-0.0131453,0.02151999,0.09954716,0.01513686,-0.06443549,0.00454619,0.07921434,0.0269639,-0.03488206,-0.00139338,0.09712975,0.00073596,0.05125507,0.04108549,0.01050608,-0.05174348,-0.05294347,0.00839198,0.06768499,0.01596654,-0.04730547,-0.02164828,0.02355752,-0.02412117,-0.07696973,0.01612352,-0.01796784,-0.03472026,0.00704783,0.0050177,0.07619619,0.00433851,0.01705733,-0.00605901,-0.06119071,-0.06120288,-0.02192442,0.01874074,-0.01012748,0.04355792,-0.00053845,-0.05964705,0.03496032,-0.05324004,0.02585479,-0.000676,-0.01252737,0.04138225,0.0120521,-0.05085592,-0.03480608,0.06855463,0.06178607,-0.06169739,-0.0348061,0.00362447,-0.00336252,0.0346055,0.07804629,-0.05725479,-0.00493221,-0.09574296,-0.20902273,0.01192834,-0.01150211,-0.02951919,-0.02475889,-0.00484904,0.01930584,-0.06779387,-0.01841304,0.00775937,0.15039936,0.01010942,-0.05849647,-0.03984758,-0.03300007,0.01734277,0.00511927,-0.01638028,-0.01912144,-0.01045099,0.05204583,-0.00782936,-0.02320118,-0.01472508,0.00438229,0.00925949,0.18945779,0.0401022,0.02259535,0.03976688,0.02727624,0.02723779,0.04955076,-0.09006287,-0.02478083,0.04628731,0.0261766,0.08977304,0.01793132,-0.03132722,-0.02242918,0.00269871,-0.00498461,-0.10585412,0.04341371,-0.03344268,-0.04442029,-0.05534561,0.00023548,0.02889691,0.00195123,0.01603896,-0.00177223,0.03426759,-0.03100534,-0.02755033,-0.03657411,-0.00171825,0.0425873,-0.01086743,0.01513318,-0.04561485,-0.04274696,-0.09099221,0.05481685,0.03892785,0.02271267,-0.02570237,0.0373109,-0.05270487,-0.02331308,0.09145419,0.04209052,-0.00381055,-0.01731243,0.00004202,-0.01883883,0.05246167,-0.00750878,0.03277715,0.00946355,-0.01932795,0.07619256,-0.02990324,-0.02824138,0.05630759,-0.01312797,-0.03103231,0.03717488,0.02835332,-0.0165215,-0.02277494,-0.00766435,-0.00949489,0.08656941,-0.0064594,-0.22943558,0.00858647,0.03488903,-0.01485876,-0.05040727,-0.00382631,0.03595697,-0.02353651,-0.03762656,-0.00301306,-0.03021457,0.05473608,0.04356623,-0.04242687,0.015087,0.04768096,0.08011714,-0.04128086,0.08030951,-0.03630453,0.07925823,0.02699135,0.21883044,-0.0086307,0.01931067,0.03150712,-0.01263941,0.00419694,0.06429585,0.05094708,-0.02488172,0.00641056,0.04647203,0.03100963,-0.05500668,0.04327838,-0.03835151,-0.02701297,-0.00471175,-0.00632359,-0.0144105,0.06487309,-0.04479592,-0.02723092,0.11317845,-0.09200007,-0.08590858,-0.0981946,0.02434693,-0.01273715,-0.04495815,0.01951883,-0.0394364,0.01195684,0.00225213,0.06802551,-0.00263797,0.05749487,-0.06193444,-0.00592043,0.00832074,0.0262406,0.03744217,0.0827525,0.04592666],"last_embed":{"hash":"7buiga","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"7buiga","at":1751288788300},"key":"Projects/Piecework/node_modules/electron-to-chromium/README.md###Made by [@kilianvalkhof](https://twitter.com/kilianvalkhof)#Other projects:#{3}","lines":[7,187],"size":6115,"outlinks":[{"title":"FromScratch","target":"https://fromscratch.rocks","line":1},{"title":"![FOSSA Status","target":"https://app.fossa.io/api/projects/git%2Bgithub.com%2FKilian%2Felectron-to-chromium.svg?type=shield","line":5},{"title":"![codecov","target":"https://codecov.io/gh/Kilian/electron-to-chromium/branch/master/graph/badge.svg","line":5},{"title":"![npm-downloads","target":"https://img.shields.io/npm/dm/electron-to-chromium.svg","line":5},{"title":"![npm","target":"https://img.shields.io/npm/v/electron-to-chromium.svg","line":5},{"title":"![travis","target":"https://img.shields.io/travis/Kilian/electron-to-chromium/master.svg","line":5},{"title":"Browserslist","target":"https://github.com/ai/browserslist","line":9},{"title":"eslint-plugin-compat","target":"https://github.com/amilajack/eslint-plugin-compat","line":9},{"title":"babel-preset-env","target":"https://github.com/babel/babel-preset-env","line":9},{"title":"Autoprefixer","target":"https://github.com/postcss/autoprefixer","line":9},{"title":"Stylelint","target":"https://github.com/stylelint/stylelint","line":9},{"title":"Browserslist","target":"https://github.com/ai/browserslist","line":128},{"title":"![FOSSA Status","target":"https://app.fossa.io/api/projects/git%2Bgithub.com%2FKilian%2Felectron-to-chromium.svg?type=large","line":180}],"class_name":"SmartBlock","last_embed":{"hash":"7buiga","at":1751288788300}},
