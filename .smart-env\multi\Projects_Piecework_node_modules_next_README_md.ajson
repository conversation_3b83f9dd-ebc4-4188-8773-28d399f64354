
"smart_sources:Projects/Piecework/node_modules/next/README.md": {"path":"Projects/Piecework/node_modules/next/README.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02472473,-0.01000505,0.00248574,-0.06868054,0.05221679,0.01869184,-0.12537901,-0.00394087,0.04495022,-0.01166802,0.02140922,-0.04569954,-0.02706541,0.05140817,0.01005479,0.02483542,0.04328481,0.04644286,-0.05296347,0.00250549,0.02606551,-0.00362296,0.09139723,-0.07562597,0.01082059,0.05456719,-0.01944263,-0.0304388,-0.00179872,-0.20165204,-0.03593667,-0.04571372,0.01164758,0.01215831,0.04744187,-0.03473648,-0.03549041,0.02383997,-0.02730235,0.05125372,0.04117058,0.02528838,-0.09613623,-0.01120725,-0.00122502,-0.05129385,0.03043018,-0.06928751,-0.03860938,-0.09145825,-0.0085914,-0.0945169,0.01742022,0.01407862,0.01746225,0.07780414,0.0304313,0.03960479,0.03616615,0.05036399,0.02054365,0.04693235,-0.19626012,0.06852606,0.07705992,-0.01184347,-0.05894193,-0.02990189,0.01348588,0.02795751,0.02800258,0.03148534,0.02084987,0.05166599,-0.0211905,-0.05419251,0.02959757,-0.05503569,-0.04243353,-0.05341925,-0.08175785,0.03555378,0.00459859,-0.00974074,-0.01761803,0.05845664,-0.02256197,-0.03583524,0.05742959,0.04882589,0.00590602,-0.10183977,0.01158928,0.05216894,-0.02461552,-0.05665432,0.02961598,0.00906272,-0.03302965,0.13082941,-0.05781718,0.01917164,0.05325541,-0.00731027,0.05073258,0.0333943,-0.00442528,-0.07311977,-0.04862726,-0.01400679,0.00342994,-0.01513669,-0.04803064,-0.08690345,-0.01558014,0.02324768,-0.02134641,0.05020077,-0.02513175,0.0611647,0.05269778,0.08050457,0.00981013,-0.05974657,0.01607416,0.04376399,0.05161331,0.03906001,0.0268317,0.10444835,0.01319514,0.07046332,-0.01390528,-0.02133395,-0.00093396,0.01368345,-0.03246969,-0.02355817,-0.01186988,-0.04213917,0.01872202,0.00111313,0.04397532,-0.05209252,-0.01563208,0.01345743,-0.04584951,0.05547919,-0.01786191,-0.02541637,0.00649447,0.03662997,-0.05284157,0.0345367,-0.02478226,-0.07961716,0.00210945,0.04686293,-0.03193448,0.05694947,0.04503706,-0.05101826,0.00991431,0.07458701,0.01678368,-0.09201227,-0.02178113,0.03164202,0.02314695,-0.0957818,-0.00710511,0.05758714,0.01937882,-0.03173605,0.08596189,0.02328085,-0.0701572,-0.05683904,-0.00559083,0.00003343,0.02956643,-0.06533987,-0.05083976,0.03888309,0.00491024,-0.00074895,0.06010745,-0.0573878,-0.00464085,0.01055056,0.02387685,0.03574901,-0.02271318,0.04299805,-0.03590418,-0.05417318,-0.04860021,-0.0340597,-0.00092661,-0.03110152,0.08620043,0.01345024,-0.01566972,0.03247015,-0.02967831,0.06531715,0.02315029,-0.03969187,0.02281667,0.00158192,-0.11990675,-0.04458305,0.08981857,0.06596203,0.00654556,-0.03680766,-0.00311465,0.05694305,0.01878258,0.05662703,-0.06194136,0.01414458,-0.11152466,-0.20855373,0.03499652,0.00278939,-0.01998864,-0.0287233,0.01846072,-0.00383446,-0.01149017,-0.01301457,0.04491799,0.15988401,0.00210791,0.018677,-0.04116175,-0.0216556,0.04295391,0.03108863,-0.01416148,0.00782834,-0.00591419,0.02040858,0.00228551,-0.02680346,-0.06135335,0.04175707,-0.01257839,0.17053467,0.06168573,-0.01782386,-0.01211442,0.03513177,0.00026836,-0.00455094,-0.07310937,0.03284278,0.02913798,0.01045272,0.04652709,0.00214393,0.00651843,-0.005749,0.02821556,-0.00185613,-0.075725,0.02086062,-0.0219314,-0.04354245,-0.00936136,0.00094511,0.03093047,0.02059325,0.02851107,0.01803613,0.07878683,-0.02191159,-0.00555068,-0.05533272,-0.06010609,0.01173467,0.06058842,0.00344631,-0.03336367,-0.01085273,-0.08746511,0.09807759,0.07244598,-0.02924372,-0.01582,0.06182867,-0.03712851,0.00490309,0.05419939,0.03483481,-0.01036856,0.07052726,0.05549274,-0.03929715,0.0927527,0.04794301,0.04883633,0.00758969,-0.04203228,-0.00848098,0.00055842,-0.00275244,-0.00838512,-0.02658388,-0.01802755,0.03580426,-0.0344232,-0.04129628,0.01838855,0.0027726,0.04104142,0.04337902,-0.00414421,-0.23328733,0.00407668,0.01389346,-0.00524299,-0.05047909,0.02722484,0.01872836,-0.0364255,-0.04821703,-0.02008365,-0.03822146,0.02011891,0.03417996,-0.03817959,-0.05656582,-0.01922159,0.04948405,-0.05281826,0.05051788,-0.0590623,-0.00942953,-0.00202991,0.23521625,-0.02896135,-0.03581541,0.05980026,-0.01476225,0.00936727,0.00599839,0.10237364,0.00993937,0.06878191,0.12380945,-0.00902268,-0.07406785,-0.02954306,-0.02100518,-0.01733013,0.02310921,-0.03804578,0.025846,0.02500068,-0.04900395,-0.01145703,0.11272819,-0.07240611,-0.04480889,-0.04337054,0.06655212,-0.01796283,-0.02551174,0.01280095,-0.01462345,-0.03688166,-0.02119118,0.02477017,-0.00253196,0.02157469,-0.03078555,-0.01996755,-0.01250983,-0.02486029,0.0272942,0.00519648,0.00329667],"last_embed":{"hash":"17ocy9z","tokens":469}}},"last_read":{"hash":"17ocy9z","at":1751288795223},"class_name":"SmartSource","last_import":{"mtime":1751244735665,"size":3337,"at":1751288765552,"hash":"17ocy9z"},"blocks":{"#":[1,16],"##Getting Started":[17,23],"##Getting Started#{1}":[19,20],"##Getting Started#{2}":[21,21],"##Getting Started#{3}":[22,23],"##Documentation":[24,27],"##Documentation#{1}":[26,27],"##Community":[28,35],"##Community#{1}":[30,35],"##Contributing":[36,51],"##Contributing#{1}":[38,39],"##Contributing#Good First Issues:":[40,51],"##Contributing#Good First Issues:#{1}":[42,51],"#---frontmatter---":[44,null]},"outlinks":[{"title":"Learn Next.js","target":"https://nextjs.org/learn","line":21},{"title":"Next.js Showcase","target":"https://nextjs.org/showcase","line":22},{"title":"https://nextjs.org/docs","target":"https://nextjs.org/docs","line":26},{"title":"GitHub Discussions","target":"https://github.com/vercel/next.js/discussions","line":30},{"title":"Discord","target":"https://nextjs.org/discord","line":32},{"title":"Code of Conduct","target":"https://github.com/vercel/next.js/blob/canary/CODE_OF_CONDUCT.md","line":34},{"title":"Contribution Guidelines","target":"/contributing.md","line":38},{"title":"good first issues","target":"https://github.com/vercel/next.js/labels/good%20first%20issue","line":42},{"title":"https://github.com/vercel/next.js/security","target":"https://github.com/vercel/next.js/security","line":50}],"last_embed":{"hash":"17ocy9z","at":1751288793808}},"smart_blocks:Projects/Piecework/node_modules/next/README.md#": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02497613,-0.00966437,0.00546204,-0.06919952,0.05262524,0.01820184,-0.12584804,-0.00356316,0.04483698,-0.01342206,0.02397908,-0.04626874,-0.02903699,0.05224007,0.01102921,0.02420344,0.0447666,0.04042182,-0.05148628,0.00269434,0.02799837,0.00116963,0.09354575,-0.07081447,0.00738528,0.052705,-0.01767215,-0.03084838,-0.00733426,-0.20192496,-0.03729701,-0.04643776,0.0082622,0.01163051,0.04332218,-0.03295572,-0.03355844,0.02350177,-0.02615621,0.05362049,0.03870299,0.0273481,-0.09774874,-0.01170445,-0.00147639,-0.0533119,0.03148556,-0.06976539,-0.03870867,-0.09540462,-0.01322563,-0.09323231,0.0193056,0.01082037,0.01948942,0.07824618,0.02814124,0.03849432,0.03370908,0.04603365,0.02174057,0.0468509,-0.19634201,0.06732393,0.07444817,-0.01120272,-0.0606981,-0.03115698,0.01135825,0.02255312,0.03104476,0.03243562,0.02029781,0.0541114,-0.01961212,-0.05549946,0.02755527,-0.05354147,-0.04367485,-0.05184046,-0.08479432,0.03522696,0.00852544,-0.01350812,-0.01906597,0.05879224,-0.02213042,-0.0364507,0.0602561,0.04579352,0.0033233,-0.10089392,0.010624,0.05626346,-0.022888,-0.05630622,0.03210369,0.00951545,-0.0273401,0.12896422,-0.05793457,0.02274357,0.05050143,-0.0088322,0.0511466,0.03328237,-0.00430253,-0.07408131,-0.04848908,-0.0192208,0.00190743,-0.01953506,-0.04529369,-0.0812948,-0.0110533,0.02710837,-0.01736391,0.05130475,-0.02175662,0.06658673,0.05459299,0.07979003,0.00559397,-0.06194315,0.01747739,0.042355,0.05391447,0.04040629,0.02491136,0.10450286,0.01512866,0.07185563,-0.01902531,-0.0221221,-0.00130699,0.01641453,-0.03425363,-0.03346519,-0.01166192,-0.04935447,0.0154075,0.00241633,0.040532,-0.05307619,-0.01527423,0.01450468,-0.04419114,0.05377644,-0.02062112,-0.0273517,0.00930967,0.03628275,-0.0508276,0.03680446,-0.02590652,-0.08041008,0.00761574,0.04506685,-0.03133344,0.05750142,0.04451483,-0.04978564,0.00968741,0.07592726,0.01364834,-0.08509833,-0.02465709,0.03153649,0.02283416,-0.09800281,-0.00177236,0.05665785,0.01945524,-0.0324292,0.08454274,0.02409799,-0.07019617,-0.05960185,-0.00727586,-0.00160635,0.0257862,-0.06375185,-0.05033256,0.04105671,0.00387811,0.00116293,0.06216951,-0.06089452,-0.00349356,0.01223057,0.02806004,0.03380264,-0.02181503,0.04158619,-0.03491527,-0.05260155,-0.04849321,-0.03156652,-0.0005183,-0.03786547,0.08877151,0.0153394,-0.01923023,0.03326796,-0.03262674,0.0648187,0.02226047,-0.04047448,0.0247589,-0.00048629,-0.11688769,-0.04641749,0.08798823,0.06386099,0.00430785,-0.03570241,-0.00337353,0.05740302,0.01919987,0.05556461,-0.05863529,0.01143805,-0.11211252,-0.21148193,0.03462369,0.00333475,-0.02035799,-0.02909359,0.01956489,-0.00213708,-0.00944387,-0.00956476,0.0450968,0.15644544,0.00313648,0.01725247,-0.03589259,-0.01865796,0.04651013,0.02886236,-0.00806391,0.00404097,-0.00490619,0.01955973,0.00384525,-0.0293794,-0.06002795,0.04621669,-0.01312884,0.16793433,0.06261462,-0.01748656,-0.01456073,0.03871726,0.0017372,-0.00175611,-0.07280577,0.03014216,0.03052546,0.01182741,0.051314,0.00507346,0.00628604,-0.00176972,0.02849311,0.00111056,-0.07797691,0.01778523,-0.02188074,-0.04323422,-0.01114445,0.00103535,0.03491619,0.02025511,0.02508388,0.01871993,0.08162193,-0.02045417,-0.00402862,-0.05493245,-0.05823508,0.01138797,0.05507975,0.00244853,-0.03008802,-0.01087223,-0.08799896,0.09861565,0.07167175,-0.02946152,-0.01608104,0.06269804,-0.03869927,0.00256582,0.0526344,0.03463701,-0.00708243,0.0664272,0.05447189,-0.03781297,0.08873828,0.04594674,0.05121394,0.01084131,-0.03830713,-0.00850099,0.00115712,-0.00295795,-0.00751534,-0.0302684,-0.01525461,0.032603,-0.03364488,-0.0407907,0.01893769,-0.00117974,0.03839868,0.04041691,-0.00541442,-0.23259759,0.00616393,0.01408569,-0.00382139,-0.05169747,0.0241346,0.01627682,-0.04071908,-0.04738819,-0.01786513,-0.03605498,0.02194756,0.02736977,-0.04088383,-0.05676171,-0.01906359,0.04981967,-0.05215851,0.04708641,-0.06134035,-0.00843493,0.00004172,0.23777562,-0.02431941,-0.03829946,0.06397232,-0.01562267,0.0112609,0.01040311,0.10454123,0.01121337,0.06824877,0.12977453,-0.00992699,-0.0752792,-0.02836785,-0.0182694,-0.01376798,0.02717322,-0.03936498,0.03024952,0.02719528,-0.05217725,-0.01085274,0.1098142,-0.07283407,-0.04722009,-0.04384004,0.06500925,-0.01528479,-0.02450928,0.00923485,-0.01037633,-0.03444698,-0.01957326,0.02299494,-0.0034701,0.01809157,-0.02692582,-0.01896646,-0.00994695,-0.02786664,0.0271047,0.00374326,0.00022956],"last_embed":{"hash":"xupw12","tokens":465}}},"text":null,"length":0,"last_read":{"hash":"xupw12","at":1751288793968},"key":"Projects/Piecework/node_modules/next/README.md#","lines":[1,16],"size":1130,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"xupw12","at":1751288793968}},
"smart_blocks:Projects/Piecework/node_modules/next/README.md##Getting Started": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06470878,-0.0493529,-0.00514199,-0.05756161,0.00678512,0.0396807,-0.0984178,0.02220503,0.01938105,-0.02682834,0.0537612,-0.01869778,-0.01725535,0.04122064,0.04428305,0.05167861,-0.02818378,-0.00693497,-0.02756113,-0.02019882,0.01545782,0.0230015,0.03089391,-0.05632092,0.01256367,0.06880718,-0.04523237,-0.00513016,0.00239108,-0.1557257,0.00359533,-0.05248356,-0.02245279,0.00674291,-0.02790214,-0.01557454,0.01218222,0.00579948,-0.01210008,0.07515801,-0.02356993,0.06260908,-0.02725712,-0.03731734,-0.02812858,-0.01602107,0.02565626,-0.03495195,0.01900119,-0.04185726,-0.01999712,-0.08368575,0.03085883,-0.05548495,0.03960516,0.04928137,0.01855879,0.07692426,0.01445219,0.04523638,0.04962036,-0.00544841,-0.13673554,0.06602419,0.07260677,0.08314588,-0.03552484,-0.02535935,0.0288592,0.05342278,-0.00026411,0.01512049,-0.00561545,0.09954821,0.01058469,-0.04753969,-0.00370059,-0.01887293,0.02046191,-0.04420495,-0.06665656,-0.03069473,0.00569869,0.00529887,-0.0558161,0.03236017,-0.00780738,-0.02411268,0.0790632,0.04439967,-0.02906348,-0.06015081,0.02178101,0.06612746,-0.0242673,0.01959422,0.05321949,0.02294455,0.00803407,0.12148176,-0.07760385,0.047826,0.03373785,-0.03516134,0.02252529,0.03458924,-0.01549726,-0.02146812,-0.03026336,-0.02323752,-0.01422847,-0.05682436,-0.04851804,-0.07082621,0.02863752,-0.06963007,-0.02595842,0.02112927,-0.00689167,0.04308372,0.0610454,0.08146306,0.04726562,-0.01024037,-0.03004864,0.04462414,0.03210749,0.01352559,-0.00609155,0.09613763,0.03187373,0.05261526,-0.0406194,0.0148414,0.00384124,-0.00498744,-0.03383736,-0.02356742,0.03625084,0.0533694,0.03125839,-0.00305827,0.03394133,-0.120804,0.01137161,0.04236905,-0.01549633,0.07471166,-0.07307148,-0.06561954,-0.01490978,0.02955138,-0.08129551,-0.02745727,-0.06024332,-0.06731711,0.03687482,-0.02858837,0.01403134,0.02254246,-0.06521939,0.02437372,-0.01108795,0.04751205,0.01322523,-0.14003332,-0.0222807,0.07439151,0.02463042,-0.07086824,-0.04360763,0.01204005,-0.03664797,-0.05938425,0.10879431,0.00706166,-0.08450378,-0.0457882,0.00694187,0.03355775,0.03968202,-0.02181968,-0.06729092,0.03875896,-0.02753213,-0.03808948,0.0631727,-0.05874264,0.01384625,0.02492868,-0.02563861,0.04565689,-0.03573253,0.01551535,0.02114007,-0.00667174,-0.0653192,0.01022033,0.03252989,-0.01864819,0.0548242,0.04933025,-0.0323519,0.04567834,-0.07935029,0.02679222,-0.00015875,-0.02772812,0.09543302,0.01442439,-0.12661348,-0.00266077,0.0835978,0.01884614,-0.01884173,-0.0448334,0.00085775,0.06759378,-0.02156967,0.08652197,-0.00143196,-0.00013992,-0.07574723,-0.21242231,0.0237455,0.04284672,-0.00730683,-0.01214666,-0.04042147,0.03460563,-0.0337472,-0.04959239,0.07064543,0.15222348,0.01763989,0.02050335,-0.02474129,-0.01173182,-0.01318588,0.01591418,-0.01082023,-0.02242772,0.01998477,-0.00149278,-0.04075903,0.02447416,-0.11484768,0.04743615,-0.0162776,0.13341404,0.01949301,-0.00023188,-0.1019538,0.06429904,-0.0238274,-0.02179768,-0.08996689,-0.02314156,0.0185143,0.05118793,0.00202373,-0.00056486,-0.00991804,-0.02213292,-0.0634958,-0.00359558,-0.10171106,-0.0046142,-0.0183723,-0.06768314,-0.05307105,0.04442471,0.03047034,-0.00368065,0.04018577,0.0368202,0.07986175,-0.04677778,-0.00327414,-0.00800561,-0.02518218,0.00567902,0.05422391,0.01017907,0.00406089,-0.07053142,-0.02722339,0.05577522,0.01703867,-0.00281256,0.00778274,0.04188016,-0.06835253,-0.05115619,0.08972581,0.01605048,0.02817928,-0.02478836,-0.01137336,-0.02582089,0.02826745,0.04167408,0.02091072,0.03604484,-0.05933307,-0.00127613,0.01661401,-0.01071502,0.04170131,0.01361985,-0.04280692,-0.02668471,-0.02165994,-0.07118416,0.03492839,-0.06393423,-0.0269597,0.04411199,-0.02254745,-0.21743433,0.02074553,0.02146866,-0.00336881,-0.02558427,-0.02024511,0.07452177,-0.03986519,0.01194378,0.05610602,0.00183713,0.05010823,0.02066529,0.01968102,0.00979894,0.06759527,0.09638325,0.03915624,0.03571834,-0.08259853,0.05515692,-0.01345811,0.24509944,0.01482077,0.03813777,0.1148025,-0.03088583,0.03638055,0.01847293,0.05075546,0.01584532,0.02882563,0.06785694,-0.04284304,-0.06241859,0.01107154,0.01574507,0.02801981,0.00992179,-0.02679764,-0.01491517,-0.02569674,-0.05009181,0.05685148,0.07881596,-0.06460829,-0.05594686,-0.14096871,0.00816365,0.00564416,-0.04421603,-0.01586611,0.02876836,-0.05990916,0.00686121,0.01816287,0.02959667,0.02705072,-0.0303094,-0.00263186,0.03710655,0.01583504,0.03860185,0.0673174,0.01213402],"last_embed":{"hash":"1kwjodl","tokens":131}}},"text":null,"length":0,"last_read":{"hash":"1kwjodl","at":1751288794314},"key":"Projects/Piecework/node_modules/next/README.md##Getting Started","lines":[17,23],"size":433,"outlinks":[{"title":"Learn Next.js","target":"https://nextjs.org/learn","line":5},{"title":"Next.js Showcase","target":"https://nextjs.org/showcase","line":6}],"class_name":"SmartBlock","last_embed":{"hash":"1kwjodl","at":1751288794314}},
"smart_blocks:Projects/Piecework/node_modules/next/README.md##Getting Started#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05509068,-0.05948605,0.0074426,-0.0564631,-0.00197237,0.03299963,-0.07238465,0.01379958,0.02023895,-0.01528211,0.04288986,-0.00294551,0.00152656,0.03724265,0.03622888,0.03463507,-0.01524932,0.00921791,-0.01937938,-0.01591899,0.02625853,0.02538598,0.01452775,-0.04700641,0.02023053,0.06446523,-0.04954681,-0.00369162,0.00943618,-0.13627738,0.00315198,-0.03161022,-0.0324466,0.01700238,-0.03067196,-0.02027507,0.02067797,-0.01158067,-0.01194308,0.07092902,-0.0295616,0.07012669,-0.02679131,-0.03091388,-0.02785119,-0.00700545,0.02104641,-0.03051764,0.00832904,-0.02363598,-0.00290184,-0.06180796,0.03385781,-0.03042107,0.03269558,0.05730165,0.01361251,0.07260266,0.00586365,0.04301521,0.04328528,0.00831698,-0.11850721,0.05543846,0.08059782,0.08905289,-0.02812183,-0.03339465,0.0142263,0.04529713,-0.00381117,0.01330844,-0.0026755,0.10926643,0.01204081,-0.04262458,0.00125843,-0.03170079,0.0134952,-0.0380061,-0.06775951,-0.04517904,0.00711919,-0.00102556,-0.05309165,0.0258096,0.00468084,-0.02533459,0.08820365,0.0387007,-0.04097513,-0.06176875,0.02777566,0.06807648,-0.01816433,0.00842527,0.05056994,0.03696414,0.0062164,0.14137934,-0.07770924,0.03103573,0.02858561,-0.05653633,0.02337429,0.0409321,-0.01976914,-0.01671235,-0.03708009,-0.01148496,-0.01607089,-0.05247719,-0.05129033,-0.05646829,0.01887198,-0.08765125,-0.03553503,0.00971917,-0.00684348,0.05311367,0.05045407,0.09534673,0.05239693,-0.00043746,-0.033254,0.03962851,0.03722189,0.02375076,-0.02638397,0.11374873,0.02416607,0.05520608,-0.04797469,0.01382391,0.00979272,-0.00088329,-0.03261076,-0.03225384,0.03844014,0.07123458,0.01636751,0.00006748,0.04946174,-0.11267018,0.02234888,0.05061823,-0.01861978,0.07634972,-0.07722545,-0.06106523,-0.01847917,0.03333436,-0.07897614,-0.02534227,-0.05628752,-0.05941024,0.02457525,-0.04904341,-0.0018279,0.00546605,-0.0496189,0.02456962,-0.02809466,0.04106931,0.01214871,-0.14894609,-0.0237307,0.08747189,0.01355603,-0.05619381,-0.03985541,0.01241262,-0.02597798,-0.05447175,0.08669233,0.00485172,-0.08328626,-0.03863915,0.00617202,0.02806666,0.05837083,-0.02568456,-0.05872364,0.02384359,-0.02823451,-0.0458646,0.05056594,-0.05615364,0.01706447,0.01951442,-0.00240027,0.05713787,-0.02199582,0.00346568,0.02875721,0.01080788,-0.0763105,0.01372966,0.02708998,-0.01614613,0.04967838,0.05792327,-0.03525782,0.04956358,-0.06083908,0.02195709,-0.02400541,-0.0185726,0.0839538,0.01894896,-0.107953,-0.00715179,0.09235177,0.02840893,-0.03618523,-0.04751547,0.00448053,0.07307187,-0.01527961,0.09290255,0.00197369,-0.01062185,-0.07915781,-0.20967315,0.01409233,0.04090165,-0.02067075,-0.00995468,-0.02930232,0.0302119,-0.04617086,-0.05975742,0.06695134,0.1328456,0.01917168,0.01654428,-0.02929353,-0.0184853,-0.03666791,-0.00734832,-0.01643792,-0.04748193,0.02104832,0.01002042,-0.04875132,0.0060742,-0.10469982,0.03752933,-0.01075028,0.14388908,0.01449094,-0.00774261,-0.09042212,0.04754727,-0.01765962,-0.02140884,-0.10794958,-0.0258868,0.02247371,0.04658389,-0.00857871,0.00626417,-0.01017327,-0.02847164,-0.07748937,-0.00070168,-0.09943222,0.01764722,-0.02175907,-0.04698625,-0.07553451,0.05141335,0.02867915,-0.00704534,0.04418765,0.03778259,0.09823386,-0.04308218,-0.00146042,0.00932778,-0.02111318,0.01827413,0.03732607,0.00783101,0.00636511,-0.06133863,-0.00763514,0.03783549,0.0078499,0.01534222,0.00605286,0.02499213,-0.07715992,-0.06267475,0.12163665,0.00793839,0.02851707,-0.03214208,-0.01221085,-0.01754585,0.02093039,0.05600673,-0.00637512,0.01601789,-0.05994604,-0.00032681,0.02640814,-0.00874449,0.03235505,0.01579688,-0.04083155,-0.03444688,-0.02741407,-0.06983346,0.0275354,-0.06443724,-0.03468137,0.04872235,-0.03558036,-0.2150646,0.02989629,0.01807749,-0.00621192,-0.03325938,-0.0282598,0.05794888,-0.02279106,0.01116989,0.05692377,0.00729559,0.03910497,0.00771073,0.01162753,0.01296556,0.07625569,0.09903722,0.03797344,0.05126593,-0.07860514,0.05134784,-0.00104211,0.25826028,0.00638152,0.03897586,0.11354163,-0.02447014,0.02575197,0.01760284,0.06266422,0.01384578,0.01764833,0.0645365,-0.05104159,-0.06276681,0.01740647,0.0280888,0.02790376,0.01850055,-0.00721363,-0.01634434,-0.01613243,-0.05058175,0.05397218,0.06900752,-0.07099549,-0.06748625,-0.16070472,0.01679475,0.01673235,-0.0584938,-0.00432863,0.02510212,-0.04375544,0.00170683,0.00931596,0.01608054,0.01914842,-0.04232595,-0.00541342,0.03759573,0.03530725,0.0366331,0.08448933,0.00416382],"last_embed":{"hash":"a2d81u","tokens":65}}},"text":null,"length":0,"last_read":{"hash":"a2d81u","at":1751288794424},"key":"Projects/Piecework/node_modules/next/README.md##Getting Started#{1}","lines":[19,20],"size":224,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"a2d81u","at":1751288794424}},
"smart_blocks:Projects/Piecework/node_modules/next/README.md##Community": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02211482,-0.05849289,-0.00504672,-0.08561613,0.02789766,-0.02811707,-0.09527929,0.00267675,-0.01801559,-0.03845018,0.00163538,-0.01068424,-0.02195232,0.03983686,0.05415927,0.04503261,-0.0158744,0.02381808,-0.04772732,0.01531888,0.00040443,-0.01138272,0.05221841,0.00328344,0.00951072,0.05035003,-0.05157827,-0.07326818,-0.05969736,-0.14109896,-0.02377208,-0.01998108,0.01170724,-0.00012974,0.02337383,-0.03754002,0.01760683,-0.02738669,-0.03605839,0.11023034,0.02063523,0.01296858,-0.04074255,-0.02376825,0.00111214,-0.00976292,0.02455277,-0.0610829,-0.01796371,-0.10137124,-0.01140759,-0.07818256,0.03272677,0.00981321,0.00105448,0.07039738,0.00980899,0.05293733,0.02207715,0.03668653,0.05785699,0.00371217,-0.19336508,0.08879278,0.04202969,0.06727327,-0.02552968,0.00673024,0.03966394,0.04290236,0.03877252,0.03076979,0.00575675,0.06540073,0.00777726,-0.03579806,-0.00744698,0.00600618,0.02018433,-0.04246888,-0.0709783,0.03717537,0.00579307,-0.00093669,-0.04687874,0.01493165,0.03119414,0.01455769,-0.02207094,0.0698007,-0.03126795,-0.05283615,0.08113988,0.06574909,-0.05692221,-0.02349726,0.04802412,-0.01547513,-0.03151467,0.14293972,-0.06533319,0.03248481,0.02412098,0.02542048,0.02279774,-0.00372058,-0.00560904,-0.04875806,0.03135921,0.00472581,-0.01639097,-0.05796237,-0.0374196,-0.03456008,0.0163693,-0.00242157,0.04172258,0.05780173,-0.02031588,0.05975483,0.03985396,0.07160738,0.04960319,-0.02074608,0.07774635,0.05255413,0.02156226,0.020479,0.02600585,0.11823539,0.01064642,0.0835845,-0.04866178,0.01233318,-0.00233164,0.01018339,-0.01383402,-0.05292127,0.03944182,-0.03218341,0.05734029,-0.04262501,0.03133199,-0.09311705,-0.00669024,0.0293168,-0.02849535,0.02618575,-0.03809926,-0.04011654,-0.00914037,0.03836999,-0.09456567,-0.01256959,0.00082423,-0.05915439,0.110269,0.01705837,-0.01121583,0.05381395,-0.01104614,-0.00862785,-0.00267799,0.08671996,0.04646417,-0.09285343,-0.02651585,-0.00827006,0.04300015,-0.08748376,-0.01985722,0.00441951,-0.02387748,-0.02709227,0.09638021,0.02106403,-0.10892584,-0.03444516,-0.02690226,0.04333989,0.00757733,0.0543115,-0.00434722,0.03206422,-0.02587965,-0.03572817,0.05473503,-0.08137492,-0.00625191,-0.00797473,-0.04949947,-0.01904118,-0.05719554,0.02663123,-0.03302703,-0.05992317,-0.00644341,-0.03530291,-0.02359006,-0.00894903,0.02246583,0.05521689,-0.04040185,0.07735556,-0.08452136,0.06682267,0.03176777,-0.03523567,0.07395893,-0.0020328,-0.07933835,-0.009674,0.10527574,0.03127578,-0.02440949,-0.05044359,0.00872005,0.04120383,0.01020759,0.03461936,-0.02759687,0.04691328,-0.07134789,-0.22608319,-0.02074437,0.05559566,-0.01194013,-0.00235791,-0.01505264,0.02964739,0.00417809,-0.02769279,0.10816995,0.17975682,-0.01972451,0.017279,-0.00689365,0.0216489,0.07462339,0.02680287,-0.04355814,-0.02033909,-0.01486768,0.00263672,-0.00188404,0.01160299,-0.09076461,0.03135983,-0.00625675,0.10839872,0.07648262,-0.00333203,-0.03332138,0.07527903,0.00821186,-0.0012982,-0.11757901,-0.01991153,0.03022948,0.01786597,0.01470074,0.00021434,0.00101697,-0.02715261,0.00744002,-0.03203404,-0.10573512,-0.04567773,-0.01339338,-0.05481443,-0.02315591,-0.03204131,0.01443006,-0.03008967,0.0417666,0.01178594,0.06707297,-0.02214292,0.00115499,-0.01500416,-0.05374826,-0.00149581,0.08695581,-0.01221037,0.07858032,-0.06782946,-0.07261843,0.11563998,0.04176262,0.0051712,0.01670776,0.09531081,-0.04095746,-0.00455771,0.06083512,-0.00600303,-0.00872799,-0.02269778,0.01594835,-0.04576352,0.00753113,-0.01749746,0.00958171,0.04583325,-0.05818924,0.01461656,0.00059559,-0.04425629,0.00234588,-0.03757188,-0.02972721,0.00777768,-0.04052279,-0.06651469,0.02441072,-0.07070959,-0.03657029,0.01912904,0.0137651,-0.21113162,0.00332113,0.0399408,-0.03770622,-0.0142085,0.00387887,0.08368559,-0.04326537,-0.05428977,0.05418709,0.05436716,0.07830701,-0.02813637,0.01931458,-0.00928485,0.04498003,0.05847403,-0.01371865,-0.00367574,-0.07527983,0.05093379,0.01250752,0.2239065,0.02006852,0.00356631,0.05835519,0.00327063,0.00900155,-0.05832554,0.04330544,-0.0585089,0.03676678,0.06907144,-0.06377165,-0.05072555,-0.03838214,-0.0233218,0.01534184,0.03533806,-0.04958998,0.01571987,-0.01277541,-0.03491525,-0.01905947,0.08503695,-0.01164747,-0.04889425,-0.10269309,0.03549858,0.00224696,-0.02548748,0.01153462,-0.00746299,-0.01235964,0.02154074,0.04326085,0.01533898,0.00388194,-0.00596289,0.00382437,-0.00227734,-0.0418939,0.07783026,-0.01643664,-0.00254476],"last_embed":{"hash":"1pldib8","tokens":181}}},"text":null,"length":0,"last_read":{"hash":"1pldib8","at":1751288794489},"key":"Projects/Piecework/node_modules/next/README.md##Community","lines":[28,35],"size":542,"outlinks":[{"title":"GitHub Discussions","target":"https://github.com/vercel/next.js/discussions","line":3},{"title":"Discord","target":"https://nextjs.org/discord","line":5},{"title":"Code of Conduct","target":"https://github.com/vercel/next.js/blob/canary/CODE_OF_CONDUCT.md","line":7}],"class_name":"SmartBlock","last_embed":{"hash":"1pldib8","at":1751288794489}},
"smart_blocks:Projects/Piecework/node_modules/next/README.md##Community#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02093262,-0.06067441,-0.00793766,-0.08312368,0.02943768,-0.02691401,-0.09556356,0.00184358,-0.02081241,-0.03910809,0.00246958,-0.01033368,-0.02114091,0.04006824,0.05443675,0.04492095,-0.01969648,0.02980929,-0.04631304,0.01438065,-0.00369513,-0.01248964,0.05136839,-0.00065489,0.01347293,0.05162339,-0.05449855,-0.07419252,-0.06110362,-0.13903956,-0.02622043,-0.01798827,0.0135525,-0.00115299,0.02156921,-0.03827006,0.01847195,-0.0264077,-0.03904107,0.11314006,0.01957121,0.01009888,-0.03737466,-0.02351152,-0.00122739,-0.00610392,0.0271405,-0.05917576,-0.01576764,-0.10295074,-0.00842146,-0.07832231,0.02974693,0.01293926,0.00067754,0.07068545,0.0081442,0.05243558,0.02266615,0.03533224,0.05893183,-0.00120932,-0.18958397,0.08678681,0.04390393,0.06540067,-0.02328514,0.00478195,0.04144461,0.04447807,0.03509966,0.03290814,0.00527456,0.06655139,0.00917508,-0.03592813,-0.00681286,0.00651738,0.01840844,-0.0432253,-0.06911846,0.03421253,0.00582445,-0.00232021,-0.04784837,0.01195252,0.03264606,0.01344224,-0.02296961,0.07184409,-0.03148164,-0.05205743,0.0822039,0.06165653,-0.05783986,-0.02676655,0.04897488,-0.01635249,-0.03296089,0.14426447,-0.06546456,0.02957353,0.02540057,0.02548879,0.02075402,-0.00518514,-0.00422518,-0.04383583,0.03783863,0.00737717,-0.01912362,-0.05583882,-0.03935593,-0.035221,0.0163785,-0.00666403,0.04030636,0.05959806,-0.01703051,0.05787036,0.04237468,0.06867769,0.05351316,-0.01994807,0.07997259,0.0532665,0.01674177,0.01826954,0.02593809,0.11400565,0.00601166,0.08268863,-0.04746583,0.01396052,-0.00181089,0.01008997,-0.01105635,-0.05161152,0.04188851,-0.03350033,0.05976013,-0.04324022,0.03278591,-0.09320834,-0.00486189,0.02968827,-0.02979492,0.02489623,-0.03937516,-0.03921363,-0.01354413,0.0398763,-0.09905984,-0.01067333,-0.00089384,-0.05925404,0.11358714,0.01324975,-0.01110731,0.04947401,-0.012821,-0.00745479,-0.00204834,0.08921189,0.04491931,-0.09118189,-0.0231465,-0.00991686,0.0450075,-0.08218752,-0.0200955,0.00422754,-0.02570184,-0.02214805,0.09610228,0.01841591,-0.11392659,-0.03138461,-0.02972929,0.04349017,0.00815918,0.06320266,-0.0049151,0.03348868,-0.02204096,-0.0344078,0.05216406,-0.08311345,-0.00912046,-0.01017686,-0.05246236,-0.01612862,-0.05798654,0.02360022,-0.0318026,-0.05906367,-0.00296195,-0.0385135,-0.02141824,-0.00330755,0.01942365,0.05292564,-0.03930286,0.07342012,-0.08517299,0.06636588,0.0343535,-0.03525944,0.07257482,-0.00785942,-0.08057942,-0.00786672,0.10603257,0.02811204,-0.02404774,-0.04911146,0.01186913,0.0399515,0.01063159,0.03176432,-0.02627087,0.04603761,-0.06969114,-0.22301213,-0.02164171,0.05635575,-0.0078772,-0.00374956,-0.01604613,0.03125344,0.00392886,-0.02885933,0.10617992,0.18082039,-0.02041805,0.01773046,-0.00474906,0.02190055,0.07416835,0.02937009,-0.0438137,-0.01566108,-0.01404611,0.00152897,-0.00127587,0.01457625,-0.08990947,0.03095002,-0.00706661,0.10891408,0.07627498,-0.00477048,-0.03520666,0.07199931,0.00853323,-0.00674385,-0.1170004,-0.020817,0.02895302,0.01509011,0.00880343,0.00152513,0.00141252,-0.02684086,0.00752904,-0.03589812,-0.10457467,-0.0426129,-0.01233854,-0.05202949,-0.0216811,-0.03142316,0.01548373,-0.03060071,0.04043433,0.00962168,0.06566543,-0.02328754,0.00118409,-0.01192907,-0.05072077,-0.00138325,0.08979586,-0.00939452,0.07880385,-0.0737743,-0.07430968,0.11874642,0.03802861,0.00620298,0.0151504,0.09595913,-0.03943531,-0.00337105,0.06432398,-0.00836297,-0.01042181,-0.02141925,0.0126905,-0.04501665,0.00844414,-0.01830593,0.00677545,0.04778454,-0.06192335,0.01339508,0.00099584,-0.04473998,0.00175659,-0.03948607,-0.03077599,0.00515243,-0.04387177,-0.06567417,0.02457534,-0.07311836,-0.03591546,0.01657061,0.0165049,-0.20990963,0.00095409,0.0403131,-0.03948249,-0.01230234,0.00339462,0.08980846,-0.04164122,-0.05308376,0.05705509,0.06152689,0.08023434,-0.02489517,0.02184273,-0.0090457,0.04298493,0.05510233,-0.01244523,-0.00651453,-0.07472762,0.05146056,0.01382111,0.22320507,0.01922513,0.00398722,0.05708347,0.00711146,0.00584909,-0.06445005,0.04193898,-0.05963423,0.03669665,0.06832179,-0.06888677,-0.04779386,-0.03545956,-0.02370255,0.01196649,0.03082148,-0.04893544,0.01373773,-0.01373052,-0.03516598,-0.01840864,0.08476093,-0.00781194,-0.04724177,-0.09939229,0.03672811,0.00368144,-0.02588126,0.01594469,-0.01392772,-0.01560461,0.02333871,0.04561333,0.01635669,0.00558654,-0.0050531,0.00488627,-0.00179474,-0.03575197,0.07649911,-0.0160971,-0.00189932],"last_embed":{"hash":"19gn2ci","tokens":180}}},"text":null,"length":0,"last_read":{"hash":"19gn2ci","at":1751288794615},"key":"Projects/Piecework/node_modules/next/README.md##Community#{1}","lines":[30,35],"size":528,"outlinks":[{"title":"GitHub Discussions","target":"https://github.com/vercel/next.js/discussions","line":1},{"title":"Discord","target":"https://nextjs.org/discord","line":3},{"title":"Code of Conduct","target":"https://github.com/vercel/next.js/blob/canary/CODE_OF_CONDUCT.md","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"19gn2ci","at":1751288794615}},
"smart_blocks:Projects/Piecework/node_modules/next/README.md##Contributing": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03399907,-0.06584044,0.00255589,-0.10976765,0.05882724,0.00740899,-0.07752795,0.00501944,0.00657972,-0.01279158,0.02871291,-0.01185156,-0.01748271,0.01572082,0.01063339,0.01763646,-0.01028932,-0.00062656,-0.03288966,0.00902506,-0.0177678,-0.06892068,0.09420092,-0.05180537,0.01483389,0.08338416,-0.05409195,-0.05520462,-0.01623112,-0.19313739,-0.02212873,-0.02210131,-0.01732326,0.00258016,0.03534979,-0.02843126,-0.04100217,0.00321317,0.00043609,0.06396458,0.041356,0.06768889,-0.06182405,-0.03898215,-0.00624964,-0.04939849,0.01762101,-0.05066302,-0.00866835,-0.07175909,-0.00379513,-0.07652751,0.03543349,0.05378063,0.04502045,0.0296369,0.01000135,0.04415514,0.02187266,0.04118812,0.05671639,0.03220275,-0.17313102,0.0449191,0.0745012,0.07977283,-0.04821022,0.00598449,0.00191976,-0.00427335,0.01327766,0.02644744,-0.00198427,0.04800738,-0.00129167,-0.02075499,0.02254402,0.00359757,0.00979026,-0.05816488,-0.03721728,0.05511547,0.01840799,0.02579195,-0.04688539,0.02065392,0.0408716,0.02603305,0.0499419,0.00384949,0.01819854,-0.08579225,0.05953781,0.09377233,0.00364235,-0.00044072,0.05411086,-0.01254085,-0.0969133,0.13514346,-0.07130165,0.0468061,0.04302627,0.00309368,0.06382862,0.02545316,0.03517363,-0.07419492,0.0065072,0.01736876,0.03011423,-0.04524841,-0.04994529,-0.06584129,0.00877881,-0.02336729,0.01869559,0.02484133,-0.01445156,-0.00551304,0.06671802,0.07651103,0.04479373,0.0110712,0.01425785,0.02618005,0.00233277,0.03677073,0.02376089,0.10934056,0.00418403,0.06722174,-0.06640171,-0.01336502,-0.03001229,-0.02063215,-0.00643427,-0.02221139,0.08601876,0.03212807,0.04076292,-0.01013675,-0.00873556,-0.08238008,-0.02146685,0.06469538,-0.02539687,0.04051835,-0.00868207,-0.08716695,-0.01349088,0.05736936,-0.08620258,-0.05244323,0.02885388,-0.02150121,0.01956921,-0.00837522,-0.01141344,0.06078809,0.00429331,-0.07088372,-0.02053487,0.07086805,0.04119811,-0.07775954,-0.03011862,0.06274474,0.02100463,-0.04937354,-0.03007491,0.00674334,-0.01604244,-0.05513602,0.06691737,-0.0011597,-0.03649683,-0.05003683,-0.01056877,0.05424044,-0.00275893,0.0062884,-0.03822771,0.0387108,0.02943467,-0.04302061,-0.02350564,-0.03211555,-0.0399613,0.01956351,-0.08492225,-0.02398269,-0.08406085,0.01948625,-0.01115564,-0.04700099,-0.05900665,-0.02628554,-0.0108989,-0.03541681,0.04691199,0.01766453,-0.03040619,0.04847743,-0.07747027,0.07071222,0.05066922,-0.05208678,0.06326231,0.03226793,-0.09895036,0.03168723,0.06235946,0.04068186,-0.04342803,0.00807564,-0.0084809,0.08673041,0.05049789,0.06865166,-0.02307573,0.04589166,-0.06487244,-0.21463458,-0.03895627,0.0194381,-0.00993967,-0.00686194,-0.03235978,0.01850051,-0.06233701,-0.04254797,0.04653548,0.14147481,0.00226853,0.01518182,-0.05252441,-0.00657046,-0.01421819,0.02531093,-0.05869304,0.01061884,-0.00411126,-0.0159917,-0.00459147,0.02413345,-0.05031104,-0.01974665,-0.00125497,0.13690592,0.07749843,-0.03536366,-0.05537637,0.01480381,-0.01569897,0.02835624,-0.1467142,-0.00837793,0.0280081,0.01060268,0.01132007,-0.02438705,-0.00363238,-0.00699777,-0.01518901,-0.02528316,-0.05280655,-0.04247195,-0.00759562,-0.07572546,-0.0184666,0.00756378,0.07996073,-0.01651585,0.05480855,0.04793431,0.08223645,0.02498066,-0.03624837,-0.03468414,-0.00093075,0.01840076,0.07359164,0.01272574,-0.00075317,-0.03595743,-0.12224129,0.0836879,0.03229547,-0.05610756,0.01428934,0.10061476,-0.05302868,0.01284902,0.09732671,0.01468133,-0.00174639,0.0137092,0.01128448,-0.0007163,0.04204634,-0.02650945,-0.01517347,0.03084975,-0.06703171,0.02975348,-0.00017117,-0.02356591,0.05189519,0.01461541,0.01077027,0.03573175,-0.04369861,-0.05181222,-0.02596525,-0.03443884,-0.00357988,0.03693103,0.04410397,-0.1925679,-0.01619121,0.05075031,-0.03582682,0.02003904,-0.00424132,0.07185194,-0.10977536,-0.0173001,0.02056921,0.04036842,0.0632609,0.00747182,0.00244822,0.01516748,0.02984609,0.04374789,-0.04975506,0.02552656,-0.09735168,0.06048188,0.05810384,0.22449635,0.04052528,-0.05610305,0.0168045,-0.00681267,0.05792223,0.00509446,0.0192612,-0.01368628,0.01723021,0.08694693,-0.01816182,-0.03389943,-0.00033672,-0.01787545,-0.00167825,0.02154066,-0.02383795,0.01761064,-0.03893842,-0.01249075,-0.01902353,0.13759121,-0.09513105,0.0002715,-0.09090688,0.05055424,0.00619683,-0.05931745,-0.01221638,0.00376062,-0.00815379,-0.00548984,0.0064898,0.0184287,0.01943266,0.0030893,-0.00006963,0.01506842,-0.01620006,0.05519543,0.01487897,-0.07390527],"last_embed":{"hash":"1sch4re","tokens":303}}},"text":null,"length":0,"last_read":{"hash":"1sch4re","at":1751288794745},"key":"Projects/Piecework/node_modules/next/README.md##Contributing","lines":[36,51],"size":1121,"outlinks":[{"title":"Contribution Guidelines","target":"/contributing.md","line":3},{"title":"good first issues","target":"https://github.com/vercel/next.js/labels/good%20first%20issue","line":7},{"title":"https://github.com/vercel/next.js/security","target":"https://github.com/vercel/next.js/security","line":15}],"class_name":"SmartBlock","last_embed":{"hash":"1sch4re","at":1751288794745}},
"smart_blocks:Projects/Piecework/node_modules/next/README.md##Contributing#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06330645,-0.03829068,0.02905772,-0.09493537,0.0315147,-0.00675719,-0.0824471,0.00673643,0.01019948,-0.00774172,0.01961167,-0.04594842,-0.04605331,0.0121277,0.02259355,0.00002912,-0.01538094,0.0181157,-0.0462218,0.00522041,-0.00001207,-0.01677221,0.0735862,-0.04034342,0.01531634,0.07391341,-0.02575289,-0.07576653,0.00289878,-0.1443764,0.01333693,-0.02003691,-0.06541292,0.00706213,0.03390936,-0.01961259,0.00459145,-0.02965763,-0.00579051,0.09128402,0.01674277,0.06431232,-0.02766873,-0.00397675,-0.0058997,-0.0381024,0.00405892,-0.02930902,0.00763775,-0.04725934,-0.00332977,-0.099633,0.02583723,0.01450919,0.06148636,0.06850876,-0.00132916,0.0594285,0.04914325,0.03879552,0.04307983,0.05012999,-0.15717418,0.04435589,0.05431058,0.07942022,-0.04961617,-0.00856933,-0.02875798,0.04146007,0.01096463,0.00231175,-0.01642599,0.05189957,0.04478261,-0.03363915,0.05003695,-0.0245865,-0.00866641,-0.06519133,-0.04290604,0.02526576,0.02466034,0.00849115,-0.03139515,0.0450111,0.0354076,-0.00720407,0.00456292,0.03200023,-0.00101563,-0.08784787,0.01561074,0.10489216,-0.01674813,-0.00719823,0.05983186,-0.00953258,-0.04073812,0.15437162,-0.07471737,0.09086941,0.01494386,-0.01359198,0.02780259,0.01454041,0.01726426,-0.03074385,-0.00365476,0.02638493,-0.0070512,-0.05861437,-0.07662507,-0.07805584,0.01611578,-0.01640936,-0.00645869,0.06734535,0.00014647,-0.03790486,0.05582769,0.06627857,0.01550113,-0.01588265,-0.01503411,0.01089808,0.01673011,0.04731614,0.02719023,0.10171968,0.01968097,0.07651205,-0.09645701,-0.01399866,-0.0212828,-0.04224335,0.00975254,-0.10436564,0.02838717,0.04240394,0.07273539,-0.01657393,-0.01431039,-0.08660884,-0.01370598,0.08461744,-0.00845279,0.04139696,-0.01065726,-0.06264542,-0.05228115,0.08909463,-0.08690108,-0.03091856,-0.01079993,-0.00655076,0.0487247,-0.0393351,0.0078445,0.07869112,-0.01147418,-0.05655754,-0.02892474,0.05865666,0.0176627,-0.06857517,-0.05004262,0.01408166,0.04903189,-0.03345606,-0.02570282,0.01394023,-0.06522861,-0.03931844,0.12087173,0.02216256,-0.07522438,-0.06163701,0.00610015,0.0091733,0.02013255,0.00029646,-0.02110698,0.03664356,0.03335259,-0.02156601,-0.02272164,-0.05411527,-0.01780284,0.01553446,-0.04187714,0.0071828,-0.05094069,0.04749604,0.01382715,-0.03819928,-0.06951864,-0.03242497,-0.0044955,-0.02086648,0.03969342,0.00476168,-0.04511413,0.04005527,-0.07066458,0.06024183,0.04829742,-0.02706861,0.07735363,0.03450987,-0.14090212,0.00858372,0.0957862,0.04975132,-0.0569382,0.04309304,0.01536638,0.07187045,0.02901382,0.05048854,-0.04380053,0.01259204,-0.05923896,-0.20247661,-0.00397853,0.04547923,-0.00367278,-0.00555688,-0.01193857,0.00715368,-0.01270705,-0.07137483,0.06890283,0.11955584,-0.00742425,0.0117436,-0.05762381,-0.01779918,-0.01053914,0.002517,-0.04285848,-0.01096913,-0.01521207,0.00427175,-0.01156881,0.0157949,-0.05191088,0.01394108,0.01116857,0.12819514,0.0644131,-0.03825098,-0.01401305,0.05130726,-0.00394125,0.00908438,-0.16878636,-0.05935287,0.04052594,0.02003144,-0.00575819,-0.03548629,-0.01078298,-0.01508843,-0.01103305,-0.02039857,-0.04653662,-0.01730172,0.00718593,-0.08565903,-0.02728061,0.01526239,0.07015729,-0.04156411,0.02967786,-0.01593283,0.07693439,0.00312676,0.00125238,-0.02531531,0.00869704,-0.02036184,0.07820423,-0.00435786,0.00632279,-0.06341504,-0.08980354,0.06283715,0.03999612,-0.04766298,0.00960829,0.06991758,-0.03149479,0.03427552,0.0324564,0.00691742,0.01882268,0.02701918,-0.02087051,-0.0248008,0.07682753,-0.00786835,-0.00734455,0.01279289,-0.08686439,0.00987205,0.03236086,0.00618991,0.05110333,-0.01658634,0.00963018,-0.00589759,-0.04907665,-0.05203141,0.000873,-0.03932177,0.02281347,0.03504061,0.01017131,-0.20118488,0.02145714,0.07004882,-0.01593436,-0.01120287,-0.01552182,0.07829014,-0.09492352,-0.01073481,0.01983458,0.06165456,0.09737291,0.00534047,0.02038042,0.01177432,0.03295724,0.02719982,-0.02336356,0.04835308,-0.10312025,0.06450962,0.05047164,0.2518478,0.00492816,0.00084437,0.02236291,-0.02694399,0.05822772,0.00676328,0.03268669,0.01943714,0.03621906,0.09645774,-0.06169248,-0.0190067,-0.00223922,-0.03208609,0.0521533,0.01090522,-0.01197156,0.01598248,-0.05218707,-0.05297577,-0.01673789,0.13070506,-0.06574959,-0.01850192,-0.1024739,-0.00742083,0.03302217,-0.0444022,0.01346571,0.01799005,-0.01422775,-0.03845068,0.00624986,-0.00253036,0.02132191,0.01721518,0.00097641,0.01096308,-0.01711217,0.04072431,0.0273078,-0.05505998],"last_embed":{"hash":"5d10uk","tokens":71}}},"text":null,"length":0,"last_read":{"hash":"5d10uk","at":1751288794994},"key":"Projects/Piecework/node_modules/next/README.md##Contributing#{1}","lines":[38,39],"size":244,"outlinks":[{"title":"Contribution Guidelines","target":"/contributing.md","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"5d10uk","at":1751288794994}},
"smart_blocks:Projects/Piecework/node_modules/next/README.md##Contributing#Good First Issues:": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01476488,-0.06794877,0.00228523,-0.09664723,0.05223526,0.00597409,-0.06680461,0.0106734,-0.00702521,0.00737101,0.03281505,-0.0012158,0.01324433,0.02037575,0.00938349,0.02948139,-0.00484976,-0.00893788,-0.01760725,0.00419757,-0.01729223,-0.07184802,0.08660835,-0.05142723,0.02798138,0.06984877,-0.04953096,-0.03294665,-0.01261413,-0.20307964,-0.03259366,-0.00819483,0.00526818,0.00758428,0.04914086,-0.0198558,-0.05162892,0.01878949,-0.01398824,0.03619852,0.05942697,0.06116825,-0.0678731,-0.04641659,-0.01133382,-0.03247991,0.03104621,-0.05611753,-0.00282341,-0.09692841,-0.00136298,-0.04528265,0.01755922,0.0623451,0.03627034,0.02410182,0.02451142,0.02936837,0.02145276,0.03819585,0.04949642,0.0171752,-0.17647482,0.04540335,0.08534238,0.07563309,-0.03772673,0.0181071,0.0118451,-0.00699188,0.00412851,0.03670989,-0.00404275,0.04513482,0.00414673,-0.01728657,0.00482737,0.01823542,0.02317172,-0.05034214,-0.03261833,0.06443686,0.01716231,0.03374823,-0.05381861,-0.00617766,0.04422852,0.04533822,0.0717063,-0.012607,0.03738512,-0.07360826,0.05460082,0.09193116,0.00966977,0.00018811,0.04382559,-0.00829543,-0.1130005,0.14604148,-0.06798244,0.0251483,0.05623592,0.02239274,0.05932664,0.03281064,0.03648884,-0.10161528,0.00050378,0.01302267,0.04721418,-0.0426064,-0.03954755,-0.07002126,0.01294641,-0.03604855,0.02019544,-0.00420773,-0.01738308,0.00174768,0.06187619,0.07620084,0.07018176,0.02278648,0.01771367,0.02222017,0.01228438,0.03578842,0.02003487,0.09254528,0.01316993,0.0479615,-0.05256937,-0.02450324,-0.03288208,-0.01328755,-0.01540212,0.01729663,0.0918814,0.02493228,0.00205539,-0.02156911,-0.00521133,-0.08205213,-0.03016164,0.05194075,-0.03753207,0.03946824,-0.02210963,-0.08685759,-0.0032014,0.02578084,-0.08735909,-0.05385544,0.0476123,-0.03785528,0.01308534,-0.00289042,-0.02853914,0.04538913,0.01410659,-0.06535358,-0.02411864,0.07809665,0.04854272,-0.07761332,-0.02085341,0.07417609,-0.00195205,-0.05408473,-0.03777217,0.01838362,0.00721124,-0.05523418,0.04486914,-0.0092039,-0.01619404,-0.04891356,-0.03012314,0.05569192,0.00329661,0.00715009,-0.03739151,0.0270373,0.03080735,-0.06681653,-0.02741376,-0.01980784,-0.03665895,0.00271181,-0.09937771,-0.02445985,-0.07955974,0.00603723,-0.03413146,-0.0387277,-0.04891651,-0.03099236,-0.02423706,-0.04316178,0.04611767,0.02012168,-0.01144263,0.06803813,-0.06822585,0.05868318,0.02803525,-0.05803324,0.06402116,0.02515624,-0.07308285,0.03078476,0.04986677,0.03863749,-0.03435819,-0.01698373,-0.02110249,0.09668712,0.06350084,0.07326147,-0.00587801,0.063422,-0.05918092,-0.22084178,-0.04850772,0.00495276,0.00092034,-0.01276064,-0.02849097,0.04061181,-0.0723162,-0.03834721,0.04720756,0.14131375,0.02010075,0.00417696,-0.04557592,-0.00026374,-0.01492152,0.02833571,-0.06508856,0.01278811,-0.00366773,-0.01466324,-0.0168795,0.01903409,-0.047848,-0.02712254,-0.01110758,0.14322962,0.06413763,-0.01984556,-0.06619322,-0.00750631,-0.02291671,0.03066635,-0.13080218,0.01534097,0.02516412,-0.00246447,0.01048456,-0.01808209,-0.01691634,-0.00707711,-0.01538946,-0.0314864,-0.05417514,-0.04645538,-0.01265317,-0.07363987,-0.00049511,0.00186505,0.06581522,-0.01915671,0.07417449,0.07078594,0.09636194,0.01749717,-0.04581752,-0.05969768,-0.00485255,0.04076002,0.05211093,0.02539723,0.00720092,-0.01482489,-0.13330823,0.09157878,0.03460802,-0.04642637,0.00324242,0.11086058,-0.04795235,0.00508941,0.12494173,0.014213,-0.01605337,0.01074544,0.01705804,0.00273974,0.02837352,-0.02076439,-0.02569567,0.03436766,-0.06364673,0.02626204,-0.01367637,-0.01862262,0.05639423,0.01509594,0.00271784,0.04876003,-0.04466524,-0.03182406,-0.03017365,-0.03065988,-0.01042812,0.0419725,0.05084967,-0.18674798,-0.01668479,0.03390398,-0.04396106,0.01733247,0.00477689,0.04905193,-0.0764183,-0.01358498,0.00380161,0.04145799,0.03152014,-0.00298497,-0.01090273,0.00721796,0.027343,0.05286797,-0.05244374,0.02433391,-0.09847876,0.06733603,0.05210783,0.21689232,0.04527652,-0.07337961,0.0035364,-0.00416343,0.05108811,0.00438038,0.02913686,-0.01173172,0.00063545,0.0835143,0.00198184,-0.02613767,0.00807864,0.00650541,-0.01693807,0.01992582,-0.01684784,-0.00384649,-0.02012979,-0.00897154,-0.01773045,0.12357658,-0.09835909,0.00370862,-0.08032537,0.05193714,-0.01141336,-0.07406428,-0.02659315,0.00957449,0.00297149,-0.00908715,-0.00270064,0.02818208,0.01257872,-0.01452586,0.00096195,0.01724256,-0.00874796,0.05901308,0.0275842,-0.06332139],"last_embed":{"hash":"16pqdkb","tokens":249}}},"text":null,"length":0,"last_read":{"hash":"16pqdkb","at":1751288795061},"key":"Projects/Piecework/node_modules/next/README.md##Contributing#Good First Issues:","lines":[40,51],"size":859,"outlinks":[{"title":"good first issues","target":"https://github.com/vercel/next.js/labels/good%20first%20issue","line":3},{"title":"https://github.com/vercel/next.js/security","target":"https://github.com/vercel/next.js/security","line":11}],"class_name":"SmartBlock","last_embed":{"hash":"16pqdkb","at":1751288795061}},
"smart_blocks:Projects/Piecework/node_modules/next/README.md##Contributing#Good First Issues:#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01543682,-0.07175383,0.00604275,-0.09367248,0.0465034,0.00916528,-0.07094458,0.00549402,0.00032529,0.00837093,0.03123007,0.01195219,0.01293443,0.02263499,0.00649507,0.03356792,-0.00413284,-0.01372428,-0.01762541,0.00446654,-0.01144082,-0.06582841,0.08213525,-0.0558661,0.02516302,0.06984393,-0.0488146,-0.02685461,-0.01878164,-0.20469591,-0.04232581,-0.01127823,0.00610551,0.0023801,0.04790444,-0.01792187,-0.05634117,0.02368787,-0.01299131,0.0313351,0.05865392,0.05741211,-0.06906518,-0.04927306,-0.00987015,-0.02816617,0.0319825,-0.05991033,-0.00517441,-0.10319936,-0.00408373,-0.04115789,0.01565109,0.05569069,0.03274632,0.02202197,0.02398543,0.0304796,0.01522593,0.03272914,0.05197006,0.01071726,-0.17976759,0.04816564,0.08744042,0.06613895,-0.04086965,0.01351576,0.01273151,-0.00206887,0.0084269,0.03129889,-0.00055237,0.04627747,0.0012637,-0.02129043,-0.00132838,0.01416669,0.0303042,-0.04433366,-0.0359319,0.05875894,0.01988421,0.0343176,-0.05846026,-0.01202875,0.03690136,0.04457727,0.07537422,-0.01926121,0.04480539,-0.07131571,0.05183075,0.09074733,0.00771031,-0.00048421,0.04217448,-0.00354465,-0.11011489,0.14615454,-0.06727196,0.02251416,0.05595966,0.02074804,0.06059437,0.03006699,0.03199255,-0.10083959,0.00633156,0.01103041,0.04961825,-0.0459184,-0.0353383,-0.07293794,0.00344092,-0.03675272,0.02815782,0.00065237,-0.01543598,0.00170037,0.06174146,0.08054555,0.07428665,0.01645447,0.01332553,0.02120716,0.01725611,0.03729234,0.01321318,0.08634383,0.01144947,0.04014263,-0.04779878,-0.02561753,-0.02942205,-0.00406699,-0.0147207,0.03231462,0.08834203,0.0204227,-0.00384034,-0.02365654,-0.00786972,-0.08390661,-0.02831261,0.04664917,-0.03923074,0.04137156,-0.02767138,-0.08646687,0.0016497,0.01831324,-0.08155318,-0.059189,0.04860747,-0.0439202,0.00913062,0.00017627,-0.03492784,0.03773637,0.00950263,-0.05911602,-0.0218504,0.08141206,0.04497156,-0.07413755,-0.01250223,0.07464743,0.00475809,-0.0591277,-0.04271135,0.02234443,0.01484954,-0.04919657,0.03956516,-0.0087467,-0.0166356,-0.05161167,-0.03909717,0.0574462,0.00217881,0.0039972,-0.03657109,0.02468345,0.02975231,-0.07015825,-0.02478927,-0.0147257,-0.03271499,0.00295691,-0.10209168,-0.03159646,-0.0782762,0.01030252,-0.03751153,-0.03944503,-0.04658378,-0.02798767,-0.0258252,-0.04393196,0.0510084,0.019529,-0.00070597,0.07658198,-0.06511164,0.06314177,0.02220529,-0.06583948,0.06090292,0.02081699,-0.07403934,0.03410238,0.05089862,0.03802069,-0.02863001,-0.02616889,-0.01981493,0.10040266,0.05505268,0.0755844,-0.00485563,0.06615336,-0.05758304,-0.22395311,-0.04810368,0.00454179,0.00357349,-0.01493565,-0.02675536,0.04226517,-0.07670984,-0.03720925,0.05403504,0.13948117,0.02144591,-0.00354626,-0.04550769,-0.00025597,-0.01154532,0.03406096,-0.06494174,0.01313134,-0.00111588,-0.01052081,-0.01403349,0.01340608,-0.05543081,-0.02391041,-0.00740472,0.14141871,0.05949244,-0.01470685,-0.07077688,-0.00628221,-0.02717936,0.03352588,-0.12325054,0.02800442,0.02513164,-0.0050182,0.01236758,-0.01551162,-0.01904424,-0.00475155,-0.01839037,-0.03538927,-0.05470213,-0.04273132,-0.01896683,-0.06839471,-0.00002693,0.00436921,0.06367822,-0.01713792,0.07799377,0.07358151,0.09476557,0.01402116,-0.04732668,-0.06009632,-0.00735688,0.04141909,0.05054025,0.02678835,0.00960016,-0.01403083,-0.13159515,0.09251571,0.0327311,-0.03894241,0.00415248,0.11404494,-0.04957543,-0.00241,0.1315351,0.01225884,-0.02305856,0.01434856,0.02048944,0.00033444,0.02891281,-0.01713509,-0.02119916,0.03889544,-0.05830514,0.02951378,-0.01592023,-0.01827538,0.0498865,0.01579441,-0.0025365,0.04767223,-0.05009328,-0.0295024,-0.02730399,-0.0418846,-0.00469576,0.04511726,0.05275334,-0.18600808,-0.01620161,0.02880086,-0.04201843,0.01612709,0.00569904,0.04526047,-0.07285509,-0.01445487,0.0104316,0.04138492,0.02660738,-0.00487158,-0.01690467,0.00399787,0.02446227,0.05211237,-0.03939114,0.03228509,-0.08948621,0.0747655,0.04539763,0.21959861,0.04429339,-0.0713113,0.00614919,-0.00364586,0.05803559,-0.00332968,0.03239834,-0.00694937,-0.00148564,0.07777051,0.00983656,-0.03263747,0.00601727,0.00587359,-0.02353988,0.01946564,-0.01445216,-0.00394811,-0.01388961,-0.00910537,-0.01261383,0.12206507,-0.0948407,0.00210656,-0.07678451,0.06091312,-0.01358558,-0.07183933,-0.02923618,0.00618024,0.00396136,-0.00082278,0.00029247,0.03770579,0.01013286,-0.01854172,0.00021847,0.01911643,-0.0045794,0.0627944,0.02814338,-0.06140206],"last_embed":{"hash":"1mnu1e2","tokens":247}}},"text":null,"length":0,"last_read":{"hash":"1mnu1e2","at":1751288795223},"key":"Projects/Piecework/node_modules/next/README.md##Contributing#Good First Issues:#{1}","lines":[42,51],"size":835,"outlinks":[{"title":"good first issues","target":"https://github.com/vercel/next.js/labels/good%20first%20issue","line":1},{"title":"https://github.com/vercel/next.js/security","target":"https://github.com/vercel/next.js/security","line":9}],"class_name":"SmartBlock","last_embed":{"hash":"1mnu1e2","at":1751288795223}},
