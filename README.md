# Task Master Template

## Overview
This is the clean template for Task Master - Intelligent Project & Task Management System.

## Template Contents
- Clean folder structure with organizational files
- Template `__start.md` for project definitions
- Global rules and automation setup
- Empty archive and performance tracking structures
- Ready for first-run auto-population

## Setup Instructions
1. Copy this template to your desired workspace location
2. Customize `__start.md` with your projects and team
3. Run Augment Agent - projects will auto-populate
4. Begin intelligent task management

## Template Features
- ✅ Proper linking structure between tasks and projects
- ✅ Archive system for completed work
- ✅ Auto-population rules for performance and team data
- ✅ Task rollover and cancellation logic
- ✅ Inline agent commands (`__agent:`)
- ✅ Reset functionality with backup system

## Customization
Edit `__start.md` to define your:
- Project categories and hierarchy
- Team members and roles
- Client information
- Status tags and priorities

#taskmaster #template #setup
