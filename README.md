# Task Master - Intelligent Project & Task Management System

## Overview
Task Master is an AI-powered workspace organization system that automatically manages projects, tasks, team coordination, and performance tracking. It integrates with Augment Agent to provide intelligent task management with automatic population, linking, and archiving.

## Key Features

### 🎯 **Intelligent Task Management**
- **Auto-linking**: Tasks automatically link to their corresponding project tasks
- **Smart Rollover**: Incomplete tasks roll over to next day or get cancelled based on memo/user input
- **Project Integration**: Tasks are directly connected to project structures

### 📊 **Auto-Population System**
- **Performance Dashboard**: Automatically updates team utilization and KPIs
- **Project Status**: Real-time project status tracking
- **Team Coordination**: Automatic team assignment and workload tracking

### 🗂️ **Archive System**
- **Project Archives**: Completed work automatically archived with links
- **Task History**: Historical task completion tracking
- **Performance History**: Long-term performance metrics

### 🔄 **Template & Reset System**
- **Template Creation**: Generate clean template from current structure
- **Backup & Reset**: `__reset` command creates backup and resets to template
- **First Run Setup**: Automatic project population on initial run

## Quick Start

### First Time Setup
1. Clone or create workspace with Task Master template
2. Run Augment Agent - projects will auto-populate from `__start.md`
3. Review and customize project priorities in daily tasks
4. Begin working with intelligent task management

### Daily Workflow
1. **Morning**: Review auto-generated daily task list
2. **Work**: Update task status as you progress
3. **Evening**: Tasks auto-rollover or archive based on completion
4. **Weekly**: Performance metrics auto-update

## Commands

### `__reset`
Creates backup in `../Backups/` folder and resets workspace to clean template.
```
__agent: __reset
```

### Inline Agent Commands
Use `__agent:` in any file for inline instructions:
```
__agent: Update this project status to completed
__agent: Archive this task and create follow-up
__agent: Generate weekly performance report
```

## File Structure

```
DaVAWT/
├── __start.md          # Project definitions and team info
├── __rules.md          # Global rules and agent commands
├── __memo.md           # Daily notes and feedback
├── __logs.md           # Activity history
├── Projects/           # All project categories
│   ├── Agency Work/
│   ├── Freelance/
│   ├── Music/
│   └── [...]
├── Tasks/              # Daily and archived tasks
│   ├── 2025-06-26-daily.md
│   └── archive/
├── Performance/        # Auto-updated metrics
├── Team/              # Team coordination
└── Clients/           # Client management
```

## Task Linking System

### Project Tasks → Daily Tasks
Tasks in daily lists automatically link to project-specific tasks:
```markdown
- [ ] **[Muchies Improvements](./Projects/Agency%20Work/Google%20Business%20Profile%20Management/Muchies/tasks.md#bathroom-standards)** - Bathroom standards implementation
```

### Archive Linking
Completed work links to archived versions:
```markdown
## Completed Projects
- [LLN Legal](./Projects/Freelance/LLN%20Legal/archive/2025-06-completion.md) - #completed #archived
```

## Auto-Population Rules

### Performance Dashboard
- **Team Utilization**: Calculated from active project assignments
- **Project Status**: Real-time counts from project files
- **Revenue Tracking**: Payment status from project metadata
- **KPIs**: Derived from task completion and client satisfaction

### Daily Task Generation
- **Priority Tasks**: From overdue and urgent projects
- **Team Coordination**: Based on active collaborations
- **Rollover Logic**: Incomplete tasks assessed for relevance

## Customization

### Project Categories
Edit `__start.md` to define your project structure:
```markdown
Projects
- Your Category
  - Your Project
    - Status #tags #and #metadata
```

### Team Structure
Update team assignments in `Team/__home.md`:
```markdown
| Name | Role | Projects | Utilization |
|------|------|----------|-------------|
| You  | Lead | All      | 100%        |
```

### Rules & Automation
Customize automation in `__rules.md`:
```markdown
- __agent: Custom automation rule
- Auto-update frequency and triggers
- Task rollover criteria
```

## Advanced Features

### Memo Integration
Daily notes in `__memo.md` automatically:
- Update project status
- Adjust team assignments
- Influence task priorities
- Generate follow-up actions

### Performance Tracking
Automatic metrics include:
- Project completion rates
- Team workload distribution
- Revenue pipeline status
- Client satisfaction indicators

### Backup System
`__reset` command:
1. Creates timestamped backup in `../Backups/DaVAWT-YYYY-MM-DD-HHMMSS/`
2. Preserves all project data and history
3. Resets workspace to clean template
4. Maintains customization in `__start.md`

## Best Practices

### Daily Workflow
1. **Start**: Review auto-generated daily tasks
2. **Update**: Mark tasks in progress/complete as you work
3. **Coordinate**: Use team links for collaboration
4. **Archive**: Let system handle completed work archiving

### Project Management
1. **Define**: Clear project scope in `__start.md`
2. **Track**: Use status tags consistently
3. **Link**: Connect related tasks and projects
4. **Archive**: Regular completion and archiving

### Team Coordination
1. **Assign**: Clear role definitions in team files
2. **Communicate**: Use project-specific coordination
3. **Track**: Monitor utilization and workload
4. **Adjust**: Based on performance metrics

## Troubleshooting

### Projects Not Populating
- Check `__start.md` format and structure
- Ensure proper markdown hierarchy
- Verify status tags are correctly formatted

### Tasks Not Linking
- Confirm project task files exist
- Check markdown link syntax
- Verify file paths are correct

### Performance Not Updating
- Review auto-update rules in `__rules.md`
- Check project status tag consistency
- Ensure team assignments are current

## Support

Task Master integrates with Augment Agent for intelligent assistance. Use inline `__agent:` commands for help with:
- Project organization
- Task management
- Performance analysis
- System customization

---

**Task Master** - Intelligent project and task management for maximum productivity and impact.
