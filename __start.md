# Task Master - Project Definitions Template

## Instructions
Customize this file to define your projects, team, and clients. The system will auto-populate project structures based on this information.

## Projects

### Agency Work
Third party ventures creating multiple streams of opportunity.

#### Google Business Profile Management
- **Client 1** - #status #tags
- **Client 2** - #status #tags

#### Your Strategic Project
- **Description** - #mvpcomplete #strategic

### Your Category
Description of your project category.

#### Your Project
- **Description** - #status #tags

### Freelance
Client development projects.

#### Client Project 1
- **Description** - #status #payment #timeline

#### Client Project 2  
- **Description** - #status #payment #timeline

### Your Other Categories
Add your own project categories and projects here.

## Team

| Name | Role | Projects | Status |
|------|------|----------|--------|
| Your Name | Lead Developer | All Projects | #active |
| Team Member 1 | Role | Specific Projects | #active |
| Team Member 2 | Role | Specific Projects | #active |

## Clients

| Client | Project | Status | Payment | Next Action |
|--------|---------|--------|---------|-------------|
| Client 1 | Project Description | #active | #paid | Next steps |
| Client 2 | Project Description | #planning | #halfpaid | Next steps |

## Status Tags
Customize these tags for your workflow:

### Project Status
- #notstarted - Project not yet begun
- #started - Project in progress
- #completed - Project finished
- #blocked - Project has blocking issues
- #cancelled - Project cancelled

### Payment Status
- #unpaid - No payment received
- #halfpaid - Partial payment received
- #paid - Full payment received
- #overdue - Payment overdue

### Priority Tags
- #urgent - Immediate attention required
- #priority - High priority
- #strategic - Long-term strategic importance
- #learning - Learning/development focus

### Timeline Tags
- #ontime - On schedule
- #overdue - Past deadline
- #ahead - Ahead of schedule

## Customization Notes
- Add your own project categories
- Define your team structure
- Set up your client list
- Customize status tags for your workflow
- The system will auto-populate folder structures based on this file

#template #projects #team #clients #setup
