# Tasks - Local Rules

## Core Task Rules
- Tasks are user tasks based off the assessment and direction the user and his team wants to go with their work
- A `__memo` will be created in the root to which it needs to be related to the project in question
- A checklist will be needed to be created to give the user some direction in the day. Please add the current date and time to make this task list actionable
- Generate daily task files with current date
- Link all tasks to corresponding project task files
- Implement smart rollover logic for incomplete tasks
- Archive completed daily task lists automatically
- Maintain task completion tracking and metrics
- Integrate with memo feedback for task adjustments
- Create project milestone archives when projects complete

## Task Rollover Rules
- **Complete**: Archive with project links maintained
- **In Progress**: Roll to tomorrow with updated status
- **Blocked**: Move to project-specific blocking list
- **Cancelled**: Archive with cancellation reason from memo

## Links
- [Parent Directory Rules](../__rules.md)
- [Task Logs](./__logs.md)