/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source visit the plugins github repository (https://github.com/Vinzent03/obsidian-advanced-uri)
*/

var gt=Object.create;var ae=Object.defineProperty;var mt=Object.getOwnPropertyDescriptor;var xt=Object.getOwnPropertyNames;var vt=Object.getPrototypeOf,wt=Object.prototype.hasOwnProperty;var De=(o,n)=>()=>(n||o((n={exports:{}}).exports,n),n.exports),jt=(o,n)=>{for(var e in n)ae(o,e,{get:n[e],enumerable:!0})},Ve=(o,n,e,t)=>{if(n&&typeof n=="object"||typeof n=="function")for(let i of xt(n))!wt.call(o,i)&&i!==e&&ae(o,i,{get:()=>n[i],enumerable:!(t=mt(n,i))||t.enumerable});return o};var xe=(o,n,e)=>(e=o!=null?gt(vt(o)):{},Ve(n||!o||!o.__esModule?ae(e,"default",{value:o,enumerable:!0}):e,o)),bt=o=>Ve(ae({},"__esModule",{value:!0}),o);var Le=De((Y,ve)=>{(function(n,e){typeof Y=="object"&&typeof ve=="object"?ve.exports=e():typeof define=="function"&&define.amd?define([],e):typeof Y=="object"?Y.feather=e():n.feather=e()})(typeof self!="undefined"?self:Y,function(){return function(o){var n={};function e(t){if(n[t])return n[t].exports;var i=n[t]={i:t,l:!1,exports:{}};return o[t].call(i.exports,i,i.exports,e),i.l=!0,i.exports}return e.m=o,e.c=n,e.d=function(t,i,a){e.o(t,i)||Object.defineProperty(t,i,{configurable:!1,enumerable:!0,get:a})},e.r=function(t){Object.defineProperty(t,"__esModule",{value:!0})},e.n=function(t){var i=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(i,"a",i),i},e.o=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)},e.p="",e(e.s=0)}({"./dist/icons.json":function(o){o.exports={activity:'<polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>',airplay:'<path d="M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1"></path><polygon points="12 15 17 21 7 21 12 15"></polygon>',"alert-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line>',"alert-octagon":'<polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line>',"alert-triangle":'<path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line>',"align-center":'<line x1="18" y1="10" x2="6" y2="10"></line><line x1="21" y1="6" x2="3" y2="6"></line><line x1="21" y1="14" x2="3" y2="14"></line><line x1="18" y1="18" x2="6" y2="18"></line>',"align-justify":'<line x1="21" y1="10" x2="3" y2="10"></line><line x1="21" y1="6" x2="3" y2="6"></line><line x1="21" y1="14" x2="3" y2="14"></line><line x1="21" y1="18" x2="3" y2="18"></line>',"align-left":'<line x1="17" y1="10" x2="3" y2="10"></line><line x1="21" y1="6" x2="3" y2="6"></line><line x1="21" y1="14" x2="3" y2="14"></line><line x1="17" y1="18" x2="3" y2="18"></line>',"align-right":'<line x1="21" y1="10" x2="7" y2="10"></line><line x1="21" y1="6" x2="3" y2="6"></line><line x1="21" y1="14" x2="3" y2="14"></line><line x1="21" y1="18" x2="7" y2="18"></line>',anchor:'<circle cx="12" cy="5" r="3"></circle><line x1="12" y1="22" x2="12" y2="8"></line><path d="M5 12H2a10 10 0 0 0 20 0h-3"></path>',aperture:'<circle cx="12" cy="12" r="10"></circle><line x1="14.31" y1="8" x2="20.05" y2="17.94"></line><line x1="9.69" y1="8" x2="21.17" y2="8"></line><line x1="7.38" y1="12" x2="13.12" y2="2.06"></line><line x1="9.69" y1="16" x2="3.95" y2="6.06"></line><line x1="14.31" y1="16" x2="2.83" y2="16"></line><line x1="16.62" y1="12" x2="10.88" y2="21.94"></line>',archive:'<polyline points="21 8 21 21 3 21 3 8"></polyline><rect x="1" y="3" width="22" height="5"></rect><line x1="10" y1="12" x2="14" y2="12"></line>',"arrow-down-circle":'<circle cx="12" cy="12" r="10"></circle><polyline points="8 12 12 16 16 12"></polyline><line x1="12" y1="8" x2="12" y2="16"></line>',"arrow-down-left":'<line x1="17" y1="7" x2="7" y2="17"></line><polyline points="17 17 7 17 7 7"></polyline>',"arrow-down-right":'<line x1="7" y1="7" x2="17" y2="17"></line><polyline points="17 7 17 17 7 17"></polyline>',"arrow-down":'<line x1="12" y1="5" x2="12" y2="19"></line><polyline points="19 12 12 19 5 12"></polyline>',"arrow-left-circle":'<circle cx="12" cy="12" r="10"></circle><polyline points="12 8 8 12 12 16"></polyline><line x1="16" y1="12" x2="8" y2="12"></line>',"arrow-left":'<line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline>',"arrow-right-circle":'<circle cx="12" cy="12" r="10"></circle><polyline points="12 16 16 12 12 8"></polyline><line x1="8" y1="12" x2="16" y2="12"></line>',"arrow-right":'<line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline>',"arrow-up-circle":'<circle cx="12" cy="12" r="10"></circle><polyline points="16 12 12 8 8 12"></polyline><line x1="12" y1="16" x2="12" y2="8"></line>',"arrow-up-left":'<line x1="17" y1="17" x2="7" y2="7"></line><polyline points="7 17 7 7 17 7"></polyline>',"arrow-up-right":'<line x1="7" y1="17" x2="17" y2="7"></line><polyline points="7 7 17 7 17 17"></polyline>',"arrow-up":'<line x1="12" y1="19" x2="12" y2="5"></line><polyline points="5 12 12 5 19 12"></polyline>',"at-sign":'<circle cx="12" cy="12" r="4"></circle><path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-3.92 7.94"></path>',award:'<circle cx="12" cy="8" r="7"></circle><polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline>',"bar-chart-2":'<line x1="18" y1="20" x2="18" y2="10"></line><line x1="12" y1="20" x2="12" y2="4"></line><line x1="6" y1="20" x2="6" y2="14"></line>',"bar-chart":'<line x1="12" y1="20" x2="12" y2="10"></line><line x1="18" y1="20" x2="18" y2="4"></line><line x1="6" y1="20" x2="6" y2="16"></line>',"battery-charging":'<path d="M5 18H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3.19M15 6h2a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-3.19"></path><line x1="23" y1="13" x2="23" y2="11"></line><polyline points="11 6 7 12 13 12 9 18"></polyline>',battery:'<rect x="1" y="6" width="18" height="12" rx="2" ry="2"></rect><line x1="23" y1="13" x2="23" y2="11"></line>',"bell-off":'<path d="M13.73 21a2 2 0 0 1-3.46 0"></path><path d="M18.63 13A17.89 17.89 0 0 1 18 8"></path><path d="M6.26 6.26A5.86 5.86 0 0 0 6 8c0 7-3 9-3 9h14"></path><path d="M18 8a6 6 0 0 0-9.33-5"></path><line x1="1" y1="1" x2="23" y2="23"></line>',bell:'<path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path>',bluetooth:'<polyline points="6.5 6.5 17.5 17.5 12 23 12 1 17.5 6.5 6.5 17.5"></polyline>',bold:'<path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path><path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>',"book-open":'<path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>',book:'<path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>',bookmark:'<path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>',box:'<path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line>',briefcase:'<rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>',calendar:'<rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line>',"camera-off":'<line x1="1" y1="1" x2="23" y2="23"></line><path d="M21 21H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3m3-3h6l2 3h4a2 2 0 0 1 2 2v9.34m-7.72-2.06a4 4 0 1 1-5.56-5.56"></path>',camera:'<path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path><circle cx="12" cy="13" r="4"></circle>',cast:'<path d="M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6"></path><line x1="2" y1="20" x2="2.01" y2="20"></line>',"check-circle":'<path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline>',"check-square":'<polyline points="9 11 12 14 22 4"></polyline><path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>',check:'<polyline points="20 6 9 17 4 12"></polyline>',"chevron-down":'<polyline points="6 9 12 15 18 9"></polyline>',"chevron-left":'<polyline points="15 18 9 12 15 6"></polyline>',"chevron-right":'<polyline points="9 18 15 12 9 6"></polyline>',"chevron-up":'<polyline points="18 15 12 9 6 15"></polyline>',"chevrons-down":'<polyline points="7 13 12 18 17 13"></polyline><polyline points="7 6 12 11 17 6"></polyline>',"chevrons-left":'<polyline points="11 17 6 12 11 7"></polyline><polyline points="18 17 13 12 18 7"></polyline>',"chevrons-right":'<polyline points="13 17 18 12 13 7"></polyline><polyline points="6 17 11 12 6 7"></polyline>',"chevrons-up":'<polyline points="17 11 12 6 7 11"></polyline><polyline points="17 18 12 13 7 18"></polyline>',chrome:'<circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="4"></circle><line x1="21.17" y1="8" x2="12" y2="8"></line><line x1="3.95" y1="6.06" x2="8.54" y2="14"></line><line x1="10.88" y1="21.94" x2="15.46" y2="14"></line>',circle:'<circle cx="12" cy="12" r="10"></circle>',clipboard:'<path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>',clock:'<circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline>',"cloud-drizzle":'<line x1="8" y1="19" x2="8" y2="21"></line><line x1="8" y1="13" x2="8" y2="15"></line><line x1="16" y1="19" x2="16" y2="21"></line><line x1="16" y1="13" x2="16" y2="15"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="12" y1="15" x2="12" y2="17"></line><path d="M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25"></path>',"cloud-lightning":'<path d="M19 16.9A5 5 0 0 0 18 7h-1.26a8 8 0 1 0-11.62 9"></path><polyline points="13 11 9 17 15 17 11 23"></polyline>',"cloud-off":'<path d="M22.61 16.95A5 5 0 0 0 18 10h-1.26a8 8 0 0 0-7.05-6M5 5a8 8 0 0 0 4 15h9a5 5 0 0 0 1.7-.3"></path><line x1="1" y1="1" x2="23" y2="23"></line>',"cloud-rain":'<line x1="16" y1="13" x2="16" y2="21"></line><line x1="8" y1="13" x2="8" y2="21"></line><line x1="12" y1="15" x2="12" y2="23"></line><path d="M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25"></path>',"cloud-snow":'<path d="M20 17.58A5 5 0 0 0 18 8h-1.26A8 8 0 1 0 4 16.25"></path><line x1="8" y1="16" x2="8.01" y2="16"></line><line x1="8" y1="20" x2="8.01" y2="20"></line><line x1="12" y1="18" x2="12.01" y2="18"></line><line x1="12" y1="22" x2="12.01" y2="22"></line><line x1="16" y1="16" x2="16.01" y2="16"></line><line x1="16" y1="20" x2="16.01" y2="20"></line>',cloud:'<path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"></path>',code:'<polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline>',codepen:'<polygon points="12 2 22 8.5 22 15.5 12 22 2 15.5 2 8.5 12 2"></polygon><line x1="12" y1="22" x2="12" y2="15.5"></line><polyline points="22 8.5 12 15.5 2 8.5"></polyline><polyline points="2 15.5 12 8.5 22 15.5"></polyline><line x1="12" y1="2" x2="12" y2="8.5"></line>',codesandbox:'<path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline><polyline points="7.5 19.79 7.5 14.6 3 12"></polyline><polyline points="21 12 16.5 14.6 16.5 19.79"></polyline><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line>',coffee:'<path d="M18 8h1a4 4 0 0 1 0 8h-1"></path><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path><line x1="6" y1="1" x2="6" y2="4"></line><line x1="10" y1="1" x2="10" y2="4"></line><line x1="14" y1="1" x2="14" y2="4"></line>',columns:'<path d="M12 3h7a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-7m0-18H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7m0-18v18"></path>',command:'<path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z"></path>',compass:'<circle cx="12" cy="12" r="10"></circle><polygon points="16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76"></polygon>',copy:'<rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>',"corner-down-left":'<polyline points="9 10 4 15 9 20"></polyline><path d="M20 4v7a4 4 0 0 1-4 4H4"></path>',"corner-down-right":'<polyline points="15 10 20 15 15 20"></polyline><path d="M4 4v7a4 4 0 0 0 4 4h12"></path>',"corner-left-down":'<polyline points="14 15 9 20 4 15"></polyline><path d="M20 4h-7a4 4 0 0 0-4 4v12"></path>',"corner-left-up":'<polyline points="14 9 9 4 4 9"></polyline><path d="M20 20h-7a4 4 0 0 1-4-4V4"></path>',"corner-right-down":'<polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>',"corner-right-up":'<polyline points="10 9 15 4 20 9"></polyline><path d="M4 20h7a4 4 0 0 0 4-4V4"></path>',"corner-up-left":'<polyline points="9 14 4 9 9 4"></polyline><path d="M20 20v-7a4 4 0 0 0-4-4H4"></path>',"corner-up-right":'<polyline points="15 14 20 9 15 4"></polyline><path d="M4 20v-7a4 4 0 0 1 4-4h12"></path>',cpu:'<rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect><rect x="9" y="9" width="6" height="6"></rect><line x1="9" y1="1" x2="9" y2="4"></line><line x1="15" y1="1" x2="15" y2="4"></line><line x1="9" y1="20" x2="9" y2="23"></line><line x1="15" y1="20" x2="15" y2="23"></line><line x1="20" y1="9" x2="23" y2="9"></line><line x1="20" y1="14" x2="23" y2="14"></line><line x1="1" y1="9" x2="4" y2="9"></line><line x1="1" y1="14" x2="4" y2="14"></line>',"credit-card":'<rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect><line x1="1" y1="10" x2="23" y2="10"></line>',crop:'<path d="M6.13 1L6 16a2 2 0 0 0 2 2h15"></path><path d="M1 6.13L16 6a2 2 0 0 1 2 2v15"></path>',crosshair:'<circle cx="12" cy="12" r="10"></circle><line x1="22" y1="12" x2="18" y2="12"></line><line x1="6" y1="12" x2="2" y2="12"></line><line x1="12" y1="6" x2="12" y2="2"></line><line x1="12" y1="22" x2="12" y2="18"></line>',database:'<ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>',delete:'<path d="M21 4H8l-7 8 7 8h13a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2z"></path><line x1="18" y1="9" x2="12" y2="15"></line><line x1="12" y1="9" x2="18" y2="15"></line>',disc:'<circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="3"></circle>',"divide-circle":'<line x1="8" y1="12" x2="16" y2="12"></line><line x1="12" y1="16" x2="12" y2="16"></line><line x1="12" y1="8" x2="12" y2="8"></line><circle cx="12" cy="12" r="10"></circle>',"divide-square":'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="8" y1="12" x2="16" y2="12"></line><line x1="12" y1="16" x2="12" y2="16"></line><line x1="12" y1="8" x2="12" y2="8"></line>',divide:'<circle cx="12" cy="6" r="2"></circle><line x1="5" y1="12" x2="19" y2="12"></line><circle cx="12" cy="18" r="2"></circle>',"dollar-sign":'<line x1="12" y1="1" x2="12" y2="23"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>',"download-cloud":'<polyline points="8 17 12 21 16 17"></polyline><line x1="12" y1="12" x2="12" y2="21"></line><path d="M20.88 18.09A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.29"></path>',download:'<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line>',dribbble:'<circle cx="12" cy="12" r="10"></circle><path d="M8.56 2.75c4.37 6.03 6.02 9.42 8.03 17.72m2.54-15.38c-3.72 4.35-8.94 5.66-16.88 5.85m19.5 1.9c-3.5-.93-6.63-.82-8.94 0-2.58.92-5.01 2.86-7.44 6.32"></path>',droplet:'<path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"></path>',"edit-2":'<path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>',"edit-3":'<path d="M12 20h9"></path><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>',edit:'<path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>',"external-link":'<path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" y1="14" x2="21" y2="3"></line>',"eye-off":'<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path><line x1="1" y1="1" x2="23" y2="23"></line>',eye:'<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle>',facebook:'<path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>',"fast-forward":'<polygon points="13 19 22 12 13 5 13 19"></polygon><polygon points="2 19 11 12 2 5 2 19"></polygon>',feather:'<path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"></path><line x1="16" y1="8" x2="2" y2="22"></line><line x1="17.5" y1="15" x2="9" y2="15"></line>',figma:'<path d="M5 5.5A3.5 3.5 0 0 1 8.5 2H12v7H8.5A3.5 3.5 0 0 1 5 5.5z"></path><path d="M12 2h3.5a3.5 3.5 0 1 1 0 7H12V2z"></path><path d="M12 12.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 1 1-7 0z"></path><path d="M5 19.5A3.5 3.5 0 0 1 8.5 16H12v3.5a3.5 3.5 0 1 1-7 0z"></path><path d="M5 12.5A3.5 3.5 0 0 1 8.5 9H12v7H8.5A3.5 3.5 0 0 1 5 12.5z"></path>',"file-minus":'<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="9" y1="15" x2="15" y2="15"></line>',"file-plus":'<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="12" y1="18" x2="12" y2="12"></line><line x1="9" y1="15" x2="15" y2="15"></line>',"file-text":'<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline>',file:'<path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline>',film:'<rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18"></rect><line x1="7" y1="2" x2="7" y2="22"></line><line x1="17" y1="2" x2="17" y2="22"></line><line x1="2" y1="12" x2="22" y2="12"></line><line x1="2" y1="7" x2="7" y2="7"></line><line x1="2" y1="17" x2="7" y2="17"></line><line x1="17" y1="17" x2="22" y2="17"></line><line x1="17" y1="7" x2="22" y2="7"></line>',filter:'<polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>',flag:'<path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path><line x1="4" y1="22" x2="4" y2="15"></line>',"folder-minus":'<path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path><line x1="9" y1="14" x2="15" y2="14"></line>',"folder-plus":'<path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path><line x1="12" y1="11" x2="12" y2="17"></line><line x1="9" y1="14" x2="15" y2="14"></line>',folder:'<path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>',framer:'<path d="M5 16V9h14V2H5l14 14h-7m-7 0l7 7v-7m-7 0h7"></path>',frown:'<circle cx="12" cy="12" r="10"></circle><path d="M16 16s-1.5-2-4-2-4 2-4 2"></path><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line>',gift:'<polyline points="20 12 20 22 4 22 4 12"></polyline><rect x="2" y="7" width="20" height="5"></rect><line x1="12" y1="22" x2="12" y2="7"></line><path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"></path><path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"></path>',"git-branch":'<line x1="6" y1="3" x2="6" y2="15"></line><circle cx="18" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><path d="M18 9a9 9 0 0 1-9 9"></path>',"git-commit":'<circle cx="12" cy="12" r="4"></circle><line x1="1.05" y1="12" x2="7" y2="12"></line><line x1="17.01" y1="12" x2="22.96" y2="12"></line>',"git-merge":'<circle cx="18" cy="18" r="3"></circle><circle cx="6" cy="6" r="3"></circle><path d="M6 21V9a9 9 0 0 0 9 9"></path>',"git-pull-request":'<circle cx="18" cy="18" r="3"></circle><circle cx="6" cy="6" r="3"></circle><path d="M13 6h3a2 2 0 0 1 2 2v7"></path><line x1="6" y1="9" x2="6" y2="21"></line>',github:'<path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>',gitlab:'<path d="M22.65 14.39L12 22.13 1.35 14.39a.84.84 0 0 1-.3-.94l1.22-3.78 2.44-7.51A.42.42 0 0 1 4.82 2a.43.43 0 0 1 .58 0 .42.42 0 0 1 .11.18l2.44 7.49h8.1l2.44-7.51A.42.42 0 0 1 18.6 2a.43.43 0 0 1 .58 0 .42.42 0 0 1 .11.18l2.44 7.51L23 13.45a.84.84 0 0 1-.35.94z"></path>',globe:'<circle cx="12" cy="12" r="10"></circle><line x1="2" y1="12" x2="22" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>',grid:'<rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect>',"hard-drive":'<line x1="22" y1="12" x2="2" y2="12"></line><path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path><line x1="6" y1="16" x2="6.01" y2="16"></line><line x1="10" y1="16" x2="10.01" y2="16"></line>',hash:'<line x1="4" y1="9" x2="20" y2="9"></line><line x1="4" y1="15" x2="20" y2="15"></line><line x1="10" y1="3" x2="8" y2="21"></line><line x1="16" y1="3" x2="14" y2="21"></line>',headphones:'<path d="M3 18v-6a9 9 0 0 1 18 0v6"></path><path d="M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z"></path>',heart:'<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>',"help-circle":'<circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line>',hexagon:'<path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>',home:'<path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline>',image:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline>',inbox:'<polyline points="22 12 16 12 14 15 10 15 8 12 2 12"></polyline><path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path>',info:'<circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line>',instagram:'<rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>',italic:'<line x1="19" y1="4" x2="10" y2="4"></line><line x1="14" y1="20" x2="5" y2="20"></line><line x1="15" y1="4" x2="9" y2="20"></line>',key:'<path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>',layers:'<polygon points="12 2 2 7 12 12 22 7 12 2"></polygon><polyline points="2 17 12 22 22 17"></polyline><polyline points="2 12 12 17 22 12"></polyline>',layout:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="3" y1="9" x2="21" y2="9"></line><line x1="9" y1="21" x2="9" y2="9"></line>',"life-buoy":'<circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="4"></circle><line x1="4.93" y1="4.93" x2="9.17" y2="9.17"></line><line x1="14.83" y1="14.83" x2="19.07" y2="19.07"></line><line x1="14.83" y1="9.17" x2="19.07" y2="4.93"></line><line x1="14.83" y1="9.17" x2="18.36" y2="5.64"></line><line x1="4.93" y1="19.07" x2="9.17" y2="14.83"></line>',"link-2":'<path d="M15 7h3a5 5 0 0 1 5 5 5 5 0 0 1-5 5h-3m-6 0H6a5 5 0 0 1-5-5 5 5 0 0 1 5-5h3"></path><line x1="8" y1="12" x2="16" y2="12"></line>',link:'<path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>',linkedin:'<path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect x="2" y="9" width="4" height="12"></rect><circle cx="4" cy="4" r="2"></circle>',list:'<line x1="8" y1="6" x2="21" y2="6"></line><line x1="8" y1="12" x2="21" y2="12"></line><line x1="8" y1="18" x2="21" y2="18"></line><line x1="3" y1="6" x2="3.01" y2="6"></line><line x1="3" y1="12" x2="3.01" y2="12"></line><line x1="3" y1="18" x2="3.01" y2="18"></line>',loader:'<line x1="12" y1="2" x2="12" y2="6"></line><line x1="12" y1="18" x2="12" y2="22"></line><line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line><line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line><line x1="2" y1="12" x2="6" y2="12"></line><line x1="18" y1="12" x2="22" y2="12"></line><line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line><line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line>',lock:'<rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path>',"log-in":'<path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path><polyline points="10 17 15 12 10 7"></polyline><line x1="15" y1="12" x2="3" y2="12"></line>',"log-out":'<path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path><polyline points="16 17 21 12 16 7"></polyline><line x1="21" y1="12" x2="9" y2="12"></line>',mail:'<path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline>',"map-pin":'<path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle>',map:'<polygon points="1 6 1 22 8 18 16 22 23 18 23 2 16 6 8 2 1 6"></polygon><line x1="8" y1="2" x2="8" y2="18"></line><line x1="16" y1="6" x2="16" y2="22"></line>',"maximize-2":'<polyline points="15 3 21 3 21 9"></polyline><polyline points="9 21 3 21 3 15"></polyline><line x1="21" y1="3" x2="14" y2="10"></line><line x1="3" y1="21" x2="10" y2="14"></line>',maximize:'<path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path>',meh:'<circle cx="12" cy="12" r="10"></circle><line x1="8" y1="15" x2="16" y2="15"></line><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line>',menu:'<line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line>',"message-circle":'<path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>',"message-square":'<path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>',"mic-off":'<line x1="1" y1="1" x2="23" y2="23"></line><path d="M9 9v3a3 3 0 0 0 5.12 2.12M15 9.34V4a3 3 0 0 0-5.94-.6"></path><path d="M17 16.95A7 7 0 0 1 5 12v-2m14 0v2a7 7 0 0 1-.11 1.23"></path><line x1="12" y1="19" x2="12" y2="23"></line><line x1="8" y1="23" x2="16" y2="23"></line>',mic:'<path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path><path d="M19 10v2a7 7 0 0 1-14 0v-2"></path><line x1="12" y1="19" x2="12" y2="23"></line><line x1="8" y1="23" x2="16" y2="23"></line>',"minimize-2":'<polyline points="4 14 10 14 10 20"></polyline><polyline points="20 10 14 10 14 4"></polyline><line x1="14" y1="10" x2="21" y2="3"></line><line x1="3" y1="21" x2="10" y2="14"></line>',minimize:'<path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"></path>',"minus-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="8" y1="12" x2="16" y2="12"></line>',"minus-square":'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="8" y1="12" x2="16" y2="12"></line>',minus:'<line x1="5" y1="12" x2="19" y2="12"></line>',monitor:'<rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect><line x1="8" y1="21" x2="16" y2="21"></line><line x1="12" y1="17" x2="12" y2="21"></line>',moon:'<path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>',"more-horizontal":'<circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle>',"more-vertical":'<circle cx="12" cy="12" r="1"></circle><circle cx="12" cy="5" r="1"></circle><circle cx="12" cy="19" r="1"></circle>',"mouse-pointer":'<path d="M3 3l7.07 16.97 2.51-7.39 7.39-2.51L3 3z"></path><path d="M13 13l6 6"></path>',move:'<polyline points="5 9 2 12 5 15"></polyline><polyline points="9 5 12 2 15 5"></polyline><polyline points="15 19 12 22 9 19"></polyline><polyline points="19 9 22 12 19 15"></polyline><line x1="2" y1="12" x2="22" y2="12"></line><line x1="12" y1="2" x2="12" y2="22"></line>',music:'<path d="M9 18V5l12-2v13"></path><circle cx="6" cy="18" r="3"></circle><circle cx="18" cy="16" r="3"></circle>',"navigation-2":'<polygon points="12 2 19 21 12 17 5 21 12 2"></polygon>',navigation:'<polygon points="3 11 22 2 13 21 11 13 3 11"></polygon>',octagon:'<polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon>',package:'<line x1="16.5" y1="9.4" x2="7.5" y2="4.21"></line><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line>',paperclip:'<path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>',"pause-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="10" y1="15" x2="10" y2="9"></line><line x1="14" y1="15" x2="14" y2="9"></line>',pause:'<rect x="6" y="4" width="4" height="16"></rect><rect x="14" y="4" width="4" height="16"></rect>',"pen-tool":'<path d="M12 19l7-7 3 3-7 7-3-3z"></path><path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path><path d="M2 2l7.586 7.586"></path><circle cx="11" cy="11" r="2"></circle>',percent:'<line x1="19" y1="5" x2="5" y2="19"></line><circle cx="6.5" cy="6.5" r="2.5"></circle><circle cx="17.5" cy="17.5" r="2.5"></circle>',"phone-call":'<path d="M15.05 5A5 5 0 0 1 19 8.95M15.05 1A9 9 0 0 1 23 8.94m-1 7.98v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"phone-forwarded":'<polyline points="19 1 23 5 19 9"></polyline><line x1="15" y1="5" x2="23" y2="5"></line><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"phone-incoming":'<polyline points="16 2 16 8 22 8"></polyline><line x1="23" y1="1" x2="16" y2="8"></line><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"phone-missed":'<line x1="23" y1="1" x2="17" y2="7"></line><line x1="17" y1="1" x2="23" y2="7"></line><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"phone-off":'<path d="M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91"></path><line x1="23" y1="1" x2="1" y2="23"></line>',"phone-outgoing":'<polyline points="23 7 23 1 17 1"></polyline><line x1="16" y1="8" x2="23" y2="1"></line><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',phone:'<path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"pie-chart":'<path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path><path d="M22 12A10 10 0 0 0 12 2v10z"></path>',"play-circle":'<circle cx="12" cy="12" r="10"></circle><polygon points="10 8 16 12 10 16 10 8"></polygon>',play:'<polygon points="5 3 19 12 5 21 5 3"></polygon>',"plus-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line>',"plus-square":'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line>',plus:'<line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line>',pocket:'<path d="M4 3h16a2 2 0 0 1 2 2v6a10 10 0 0 1-10 10A10 10 0 0 1 2 11V5a2 2 0 0 1 2-2z"></path><polyline points="8 10 12 14 16 10"></polyline>',power:'<path d="M18.36 6.64a9 9 0 1 1-12.73 0"></path><line x1="12" y1="2" x2="12" y2="12"></line>',printer:'<polyline points="6 9 6 2 18 2 18 9"></polyline><path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path><rect x="6" y="14" width="12" height="8"></rect>',radio:'<circle cx="12" cy="12" r="2"></circle><path d="M16.24 7.76a6 6 0 0 1 0 8.49m-8.48-.01a6 6 0 0 1 0-8.49m11.31-2.82a10 10 0 0 1 0 14.14m-14.14 0a10 10 0 0 1 0-14.14"></path>',"refresh-ccw":'<polyline points="1 4 1 10 7 10"></polyline><polyline points="23 20 23 14 17 14"></polyline><path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>',"refresh-cw":'<polyline points="23 4 23 10 17 10"></polyline><polyline points="1 20 1 14 7 14"></polyline><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>',repeat:'<polyline points="17 1 21 5 17 9"></polyline><path d="M3 11V9a4 4 0 0 1 4-4h14"></path><polyline points="7 23 3 19 7 15"></polyline><path d="M21 13v2a4 4 0 0 1-4 4H3"></path>',rewind:'<polygon points="11 19 2 12 11 5 11 19"></polygon><polygon points="22 19 13 12 22 5 22 19"></polygon>',"rotate-ccw":'<polyline points="1 4 1 10 7 10"></polyline><path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>',"rotate-cw":'<polyline points="23 4 23 10 17 10"></polyline><path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>',rss:'<path d="M4 11a9 9 0 0 1 9 9"></path><path d="M4 4a16 16 0 0 1 16 16"></path><circle cx="5" cy="19" r="1"></circle>',save:'<path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path><polyline points="17 21 17 13 7 13 7 21"></polyline><polyline points="7 3 7 8 15 8"></polyline>',scissors:'<circle cx="6" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><line x1="20" y1="4" x2="8.12" y2="15.88"></line><line x1="14.47" y1="14.48" x2="20" y2="20"></line><line x1="8.12" y1="8.12" x2="12" y2="12"></line>',search:'<circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line>',send:'<line x1="22" y1="2" x2="11" y2="13"></line><polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>',server:'<rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect><rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect><line x1="6" y1="6" x2="6.01" y2="6"></line><line x1="6" y1="18" x2="6.01" y2="18"></line>',settings:'<circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>',"share-2":'<circle cx="18" cy="5" r="3"></circle><circle cx="6" cy="12" r="3"></circle><circle cx="18" cy="19" r="3"></circle><line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line><line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>',share:'<path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path><polyline points="16 6 12 2 8 6"></polyline><line x1="12" y1="2" x2="12" y2="15"></line>',"shield-off":'<path d="M19.69 14a6.9 6.9 0 0 0 .31-2V5l-8-3-3.16 1.18"></path><path d="M4.73 4.73L4 5v7c0 6 8 10 8 10a20.29 20.29 0 0 0 5.62-4.38"></path><line x1="1" y1="1" x2="23" y2="23"></line>',shield:'<path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>',"shopping-bag":'<path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path><line x1="3" y1="6" x2="21" y2="6"></line><path d="M16 10a4 4 0 0 1-8 0"></path>',"shopping-cart":'<circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>',shuffle:'<polyline points="16 3 21 3 21 8"></polyline><line x1="4" y1="20" x2="21" y2="3"></line><polyline points="21 16 21 21 16 21"></polyline><line x1="15" y1="15" x2="21" y2="21"></line><line x1="4" y1="4" x2="9" y2="9"></line>',sidebar:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="9" y1="3" x2="9" y2="21"></line>',"skip-back":'<polygon points="19 20 9 12 19 4 19 20"></polygon><line x1="5" y1="19" x2="5" y2="5"></line>',"skip-forward":'<polygon points="5 4 15 12 5 20 5 4"></polygon><line x1="19" y1="5" x2="19" y2="19"></line>',slack:'<path d="M14.5 10c-.83 0-1.5-.67-1.5-1.5v-5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5z"></path><path d="M20.5 10H19V8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"></path><path d="M9.5 14c.83 0 1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5S8 21.33 8 20.5v-5c0-.83.67-1.5 1.5-1.5z"></path><path d="M3.5 14H5v1.5c0 .83-.67 1.5-1.5 1.5S2 16.33 2 15.5 2.67 14 3.5 14z"></path><path d="M14 14.5c0-.83.67-1.5 1.5-1.5h5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-5c-.83 0-1.5-.67-1.5-1.5z"></path><path d="M15.5 19H14v1.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z"></path><path d="M10 9.5C10 8.67 9.33 8 8.5 8h-5C2.67 8 2 8.67 2 9.5S2.67 11 3.5 11h5c.83 0 1.5-.67 1.5-1.5z"></path><path d="M8.5 5H10V3.5C10 2.67 9.33 2 8.5 2S7 2.67 7 3.5 7.67 5 8.5 5z"></path>',slash:'<circle cx="12" cy="12" r="10"></circle><line x1="4.93" y1="4.93" x2="19.07" y2="19.07"></line>',sliders:'<line x1="4" y1="21" x2="4" y2="14"></line><line x1="4" y1="10" x2="4" y2="3"></line><line x1="12" y1="21" x2="12" y2="12"></line><line x1="12" y1="8" x2="12" y2="3"></line><line x1="20" y1="21" x2="20" y2="16"></line><line x1="20" y1="12" x2="20" y2="3"></line><line x1="1" y1="14" x2="7" y2="14"></line><line x1="9" y1="8" x2="15" y2="8"></line><line x1="17" y1="16" x2="23" y2="16"></line>',smartphone:'<rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect><line x1="12" y1="18" x2="12.01" y2="18"></line>',smile:'<circle cx="12" cy="12" r="10"></circle><path d="M8 14s1.5 2 4 2 4-2 4-2"></path><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line>',speaker:'<rect x="4" y="2" width="16" height="20" rx="2" ry="2"></rect><circle cx="12" cy="14" r="4"></circle><line x1="12" y1="6" x2="12.01" y2="6"></line>',square:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>',star:'<polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>',"stop-circle":'<circle cx="12" cy="12" r="10"></circle><rect x="9" y="9" width="6" height="6"></rect>',sun:'<circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>',sunrise:'<path d="M17 18a5 5 0 0 0-10 0"></path><line x1="12" y1="2" x2="12" y2="9"></line><line x1="4.22" y1="10.22" x2="5.64" y2="11.64"></line><line x1="1" y1="18" x2="3" y2="18"></line><line x1="21" y1="18" x2="23" y2="18"></line><line x1="18.36" y1="11.64" x2="19.78" y2="10.22"></line><line x1="23" y1="22" x2="1" y2="22"></line><polyline points="8 6 12 2 16 6"></polyline>',sunset:'<path d="M17 18a5 5 0 0 0-10 0"></path><line x1="12" y1="9" x2="12" y2="2"></line><line x1="4.22" y1="10.22" x2="5.64" y2="11.64"></line><line x1="1" y1="18" x2="3" y2="18"></line><line x1="21" y1="18" x2="23" y2="18"></line><line x1="18.36" y1="11.64" x2="19.78" y2="10.22"></line><line x1="23" y1="22" x2="1" y2="22"></line><polyline points="16 5 12 9 8 5"></polyline>',table:'<path d="M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18"></path>',tablet:'<rect x="4" y="2" width="16" height="20" rx="2" ry="2"></rect><line x1="12" y1="18" x2="12.01" y2="18"></line>',tag:'<path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path><line x1="7" y1="7" x2="7.01" y2="7"></line>',target:'<circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle>',terminal:'<polyline points="4 17 10 11 4 5"></polyline><line x1="12" y1="19" x2="20" y2="19"></line>',thermometer:'<path d="M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z"></path>',"thumbs-down":'<path d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17"></path>',"thumbs-up":'<path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>',"toggle-left":'<rect x="1" y="5" width="22" height="14" rx="7" ry="7"></rect><circle cx="8" cy="12" r="3"></circle>',"toggle-right":'<rect x="1" y="5" width="22" height="14" rx="7" ry="7"></rect><circle cx="16" cy="12" r="3"></circle>',tool:'<path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>',"trash-2":'<polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line>',trash:'<polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>',trello:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><rect x="7" y="7" width="3" height="9"></rect><rect x="14" y="7" width="3" height="5"></rect>',"trending-down":'<polyline points="23 18 13.5 8.5 8.5 13.5 1 6"></polyline><polyline points="17 18 23 18 23 12"></polyline>',"trending-up":'<polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline><polyline points="17 6 23 6 23 12"></polyline>',triangle:'<path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>',truck:'<rect x="1" y="3" width="15" height="13"></rect><polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon><circle cx="5.5" cy="18.5" r="2.5"></circle><circle cx="18.5" cy="18.5" r="2.5"></circle>',tv:'<rect x="2" y="7" width="20" height="15" rx="2" ry="2"></rect><polyline points="17 2 12 7 7 2"></polyline>',twitch:'<path d="M21 2H3v16h5v4l4-4h5l4-4V2zM11 11V7M16 11V7"></path>',twitter:'<path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>',type:'<polyline points="4 7 4 4 20 4 20 7"></polyline><line x1="9" y1="20" x2="15" y2="20"></line><line x1="12" y1="4" x2="12" y2="20"></line>',umbrella:'<path d="M23 12a11.05 11.05 0 0 0-22 0zm-5 7a3 3 0 0 1-6 0v-7"></path>',underline:'<path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"></path><line x1="4" y1="21" x2="20" y2="21"></line>',unlock:'<rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 9.9-1"></path>',"upload-cloud":'<polyline points="16 16 12 12 8 16"></polyline><line x1="12" y1="12" x2="12" y2="21"></line><path d="M20.39 18.39A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.3"></path><polyline points="16 16 12 12 8 16"></polyline>',upload:'<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line>',"user-check":'<path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><polyline points="17 11 19 13 23 9"></polyline>',"user-minus":'<path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="23" y1="11" x2="17" y2="11"></line>',"user-plus":'<path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="20" y1="8" x2="20" y2="14"></line><line x1="23" y1="11" x2="17" y2="11"></line>',"user-x":'<path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="18" y1="8" x2="23" y2="13"></line><line x1="23" y1="8" x2="18" y2="13"></line>',user:'<path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle>',users:'<path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path>',"video-off":'<path d="M16 16v1a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2m5.66 0H14a2 2 0 0 1 2 2v3.34l1 1L23 7v10"></path><line x1="1" y1="1" x2="23" y2="23"></line>',video:'<polygon points="23 7 16 12 23 17 23 7"></polygon><rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>',voicemail:'<circle cx="5.5" cy="11.5" r="4.5"></circle><circle cx="18.5" cy="11.5" r="4.5"></circle><line x1="5.5" y1="16" x2="18.5" y2="16"></line>',"volume-1":'<polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>',"volume-2":'<polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path>',"volume-x":'<polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><line x1="23" y1="9" x2="17" y2="15"></line><line x1="17" y1="9" x2="23" y2="15"></line>',volume:'<polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>',watch:'<circle cx="12" cy="12" r="7"></circle><polyline points="12 9 12 12 13.5 13.5"></polyline><path d="M16.51 17.35l-.35 3.83a2 2 0 0 1-2 1.82H9.83a2 2 0 0 1-2-1.82l-.35-3.83m.01-10.7l.35-3.83A2 2 0 0 1 9.83 1h4.35a2 2 0 0 1 2 1.82l.35 3.83"></path>',"wifi-off":'<line x1="1" y1="1" x2="23" y2="23"></line><path d="M16.72 11.06A10.94 10.94 0 0 1 19 12.55"></path><path d="M5 12.55a10.94 10.94 0 0 1 5.17-2.39"></path><path d="M10.71 5.05A16 16 0 0 1 22.58 9"></path><path d="M1.42 9a15.91 15.91 0 0 1 4.7-2.88"></path><path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path><line x1="12" y1="20" x2="12.01" y2="20"></line>',wifi:'<path d="M5 12.55a11 11 0 0 1 14.08 0"></path><path d="M1.42 9a16 16 0 0 1 21.16 0"></path><path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path><line x1="12" y1="20" x2="12.01" y2="20"></line>',wind:'<path d="M9.59 4.59A2 2 0 1 1 11 8H2m10.59 11.41A2 2 0 1 0 14 16H2m15.73-8.27A2.5 2.5 0 1 1 19.5 12H2"></path>',"x-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line>',"x-octagon":'<polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line>',"x-square":'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="9" y1="9" x2="15" y2="15"></line><line x1="15" y1="9" x2="9" y2="15"></line>',x:'<line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line>',youtube:'<path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path><polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>',"zap-off":'<polyline points="12.41 6.75 13 2 10.57 4.92"></polyline><polyline points="18.57 12.91 21 10 15.66 10"></polyline><polyline points="8 8 3 14 12 14 11 22 16 16"></polyline><line x1="1" y1="1" x2="23" y2="23"></line>',zap:'<polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>',"zoom-in":'<circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="11" y1="8" x2="11" y2="14"></line><line x1="8" y1="11" x2="14" y2="11"></line>',"zoom-out":'<circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="8" y1="11" x2="14" y2="11"></line>'}},"./node_modules/classnames/dedupe.js":function(o,n,e){var t,i;(function(){"use strict";var a=function(){function l(){}l.prototype=Object.create(null);function r(f,y){for(var g=y.length,x=0;x<g;++x)u(f,y[x])}var c={}.hasOwnProperty;function s(f,y){f[y]=!0}function d(f,y){for(var g in y)c.call(y,g)&&(f[g]=!!y[g])}var p=/\s+/;function h(f,y){for(var g=y.split(p),x=g.length,b=0;b<x;++b)f[g[b]]=!0}function u(f,y){if(y){var g=typeof y;g==="string"?h(f,y):Array.isArray(y)?r(f,y):g==="object"?d(f,y):g==="number"&&s(f,y)}}function m(){for(var f=arguments.length,y=Array(f),g=0;g<f;g++)y[g]=arguments[g];var x=new l;r(x,y);var b=[];for(var M in x)x[M]&&b.push(M);return b.join(" ")}return m}();typeof o!="undefined"&&o.exports?o.exports=a:(t=[],i=function(){return a}.apply(n,t),i!==void 0&&(o.exports=i))})()},"./node_modules/core-js/es/array/from.js":function(o,n,e){e("./node_modules/core-js/modules/es.string.iterator.js"),e("./node_modules/core-js/modules/es.array.from.js");var t=e("./node_modules/core-js/internals/path.js");o.exports=t.Array.from},"./node_modules/core-js/internals/a-function.js":function(o,n){o.exports=function(e){if(typeof e!="function")throw TypeError(String(e)+" is not a function");return e}},"./node_modules/core-js/internals/an-object.js":function(o,n,e){var t=e("./node_modules/core-js/internals/is-object.js");o.exports=function(i){if(!t(i))throw TypeError(String(i)+" is not an object");return i}},"./node_modules/core-js/internals/array-from.js":function(o,n,e){"use strict";var t=e("./node_modules/core-js/internals/bind-context.js"),i=e("./node_modules/core-js/internals/to-object.js"),a=e("./node_modules/core-js/internals/call-with-safe-iteration-closing.js"),l=e("./node_modules/core-js/internals/is-array-iterator-method.js"),r=e("./node_modules/core-js/internals/to-length.js"),c=e("./node_modules/core-js/internals/create-property.js"),s=e("./node_modules/core-js/internals/get-iterator-method.js");o.exports=function(p){var h=i(p),u=typeof this=="function"?this:Array,m=arguments.length,f=m>1?arguments[1]:void 0,y=f!==void 0,g=0,x=s(h),b,M,A,P;if(y&&(f=t(f,m>2?arguments[2]:void 0,2)),x!=null&&!(u==Array&&l(x)))for(P=x.call(h),M=new u;!(A=P.next()).done;g++)c(M,g,y?a(P,f,[A.value,g],!0):A.value);else for(b=r(h.length),M=new u(b);b>g;g++)c(M,g,y?f(h[g],g):h[g]);return M.length=g,M}},"./node_modules/core-js/internals/array-includes.js":function(o,n,e){var t=e("./node_modules/core-js/internals/to-indexed-object.js"),i=e("./node_modules/core-js/internals/to-length.js"),a=e("./node_modules/core-js/internals/to-absolute-index.js");o.exports=function(l){return function(r,c,s){var d=t(r),p=i(d.length),h=a(s,p),u;if(l&&c!=c){for(;p>h;)if(u=d[h++],u!=u)return!0}else for(;p>h;h++)if((l||h in d)&&d[h]===c)return l||h||0;return!l&&-1}}},"./node_modules/core-js/internals/bind-context.js":function(o,n,e){var t=e("./node_modules/core-js/internals/a-function.js");o.exports=function(i,a,l){if(t(i),a===void 0)return i;switch(l){case 0:return function(){return i.call(a)};case 1:return function(r){return i.call(a,r)};case 2:return function(r,c){return i.call(a,r,c)};case 3:return function(r,c,s){return i.call(a,r,c,s)}}return function(){return i.apply(a,arguments)}}},"./node_modules/core-js/internals/call-with-safe-iteration-closing.js":function(o,n,e){var t=e("./node_modules/core-js/internals/an-object.js");o.exports=function(i,a,l,r){try{return r?a(t(l)[0],l[1]):a(l)}catch(s){var c=i.return;throw c!==void 0&&t(c.call(i)),s}}},"./node_modules/core-js/internals/check-correctness-of-iteration.js":function(o,n,e){var t=e("./node_modules/core-js/internals/well-known-symbol.js"),i=t("iterator"),a=!1;try{var l=0,r={next:function(){return{done:!!l++}},return:function(){a=!0}};r[i]=function(){return this},Array.from(r,function(){throw 2})}catch(c){}o.exports=function(c,s){if(!s&&!a)return!1;var d=!1;try{var p={};p[i]=function(){return{next:function(){return{done:d=!0}}}},c(p)}catch(h){}return d}},"./node_modules/core-js/internals/classof-raw.js":function(o,n){var e={}.toString;o.exports=function(t){return e.call(t).slice(8,-1)}},"./node_modules/core-js/internals/classof.js":function(o,n,e){var t=e("./node_modules/core-js/internals/classof-raw.js"),i=e("./node_modules/core-js/internals/well-known-symbol.js"),a=i("toStringTag"),l=t(function(){return arguments}())=="Arguments",r=function(c,s){try{return c[s]}catch(d){}};o.exports=function(c){var s,d,p;return c===void 0?"Undefined":c===null?"Null":typeof(d=r(s=Object(c),a))=="string"?d:l?t(s):(p=t(s))=="Object"&&typeof s.callee=="function"?"Arguments":p}},"./node_modules/core-js/internals/copy-constructor-properties.js":function(o,n,e){var t=e("./node_modules/core-js/internals/has.js"),i=e("./node_modules/core-js/internals/own-keys.js"),a=e("./node_modules/core-js/internals/object-get-own-property-descriptor.js"),l=e("./node_modules/core-js/internals/object-define-property.js");o.exports=function(r,c){for(var s=i(c),d=l.f,p=a.f,h=0;h<s.length;h++){var u=s[h];t(r,u)||d(r,u,p(c,u))}}},"./node_modules/core-js/internals/correct-prototype-getter.js":function(o,n,e){var t=e("./node_modules/core-js/internals/fails.js");o.exports=!t(function(){function i(){}return i.prototype.constructor=null,Object.getPrototypeOf(new i)!==i.prototype})},"./node_modules/core-js/internals/create-iterator-constructor.js":function(o,n,e){"use strict";var t=e("./node_modules/core-js/internals/iterators-core.js").IteratorPrototype,i=e("./node_modules/core-js/internals/object-create.js"),a=e("./node_modules/core-js/internals/create-property-descriptor.js"),l=e("./node_modules/core-js/internals/set-to-string-tag.js"),r=e("./node_modules/core-js/internals/iterators.js"),c=function(){return this};o.exports=function(s,d,p){var h=d+" Iterator";return s.prototype=i(t,{next:a(1,p)}),l(s,h,!1,!0),r[h]=c,s}},"./node_modules/core-js/internals/create-property-descriptor.js":function(o,n){o.exports=function(e,t){return{enumerable:!(e&1),configurable:!(e&2),writable:!(e&4),value:t}}},"./node_modules/core-js/internals/create-property.js":function(o,n,e){"use strict";var t=e("./node_modules/core-js/internals/to-primitive.js"),i=e("./node_modules/core-js/internals/object-define-property.js"),a=e("./node_modules/core-js/internals/create-property-descriptor.js");o.exports=function(l,r,c){var s=t(r);s in l?i.f(l,s,a(0,c)):l[s]=c}},"./node_modules/core-js/internals/define-iterator.js":function(o,n,e){"use strict";var t=e("./node_modules/core-js/internals/export.js"),i=e("./node_modules/core-js/internals/create-iterator-constructor.js"),a=e("./node_modules/core-js/internals/object-get-prototype-of.js"),l=e("./node_modules/core-js/internals/object-set-prototype-of.js"),r=e("./node_modules/core-js/internals/set-to-string-tag.js"),c=e("./node_modules/core-js/internals/hide.js"),s=e("./node_modules/core-js/internals/redefine.js"),d=e("./node_modules/core-js/internals/well-known-symbol.js"),p=e("./node_modules/core-js/internals/is-pure.js"),h=e("./node_modules/core-js/internals/iterators.js"),u=e("./node_modules/core-js/internals/iterators-core.js"),m=u.IteratorPrototype,f=u.BUGGY_SAFARI_ITERATORS,y=d("iterator"),g="keys",x="values",b="entries",M=function(){return this};o.exports=function(A,P,T,yt,z,ft,ke){i(T,P,yt);var ie=function(k){if(k===z&&N)return N;if(!f&&k in C)return C[k];switch(k){case g:return function(){return new T(this,k)};case x:return function(){return new T(this,k)};case b:return function(){return new T(this,k)}}return function(){return new T(this)}},Ee=P+" Iterator",me=!1,C=A.prototype,U=C[y]||C["@@iterator"]||z&&C[z],N=!f&&U||ie(z),Re=P=="Array"&&C.entries||U,D,$,oe;if(Re&&(D=a(Re.call(new A)),m!==Object.prototype&&D.next&&(!p&&a(D)!==m&&(l?l(D,m):typeof D[y]!="function"&&c(D,y,M)),r(D,Ee,!0,!0),p&&(h[Ee]=M))),z==x&&U&&U.name!==x&&(me=!0,N=function(){return U.call(this)}),(!p||ke)&&C[y]!==N&&c(C,y,N),h[P]=N,z)if($={values:ie(x),keys:ft?N:ie(g),entries:ie(b)},ke)for(oe in $)(f||me||!(oe in C))&&s(C,oe,$[oe]);else t({target:P,proto:!0,forced:f||me},$);return $}},"./node_modules/core-js/internals/descriptors.js":function(o,n,e){var t=e("./node_modules/core-js/internals/fails.js");o.exports=!t(function(){return Object.defineProperty({},"a",{get:function(){return 7}}).a!=7})},"./node_modules/core-js/internals/document-create-element.js":function(o,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/is-object.js"),a=t.document,l=i(a)&&i(a.createElement);o.exports=function(r){return l?a.createElement(r):{}}},"./node_modules/core-js/internals/enum-bug-keys.js":function(o,n){o.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},"./node_modules/core-js/internals/export.js":function(o,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/object-get-own-property-descriptor.js").f,a=e("./node_modules/core-js/internals/hide.js"),l=e("./node_modules/core-js/internals/redefine.js"),r=e("./node_modules/core-js/internals/set-global.js"),c=e("./node_modules/core-js/internals/copy-constructor-properties.js"),s=e("./node_modules/core-js/internals/is-forced.js");o.exports=function(d,p){var h=d.target,u=d.global,m=d.stat,f,y,g,x,b,M;if(u?y=t:m?y=t[h]||r(h,{}):y=(t[h]||{}).prototype,y)for(g in p){if(b=p[g],d.noTargetGet?(M=i(y,g),x=M&&M.value):x=y[g],f=s(u?g:h+(m?".":"#")+g,d.forced),!f&&x!==void 0){if(typeof b==typeof x)continue;c(b,x)}(d.sham||x&&x.sham)&&a(b,"sham",!0),l(y,g,b,d)}}},"./node_modules/core-js/internals/fails.js":function(o,n){o.exports=function(e){try{return!!e()}catch(t){return!0}}},"./node_modules/core-js/internals/function-to-string.js":function(o,n,e){var t=e("./node_modules/core-js/internals/shared.js");o.exports=t("native-function-to-string",Function.toString)},"./node_modules/core-js/internals/get-iterator-method.js":function(o,n,e){var t=e("./node_modules/core-js/internals/classof.js"),i=e("./node_modules/core-js/internals/iterators.js"),a=e("./node_modules/core-js/internals/well-known-symbol.js"),l=a("iterator");o.exports=function(r){if(r!=null)return r[l]||r["@@iterator"]||i[t(r)]}},"./node_modules/core-js/internals/global.js":function(o,n,e){(function(t){var i="object",a=function(l){return l&&l.Math==Math&&l};o.exports=a(typeof globalThis==i&&globalThis)||a(typeof window==i&&window)||a(typeof self==i&&self)||a(typeof t==i&&t)||Function("return this")()}).call(this,e("./node_modules/webpack/buildin/global.js"))},"./node_modules/core-js/internals/has.js":function(o,n){var e={}.hasOwnProperty;o.exports=function(t,i){return e.call(t,i)}},"./node_modules/core-js/internals/hidden-keys.js":function(o,n){o.exports={}},"./node_modules/core-js/internals/hide.js":function(o,n,e){var t=e("./node_modules/core-js/internals/descriptors.js"),i=e("./node_modules/core-js/internals/object-define-property.js"),a=e("./node_modules/core-js/internals/create-property-descriptor.js");o.exports=t?function(l,r,c){return i.f(l,r,a(1,c))}:function(l,r,c){return l[r]=c,l}},"./node_modules/core-js/internals/html.js":function(o,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=t.document;o.exports=i&&i.documentElement},"./node_modules/core-js/internals/ie8-dom-define.js":function(o,n,e){var t=e("./node_modules/core-js/internals/descriptors.js"),i=e("./node_modules/core-js/internals/fails.js"),a=e("./node_modules/core-js/internals/document-create-element.js");o.exports=!t&&!i(function(){return Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a!=7})},"./node_modules/core-js/internals/indexed-object.js":function(o,n,e){var t=e("./node_modules/core-js/internals/fails.js"),i=e("./node_modules/core-js/internals/classof-raw.js"),a="".split;o.exports=t(function(){return!Object("z").propertyIsEnumerable(0)})?function(l){return i(l)=="String"?a.call(l,""):Object(l)}:Object},"./node_modules/core-js/internals/internal-state.js":function(o,n,e){var t=e("./node_modules/core-js/internals/native-weak-map.js"),i=e("./node_modules/core-js/internals/global.js"),a=e("./node_modules/core-js/internals/is-object.js"),l=e("./node_modules/core-js/internals/hide.js"),r=e("./node_modules/core-js/internals/has.js"),c=e("./node_modules/core-js/internals/shared-key.js"),s=e("./node_modules/core-js/internals/hidden-keys.js"),d=i.WeakMap,p,h,u,m=function(A){return u(A)?h(A):p(A,{})},f=function(A){return function(P){var T;if(!a(P)||(T=h(P)).type!==A)throw TypeError("Incompatible receiver, "+A+" required");return T}};if(t){var y=new d,g=y.get,x=y.has,b=y.set;p=function(A,P){return b.call(y,A,P),P},h=function(A){return g.call(y,A)||{}},u=function(A){return x.call(y,A)}}else{var M=c("state");s[M]=!0,p=function(A,P){return l(A,M,P),P},h=function(A){return r(A,M)?A[M]:{}},u=function(A){return r(A,M)}}o.exports={set:p,get:h,has:u,enforce:m,getterFor:f}},"./node_modules/core-js/internals/is-array-iterator-method.js":function(o,n,e){var t=e("./node_modules/core-js/internals/well-known-symbol.js"),i=e("./node_modules/core-js/internals/iterators.js"),a=t("iterator"),l=Array.prototype;o.exports=function(r){return r!==void 0&&(i.Array===r||l[a]===r)}},"./node_modules/core-js/internals/is-forced.js":function(o,n,e){var t=e("./node_modules/core-js/internals/fails.js"),i=/#|\.prototype\./,a=function(d,p){var h=r[l(d)];return h==s?!0:h==c?!1:typeof p=="function"?t(p):!!p},l=a.normalize=function(d){return String(d).replace(i,".").toLowerCase()},r=a.data={},c=a.NATIVE="N",s=a.POLYFILL="P";o.exports=a},"./node_modules/core-js/internals/is-object.js":function(o,n){o.exports=function(e){return typeof e=="object"?e!==null:typeof e=="function"}},"./node_modules/core-js/internals/is-pure.js":function(o,n){o.exports=!1},"./node_modules/core-js/internals/iterators-core.js":function(o,n,e){"use strict";var t=e("./node_modules/core-js/internals/object-get-prototype-of.js"),i=e("./node_modules/core-js/internals/hide.js"),a=e("./node_modules/core-js/internals/has.js"),l=e("./node_modules/core-js/internals/well-known-symbol.js"),r=e("./node_modules/core-js/internals/is-pure.js"),c=l("iterator"),s=!1,d=function(){return this},p,h,u;[].keys&&(u=[].keys(),"next"in u?(h=t(t(u)),h!==Object.prototype&&(p=h)):s=!0),p==null&&(p={}),!r&&!a(p,c)&&i(p,c,d),o.exports={IteratorPrototype:p,BUGGY_SAFARI_ITERATORS:s}},"./node_modules/core-js/internals/iterators.js":function(o,n){o.exports={}},"./node_modules/core-js/internals/native-symbol.js":function(o,n,e){var t=e("./node_modules/core-js/internals/fails.js");o.exports=!!Object.getOwnPropertySymbols&&!t(function(){return!String(Symbol())})},"./node_modules/core-js/internals/native-weak-map.js":function(o,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/function-to-string.js"),a=t.WeakMap;o.exports=typeof a=="function"&&/native code/.test(i.call(a))},"./node_modules/core-js/internals/object-create.js":function(o,n,e){var t=e("./node_modules/core-js/internals/an-object.js"),i=e("./node_modules/core-js/internals/object-define-properties.js"),a=e("./node_modules/core-js/internals/enum-bug-keys.js"),l=e("./node_modules/core-js/internals/hidden-keys.js"),r=e("./node_modules/core-js/internals/html.js"),c=e("./node_modules/core-js/internals/document-create-element.js"),s=e("./node_modules/core-js/internals/shared-key.js"),d=s("IE_PROTO"),p="prototype",h=function(){},u=function(){var m=c("iframe"),f=a.length,y="<",g="script",x=">",b="java"+g+":",M;for(m.style.display="none",r.appendChild(m),m.src=String(b),M=m.contentWindow.document,M.open(),M.write(y+g+x+"document.F=Object"+y+"/"+g+x),M.close(),u=M.F;f--;)delete u[p][a[f]];return u()};o.exports=Object.create||function(f,y){var g;return f!==null?(h[p]=t(f),g=new h,h[p]=null,g[d]=f):g=u(),y===void 0?g:i(g,y)},l[d]=!0},"./node_modules/core-js/internals/object-define-properties.js":function(o,n,e){var t=e("./node_modules/core-js/internals/descriptors.js"),i=e("./node_modules/core-js/internals/object-define-property.js"),a=e("./node_modules/core-js/internals/an-object.js"),l=e("./node_modules/core-js/internals/object-keys.js");o.exports=t?Object.defineProperties:function(c,s){a(c);for(var d=l(s),p=d.length,h=0,u;p>h;)i.f(c,u=d[h++],s[u]);return c}},"./node_modules/core-js/internals/object-define-property.js":function(o,n,e){var t=e("./node_modules/core-js/internals/descriptors.js"),i=e("./node_modules/core-js/internals/ie8-dom-define.js"),a=e("./node_modules/core-js/internals/an-object.js"),l=e("./node_modules/core-js/internals/to-primitive.js"),r=Object.defineProperty;n.f=t?r:function(s,d,p){if(a(s),d=l(d,!0),a(p),i)try{return r(s,d,p)}catch(h){}if("get"in p||"set"in p)throw TypeError("Accessors not supported");return"value"in p&&(s[d]=p.value),s}},"./node_modules/core-js/internals/object-get-own-property-descriptor.js":function(o,n,e){var t=e("./node_modules/core-js/internals/descriptors.js"),i=e("./node_modules/core-js/internals/object-property-is-enumerable.js"),a=e("./node_modules/core-js/internals/create-property-descriptor.js"),l=e("./node_modules/core-js/internals/to-indexed-object.js"),r=e("./node_modules/core-js/internals/to-primitive.js"),c=e("./node_modules/core-js/internals/has.js"),s=e("./node_modules/core-js/internals/ie8-dom-define.js"),d=Object.getOwnPropertyDescriptor;n.f=t?d:function(h,u){if(h=l(h),u=r(u,!0),s)try{return d(h,u)}catch(m){}if(c(h,u))return a(!i.f.call(h,u),h[u])}},"./node_modules/core-js/internals/object-get-own-property-names.js":function(o,n,e){var t=e("./node_modules/core-js/internals/object-keys-internal.js"),i=e("./node_modules/core-js/internals/enum-bug-keys.js"),a=i.concat("length","prototype");n.f=Object.getOwnPropertyNames||function(r){return t(r,a)}},"./node_modules/core-js/internals/object-get-own-property-symbols.js":function(o,n){n.f=Object.getOwnPropertySymbols},"./node_modules/core-js/internals/object-get-prototype-of.js":function(o,n,e){var t=e("./node_modules/core-js/internals/has.js"),i=e("./node_modules/core-js/internals/to-object.js"),a=e("./node_modules/core-js/internals/shared-key.js"),l=e("./node_modules/core-js/internals/correct-prototype-getter.js"),r=a("IE_PROTO"),c=Object.prototype;o.exports=l?Object.getPrototypeOf:function(s){return s=i(s),t(s,r)?s[r]:typeof s.constructor=="function"&&s instanceof s.constructor?s.constructor.prototype:s instanceof Object?c:null}},"./node_modules/core-js/internals/object-keys-internal.js":function(o,n,e){var t=e("./node_modules/core-js/internals/has.js"),i=e("./node_modules/core-js/internals/to-indexed-object.js"),a=e("./node_modules/core-js/internals/array-includes.js"),l=e("./node_modules/core-js/internals/hidden-keys.js"),r=a(!1);o.exports=function(c,s){var d=i(c),p=0,h=[],u;for(u in d)!t(l,u)&&t(d,u)&&h.push(u);for(;s.length>p;)t(d,u=s[p++])&&(~r(h,u)||h.push(u));return h}},"./node_modules/core-js/internals/object-keys.js":function(o,n,e){var t=e("./node_modules/core-js/internals/object-keys-internal.js"),i=e("./node_modules/core-js/internals/enum-bug-keys.js");o.exports=Object.keys||function(l){return t(l,i)}},"./node_modules/core-js/internals/object-property-is-enumerable.js":function(o,n,e){"use strict";var t={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,a=i&&!t.call({1:2},1);n.f=a?function(r){var c=i(this,r);return!!c&&c.enumerable}:t},"./node_modules/core-js/internals/object-set-prototype-of.js":function(o,n,e){var t=e("./node_modules/core-js/internals/validate-set-prototype-of-arguments.js");o.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var i=!1,a={},l;try{l=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set,l.call(a,[]),i=a instanceof Array}catch(r){}return function(c,s){return t(c,s),i?l.call(c,s):c.__proto__=s,c}}():void 0)},"./node_modules/core-js/internals/own-keys.js":function(o,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/object-get-own-property-names.js"),a=e("./node_modules/core-js/internals/object-get-own-property-symbols.js"),l=e("./node_modules/core-js/internals/an-object.js"),r=t.Reflect;o.exports=r&&r.ownKeys||function(s){var d=i.f(l(s)),p=a.f;return p?d.concat(p(s)):d}},"./node_modules/core-js/internals/path.js":function(o,n,e){o.exports=e("./node_modules/core-js/internals/global.js")},"./node_modules/core-js/internals/redefine.js":function(o,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/shared.js"),a=e("./node_modules/core-js/internals/hide.js"),l=e("./node_modules/core-js/internals/has.js"),r=e("./node_modules/core-js/internals/set-global.js"),c=e("./node_modules/core-js/internals/function-to-string.js"),s=e("./node_modules/core-js/internals/internal-state.js"),d=s.get,p=s.enforce,h=String(c).split("toString");i("inspectSource",function(u){return c.call(u)}),(o.exports=function(u,m,f,y){var g=y?!!y.unsafe:!1,x=y?!!y.enumerable:!1,b=y?!!y.noTargetGet:!1;if(typeof f=="function"&&(typeof m=="string"&&!l(f,"name")&&a(f,"name",m),p(f).source=h.join(typeof m=="string"?m:"")),u===t){x?u[m]=f:r(m,f);return}else g?!b&&u[m]&&(x=!0):delete u[m];x?u[m]=f:a(u,m,f)})(Function.prototype,"toString",function(){return typeof this=="function"&&d(this).source||c.call(this)})},"./node_modules/core-js/internals/require-object-coercible.js":function(o,n){o.exports=function(e){if(e==null)throw TypeError("Can't call method on "+e);return e}},"./node_modules/core-js/internals/set-global.js":function(o,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/hide.js");o.exports=function(a,l){try{i(t,a,l)}catch(r){t[a]=l}return l}},"./node_modules/core-js/internals/set-to-string-tag.js":function(o,n,e){var t=e("./node_modules/core-js/internals/object-define-property.js").f,i=e("./node_modules/core-js/internals/has.js"),a=e("./node_modules/core-js/internals/well-known-symbol.js"),l=a("toStringTag");o.exports=function(r,c,s){r&&!i(r=s?r:r.prototype,l)&&t(r,l,{configurable:!0,value:c})}},"./node_modules/core-js/internals/shared-key.js":function(o,n,e){var t=e("./node_modules/core-js/internals/shared.js"),i=e("./node_modules/core-js/internals/uid.js"),a=t("keys");o.exports=function(l){return a[l]||(a[l]=i(l))}},"./node_modules/core-js/internals/shared.js":function(o,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/set-global.js"),a=e("./node_modules/core-js/internals/is-pure.js"),l="__core-js_shared__",r=t[l]||i(l,{});(o.exports=function(c,s){return r[c]||(r[c]=s!==void 0?s:{})})("versions",[]).push({version:"3.1.3",mode:a?"pure":"global",copyright:"\xA9 2019 Denis Pushkarev (zloirock.ru)"})},"./node_modules/core-js/internals/string-at.js":function(o,n,e){var t=e("./node_modules/core-js/internals/to-integer.js"),i=e("./node_modules/core-js/internals/require-object-coercible.js");o.exports=function(a,l,r){var c=String(i(a)),s=t(l),d=c.length,p,h;return s<0||s>=d?r?"":void 0:(p=c.charCodeAt(s),p<55296||p>56319||s+1===d||(h=c.charCodeAt(s+1))<56320||h>57343?r?c.charAt(s):p:r?c.slice(s,s+2):(p-55296<<10)+(h-56320)+65536)}},"./node_modules/core-js/internals/to-absolute-index.js":function(o,n,e){var t=e("./node_modules/core-js/internals/to-integer.js"),i=Math.max,a=Math.min;o.exports=function(l,r){var c=t(l);return c<0?i(c+r,0):a(c,r)}},"./node_modules/core-js/internals/to-indexed-object.js":function(o,n,e){var t=e("./node_modules/core-js/internals/indexed-object.js"),i=e("./node_modules/core-js/internals/require-object-coercible.js");o.exports=function(a){return t(i(a))}},"./node_modules/core-js/internals/to-integer.js":function(o,n){var e=Math.ceil,t=Math.floor;o.exports=function(i){return isNaN(i=+i)?0:(i>0?t:e)(i)}},"./node_modules/core-js/internals/to-length.js":function(o,n,e){var t=e("./node_modules/core-js/internals/to-integer.js"),i=Math.min;o.exports=function(a){return a>0?i(t(a),9007199254740991):0}},"./node_modules/core-js/internals/to-object.js":function(o,n,e){var t=e("./node_modules/core-js/internals/require-object-coercible.js");o.exports=function(i){return Object(t(i))}},"./node_modules/core-js/internals/to-primitive.js":function(o,n,e){var t=e("./node_modules/core-js/internals/is-object.js");o.exports=function(i,a){if(!t(i))return i;var l,r;if(a&&typeof(l=i.toString)=="function"&&!t(r=l.call(i))||typeof(l=i.valueOf)=="function"&&!t(r=l.call(i))||!a&&typeof(l=i.toString)=="function"&&!t(r=l.call(i)))return r;throw TypeError("Can't convert object to primitive value")}},"./node_modules/core-js/internals/uid.js":function(o,n){var e=0,t=Math.random();o.exports=function(i){return"Symbol(".concat(i===void 0?"":i,")_",(++e+t).toString(36))}},"./node_modules/core-js/internals/validate-set-prototype-of-arguments.js":function(o,n,e){var t=e("./node_modules/core-js/internals/is-object.js"),i=e("./node_modules/core-js/internals/an-object.js");o.exports=function(a,l){if(i(a),!t(l)&&l!==null)throw TypeError("Can't set "+String(l)+" as a prototype")}},"./node_modules/core-js/internals/well-known-symbol.js":function(o,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/shared.js"),a=e("./node_modules/core-js/internals/uid.js"),l=e("./node_modules/core-js/internals/native-symbol.js"),r=t.Symbol,c=i("wks");o.exports=function(s){return c[s]||(c[s]=l&&r[s]||(l?r:a)("Symbol."+s))}},"./node_modules/core-js/modules/es.array.from.js":function(o,n,e){var t=e("./node_modules/core-js/internals/export.js"),i=e("./node_modules/core-js/internals/array-from.js"),a=e("./node_modules/core-js/internals/check-correctness-of-iteration.js"),l=!a(function(r){Array.from(r)});t({target:"Array",stat:!0,forced:l},{from:i})},"./node_modules/core-js/modules/es.string.iterator.js":function(o,n,e){"use strict";var t=e("./node_modules/core-js/internals/string-at.js"),i=e("./node_modules/core-js/internals/internal-state.js"),a=e("./node_modules/core-js/internals/define-iterator.js"),l="String Iterator",r=i.set,c=i.getterFor(l);a(String,"String",function(s){r(this,{type:l,string:String(s),index:0})},function(){var d=c(this),p=d.string,h=d.index,u;return h>=p.length?{value:void 0,done:!0}:(u=t(p,h,!0),d.index+=u.length,{value:u,done:!1})})},"./node_modules/webpack/buildin/global.js":function(o,n){var e;e=function(){return this}();try{e=e||Function("return this")()||(0,eval)("this")}catch(t){typeof window=="object"&&(e=window)}o.exports=e},"./src/default-attrs.json":function(o){o.exports={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"}},"./src/icon.js":function(o,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var t=Object.assign||function(u){for(var m=1;m<arguments.length;m++){var f=arguments[m];for(var y in f)Object.prototype.hasOwnProperty.call(f,y)&&(u[y]=f[y])}return u},i=function(){function u(m,f){for(var y=0;y<f.length;y++){var g=f[y];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(m,g.key,g)}}return function(m,f,y){return f&&u(m.prototype,f),y&&u(m,y),m}}(),a=e("./node_modules/classnames/dedupe.js"),l=s(a),r=e("./src/default-attrs.json"),c=s(r);function s(u){return u&&u.__esModule?u:{default:u}}function d(u,m){if(!(u instanceof m))throw new TypeError("Cannot call a class as a function")}var p=function(){function u(m,f){var y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];d(this,u),this.name=m,this.contents=f,this.tags=y,this.attrs=t({},c.default,{class:"feather feather-"+m})}return i(u,[{key:"toSvg",value:function(){var f=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},y=t({},this.attrs,f,{class:(0,l.default)(this.attrs.class,f.class)});return"<svg "+h(y)+">"+this.contents+"</svg>"}},{key:"toString",value:function(){return this.contents}}]),u}();function h(u){return Object.keys(u).map(function(m){return m+'="'+u[m]+'"'}).join(" ")}n.default=p},"./src/icons.js":function(o,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var t=e("./src/icon.js"),i=s(t),a=e("./dist/icons.json"),l=s(a),r=e("./src/tags.json"),c=s(r);function s(d){return d&&d.__esModule?d:{default:d}}n.default=Object.keys(l.default).map(function(d){return new i.default(d,l.default[d],c.default[d])}).reduce(function(d,p){return d[p.name]=p,d},{})},"./src/index.js":function(o,n,e){"use strict";var t=e("./src/icons.js"),i=s(t),a=e("./src/to-svg.js"),l=s(a),r=e("./src/replace.js"),c=s(r);function s(d){return d&&d.__esModule?d:{default:d}}o.exports={icons:i.default,toSvg:l.default,replace:c.default}},"./src/replace.js":function(o,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var t=Object.assign||function(h){for(var u=1;u<arguments.length;u++){var m=arguments[u];for(var f in m)Object.prototype.hasOwnProperty.call(m,f)&&(h[f]=m[f])}return h},i=e("./node_modules/classnames/dedupe.js"),a=c(i),l=e("./src/icons.js"),r=c(l);function c(h){return h&&h.__esModule?h:{default:h}}function s(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(typeof document=="undefined")throw new Error("`feather.replace()` only works in a browser environment.");var u=document.querySelectorAll("[data-feather]");Array.from(u).forEach(function(m){return d(m,h)})}function d(h){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=p(h),f=m["data-feather"];delete m["data-feather"];var y=r.default[f].toSvg(t({},u,m,{class:(0,a.default)(u.class,m.class)})),g=new DOMParser().parseFromString(y,"image/svg+xml"),x=g.querySelector("svg");h.parentNode.replaceChild(x,h)}function p(h){return Array.from(h.attributes).reduce(function(u,m){return u[m.name]=m.value,u},{})}n.default=s},"./src/tags.json":function(o){o.exports={activity:["pulse","health","action","motion"],airplay:["stream","cast","mirroring"],"alert-circle":["warning","alert","danger"],"alert-octagon":["warning","alert","danger"],"alert-triangle":["warning","alert","danger"],"align-center":["text alignment","center"],"align-justify":["text alignment","justified"],"align-left":["text alignment","left"],"align-right":["text alignment","right"],anchor:[],archive:["index","box"],"at-sign":["mention","at","email","message"],award:["achievement","badge"],aperture:["camera","photo"],"bar-chart":["statistics","diagram","graph"],"bar-chart-2":["statistics","diagram","graph"],battery:["power","electricity"],"battery-charging":["power","electricity"],bell:["alarm","notification","sound"],"bell-off":["alarm","notification","silent"],bluetooth:["wireless"],"book-open":["read","library"],book:["read","dictionary","booklet","magazine","library"],bookmark:["read","clip","marker","tag"],box:["cube"],briefcase:["work","bag","baggage","folder"],calendar:["date"],camera:["photo"],cast:["chromecast","airplay"],"chevron-down":["expand"],"chevron-up":["collapse"],circle:["off","zero","record"],clipboard:["copy"],clock:["time","watch","alarm"],"cloud-drizzle":["weather","shower"],"cloud-lightning":["weather","bolt"],"cloud-rain":["weather"],"cloud-snow":["weather","blizzard"],cloud:["weather"],codepen:["logo"],codesandbox:["logo"],code:["source","programming"],coffee:["drink","cup","mug","tea","cafe","hot","beverage"],columns:["layout"],command:["keyboard","cmd","terminal","prompt"],compass:["navigation","safari","travel","direction"],copy:["clone","duplicate"],"corner-down-left":["arrow","return"],"corner-down-right":["arrow"],"corner-left-down":["arrow"],"corner-left-up":["arrow"],"corner-right-down":["arrow"],"corner-right-up":["arrow"],"corner-up-left":["arrow"],"corner-up-right":["arrow"],cpu:["processor","technology"],"credit-card":["purchase","payment","cc"],crop:["photo","image"],crosshair:["aim","target"],database:["storage","memory"],delete:["remove"],disc:["album","cd","dvd","music"],"dollar-sign":["currency","money","payment"],droplet:["water"],edit:["pencil","change"],"edit-2":["pencil","change"],"edit-3":["pencil","change"],eye:["view","watch"],"eye-off":["view","watch","hide","hidden"],"external-link":["outbound"],facebook:["logo","social"],"fast-forward":["music"],figma:["logo","design","tool"],"file-minus":["delete","remove","erase"],"file-plus":["add","create","new"],"file-text":["data","txt","pdf"],film:["movie","video"],filter:["funnel","hopper"],flag:["report"],"folder-minus":["directory"],"folder-plus":["directory"],folder:["directory"],framer:["logo","design","tool"],frown:["emoji","face","bad","sad","emotion"],gift:["present","box","birthday","party"],"git-branch":["code","version control"],"git-commit":["code","version control"],"git-merge":["code","version control"],"git-pull-request":["code","version control"],github:["logo","version control"],gitlab:["logo","version control"],globe:["world","browser","language","translate"],"hard-drive":["computer","server","memory","data"],hash:["hashtag","number","pound"],headphones:["music","audio","sound"],heart:["like","love","emotion"],"help-circle":["question mark"],hexagon:["shape","node.js","logo"],home:["house","living"],image:["picture"],inbox:["email"],instagram:["logo","camera"],key:["password","login","authentication","secure"],layers:["stack"],layout:["window","webpage"],"life-buoy":["help","life ring","support"],link:["chain","url"],"link-2":["chain","url"],linkedin:["logo","social media"],list:["options"],lock:["security","password","secure"],"log-in":["sign in","arrow","enter"],"log-out":["sign out","arrow","exit"],mail:["email","message"],"map-pin":["location","navigation","travel","marker"],map:["location","navigation","travel"],maximize:["fullscreen"],"maximize-2":["fullscreen","arrows","expand"],meh:["emoji","face","neutral","emotion"],menu:["bars","navigation","hamburger"],"message-circle":["comment","chat"],"message-square":["comment","chat"],"mic-off":["record","sound","mute"],mic:["record","sound","listen"],minimize:["exit fullscreen","close"],"minimize-2":["exit fullscreen","arrows","close"],minus:["subtract"],monitor:["tv","screen","display"],moon:["dark","night"],"more-horizontal":["ellipsis"],"more-vertical":["ellipsis"],"mouse-pointer":["arrow","cursor"],move:["arrows"],music:["note"],navigation:["location","travel"],"navigation-2":["location","travel"],octagon:["stop"],package:["box","container"],paperclip:["attachment"],pause:["music","stop"],"pause-circle":["music","audio","stop"],"pen-tool":["vector","drawing"],percent:["discount"],"phone-call":["ring"],"phone-forwarded":["call"],"phone-incoming":["call"],"phone-missed":["call"],"phone-off":["call","mute"],"phone-outgoing":["call"],phone:["call"],play:["music","start"],"pie-chart":["statistics","diagram"],"play-circle":["music","start"],plus:["add","new"],"plus-circle":["add","new"],"plus-square":["add","new"],pocket:["logo","save"],power:["on","off"],printer:["fax","office","device"],radio:["signal"],"refresh-cw":["synchronise","arrows"],"refresh-ccw":["arrows"],repeat:["loop","arrows"],rewind:["music"],"rotate-ccw":["arrow"],"rotate-cw":["arrow"],rss:["feed","subscribe"],save:["floppy disk"],scissors:["cut"],search:["find","magnifier","magnifying glass"],send:["message","mail","email","paper airplane","paper aeroplane"],settings:["cog","edit","gear","preferences"],"share-2":["network","connections"],shield:["security","secure"],"shield-off":["security","insecure"],"shopping-bag":["ecommerce","cart","purchase","store"],"shopping-cart":["ecommerce","cart","purchase","store"],shuffle:["music"],"skip-back":["music"],"skip-forward":["music"],slack:["logo"],slash:["ban","no"],sliders:["settings","controls"],smartphone:["cellphone","device"],smile:["emoji","face","happy","good","emotion"],speaker:["audio","music"],star:["bookmark","favorite","like"],"stop-circle":["media","music"],sun:["brightness","weather","light"],sunrise:["weather","time","morning","day"],sunset:["weather","time","evening","night"],tablet:["device"],tag:["label"],target:["logo","bullseye"],terminal:["code","command line","prompt"],thermometer:["temperature","celsius","fahrenheit","weather"],"thumbs-down":["dislike","bad","emotion"],"thumbs-up":["like","good","emotion"],"toggle-left":["on","off","switch"],"toggle-right":["on","off","switch"],tool:["settings","spanner"],trash:["garbage","delete","remove","bin"],"trash-2":["garbage","delete","remove","bin"],triangle:["delta"],truck:["delivery","van","shipping","transport","lorry"],tv:["television","stream"],twitch:["logo"],twitter:["logo","social"],type:["text"],umbrella:["rain","weather"],unlock:["security"],"user-check":["followed","subscribed"],"user-minus":["delete","remove","unfollow","unsubscribe"],"user-plus":["new","add","create","follow","subscribe"],"user-x":["delete","remove","unfollow","unsubscribe","unavailable"],user:["person","account"],users:["group"],"video-off":["camera","movie","film"],video:["camera","movie","film"],voicemail:["phone"],volume:["music","sound","mute"],"volume-1":["music","sound"],"volume-2":["music","sound"],"volume-x":["music","sound","mute"],watch:["clock","time"],"wifi-off":["disabled"],wifi:["connection","signal","wireless"],wind:["weather","air"],"x-circle":["cancel","close","delete","remove","times","clear"],"x-octagon":["delete","stop","alert","warning","times","clear"],"x-square":["cancel","close","delete","remove","times","clear"],x:["cancel","close","delete","remove","times","clear"],youtube:["logo","video","play"],"zap-off":["flash","camera","lightning"],zap:["flash","camera","lightning"],"zoom-in":["magnifying glass"],"zoom-out":["magnifying glass"]}},"./src/to-svg.js":function(o,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var t=e("./src/icons.js"),i=a(t);function a(r){return r&&r.__esModule?r:{default:r}}function l(r){var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(console.warn("feather.toSvg() is deprecated. Please use feather.icons[name].toSvg() instead."),!r)throw new Error("The required `key` (icon name) parameter is missing.");if(!i.default[r])throw new Error("No icon matching '"+r+"'. See the complete list of icons at https://feathericons.com");return i.default[r].toSvg(c)}n.default=l},0:function(o,n,e){e("./node_modules/core-js/es/array/from.js"),o.exports=e("./src/index.js")}})})});var Ie=De(v=>{"use strict";Object.defineProperty(v,"__esModule",{value:!0});var F=require("obsidian"),we="YYYY-MM-DD",je="gggg-[W]ww",ze="YYYY-MM",Ue="YYYY-[Q]Q",He="YYYY";function G(o){var e,t;let n=window.app.plugins.getPlugin("periodic-notes");return n&&((t=(e=n.settings)==null?void 0:e[o])==null?void 0:t.enabled)}function Q(){var o,n,e,t;try{let{internalPlugins:i,plugins:a}=window.app;if(G("daily")){let{format:s,folder:d,template:p}=((n=(o=a.getPlugin("periodic-notes"))==null?void 0:o.settings)==null?void 0:n.daily)||{};return{format:s||we,folder:(d==null?void 0:d.trim())||"",template:(p==null?void 0:p.trim())||""}}let{folder:l,format:r,template:c}=((t=(e=i.getPluginById("daily-notes"))==null?void 0:e.instance)==null?void 0:t.options)||{};return{format:r||we,folder:(l==null?void 0:l.trim())||"",template:(c==null?void 0:c.trim())||""}}catch(i){console.info("No custom daily note settings found!",i)}}function Z(){var o,n,e,t,i,a,l;try{let r=window.app.plugins,c=(o=r.getPlugin("calendar"))==null?void 0:o.options,s=(e=(n=r.getPlugin("periodic-notes"))==null?void 0:n.settings)==null?void 0:e.weekly;if(G("weekly"))return{format:s.format||je,folder:((t=s.folder)==null?void 0:t.trim())||"",template:((i=s.template)==null?void 0:i.trim())||""};let d=c||{};return{format:d.weeklyNoteFormat||je,folder:((a=d.weeklyNoteFolder)==null?void 0:a.trim())||"",template:((l=d.weeklyNoteTemplate)==null?void 0:l.trim())||""}}catch(r){console.info("No custom weekly note settings found!",r)}}function J(){var n,e,t,i;let o=window.app.plugins;try{let a=G("monthly")&&((e=(n=o.getPlugin("periodic-notes"))==null?void 0:n.settings)==null?void 0:e.monthly)||{};return{format:a.format||ze,folder:((t=a.folder)==null?void 0:t.trim())||"",template:((i=a.template)==null?void 0:i.trim())||""}}catch(a){console.info("No custom monthly note settings found!",a)}}function X(){var n,e,t,i;let o=window.app.plugins;try{let a=G("quarterly")&&((e=(n=o.getPlugin("periodic-notes"))==null?void 0:n.settings)==null?void 0:e.quarterly)||{};return{format:a.format||Ue,folder:((t=a.folder)==null?void 0:t.trim())||"",template:((i=a.template)==null?void 0:i.trim())||""}}catch(a){console.info("No custom quarterly note settings found!",a)}}function q(){var n,e,t,i;let o=window.app.plugins;try{let a=G("yearly")&&((e=(n=o.getPlugin("periodic-notes"))==null?void 0:n.settings)==null?void 0:e.yearly)||{};return{format:a.format||He,folder:((t=a.folder)==null?void 0:t.trim())||"",template:((i=a.template)==null?void 0:i.trim())||""}}catch(a){console.info("No custom yearly note settings found!",a)}}function We(...o){let n=[];for(let t=0,i=o.length;t<i;t++)n=n.concat(o[t].split("/"));let e=[];for(let t=0,i=n.length;t<i;t++){let a=n[t];!a||a==="."||e.push(a)}return n[0]===""&&e.unshift(""),e.join("/")}function At(o){let n=o.substring(o.lastIndexOf("/")+1);return n.lastIndexOf(".")!=-1&&(n=n.substring(0,n.lastIndexOf("."))),n}async function Ft(o){let n=o.replace(/\\/g,"/").split("/");if(n.pop(),n.length){let e=We(...n);window.app.vault.getAbstractFileByPath(e)||await window.app.vault.createFolder(e)}}async function _(o,n){n.endsWith(".md")||(n+=".md");let e=F.normalizePath(We(o,n));return await Ft(e),e}async function H(o){let{metadataCache:n,vault:e}=window.app,t=F.normalizePath(o);if(t==="/")return Promise.resolve(["",null]);try{let i=n.getFirstLinkpathDest(t,""),a=await e.cachedRead(i),l=window.app.foldManager.load(i);return[a,l]}catch(i){return console.error(`Failed to read the daily note template '${t}'`,i),new F.Notice("Failed to read the daily note template"),["",null]}}function O(o,n="day"){let e=o.clone().startOf(n).format();return`${n}-${e}`}function Be(o){return o.replace(/\[[^\]]*\]/g,"")}function Pt(o,n){if(n==="week"){let e=Be(o);return/w{1,2}/i.test(e)&&(/M{1,4}/.test(e)||/D{1,4}/.test(e))}return!1}function W(o,n){return $e(o.basename,n)}function It(o,n){return $e(At(o),n)}function $e(o,n){let t={day:Q,week:Z,month:J,quarter:X,year:q}[n]().format.split("/").pop(),i=window.moment(o,t,!0);if(!i.isValid())return null;if(Pt(t,n)&&n==="week"){let a=Be(t);if(/w{1,2}/i.test(a))return window.moment(o,t.replace(/M{1,4}/g,"").replace(/D{1,4}/g,""),!1)}return i}var be=class extends Error{};async function Ye(o){let n=window.app,{vault:e}=n,t=window.moment,{template:i,format:a,folder:l}=Q(),[r,c]=await H(i),s=o.format(a),d=await _(l,s);try{let p=await e.create(d,r.replace(/{{\s*date\s*}}/gi,s).replace(/{{\s*time\s*}}/gi,t().format("HH:mm")).replace(/{{\s*title\s*}}/gi,s).replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(h,u,m,f,y,g)=>{let x=t(),b=o.clone().set({hour:x.get("hour"),minute:x.get("minute"),second:x.get("second")});return m&&b.add(parseInt(f,10),y),g?b.format(g.substring(1).trim()):b.format(a)}).replace(/{{\s*yesterday\s*}}/gi,o.clone().subtract(1,"day").format(a)).replace(/{{\s*tomorrow\s*}}/gi,o.clone().add(1,"d").format(a)));return n.foldManager.save(p,c),p}catch(p){console.error(`Failed to create file: '${d}'`,p),new F.Notice("Unable to create new file.")}}function St(o,n){var e;return(e=n[O(o,"day")])!=null?e:null}function Ot(){let{vault:o}=window.app,{folder:n}=Q(),e=o.getAbstractFileByPath(F.normalizePath(n));if(!e)throw new be("Failed to find daily notes folder");let t={};return F.Vault.recurseChildren(e,i=>{if(i instanceof F.TFile){let a=W(i,"day");if(a){let l=O(a,"day");t[l]=i}}}),t}var Me=class extends Error{};function Ct(){let{moment:o}=window,n=o.localeData()._week.dow,e=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"];for(;n;)e.push(e.shift()),n--;return e}function Tt(o){return Ct().indexOf(o.toLowerCase())}async function Ke(o){let{vault:n}=window.app,{template:e,format:t,folder:i}=Z(),[a,l]=await H(e),r=o.format(t),c=await _(i,r);try{let s=await n.create(c,a.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(d,p,h,u,m,f)=>{let y=window.moment(),g=o.clone().set({hour:y.get("hour"),minute:y.get("minute"),second:y.get("second")});return h&&g.add(parseInt(u,10),m),f?g.format(f.substring(1).trim()):g.format(t)}).replace(/{{\s*title\s*}}/gi,r).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*(sunday|monday|tuesday|wednesday|thursday|friday|saturday)\s*:(.*?)}}/gi,(d,p,h)=>{let u=Tt(p);return o.weekday(u).format(h.trim())}));return window.app.foldManager.save(s,l),s}catch(s){console.error(`Failed to create file: '${c}'`,s),new F.Notice("Unable to create new file.")}}function kt(o,n){var e;return(e=n[O(o,"week")])!=null?e:null}function Et(){let o={};if(!Qe())return o;let{vault:n}=window.app,{folder:e}=Z(),t=n.getAbstractFileByPath(F.normalizePath(e));if(!t)throw new Me("Failed to find weekly notes folder");return F.Vault.recurseChildren(t,i=>{if(i instanceof F.TFile){let a=W(i,"week");if(a){let l=O(a,"week");o[l]=i}}}),o}var Ae=class extends Error{};async function Ge(o){let{vault:n}=window.app,{template:e,format:t,folder:i}=J(),[a,l]=await H(e),r=o.format(t),c=await _(i,r);try{let s=await n.create(c,a.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(d,p,h,u,m,f)=>{let y=window.moment(),g=o.clone().set({hour:y.get("hour"),minute:y.get("minute"),second:y.get("second")});return h&&g.add(parseInt(u,10),m),f?g.format(f.substring(1).trim()):g.format(t)}).replace(/{{\s*date\s*}}/gi,r).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,r));return window.app.foldManager.save(s,l),s}catch(s){console.error(`Failed to create file: '${c}'`,s),new F.Notice("Unable to create new file.")}}function Rt(o,n){var e;return(e=n[O(o,"month")])!=null?e:null}function Nt(){let o={};if(!Ze())return o;let{vault:n}=window.app,{folder:e}=J(),t=n.getAbstractFileByPath(F.normalizePath(e));if(!t)throw new Ae("Failed to find monthly notes folder");return F.Vault.recurseChildren(t,i=>{if(i instanceof F.TFile){let a=W(i,"month");if(a){let l=O(a,"month");o[l]=i}}}),o}var Fe=class extends Error{};async function Dt(o){let{vault:n}=window.app,{template:e,format:t,folder:i}=X(),[a,l]=await H(e),r=o.format(t),c=await _(i,r);try{let s=await n.create(c,a.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(d,p,h,u,m,f)=>{let y=window.moment(),g=o.clone().set({hour:y.get("hour"),minute:y.get("minute"),second:y.get("second")});return h&&g.add(parseInt(u,10),m),f?g.format(f.substring(1).trim()):g.format(t)}).replace(/{{\s*date\s*}}/gi,r).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,r));return window.app.foldManager.save(s,l),s}catch(s){console.error(`Failed to create file: '${c}'`,s),new F.Notice("Unable to create new file.")}}function Vt(o,n){var e;return(e=n[O(o,"quarter")])!=null?e:null}function Lt(){let o={};if(!Je())return o;let{vault:n}=window.app,{folder:e}=X(),t=n.getAbstractFileByPath(F.normalizePath(e));if(!t)throw new Fe("Failed to find quarterly notes folder");return F.Vault.recurseChildren(t,i=>{if(i instanceof F.TFile){let a=W(i,"quarter");if(a){let l=O(a,"quarter");o[l]=i}}}),o}var Pe=class extends Error{};async function zt(o){let{vault:n}=window.app,{template:e,format:t,folder:i}=q(),[a,l]=await H(e),r=o.format(t),c=await _(i,r);try{let s=await n.create(c,a.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(d,p,h,u,m,f)=>{let y=window.moment(),g=o.clone().set({hour:y.get("hour"),minute:y.get("minute"),second:y.get("second")});return h&&g.add(parseInt(u,10),m),f?g.format(f.substring(1).trim()):g.format(t)}).replace(/{{\s*date\s*}}/gi,r).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,r));return window.app.foldManager.save(s,l),s}catch(s){console.error(`Failed to create file: '${c}'`,s),new F.Notice("Unable to create new file.")}}function Ut(o,n){var e;return(e=n[O(o,"year")])!=null?e:null}function Ht(){let o={};if(!Xe())return o;let{vault:n}=window.app,{folder:e}=q(),t=n.getAbstractFileByPath(F.normalizePath(e));if(!t)throw new Pe("Failed to find yearly notes folder");return F.Vault.recurseChildren(t,i=>{if(i instanceof F.TFile){let a=W(i,"year");if(a){let l=O(a,"year");o[l]=i}}}),o}function Wt(){var t,i;let{app:o}=window,n=o.internalPlugins.plugins["daily-notes"];if(n&&n.enabled)return!0;let e=o.plugins.getPlugin("periodic-notes");return e&&((i=(t=e.settings)==null?void 0:t.daily)==null?void 0:i.enabled)}function Qe(){var e,t;let{app:o}=window;if(o.plugins.getPlugin("calendar"))return!0;let n=o.plugins.getPlugin("periodic-notes");return n&&((t=(e=n.settings)==null?void 0:e.weekly)==null?void 0:t.enabled)}function Ze(){var e,t;let{app:o}=window,n=o.plugins.getPlugin("periodic-notes");return n&&((t=(e=n.settings)==null?void 0:e.monthly)==null?void 0:t.enabled)}function Je(){var e,t;let{app:o}=window,n=o.plugins.getPlugin("periodic-notes");return n&&((t=(e=n.settings)==null?void 0:e.quarterly)==null?void 0:t.enabled)}function Xe(){var e,t;let{app:o}=window,n=o.plugins.getPlugin("periodic-notes");return n&&((t=(e=n.settings)==null?void 0:e.yearly)==null?void 0:t.enabled)}function Bt(o){let n={day:Q,week:Z,month:J,quarter:X,year:q}[o];return n()}function $t(o,n){return{day:Ye,month:Ge,week:Ke}[o](n)}v.DEFAULT_DAILY_NOTE_FORMAT=we;v.DEFAULT_MONTHLY_NOTE_FORMAT=ze;v.DEFAULT_QUARTERLY_NOTE_FORMAT=Ue;v.DEFAULT_WEEKLY_NOTE_FORMAT=je;v.DEFAULT_YEARLY_NOTE_FORMAT=He;v.appHasDailyNotesPluginLoaded=Wt;v.appHasMonthlyNotesPluginLoaded=Ze;v.appHasQuarterlyNotesPluginLoaded=Je;v.appHasWeeklyNotesPluginLoaded=Qe;v.appHasYearlyNotesPluginLoaded=Xe;v.createDailyNote=Ye;v.createMonthlyNote=Ge;v.createPeriodicNote=$t;v.createQuarterlyNote=Dt;v.createWeeklyNote=Ke;v.createYearlyNote=zt;v.getAllDailyNotes=Ot;v.getAllMonthlyNotes=Nt;v.getAllQuarterlyNotes=Lt;v.getAllWeeklyNotes=Et;v.getAllYearlyNotes=Ht;v.getDailyNote=St;v.getDailyNoteSettings=Q;v.getDateFromFile=W;v.getDateFromPath=It;v.getDateUID=O;v.getMonthlyNote=Rt;v.getMonthlyNoteSettings=J;v.getPeriodicNoteSettings=Bt;v.getQuarterlyNote=Vt;v.getQuarterlyNoteSettings=X;v.getTemplateInfo=H;v.getWeeklyNote=kt;v.getWeeklyNoteSettings=Z;v.getYearlyNote=Ut;v.getYearlyNoteSettings=q});var Xt={};jt(Xt,{default:()=>ge});module.exports=bt(Xt);var j=require("obsidian");var Mt=xe(Le()),E=require("obsidian");var K=o=>o.match(/\.MD$|\.md$/m)?o.split(/\.MD$|\.md$/m).slice(0,-1).join(".md"):o;var R=xe(Ie());var qe=require("obsidian"),le=class o{static getBlock(n,e,t){var s,d;let i=e.getCursor("to"),a=n.metadataCache.getFileCache(t),l=a==null?void 0:a.sections;if(!l||l.length===0){console.log("error reading FileCache (empty file?)");return}let r=l.findIndex(p=>p.position.start.line>i.line),c=r>0?l[r-1]:l[l.length-1];return(c==null?void 0:c.type)=="list"&&(c=(d=(s=a.listItems)==null?void 0:s.find(p=>p.position.start.line<=i.line&&p.position.end.line>=i.line))!=null?d:c),c}static getIdOfBlock(n,e){let t=e.id;if(t)return t;let i=e.position.end,a={ch:i.col,line:i.line},l=Math.random().toString(36).substring(2,8),r=o.shouldInsertAfter(e)?`

`:" ";return n.replaceRange(`${r}^${l}`,a),l}static shouldInsertAfter(n){if(n.type)return["blockquote","code","table","heading","comment","footnoteDefinition"].includes(n.type)}static getBlockId(n){let e=n.workspace.getActiveViewOfType(qe.MarkdownView);if(e){let t=e.editor,i=e.file,a=this.getBlock(n,t,i);if(a)return this.getIdOfBlock(t,a)}}};var _e={openFileOnWrite:!0,openDailyInNewPane:!1,openFileOnWriteInNewPane:!1,openFileWithoutWriteInNewPane:!1,idField:"id",useUID:!1,addFilepathWhenUsingUID:!1,allowEval:!1,includeVaultName:!0,vaultParam:"name"};var et=require("obsidian"),tt=xe(Ie());function nt(...o){let n=[];for(let t=0,i=o.length;t<i;t++)n=n.concat(o[t].split("/"));let e=[];for(let t=0,i=n.length;t<i;t++){let a=n[t];!a||a==="."||e.push(a)}return n[0]===""&&e.unshift(""),e.join("/")}async function Yt(o,n){n.endsWith(".md")||(n+=".md");let e=(0,et.normalizePath)(nt(o,n));return await Kt(e),e}async function Kt(o){let n=o.replace(/\\/g,"/").split("/");if(n.pop(),n.length){let e=nt(...n);window.app.vault.getAbstractFileByPath(e)||await window.app.vault.createFolder(e)}}async function it(o){let{format:n,folder:e}=(0,tt.getDailyNoteSettings)(),t=o.format(n);return await Yt(e,t)}var w=require("obsidian");var ot=require("obsidian"),B=class extends ot.SuggestModal{constructor(e,t){super(e.app);this.file=t;this.modes=[null,"overwrite","append","prepend"];this.plugin=e,this.setPlaceholder("Type your data to be written to the file or leave it empty to just open it")}getSuggestions(e){e==""&&(e=null);let t=[];for(let i of this.modes)if(!(i==="overwrite"&&!e)){let a;e?i?a=`Write "${e}" in ${i} mode`:a=`Write "${e}"`:i?a=`Open in ${i} mode`:a="Open",t.push({data:e,display:a,mode:i,func:()=>{this.file?this.plugin.tools.copyURI({filepath:this.file,data:e,mode:i}):this.plugin.tools.copyURI({daily:"true",data:e,mode:i})}})}return t}renderSuggestion(e,t){t.innerText=e.display}onChooseSuggestion(e,t){e.func()}};var at=require("obsidian"),V=class extends at.FuzzySuggestModal{constructor(e,t,i=!0){super(e.app);this.placeHolder=t;this.allowNoFile=i;this.plugin=e,this.setPlaceholder(this.placeHolder)}getItems(){let e=[];this.allowNoFile&&e.push({display:"<Don't specify a file>",source:void 0});let t=this.app.workspace.getActiveFile();return t&&e.push({display:"<Current file>",source:t.path}),[...e,...this.app.vault.getFiles().map(i=>({display:i.path,source:i.path}))]}getItemText(e){return e.display}onChooseItem(e,t){}};var lt=require("obsidian");function Se(o){return o.viewmode?{state:{mode:o.viewmode,source:o.viewmode=="source"}}:void 0}function ee(o){return navigator.clipboard.writeText(o)}function re(o,n){var a;let e=(a=n.parent)==null?void 0:a.path,t=e==="/"?"":e,i=n.name;for(let l=1;l<100;l++){let r=K(i),c=t+(t==""?"":"/")+r+` ${l}.md`;if(!(o.vault.getAbstractFileByPath(c)!==null))return c}}function rt(o,n){let e=new URL(o.vault.getResourcePath(n));return e.host="localhosthostlocal",e.protocol="file",e.search="",e.pathname=decodeURIComponent(e.pathname),e.toString().replace("/localhosthostlocal/","/")}function Oe(o,n,e){var l,r;let t=o.metadataCache.getFileCache(n),i=t.sections,a=(l=t.headings)==null?void 0:l.find(c=>c.heading===e);if(a){let c=i.findIndex(u=>u.type==="heading"&&u.position.start.line===a.position.start.line),s=i.slice(c+1),d=s==null?void 0:s.findIndex(u=>u.type==="heading");return{lastLine:((r=s[(d!==-1?d:s.length)-1])!=null?r:i[c]).position.end.line+1,firstLine:i[c].position.end.line+1}}else new lt.Notice("Can't find heading")}var te=class{constructor(n){this.plugin=n;this.app=this.plugin.app}get tools(){return this.plugin.tools}handlePluginManagement(n){if(n["enable-plugin"]){let e=n["enable-plugin"];e in this.app.plugins.manifests&&!this.app.plugins.getPlugin(e)?(this.app.plugins.enablePluginAndSave(e),new w.Notice(`Enabled ${e}`)):this.app.internalPlugins.plugins[e]&&(this.app.internalPlugins.plugins[e].enable(!0),new w.Notice(`Enabled ${e}`))}else if(n["disable-plugin"]){let e=n["disable-plugin"];this.app.plugins.getPlugin(e)?(this.app.plugins.disablePluginAndSave(e),new w.Notice(`Disabled ${e}`)):this.app.internalPlugins.plugins[e]&&(this.app.internalPlugins.plugins[e].disable(!0),new w.Notice(`Disabled ${e}`))}}handleFrontmatterKey(n){var a;let e=n.frontmatterkey,t=this.app.vault.getAbstractFileByPath((a=n.filepath)!=null?a:this.app.workspace.getActiveFile().path);if(!(t instanceof w.TFile))return;let i=this.app.metadataCache.getFileCache(t).frontmatter;if(n.data){let l=n.data;try{l=JSON.parse(l)}catch(r){l=`"${l}"`,l=JSON.parse(l)}this.app.fileManager.processFrontMatter(t,r=>{if(e.startsWith("[")&&e.endsWith("]")){let c=e.substring(1,e.length-1).split(","),s=r;for(let d=0;d<c.length;d++){let p=c[d];if(s instanceof Array){let h=parseInt(p);Number.isNaN(h)&&(s=s.find(u=>u==p)),d==c.length-1?s[parseInt(p)]=l:s=s[parseInt(p)]}else d==c.length-1?s[p]=l:s=s[p]}}else r[e]=l})}else{let l;if(e.startsWith("[")&&e.endsWith("]")){let r=e.substring(1,e.length-1).split(","),c=i;for(let s of r)if(c instanceof Array){let d=parseInt(s);Number.isNaN(d)&&(c=c.find(p=>p==s)),c=c[parseInt(s)]}else c=c[s];l=c}else l=i[e];ee(l)}}handleWorkspace(n){let e=this.app.internalPlugins.getEnabledPluginById("workspaces");if(!e)new w.Notice("Workspaces plugin is not enabled"),this.plugin.failure(n);else{if(n.saveworkspace=="true"){let t=e.activeWorkspace;e.saveWorkspace(t),new w.Notice(`Saved current workspace to ${t}`)}n.clipboard&&n.clipboard!="false"?this.tools.copyURI({workspace:e.activeWorkspace}):n.workspace!=null&&e.loadWorkspace(n.workspace),this.plugin.success(n)}}async handleCommand(n){if(n.filepath)if(n.mode){if(n.mode=="new"){let t=this.app.metadataCache.getFirstLinkpathDest(n.filepath,"/");t instanceof w.TFile&&(n.filepath=re(this.app,t))}await this.plugin.open({file:n.filepath,mode:"source",parameters:n});let e=this.app.workspace.getActiveViewOfType(w.MarkdownView);if(e){let t=e.editor,i=t.getValue();if(n.mode==="append"){t.setValue(i+`
`);let a=t.lineCount();t.setCursor({ch:0,line:a})}else n.mode==="prepend"?(t.setValue(`
`+i),t.setCursor({ch:0,line:0})):n.mode==="overwrite"&&t.setValue("")}}else n.line!=null||n.column!=null||n.offset!=null?(await this.plugin.open({file:n.filepath,mode:"source",parameters:n}),await this.plugin.setCursorInLine(n)):await this.plugin.open({file:n.filepath,setting:this.plugin.settings.openFileWithoutWriteInNewPane,parameters:n});else(n.openmode||n.viewmode)&&await this.plugin.open({parameters:n});if(n.commandid)this.app.commands.executeCommandById(n.commandid);else if(n.commandname){let e=this.app.commands.commands;for(let t in e)if(e[t].name===n.commandname){e[t].callback?await e[t].callback():e[t].checkCallback(!1);break}}if(n.confirm&&n.confirm!="false"){await new Promise(t=>setTimeout(t,750));let e=document.querySelector(".mod-cta:not([style*='display: none'])");e.click instanceof Function&&e.click()}this.plugin.success(n)}async handleEval(n){if(n.filepath)if(n.mode){if(n.mode=="new"){let t=this.app.metadataCache.getFirstLinkpathDest(n.filepath,"/");t instanceof w.TFile&&(n.filepath=re(this.app,t))}await this.plugin.open({file:n.filepath,mode:"source",parameters:n});let e=this.app.workspace.getActiveViewOfType(w.MarkdownView);if(e){let t=e.editor,i=t.getValue();if(n.mode==="append"){t.setValue(i+`
`);let a=t.lineCount();t.setCursor({ch:0,line:a})}else n.mode==="prepend"?(t.setValue(`
`+i),t.setCursor({ch:0,line:0})):n.mode==="overwrite"&&t.setValue("")}}else n.line!=null||n.column!=null||n.offset!=null?(await this.plugin.open({file:n.filepath,mode:"source",parameters:n}),await this.plugin.setCursorInLine(n)):await this.plugin.open({file:n.filepath,setting:this.plugin.settings.openFileWithoutWriteInNewPane,parameters:n});this.plugin.settings.allowEval?((0,eval)(n.eval),this.plugin.success(n)):(new w.Notice("Eval is not allowed. Please enable it in the settings."),this.plugin.failure(n))}async handleDoesFileExist(n){let e=await this.app.vault.adapter.exists(n.filepath);ee((e?1:0).toString()),this.plugin.success(n)}async handleSearchAndReplace(n){let e;if(n.filepath){let t=this.app.vault.getAbstractFileByPath(n.filepath);t instanceof w.TFile&&(e=t)}else e=this.app.workspace.getActiveFile();if(e){let t=await this.app.vault.read(e);if(n.searchregex)try{let[,,i,a]=n.searchregex.match(/(\/?)(.+)\1([a-z]*)/i),l=new RegExp(i,a);t=t.replace(l,n.replace),this.plugin.success(n)}catch(i){new w.Notice(`Can't parse ${n.searchregex} as RegEx`),this.plugin.failure(n)}else t=t.replaceAll(n.search,n.replace),this.plugin.success(n);await this.plugin.writeAndOpenFile(e.path,t,n)}else new w.Notice("Cannot find file"),this.plugin.failure(n)}async handleSearch(n){n.filepath&&await this.plugin.open({file:n.filepath,parameters:n});let e=this.app.workspace.getActiveViewOfType(w.FileView);e.currentMode.showSearch();let t=e.currentMode.search;t.searchInputEl.value=n.search,t.searchInputEl.dispatchEvent(new Event("input"))}async handleWrite(n,e=!1){var i;let t;if(n.filepath?t=this.app.vault.getAbstractFileByPath(n.filepath):t=this.app.workspace.getActiveFile(),n.filepath||t){let a,l=(i=n.filepath)!=null?i:t.path;n.mode==="overwrite"?(a=await this.plugin.writeAndOpenFile(l,n.data,n),this.plugin.success(n)):n.mode==="prepend"?(t instanceof w.TFile?a=await this.plugin.prepend(t,n):a=await this.plugin.prepend(l,n),this.plugin.success(n)):n.mode==="append"?(t instanceof w.TFile?a=await this.plugin.append(t,n):a=await this.plugin.append(l,n),this.plugin.success(n)):n.mode==="new"?t instanceof w.TFile?(a=await this.plugin.writeAndOpenFile(re(this.app,t),n.data,n),this.plugin.hookSuccess(n,a)):(a=await this.plugin.writeAndOpenFile(l,n.data,n),this.plugin.hookSuccess(n,a)):!e&&t instanceof w.TFile?(new w.Notice("File already exists"),this.plugin.openExistingFileAndSetCursor(t.path,n),this.plugin.failure(n)):(a=await this.plugin.writeAndOpenFile(l,n.data,n),this.plugin.success(n)),n.uid&&this.tools.writeUIDToFile(a,n.uid)}else new w.Notice("Cannot find file"),this.plugin.failure(n)}async handleOpen(n){if(n.heading!=null){await this.plugin.open({file:n.filepath+"#"+n.heading,setting:this.plugin.settings.openFileWithoutWriteInNewPane,parameters:n});let e=this.app.workspace.getActiveViewOfType(w.MarkdownView);if(!e)return;let i=this.app.metadataCache.getFileCache(e.file).headings.find(a=>a.heading===n.heading);e.editor.focus(),e.editor.setCursor({line:i.position.start.line+1,ch:0})}else if(n.block!=null){await this.plugin.open({file:n.filepath+"#^"+n.block,setting:this.plugin.settings.openFileWithoutWriteInNewPane,parameters:n});let e=this.app.workspace.getActiveViewOfType(w.MarkdownView);if(!e)return;let i=this.app.metadataCache.getFileCache(e.file).blocks[n.block.toLowerCase()];e.editor.focus(),i&&e.editor.setCursor({line:i.position.start.line,ch:0})}else await this.plugin.open({file:n.filepath,setting:this.plugin.settings.openFileWithoutWriteInNewPane,parameters:n}),(n.line!=null||n.column!=null||n.offset!=null)&&await this.plugin.setCursorInLine(n);if(n.mode!=null&&await this.plugin.setCursor(n),n.uid){let e=this.app.workspace.getActiveViewOfType(w.MarkdownView);this.tools.writeUIDToFile(e.file,n.uid)}this.plugin.success(n)}async handleOpenBlock(n){let e=this.tools.getFileFromBlockID(n.block);e&&await this.plugin.chooseHandler({...n,filepath:e.path},!1)}handleCopyFileURI(n,e){let t=this.app.workspace.getActiveViewOfType(w.FileView);if(!(!t&&!e)){if(t instanceof w.MarkdownView){let i=t.editor.getCursor(),a=this.app.metadataCache.getFileCache(t.file);if(a.headings){for(let l of a.headings)if(l.position.start.line<=i.line&&l.position.end.line>=i.line){this.tools.copyURI({filepath:t.file.path,heading:l.heading});return}}if(a.blocks)for(let l of Object.keys(a.blocks)){let r=a.blocks[l];if(r.position.start.line<=i.line&&r.position.end.line>=i.line){this.tools.copyURI({filepath:t.file.path,block:r.id});return}}}if(n){let i=e!=null?e:this.app.workspace.getActiveFile();if(!i){new w.Notice("No file opened");return}this.tools.copyURI({filepath:i.path})}else{let i=new V(this.plugin,"Choose a file",!1);i.open(),i.onChooseItem=(a,l)=>{new B(this.plugin,a.source).open()}}}}handleOpenSettings(n){if(this.app.setting.containerEl.parentElement===null&&this.app.setting.open(),n.settingid=="plugin-browser"?(this.app.setting.openTabById("community-plugins"),this.app.setting.activeTab.containerEl.find(".mod-cta").click()):n.settingid=="theme-browser"?(this.app.setting.openTabById("appearance"),this.app.setting.activeTab.containerEl.find(".mod-cta").click()):this.app.setting.openTabById(n.settingid),n.settingsection){let e=this.app.setting.tabContentContainer.querySelectorAll("*"),t=Array.prototype.find.call(e,i=>i.textContent==n.settingsection);t&&t.scrollIntoView()}this.plugin.success(n)}async handleUpdatePlugins(n){new w.Notice("Checking for updates\u2026"),await this.app.plugins.checkForUpdates(),Object.keys(this.app.plugins.updates).length>0&&(n.settingid="community-plugins",this.handleOpenSettings(n),this.app.setting.activeTab.containerEl.findAll(".mod-cta").last().click()),this.plugin.success(n)}async handleBookmarks(n){let e=this.app.internalPlugins.getEnabledPluginById("bookmarks"),i=e.getBookmarks().find(l=>l.title==n.bookmark),a;n.openmode=="true"||n.openmode=="false"?a=n.openmode=="true":a=n.openmode,e.openBookmark(i,a)}async handleCanvas(n){n.filepath&&await this.plugin.open({file:n.filepath,setting:this.plugin.settings.openFileWithoutWriteInNewPane,parameters:n});let e=this.app.workspace.activeLeaf.view;if(e.getViewType()!="canvas"){new w.Notice("Active view is not a canvas");return}let t=e;if(n.canvasnodes){let i=n.canvasnodes.split(","),a=t.canvas.nodes,l=i.map(c=>a.get(c)),r=t.canvas.selection;t.canvas.updateSelection(()=>{for(let c of l)r.add(c)}),t.canvas.zoomToSelection()}if(n.canvasviewport){let[i,a,l]=n.canvasviewport.split(",");if(i!="-")if(i.startsWith("--")||i.startsWith("++")){let r=t.canvas.tx+Number(i.substring(1));t.canvas.tx=r}else t.canvas.tx=Number(i);if(a!="-")if(a.startsWith("--")||a.startsWith("++")){let r=t.canvas.ty+Number(a.substring(1));t.canvas.ty=r}else t.canvas.ty=Number(a);if(l!="-")if(l.startsWith("--")||l.startsWith("++")){let r=t.canvas.tZoom+Number(l.substring(1));t.canvas.tZoom=r}else t.canvas.tZoom=Number(l);t.canvas.markViewportChanged()}}};var st=require("obsidian"),se=class extends st.FuzzySuggestModal{constructor(e,t){super(e.app);this.plugin=e,this.file=t}getItems(){let e=this.app.commands.commands;return Object.keys(e).map(i=>({id:e[i].id,name:e[i].name}))}getItemText(e){return e.name}onChooseItem(e,t){this.plugin.tools.copyURI({filepath:this.file,commandid:e.id})}};var ct=require("obsidian"),ce=class extends ct.SuggestModal{constructor(e,t,i){super(e.app);this.search=t;this.filepath=i;this.emptyText="Empty text (replace with nothing)";this.plugin=e,this.setPlaceholder("Replacement text")}getSuggestions(e){return e===""&&(e=this.emptyText),[e]}renderSuggestion(e,t){t.innerText=e}onChooseSuggestion(e,t){this.search.isRegEx?this.plugin.tools.copyURI({filepath:this.filepath,searchregex:this.search.source,replace:e==this.emptyText?"":e}):this.plugin.tools.copyURI({filepath:this.filepath,search:this.search.source,replace:e==this.emptyText?"":e})}};var dt=require("obsidian"),de=class extends dt.SuggestModal{constructor(e){super(e.app);this.plugin=e,this.setPlaceholder("Searched text. RegEx is supported")}getSuggestions(e){e===""&&(e="...");let t;try{t=new RegExp(e)}catch(i){}return[{source:e,isRegEx:!1,display:e},{source:e,display:t?`As RegEx: ${e}`:"Can't parse RegEx",isRegEx:!0}]}renderSuggestion(e,t){t.innerText=e.display}onChooseSuggestion(e,t){}};var S=require("obsidian"),pe=class extends S.PluginSettingTab{constructor(e,t){super(e,t);this.plugin=t}display(){let{containerEl:e}=this;e.empty(),e.createEl("h2",{text:this.plugin.manifest.name}),new S.Setting(e).setName("Open file on write").addToggle(t=>t.setValue(this.plugin.settings.openFileOnWrite).onChange(i=>{this.plugin.settings.openFileOnWrite=i,this.plugin.saveSettings()})),new S.Setting(e).setName("Open file on write in a new pane").setDisabled(this.plugin.settings.openFileOnWrite).addToggle(t=>t.setValue(this.plugin.settings.openFileOnWriteInNewPane).onChange(i=>{this.plugin.settings.openFileOnWriteInNewPane=i,this.plugin.saveSettings()})),new S.Setting(e).setName("Open daily note in a new pane").addToggle(t=>t.setValue(this.plugin.settings.openDailyInNewPane).onChange(i=>{this.plugin.settings.openDailyInNewPane=i,this.plugin.saveSettings()})),new S.Setting(e).setName("Open file without write in new pane").addToggle(t=>t.setValue(this.plugin.settings.openFileWithoutWriteInNewPane).onChange(i=>{this.plugin.settings.openFileWithoutWriteInNewPane=i,this.plugin.saveSettings()})),new S.Setting(e).setName("Use UID instead of file paths").addToggle(t=>t.setValue(this.plugin.settings.useUID).onChange(i=>{this.plugin.settings.useUID=i,this.plugin.saveSettings(),this.display()})),new S.Setting(e).setName("Include vault name/ID parameter").addToggle(t=>t.setValue(this.plugin.settings.includeVaultName).onChange(i=>{this.plugin.settings.includeVaultName=i,this.plugin.saveSettings(),this.display()})),this.plugin.settings.includeVaultName&&new S.Setting(e).setName("Vault identifying parameter").setDesc("Choose whether to use the vault Name or its internal ID as the identifying parameter.").addDropdown(t=>t.addOption("name","Name").addOption("id","ID").setValue(this.plugin.settings.vaultParam).onChange(i=>{this.plugin.settings.vaultParam=i,this.plugin.saveSettings()})),this.plugin.settings.useUID&&new S.Setting(e).setName("Add filepath parameter").setDesc("When using UID instead of file paths, you can still add the filepath parameter to know what this URI is about. It's NOT actually used.").addToggle(t=>t.setValue(this.plugin.settings.addFilepathWhenUsingUID).onChange(i=>{this.plugin.settings.addFilepathWhenUsingUID=i,this.plugin.saveSettings()})),new S.Setting(e).setName("UID field in frontmatter").addText(t=>t.setValue(this.plugin.settings.idField).onChange(i=>{this.plugin.settings.idField=i,this.plugin.saveSettings()})),new S.Setting(e).setName("Allow executing arbitrary code via eval").setDesc("\u26A0\uFE0F This can be dangerous as it allows executing arbitrary code. Only enable this if you trust the source of the URIs you are using and know what you are doing. \u26A0\uFE0F").addToggle(t=>t.setValue(this.plugin.settings.allowEval).onChange(i=>{this.plugin.settings.allowEval=i,this.plugin.saveSettings()})),new S.Setting(e).setName("Donate").setDesc("If you like this Plugin, consider donating to support continued development.").addButton(t=>{t.buttonEl.outerHTML="<a href='https://ko-fi.com/F1F195IQ5' target='_blank'><img height='36' style='border:0px;height:36px;' src='https://cdn.ko-fi.com/cdn/kofi3.png?v=3' border='0' alt='Buy Me a Coffee at ko-fi.com' /></a>"})}};var L=require("obsidian");var ue,Gt=new Uint8Array(16);function Ce(){if(!ue&&(ue=typeof crypto!="undefined"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto!="undefined"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!ue))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return ue(Gt)}var pt=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function Qt(o){return typeof o=="string"&&pt.test(o)}var ut=Qt;var I=[];for(he=0;he<256;++he)I.push((he+256).toString(16).substr(1));var he;function Zt(o){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,e=(I[o[n+0]]+I[o[n+1]]+I[o[n+2]]+I[o[n+3]]+"-"+I[o[n+4]]+I[o[n+5]]+"-"+I[o[n+6]]+I[o[n+7]]+"-"+I[o[n+8]]+I[o[n+9]]+"-"+I[o[n+10]]+I[o[n+11]]+I[o[n+12]]+I[o[n+13]]+I[o[n+14]]+I[o[n+15]]).toLowerCase();if(!ut(e))throw TypeError("Stringified UUID is invalid");return e}var ht=Zt;function Jt(o,n,e){o=o||{};var t=o.random||(o.rng||Ce)();if(t[6]=t[6]&15|64,t[8]=t[8]&63|128,n){e=e||0;for(var i=0;i<16;++i)n[e+i]=t[i];return n}return ht(t)}var Te=Jt;var ne=class{constructor(n){this.plugin=n;this.app=this.plugin.app}get settings(){return this.plugin.settings}async writeUIDToFile(n,e){var s;let t=(s=this.app.metadataCache.getFileCache(n))==null?void 0:s.frontmatter,i=await this.app.vault.read(n),a=(!t||t.length===0)&&!i.match(/^-{3}\s*\n*\r*-{3}/),l=i.split(`
`),r=`${this.plugin.settings.idField}:`;if(a)l.unshift("---"),l.unshift(`${r} ${e}`),l.unshift("---");else{let d=l.findIndex(p=>p.startsWith(r));d!=-1?l[d]=`${r} ${e}`:l.splice(1,0,`${r} ${e}`)}let c=l.join(`
`);return await this.app.vault.modify(n,c),e}async getUIDFromFile(n){var i;let e=(i=this.app.metadataCache.getFileCache(n))!=null?i:await new Promise(a=>{let l=this.app.metadataCache.on("changed",r=>{if(r.path==n.path){let c=this.app.metadataCache.getFileCache(n);this.app.metadataCache.offref(l),a(c)}})}),t=(0,L.parseFrontMatterEntry)(e.frontmatter,this.plugin.settings.idField);return t!=null?t instanceof Array?t[0]:t:await this.writeUIDToFile(n,Te())}async generateURI(n){let e="obsidian://adv-uri",t="",i=this.app.vault.getAbstractFileByPath(n.filepath);this.settings.includeVaultName&&(t+="?vault=",this.settings.vaultParam=="id"&&this.app.appId?t+=encodeURIComponent(this.app.appId):t+=encodeURIComponent(this.app.vault.getName())),this.settings.useUID&&i instanceof L.TFile&&i.extension=="md"&&(this.settings.addFilepathWhenUsingUID||(n.filepath=void 0),n.uid=await this.getUIDFromFile(i));let a=Object.keys(n).filter(l=>n[l]).sort((l,r)=>{let c=["filepath","filename","uid","daily"],s=["data","eval"];return c.includes(l)?-1:c.includes(r)||s.includes(l)?1:s.includes(r)?-1:0});for(let l of a)n[l]!=null&&(t+=t?"&":"?",t+=`${l}=${encodeURIComponent(n[l])}`);return t.endsWith("%20")&&(t+="&"),e+t}async copyURI(n){let e=await this.generateURI(n);await ee(e),new L.Notice("Advanced URI copied to your clipboard")}getFileFromUID(n){var i;let e=this.app.vault.getMarkdownFiles(),t=this.settings.idField;for(let a of e){let l=(0,L.parseFrontMatterEntry)((i=this.app.metadataCache.getFileCache(a))==null?void 0:i.frontmatter,t);if(l instanceof Array){if(l.contains(n))return a}else if(l==n)return a}}getFileFromBlockID(n){var t,i;let e=this.app.vault.getMarkdownFiles();n=n.toLowerCase();for(let a of e)if(((i=(t=this.app.metadataCache.getFileCache(a))==null?void 0:t.blocks)==null?void 0:i[n])!=null)return a}};var fe=require("obsidian"),ye=class extends fe.FuzzySuggestModal{constructor(e){super(e.app);this.plugin=e,this.setPlaceholder("Choose a workspace")}getItems(){let e=this.app.internalPlugins.getEnabledPluginById("workspaces");if(!e)new fe.Notice("Workspaces plugin is not enabled");else return Object.keys(e.workspaces)}getItemText(e){return e}onChooseItem(e,t){this.plugin.tools.copyURI({workspace:e})}};var ge=class extends j.Plugin{constructor(){super(...arguments);this.handlers=new te(this);this.tools=new ne(this)}async onload(){await this.loadSettings(),this.addSettingTab(new pe(this.app,this)),this.addCommand({id:"copy-uri-current-file",name:"Copy URI for file with options",callback:()=>this.handlers.handleCopyFileURI(!1)}),this.addCommand({id:"copy-uri-current-file-simple",name:"Copy URI for current file",callback:()=>this.handlers.handleCopyFileURI(!0)}),this.addCommand({id:"copy-uri-daily",name:"Copy URI for daily note",callback:()=>new B(this).open()}),this.addCommand({id:"copy-uri-search-and-replace",name:"Copy URI for search and replace",callback:()=>{let e=new V(this,"Used file for search and replace");e.open(),e.onChooseItem=t=>{let i=new de(this);i.open(),i.onChooseSuggestion=a=>{new ce(this,a,t==null?void 0:t.source).open()}}}}),this.addCommand({id:"copy-uri-command",name:"Copy URI for command",callback:()=>{let e=new V(this,"Select a file to be opened before executing the command");e.open(),e.onChooseItem=t=>{new se(this,t==null?void 0:t.source).open()}}}),this.addCommand({id:"copy-uri-block",name:"Copy URI for current block",checkCallback:e=>{let t=this.app.workspace.getActiveViewOfType(j.MarkdownView);if(e)return t!=null;let i=le.getBlockId(this.app);i&&this.tools.copyURI({filepath:t.file.path,block:i})}}),this.addCommand({id:"copy-uri-workspace",name:"Copy URI for workspace",callback:()=>{new ye(this).open()}}),this.addCommand({id:"copy-uri-canvas-node",name:"Copy URI for selected canvas nodes",checkCallback:e=>{let t=this.app.workspace.activeLeaf.view;if(e)return t.getViewType()==="canvas"&&t.canvas.selection.size>0;if(t.getViewType()!=="canvas")return!1;let i=t,a=[];i.canvas.selection.forEach(l=>{a.push(l.id)}),this.tools.copyURI({canvasnodes:a.join(","),filepath:t.file.path})}}),this.addCommand({id:"copy-uri-canvas-viewport",name:"Copy URI for current canvas viewport",checkCallback:e=>{let t=this.app.workspace.activeLeaf.view;if(e)return t.getViewType()==="canvas";if(t.getViewType()!=="canvas")return!1;let a=t.canvas,l=a.tx.toFixed(0),r=a.ty.toFixed(0),c=a.tZoom.toFixed(3);this.tools.copyURI({filepath:t.file.path,canvasviewport:`${l},${r},${c}`})}}),this.registerObsidianProtocolHandler("advanced-uri",async e=>{let t=e;for(let i in t)t[i]=decodeURIComponent(t[i]);this.onUriCall(t)}),this.registerObsidianProtocolHandler("adv-uri",async e=>{let t=e;this.onUriCall(t)}),this.registerObsidianProtocolHandler("hook-get-advanced-uri",async e=>{let t=e;for(let a in t)t[a]=decodeURIComponent(t[a]);let i=this.app.workspace.getActiveFile();i?this.hookSuccess(t,i):this.failure(t,{errorMessage:"No file opened"})}),this.registerEvent(this.app.workspace.on("file-menu",(e,t,i)=>{(i==="more-options"||i==="tab-header"||i=="file-explorer-context-menu")&&t instanceof j.TFile&&e.addItem(a=>{a.setTitle("Copy Advanced URI").setIcon("link").setSection("info").onClick(l=>this.handlers.handleCopyFileURI(!0,t))})}))}async onUriCall(e){var i,a,l;let t=!1;if(this.lastParameters={...e},e.uid){let r=(i=this.tools.getFileFromUID(e.uid))==null?void 0:i.path;r!=null&&(e.filepath=r,e.uid=void 0)}else if(e.filename){let r=this.app.metadataCache.getFirstLinkpathDest(e.filename,"");r||(r=this.app.vault.getMarkdownFiles().find(d=>{var p;return(p=(0,j.parseFrontMatterAliases)(this.app.metadataCache.getFileCache(d).frontmatter))==null?void 0:p.includes(e.filename)}));let c=this.app.fileManager.getNewFileParent((a=this.app.workspace.getActiveFile())==null?void 0:a.path),s=c.isRoot()?"":c.path+"/";e.filepath=(l=r==null?void 0:r.path)!=null?l:s+(0,j.normalizePath)(e.filename)}if(e.filepath){e.filepath=(0,j.normalizePath)(e.filepath);let r=e.filepath.lastIndexOf(".");e.filepath.substring(r<0?e.filepath.length:r)===""&&(e.filepath=e.filepath+".md")}else if(e.daily==="true"){if(!(0,R.appHasDailyNotesPluginLoaded)()){new j.Notice("Daily notes plugin is not loaded");return}let r=window.moment(Date.now()),c=(0,R.getAllDailyNotes)(),s=(0,R.getDailyNote)(r,c);s||(e.exists==="true"?e.filepath=await it(r):(s=await(0,R.createDailyNote)(r),await new Promise(d=>setTimeout(d,500)),t=!0)),s!==void 0&&(e.filepath=s.path)}e.clipboard==="true"&&(e.data=await navigator.clipboard.readText()),this.chooseHandler(e,t)}async chooseHandler(e,t){e["enable-plugin"]||e["disable-plugin"]?this.handlers.handlePluginManagement(e):e.frontmatterkey?this.handlers.handleFrontmatterKey(e):e.workspace||e.saveworkspace=="true"?this.handlers.handleWorkspace(e):e.commandname||e.commandid?this.handlers.handleCommand(e):e.bookmark?this.handlers.handleBookmarks(e):e.eval?this.handlers.handleEval(e):e.filepath&&e.exists==="true"?this.handlers.handleDoesFileExist(e):e.canvasnodes||e.canvasviewport?this.handlers.handleCanvas(e):e.data?this.handlers.handleWrite(e,t):e.filepath&&e.heading?(await this.handlers.handleOpen(e),e.filepath=void 0,e.heading=void 0,this.chooseHandler(e,t)):e.filepath&&e.block?(await this.handlers.handleOpen(e),e.filepath=void 0,e.block=void 0,this.chooseHandler(e,t)):(e.search||e.searchregex)&&e.replace!=null?this.handlers.handleSearchAndReplace(e):e.search?this.handlers.handleSearch(e):e.filepath?this.handlers.handleOpen(e):e.block?this.handlers.handleOpenBlock(e):e.settingid?this.handlers.handleOpenSettings(e):e.updateplugins&&this.handlers.handleUpdatePlugins(e)}async hookSuccess(e,t){if(!e["x-success"])return;let i={title:K(t.name),advanceduri:await this.tools.generateURI({filepath:t.path}),urlkey:"advanceduri",fileuri:rt(this.app,t)};this.success(e,i)}success(e,t){if(e["x-success"]){let i=new URL(e["x-success"]);for(let a in t)i.searchParams.set(a,t[a]);window.open(i.toString())}}failure(e,t){if(e["x-error"]){let i=new URL(e["x-error"]);for(let a in t)i.searchParams.set(a,t[a]);window.open(i.toString())}}async append(e,t){var l;let i,a;if(t.heading){if(e instanceof j.TFile){i=e.path;let r=(l=Oe(this.app,e,t.heading))==null?void 0:l.lastLine;if(r===void 0)return;let s=(await this.app.vault.read(e)).split(`
`);s.splice(r,0,...t.data.split(`
`)),a=s.join(`
`)}}else if(e instanceof j.TFile){i=e.path;let r=await this.app.vault.read(e);if(t.line){let c=Math.max(Number(t.line),0),s=r.split(`
`);s.splice(c,0,t.data),a=s.join(`
`)}else a=r+`
`+t.data}else i=e,a=t.data;return this.writeAndOpenFile(i,a,t)}async prepend(e,t){var l;let i,a;if(t.heading){if(e instanceof j.TFile){i=e.path;let r=(l=Oe(this.app,e,t.heading))==null?void 0:l.firstLine;if(r===void 0)return;let s=(await this.app.vault.read(e)).split(`
`);s.splice(r,0,...t.data.split(`
`)),a=s.join(`
`)}}else if(e instanceof j.TFile){i=e.path;let r=await this.app.vault.read(e),c=this.app.metadataCache.getFileCache(e),s=0;t.line?s+=Math.max(Number(t.line)-1,0):c.frontmatterPosition&&(s+=c.frontmatterPosition.end.line+1);let d=r.split(`
`);d.splice(s,0,t.data),a=d.join(`
`)}else i=e,a=t.data;return this.writeAndOpenFile(i,a,t)}async writeAndOpenFile(e,t,i){let a=this.app.vault.getAbstractFileByPath(e);if(a instanceof j.TFile)await this.app.vault.modify(a,t);else{let l=e.split("/"),r=l.slice(0,l.length-1).join("/");l.length>1&&!(this.app.vault.getAbstractFileByPath(r)instanceof j.TFolder)&&await this.app.vault.createFolder(r),/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/.test(t)?await this.app.vault.createBinary(e,(0,j.base64ToArrayBuffer)(t)):await this.app.vault.create(e,t)}return this.openExistingFileAndSetCursor(e,i),this.app.vault.getAbstractFileByPath(e)}async openExistingFileAndSetCursor(e,t){t.openmode!="silent"&&this.settings.openFileOnWrite&&(await this.open({file:e,setting:this.settings.openFileOnWriteInNewPane,parameters:t}),(t.line!=null||t.column!=null||t.offset!=null)&&await this.setCursorInLine(t))}async open({file:e,setting:t,parameters:i,supportPopover:a,mode:l}){var c;let r;if(i.openmode=="popover"&&(a==null||a)){let s=this.app.plugins.plugins["obsidian-hover-editor"];s||(new j.Notice("Cannot find Hover Editor plugin. Please file an issue."),this.failure(i)),await new Promise(d=>{r=s.spawnPopover(void 0,()=>{this.app.workspace.setActiveLeaf(r,{focus:!0}),d()})})}else{let s=t;if(i.newpane!==void 0&&(s=i.newpane=="true"),i.openmode!==void 0&&(i.openmode=="true"||i.openmode=="false"?s=i.openmode=="true":i.openmode=="popover"?s=!1:j.Platform.isMobile&&i.openmode=="window"||(s=i.openmode)),s=="silent")return;if(j.Platform.isMobileApp&&s=="window"&&(s=!0),e!=null){let d=!1;isBoolean(s)&&this.app.workspace.iterateAllLeaves(p=>{var h;if(((h=p.view.file)==null?void 0:h.path)===i.filepath){if(d&&p.width==0)return;d=!0,this.app.workspace.setActiveLeaf(p,{focus:!0}),r=p}})}r||(r=this.app.workspace.getLeaf(s),this.app.workspace.setActiveLeaf(r,{focus:!0}))}if(e instanceof j.TFile?await r.openFile(e):e!=null&&await this.app.workspace.openLinkText(e,"/",!1,l!=null?{state:{mode:l}}:Se(i)),r.view instanceof j.MarkdownView){let s=r.getViewState();l!=null?s.state.mode=l:s.state={...s.state,...(c=Se(i))==null?void 0:c.state},await r.setViewState(s)}return r}async setCursor(e){let t=this.app.workspace.getActiveViewOfType(j.MarkdownView);if(!t)return;let i=e.mode,a=t.editor,l=t.leaf.getViewState();if(l.state.mode="source",i==="append"){let r=a.lastLine(),c=a.getLine(r).length;await t.leaf.setViewState(l,{focus:!0}),a.setCursor({ch:c,line:r})}else i==="prepend"&&(await t.leaf.setViewState(l,{focus:!0}),a.setCursor({ch:0,line:0}));await new Promise(r=>setTimeout(r,10)),e.viewmode=="preview"&&(l.state.mode="preview",await t.leaf.setViewState(l))}async setCursorInLine(e){let t=this.app.workspace.getActiveViewOfType(j.MarkdownView);if(!t)return;let i=t.leaf.getViewState(),a=e.line!=null?Number(e.line):void 0,l=e.column?Number(e.column):void 0;i.state.mode="source",await t.leaf.setViewState(i);let r,c;if(e.offset!=null){let s=t.editor.offsetToPos(Number(e.offset));r=s.line,c=s.ch}else{r=a!=null?Math.min(a-1,t.editor.lineCount()-1):t.editor.getCursor().line;let s=t.editor.getLine(r).length-1;c=Math.min(l-1,s)}t.editor.focus(),t.editor.setCursor({line:r,ch:c}),t.editor.scrollIntoView({from:{line:r,ch:c},to:{line:r,ch:c}},!0),await new Promise(s=>setTimeout(s,10)),e.viewmode=="preview"&&(i.state.mode="preview",await t.leaf.setViewState(i))}async loadSettings(){this.settings=Object.assign(_e,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}};
//! All of these methods are taken from https://www.npmjs.com/package/obsidian-daily-notes-interface.
/*! Bundled license information:

feather-icons/dist/feather.js:
  (*!
    Copyright (c) 2016 Jed Watson.
    Licensed under the MIT License (MIT), see
    http://jedwatson.github.io/classnames
  *)
*/

/* nosourcemap */