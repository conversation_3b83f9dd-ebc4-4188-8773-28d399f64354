# Projects - Local Rules

## Core Project Rules
- Each project has a team member associated to it
- Team members are added to the Teams Folder, extra details will be added later
- Projects should have a deadline when specified
- Changes in project times will be extracted from the `__memo` file and also need to indicated in the project
- The project timeline is essentially built over in a smart way that aligns with the user interest

## Directory Structure
- Each project must have `__home.md`, `__rules.md`, and `__logs.md` files
- Projects link to their rules and log files
- Archive folders maintain historical project data

## Links
- [Parent Directory Rules](../__rules.md)
- [Project Logs](./__logs.md)