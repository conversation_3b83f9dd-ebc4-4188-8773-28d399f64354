
"smart_sources:Projects/Bytebridge/task-ai/tasks.md": {"path":"Projects/Bytebridge/task-ai/tasks.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0832508,-0.02698245,0.01873024,-0.04056115,-0.04845051,0.00515235,-0.02078936,0.04400733,0.01591586,-0.00460552,0.07601066,-0.02193199,0.03961075,0.05814751,0.01252863,-0.01471332,-0.03571502,0.02253059,0.01938783,-0.03435033,-0.00603481,-0.02540868,0.00604793,-0.02076657,-0.03795181,0.07487834,-0.03318624,-0.08241285,-0.02267088,-0.20406231,0.03248005,0.00406307,0.07502007,-0.02411266,-0.00574054,0.05034547,-0.0301534,0.02753436,-0.00226846,-0.02007785,0.07932135,0.02806721,-0.00162776,-0.00434667,-0.00316003,-0.03742234,-0.0324625,-0.04092427,-0.0154934,-0.0229382,-0.03067989,-0.07186162,0.03663372,0.0208397,0.03888849,0.03262716,0.02067005,0.09775825,0.03010448,-0.00373897,0.02279813,-0.00346127,-0.20691915,0.11183862,0.02739182,0.02584912,-0.07450332,-0.06019099,0.02259581,0.02792169,-0.06808486,0.05136237,0.0208477,0.05620275,0.04402532,0.01787294,0.02763638,-0.01898727,0.0369276,-0.0274337,-0.02071198,0.00210736,-0.03429077,0.04171849,-0.02533813,0.00754055,0.0012191,-0.00632772,0.06102717,-0.02439748,-0.00562002,-0.07459194,-0.03192435,0.05149092,-0.03260326,0.01962901,0.02746232,0.00822645,-0.09236419,0.10708755,-0.02139793,0.02575817,-0.02103734,-0.05228847,0.04560727,-0.01362608,0.05645779,-0.05450939,-0.05185337,-0.02468516,-0.03288772,0.02384149,-0.02537568,-0.01337156,-0.00643233,0.04948454,-0.01103048,0.03213409,-0.04391826,-0.0197848,0.03378227,0.01135113,0.05695677,0.01395666,-0.040945,-0.00893829,0.04765689,0.07253458,-0.01244472,0.04051223,0.05389472,0.04494454,-0.05117243,0.04652252,-0.01594712,0.04383001,-0.01205678,-0.05904022,-0.01808634,-0.00655493,-0.04627298,0.05185924,0.01420587,-0.10458792,-0.05991139,0.13876291,0.02352841,0.01013785,-0.00488878,-0.07408184,-0.00639988,0.04427313,-0.05340665,-0.00461523,-0.00439123,-0.01483019,0.04917259,-0.00902848,-0.0549686,0.04872129,-0.00477253,-0.04552051,-0.07542018,0.15146694,0.01415874,-0.12634271,-0.04652195,-0.00944727,-0.00673153,0.02107606,-0.01619432,0.01852057,-0.01949474,0.03528651,0.05834035,-0.01101241,-0.07388784,0.02997926,0.03604463,0.01671339,0.06464162,-0.0570491,-0.03414341,-0.03419889,0.01062833,-0.05818394,0.01935859,-0.03074098,0.03605018,-0.03825942,-0.03511779,0.00720638,0.05754434,0.06152918,-0.00832449,-0.04917723,0.01971935,-0.01224919,-0.02196232,-0.0096893,0.09115299,0.05152665,0.0083081,0.01831393,-0.03064812,-0.01199806,-0.0355379,-0.02580089,0.05381737,0.06271262,-0.05162184,-0.01805312,0.09069157,0.01020136,-0.03573131,-0.02227324,-0.02148975,0.06535523,0.02526773,0.05336019,-0.01649416,0.12161715,-0.01694584,-0.22046612,-0.0224637,0.01944659,-0.01441039,-0.04723968,-0.07182237,0.0272863,-0.03371976,-0.02957426,-0.00748362,0.06011694,-0.05903001,0.01159991,-0.0086351,0.01743637,-0.0163822,-0.0099989,0.0035437,-0.00907491,-0.0054658,0.03682755,0.0535617,-0.0252302,-0.05547552,0.02187297,-0.03535999,0.13707605,-0.04013133,0.0155924,0.03827929,0.01664335,0.05210729,-0.03956994,-0.18851653,0.02710141,0.05278045,0.05856408,0.02473125,-0.00196292,-0.03121169,-0.0489396,0.04723112,0.02004689,-0.11416936,-0.08432234,-0.02900395,-0.01565151,-0.10344438,0.00308682,-0.00015966,-0.00698318,0.00015035,-0.00305799,0.02769686,-0.01071531,-0.03780102,-0.02919801,0.01885221,-0.0424534,0.02962705,0.01225688,-0.03595221,-0.00088831,-0.01004855,0.07307184,0.02607569,-0.01775456,-0.02948564,0.03369476,-0.07997478,0.00650657,0.08799707,-0.00100871,-0.02439593,0.04476567,-0.04274509,-0.03452737,-0.02307881,0.0138962,-0.03892207,-0.02539749,0.02355504,0.00480756,0.00253234,0.04663349,0.05103426,0.05261693,-0.03491473,0.06199493,-0.02501686,-0.01239043,0.00234589,0.00176668,-0.01398534,0.10100065,0.04794722,-0.23995058,0.02009965,0.04913827,-0.0230024,-0.05671962,0.00322597,0.06074652,-0.05774197,-0.04779327,0.00105448,-0.06879558,0.01134852,-0.01205201,0.02413072,-0.00694898,0.03310934,0.05842996,0.00868967,0.06005068,-0.11212911,-0.00203075,0.05972884,0.22863238,-0.01559294,0.05626331,0.02478586,-0.01092229,-0.04101921,0.09518924,0.05045936,0.01357707,0.0277811,0.09621276,0.01382889,-0.0044674,0.07105915,0.01291114,-0.01589914,-0.00667909,0.05777755,0.00959882,0.03354574,0.00434647,0.00151965,0.0890815,0.02687905,-0.01342193,-0.06451297,-0.03522637,0.01270973,-0.01620968,-0.05572225,-0.0096797,-0.014478,0.04835423,0.02968092,0.00656726,0.00134821,-0.04028327,0.02825675,-0.00119466,-0.0008059,0.07903747,0.04147125,-0.0434657],"last_embed":{"hash":"1wjkpku","tokens":431}}},"last_read":{"hash":"1wjkpku","at":1751095117891},"class_name":"SmartSource","last_import":{"mtime":1751092308400,"size":2889,"at":1751095094771,"hash":"1wjkpku"},"blocks":{"#__task.ai - Project Tasks":[1,85],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}":[3,36],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development":[5,12],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development#{1}":[6,6],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development#{2}":[7,7],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development#{3}":[8,8],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development#{4}":[9,9],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development#{5}":[10,10],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development#{6}":[11,12],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes":[13,20],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes#{1}":[14,14],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes#{2}":[15,15],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes#{3}":[16,16],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes#{4}":[17,17],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes#{5}":[18,18],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes#{6}":[19,20],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow":[21,28],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow#{1}":[22,22],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow#{2}":[23,23],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow#{3}":[24,24],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow#{4}":[25,25],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow#{5}":[26,26],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow#{6}":[27,28],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards":[29,36],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards#{1}":[30,30],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards#{2}":[31,31],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards#{3}":[32,32],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards#{4}":[33,33],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards#{5}":[34,34],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards#{6}":[35,36],"#__task.ai - Project Tasks#Technical Development":[37,54],"#__task.ai - Project Tasks#Technical Development##core-functionality":[39,46],"#__task.ai - Project Tasks#Technical Development##core-functionality#{1}":[40,40],"#__task.ai - Project Tasks#Technical Development##core-functionality#{2}":[41,41],"#__task.ai - Project Tasks#Technical Development##core-functionality#{3}":[42,42],"#__task.ai - Project Tasks#Technical Development##core-functionality#{4}":[43,43],"#__task.ai - Project Tasks#Technical Development##core-functionality#{5}":[44,44],"#__task.ai - Project Tasks#Technical Development##core-functionality#{6}":[45,46],"#__task.ai - Project Tasks#Technical Development##github-maintenance":[47,54],"#__task.ai - Project Tasks#Technical Development##github-maintenance#{1}":[48,48],"#__task.ai - Project Tasks#Technical Development##github-maintenance#{2}":[49,49],"#__task.ai - Project Tasks#Technical Development##github-maintenance#{3}":[50,50],"#__task.ai - Project Tasks#Technical Development##github-maintenance#{4}":[51,51],"#__task.ai - Project Tasks#Technical Development##github-maintenance#{5}":[52,52],"#__task.ai - Project Tasks#Technical Development##github-maintenance#{6}":[53,54],"#__task.ai - Project Tasks#User Research & Testing":[55,72],"#__task.ai - Project Tasks#User Research & Testing##user-feedback":[57,64],"#__task.ai - Project Tasks#User Research & Testing##user-feedback#{1}":[58,58],"#__task.ai - Project Tasks#User Research & Testing##user-feedback#{2}":[59,59],"#__task.ai - Project Tasks#User Research & Testing##user-feedback#{3}":[60,60],"#__task.ai - Project Tasks#User Research & Testing##user-feedback#{4}":[61,61],"#__task.ai - Project Tasks#User Research & Testing##user-feedback#{5}":[62,62],"#__task.ai - Project Tasks#User Research & Testing##user-feedback#{6}":[63,64],"#__task.ai - Project Tasks#User Research & Testing##documentation":[65,72],"#__task.ai - Project Tasks#User Research & Testing##documentation#{1}":[66,66],"#__task.ai - Project Tasks#User Research & Testing##documentation#{2}":[67,67],"#__task.ai - Project Tasks#User Research & Testing##documentation#{3}":[68,68],"#__task.ai - Project Tasks#User Research & Testing##documentation#{4}":[69,69],"#__task.ai - Project Tasks#User Research & Testing##documentation#{5}":[70,70],"#__task.ai - Project Tasks#User Research & Testing##documentation#{6}":[71,72],"#__task.ai - Project Tasks#Success Metrics":[73,79],"#__task.ai - Project Tasks#Success Metrics#{1}":[74,74],"#__task.ai - Project Tasks#Success Metrics#{2}":[75,75],"#__task.ai - Project Tasks#Success Metrics#{3}":[76,76],"#__task.ai - Project Tasks#Success Metrics#{4}":[77,77],"#__task.ai - Project Tasks#Success Metrics#{5}":[78,79],"#__task.ai - Project Tasks#Repository Links":[80,85],"#__task.ai - Project Tasks#Repository Links#{1}":[81,81],"#__task.ai - Project Tasks#Repository Links#{2}":[82,82],"#__task.ai - Project Tasks#Repository Links#{3}":[83,83],"#__task.ai - Project Tasks#Repository Links#{4}":[84,85],"#taskmaster #opensource #github #agent #ux #frontend #navigation":[86,87]},"outlinks":[],"last_embed":{"hash":"1wjkpku","at":1751095116285}},"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07755949,-0.02451857,0.02154092,-0.04044517,-0.04858808,0.00655172,-0.02280453,0.04696983,0.01219521,-0.00631685,0.07615937,-0.02074303,0.03956361,0.05434867,0.01769872,-0.01416772,-0.0393806,0.02244077,0.01902301,-0.03925324,-0.00716946,-0.03074049,0.01141568,-0.02053407,-0.03551755,0.06857438,-0.03911146,-0.08539988,-0.02972239,-0.20534787,0.02663723,0.00381806,0.07891986,-0.02277093,-0.00499819,0.04628076,-0.025355,0.02541827,-0.00552903,-0.01324571,0.08031148,0.02566856,0.00411422,-0.00038368,-0.00526085,-0.03675107,-0.03089579,-0.03994118,-0.02290931,-0.01945999,-0.03170325,-0.07704426,0.03097901,0.02611109,0.04781993,0.03453992,0.02121346,0.09722945,0.02541643,-0.00747872,0.02519482,-0.00006963,-0.20674819,0.11144489,0.02155885,0.02429025,-0.07494944,-0.06324505,0.02559959,0.02260906,-0.06659743,0.05424086,0.01961103,0.06171136,0.03930274,0.01827075,0.02742723,-0.01790825,0.03744006,-0.02740902,-0.01902651,0.01278126,-0.03290578,0.03774576,-0.02782971,0.00553679,0.00368571,-0.00752739,0.05411701,-0.02188709,-0.00173095,-0.07465865,-0.0276099,0.0520324,-0.03647044,0.01856095,0.03434121,0.00542568,-0.09608261,0.10547877,-0.02474353,0.02513002,-0.02043847,-0.05046148,0.04791782,-0.01214405,0.05832454,-0.05977192,-0.04231417,-0.0235959,-0.03471142,0.02094103,-0.02716369,-0.00996259,-0.00167671,0.05403893,-0.00226273,0.03391841,-0.04310285,-0.02267796,0.03554186,0.01234168,0.05399044,0.00981945,-0.03384004,-0.00536203,0.04705849,0.06877618,-0.01130634,0.04300518,0.05162276,0.05119718,-0.04888009,0.04706777,-0.02003431,0.0443727,-0.0125147,-0.06078962,-0.01797552,-0.00367507,-0.04136178,0.05363308,0.0096309,-0.11018049,-0.06185782,0.13699959,0.02368684,0.01264685,-0.0120151,-0.07436743,-0.00628416,0.04414689,-0.05743456,-0.00247491,-0.00478388,-0.00665671,0.05132175,-0.0060729,-0.05119489,0.050225,-0.00915459,-0.04696809,-0.07680268,0.14680959,0.0149708,-0.13084008,-0.0468128,-0.01316756,-0.00090621,0.02445841,-0.01316976,0.01673336,-0.01614955,0.03413051,0.05972385,-0.00810344,-0.07163753,0.03177344,0.03882324,0.01816767,0.06687222,-0.04624095,-0.03214747,-0.03206708,0.0066844,-0.05943834,0.01668875,-0.03038498,0.03525259,-0.04073823,-0.04121608,0.00386328,0.0532766,0.05946406,-0.01217544,-0.05285296,0.02054826,-0.01728043,-0.02406724,-0.00585604,0.0898447,0.0504453,0.00657676,0.01236511,-0.03085505,-0.00835624,-0.0279686,-0.02710263,0.05433239,0.05903894,-0.04733264,-0.01852767,0.08994252,0.01078507,-0.03208189,-0.0214464,-0.01490328,0.06565051,0.0256851,0.05457824,-0.01520242,0.12535432,-0.01746603,-0.22293054,-0.02845426,0.02236923,-0.00531342,-0.03977563,-0.07484225,0.0324257,-0.03310324,-0.02946028,-0.00472212,0.06401724,-0.05400537,0.00261326,-0.00357384,0.02546713,-0.0112666,-0.02013572,-0.00079191,-0.010211,-0.01077474,0.02856488,0.056222,-0.0183333,-0.05224311,0.02606134,-0.0375539,0.13692133,-0.04024797,0.00787187,0.04007371,0.01794211,0.0557969,-0.041839,-0.19102815,0.02607541,0.06025829,0.05702,0.02041535,-0.00038622,-0.03176777,-0.04219454,0.04797583,0.02085008,-0.11724228,-0.08491012,-0.02940398,-0.01681443,-0.09920997,-0.00127161,-0.00516388,-0.00914867,-0.00740084,0.00049425,0.02391515,-0.01277786,-0.04193384,-0.02951526,0.02510428,-0.0368276,0.03504641,0.00878033,-0.03489014,0.0014298,-0.01540975,0.07182064,0.02073867,-0.01569145,-0.02243027,0.03825928,-0.08157555,0.00965416,0.09078124,-0.00121936,-0.02007737,0.0426206,-0.03804131,-0.03654441,-0.02445446,0.00995376,-0.04492195,-0.02572271,0.0239996,0.00650695,0.00417978,0.04343687,0.04976448,0.05147651,-0.03908421,0.06199889,-0.03018881,-0.01186864,0.0032337,-0.00365155,-0.02057021,0.09721654,0.04943826,-0.24281424,0.01736531,0.04697825,-0.01857126,-0.05109034,0.00365569,0.06375352,-0.05839087,-0.04611038,0.00091817,-0.05778589,0.01514314,-0.0100685,0.02372803,-0.00306723,0.03198022,0.05740729,0.00782433,0.05703076,-0.12215997,0.00072693,0.06369326,0.2261481,-0.01527959,0.05131749,0.02846505,-0.0067995,-0.04389629,0.09556804,0.04496207,0.00112792,0.03064116,0.09649313,0.01059016,-0.00498648,0.0672324,0.01514169,-0.01711596,-0.00952536,0.0510673,0.00652003,0.03312251,0.00934756,0.00136354,0.0865837,0.01990755,-0.00325225,-0.06759841,-0.02768787,0.01157447,-0.01240315,-0.05035787,-0.01243079,-0.01215912,0.05156272,0.02728152,0.00426522,0.00326889,-0.04252059,0.02914543,-0.00172398,-0.00338418,0.0814606,0.0337793,-0.04214936],"last_embed":{"hash":"3dql9g","tokens":461}}},"text":null,"length":0,"last_read":{"hash":"3dql9g","at":1751095116662},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks","lines":[1,85],"size":2823,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"3dql9g","at":1751095116662}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0760539,-0.02335995,0.02973567,-0.04017764,-0.05054727,0.02363795,-0.01275198,0.04366546,0.0214511,-0.00818405,0.05969954,-0.00878109,0.03346941,0.05109314,0.01041547,-0.01511215,-0.03668442,0.01254526,0.00829838,-0.02800727,-0.00450372,-0.02502221,0.00718341,-0.02660955,-0.0336142,0.06675483,-0.04498418,-0.07055032,-0.01548394,-0.20150115,0.03322945,0.00161239,0.07543495,-0.03073346,-0.01774428,0.03347442,-0.03473289,0.03683026,0.01136313,-0.01424406,0.07817422,0.02413424,0.00684069,0.00031589,-0.00318975,-0.03415585,-0.02526328,-0.04267504,-0.01443608,-0.02672835,-0.02103604,-0.07176145,0.03080872,0.02651501,0.04643478,0.02065344,0.02412401,0.10419172,0.01536951,-0.01061872,0.01950444,-0.00253071,-0.21497007,0.10718776,0.00490439,0.02124701,-0.07650634,-0.07103056,0.00488657,0.02852985,-0.07378621,0.05173702,0.01477351,0.0496092,0.03861323,0.00936871,0.01990719,-0.02715933,0.0310146,-0.00807988,-0.02361508,0.00221473,-0.04126835,0.01826769,-0.02302936,0.00528247,0.00624621,-0.01435367,0.03189541,-0.01202966,-0.0031376,-0.07829469,-0.02868904,0.05437943,-0.03423403,0.01244106,0.0262235,0.01415224,-0.10190744,0.10482688,-0.01297028,0.02868972,-0.01699243,-0.05445552,0.05321315,-0.00549328,0.06031863,-0.0408527,-0.04185507,-0.02408842,-0.0316757,0.0324195,-0.02292386,-0.00277103,-0.00150042,0.0641241,-0.00504922,0.03489634,-0.04649818,-0.01856052,0.03706702,0.01483179,0.05430971,0.00788866,-0.03975938,-0.01160177,0.05392288,0.07072739,-0.01824298,0.03687893,0.05622766,0.04920276,-0.04841602,0.0577592,-0.00941416,0.02982607,-0.02322775,-0.04994506,-0.00551534,0.00200791,-0.04139265,0.06412064,0.01040118,-0.10636706,-0.0559725,0.14104861,0.03022454,0.0132403,-0.01289888,-0.05934863,-0.00839892,0.03295126,-0.05747522,-0.00801399,-0.01462911,-0.00943571,0.05566562,-0.02048225,-0.06452939,0.05447316,-0.00727174,-0.05248327,-0.0803495,0.14534748,0.01348215,-0.13019228,-0.04323008,-0.00939169,0.00535985,0.01991552,-0.0200756,0.01023072,-0.02060206,0.05278172,0.07531021,0.00307426,-0.07839711,0.02367232,0.02975119,0.00326011,0.07883492,-0.05886436,-0.03791361,-0.04000425,0.00540401,-0.0580249,0.02440033,-0.02410622,0.05142153,-0.03482424,-0.03438301,0.0141997,0.04578623,0.05378122,-0.01120166,-0.0566005,0.01576029,-0.0183453,-0.0313462,-0.02116598,0.07767792,0.0434288,0.00727028,0.00428006,-0.01477378,0.00179819,-0.0274081,-0.03346699,0.05466596,0.06103764,-0.05672833,-0.0282521,0.09596416,0.0113779,-0.02190748,-0.0009229,-0.01494065,0.06765547,0.01558932,0.04063593,-0.01590466,0.13378166,-0.00883761,-0.21663621,-0.01702441,0.02780716,-0.0132693,-0.04632581,-0.07737166,0.02865371,-0.02370193,-0.03200975,-0.01013435,0.04792922,-0.06926834,0.01140323,0.0077353,0.02532226,-0.01016942,-0.00013603,0.00304927,0.00220777,-0.00876809,0.01381517,0.06474607,-0.01770554,-0.05843971,0.02928038,-0.03622865,0.1385864,-0.04540657,0.00798884,0.03247866,0.01810613,0.05395417,-0.05068002,-0.1887861,0.02349793,0.06274172,0.06547152,0.01541428,0.00195325,-0.03457208,-0.03123571,0.04620145,0.02599789,-0.10973858,-0.08348426,-0.03515333,-0.01083677,-0.10192153,-0.00518563,0.0022953,-0.01304405,0.00388714,0.0033407,0.02309455,-0.00544124,-0.05003226,-0.04056188,0.03220832,-0.05101022,0.05419099,0.00252088,-0.03427596,0.00336613,-0.03199011,0.06648804,0.01786761,-0.02488806,-0.02585858,0.03905477,-0.08240603,0.00444199,0.07946939,-0.01243731,-0.02363517,0.04567333,-0.0443793,-0.04325305,-0.01954201,0.0052294,-0.03526364,-0.03942975,0.0168345,-0.0005773,0.00284986,0.04767498,0.04264954,0.04503749,-0.03247825,0.04258581,-0.03000784,-0.01294806,-0.01080611,-0.00004234,-0.01449319,0.08726133,0.03253277,-0.23892425,0.01804719,0.04757586,-0.01140057,-0.04757938,0.0201752,0.06358041,-0.04536294,-0.05842628,0.00117068,-0.07242396,0.00068985,-0.00189828,0.03476574,0.00017803,0.03881208,0.0565945,0.01467902,0.06318515,-0.12981686,0.00933945,0.07013602,0.22575942,-0.00419637,0.04894515,0.02066442,-0.0088914,-0.04505953,0.09439292,0.04817618,0.00648863,0.04027634,0.09407359,0.02913115,0.00398311,0.07849202,0.00628603,-0.01769952,-0.01239737,0.05601492,0.00780496,0.02741802,0.00657448,-0.00104212,0.0914022,0.02698444,0.01777111,-0.05898539,-0.03343285,0.01685241,-0.014292,-0.04388633,-0.0073813,-0.00536772,0.04579469,0.02973167,-0.00495377,0.00171566,-0.03526657,0.03632485,-0.00290887,0.01094607,0.08502499,0.03069156,-0.04680755],"last_embed":{"hash":"n02usq","tokens":310}}},"text":null,"length":0,"last_read":{"hash":"n02usq","at":1751095116954},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}","lines":[3,36],"size":1253,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"n02usq","at":1751095116954}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05795189,-0.02862647,0.0141423,-0.05124723,-0.03506882,0.02189348,-0.0258557,0.02712435,0.02360762,0.00333918,0.03495022,-0.01452999,0.02989786,0.04721986,0.01564047,0.01376301,-0.02372804,0.00606802,0.01619826,-0.03194203,0.01730355,-0.0177162,0.00041363,-0.05451313,-0.03590301,0.0681093,-0.02105163,-0.0651465,-0.00371821,-0.16332816,0.03061542,-0.00646034,0.07573511,-0.03865084,0.0135711,0.03305581,-0.02725752,0.04348135,-0.00680539,-0.00211164,0.07497091,0.01606797,0.00008266,-0.01547811,0.01036773,-0.03356206,-0.02041116,-0.0459893,-0.00110973,-0.0317733,0.00958441,-0.05124718,0.01758502,0.00779344,0.04729015,0.01562955,0.02735142,0.08536506,0.00923745,-0.0021769,-0.00422102,-0.00893191,-0.20372719,0.11251732,0.01720848,0.02749017,-0.04798014,-0.06724691,0.01765196,0.0193219,-0.06894506,0.04152539,0.00686287,0.04687249,0.04214977,0.01306151,0.02463476,-0.01834751,0.03184607,-0.00791149,-0.02493561,-0.01179485,-0.0594011,0.03166878,-0.03134983,0.03035683,0.00451943,-0.00227447,0.02486493,-0.00348139,-0.02748076,-0.10575963,-0.02014602,0.04401508,-0.03094624,0.02331356,0.03955673,-0.00490155,-0.12246116,0.11395416,-0.00771073,0.01271576,-0.00932529,-0.03522978,0.04940175,-0.01320886,0.03653873,-0.06032484,-0.02557114,-0.03376684,-0.04021832,0.03062661,-0.02078189,-0.01254331,0.01153381,0.0204558,-0.02024904,0.02569089,-0.05039513,-0.00362989,0.03389239,0.00535887,0.02805,0.02661092,-0.03320352,-0.01958228,0.05490078,0.06315096,-0.02051254,0.0264499,0.03866725,0.05731928,-0.05277579,0.04656975,0.00434136,0.02445461,-0.00465694,-0.04849273,0.0128628,-0.01498073,-0.05511576,0.05880573,0.02227864,-0.10293917,-0.02575929,0.11203656,0.0316458,0.02364242,-0.02358894,-0.0189241,-0.01357802,0.01274524,-0.06663522,-0.00512149,-0.00582119,-0.00057172,0.03317925,-0.01021235,-0.0309245,0.05028908,0.0047712,-0.05251824,-0.06525922,0.16438641,-0.00749632,-0.15006359,-0.02843229,0.00708773,0.03171203,0.01723961,-0.02345781,0.0228447,-0.00655478,0.04781376,0.07686957,-0.00305439,-0.06932028,0.02617407,0.04201901,0.01327226,0.09160478,-0.05189904,-0.03236829,-0.04445753,-0.01766261,-0.07099105,0.04031164,-0.03711928,0.03713474,-0.02291497,-0.05730002,0.02737095,0.03020028,0.05729555,0.00518579,-0.04461765,0.01686491,-0.00924695,-0.02015524,-0.03110715,0.05744321,0.0619914,-0.00055313,0.02452795,-0.06765333,-0.0126153,-0.01652895,-0.04493491,0.08003177,0.07326655,-0.05433286,-0.02206019,0.10569271,0.025063,-0.02224289,0.02531369,-0.01859445,0.07950645,0.03508262,0.03034504,-0.00724093,0.1079361,-0.00460538,-0.22729245,0.00273096,0.03479095,-0.03961015,-0.05976833,-0.07310419,0.03384112,-0.02025341,-0.0683642,-0.00010786,0.0660973,-0.07654767,0.00767901,0.01952937,0.0049151,-0.00810352,0.00346741,-0.01687259,-0.00691112,-0.013779,-0.00931401,0.06176393,-0.01391947,-0.06280912,0.02880385,-0.01677396,0.13895485,-0.04092514,0.03852513,0.01215841,0.01746007,0.0285458,-0.05511836,-0.19446316,0.02701478,0.05402055,0.07998569,0.00810355,0.01363589,-0.02234592,-0.03701951,0.03288896,0.01869115,-0.13670853,-0.06566159,-0.04360178,-0.0142678,-0.11928525,-0.02759754,-0.00200747,-0.03938764,-0.00883596,0.00789402,0.0491663,-0.01126365,-0.04037206,-0.05370425,0.03461056,-0.02876907,0.07039893,0.02761884,-0.02164701,-0.02340939,-0.05070106,0.03762717,0.02893157,-0.03074427,-0.03432386,0.05262564,-0.08824176,0.0057288,0.080274,0.0019223,-0.01779504,0.04410191,-0.05351507,-0.05315508,-0.01355711,-0.00162198,-0.01871363,-0.03497629,0.01039937,0.01339334,0.01011088,0.05148127,0.03793344,0.03691462,-0.02348056,0.05924498,-0.03484287,-0.03467555,-0.01077792,-0.01035591,0.01338847,0.09706871,0.00632981,-0.24876797,0.02683272,0.0417087,0.00395129,-0.05522222,0.04455085,0.07525939,-0.04236633,-0.06509814,0.00224794,-0.06653002,-0.01773736,0.0229073,0.06951718,0.00740985,0.03707587,0.08483707,-0.0033525,0.02971743,-0.11186042,0.00441261,0.05448047,0.21339992,0.00137956,0.04628082,0.01464048,-0.03553462,-0.02177666,0.08831986,0.07486389,0.02199752,0.03143438,0.06094294,0.01719004,0.00258819,0.09177072,0.02879105,-0.02177202,-0.02068423,0.05451135,-0.01274223,0.01697761,-0.00555447,0.01161129,0.05468975,0.04589654,-0.00659198,-0.06525453,-0.04266506,0.00994123,-0.02822942,-0.03818308,-0.01571506,-0.00699875,0.05511405,0.05904378,0.01353528,0.00495016,-0.02498464,0.04196649,-0.00389587,0.0038551,0.08978564,0.03645793,-0.04038149],"last_embed":{"hash":"xzylla","tokens":105}}},"text":null,"length":0,"last_read":{"hash":"xzylla","at":1751095117135},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development","lines":[5,12],"size":322,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"xzylla","at":1751095117135}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07380438,-0.05560259,0.05568874,-0.05290741,-0.03773048,-0.00433759,-0.03647319,0.02051559,-0.00454908,-0.03516158,0.08193979,-0.05584473,0.0504629,0.05813239,0.03223854,-0.00815738,-0.03607227,0.04384821,0.02411522,-0.01209821,-0.03024622,0.00938668,0.00923624,-0.01458037,-0.04870052,0.11699332,-0.0315491,-0.0629662,0.00203834,-0.19668265,0.01192584,-0.00982875,0.03696436,0.01096103,0.00920645,0.05276139,-0.03751153,0.02067089,0.0301174,-0.01404353,0.0755368,0.01968005,-0.02398862,-0.02328132,0.0029905,-0.02094186,-0.0190772,-0.02682755,-0.02181724,-0.03800545,-0.05328644,-0.08576762,0.02817905,0.03387347,0.03087645,0.05024571,0.01545779,0.10001228,0.05487812,0.04051512,0.01181962,0.02870963,-0.20359188,0.11928735,0.05015981,0.02637878,-0.06476142,-0.06682504,-0.01308173,0.06680401,-0.0703856,0.04007351,0.02565694,0.05930781,0.04991152,0.02082151,0.02845796,-0.03424116,0.04354394,-0.03534446,-0.02958063,0.00755483,-0.02086831,0.03321703,-0.02341298,0.00465029,-0.01842604,0.0140973,0.04571291,-0.02282421,-0.00944188,-0.05954861,-0.00718536,0.06588292,-0.03895644,-0.00755066,-0.01939256,0.02105399,-0.04413797,0.1310347,-0.05234886,0.01095704,-0.01596012,-0.04002175,0.0181088,-0.01591778,0.04279944,-0.0043436,-0.03903619,-0.01027089,-0.00358315,0.01260228,0.00546362,-0.01356461,-0.0565838,0.02400127,-0.02821685,0.02346705,-0.08119382,-0.02813845,0.02246584,0.00337218,0.06740281,0.0070671,-0.04680693,-0.00157304,0.04424557,0.0765029,-0.00189039,0.04036294,0.05304788,0.0046623,-0.02799555,0.04061275,-0.01997339,0.05109243,-0.02018952,-0.04833333,-0.04421329,-0.01337925,-0.04102129,0.00826933,0.00916126,-0.05749971,-0.07738081,0.13864735,-0.02931197,-0.00175432,0.00491287,-0.11057629,-0.02127734,0.04279168,-0.06428994,-0.02080891,-0.01621927,-0.00390445,0.06183646,-0.03024325,-0.10756215,0.03964638,0.01216504,-0.03724468,-0.07506073,0.15383868,0.02512256,-0.09298597,-0.06893941,-0.03009702,-0.05347482,0.02257595,-0.02143616,0.01251099,-0.02258916,0.04778799,0.04491803,0.00056912,-0.0567481,0.02505116,-0.01896203,0.01380615,0.03513461,-0.06873426,-0.03668638,-0.02241922,0.00877346,-0.03626051,-0.05190599,-0.01519351,0.04071276,-0.0756974,-0.0274121,0.00123348,0.07993482,0.01546835,0.00513513,-0.0301427,0.00813481,0.00250093,-0.03099464,-0.0058291,0.12011085,0.05852445,0.00165408,0.03201897,-0.03773706,0.00176343,-0.03576964,-0.01055652,0.02247191,0.0429935,-0.04892418,-0.02385079,0.07735682,0.01328697,-0.04556163,-0.03696138,-0.01249767,0.00825051,0.02505071,0.08281811,-0.02057956,0.10642593,-0.03302226,-0.2227875,-0.0142079,-0.01915752,0.00402741,-0.08032397,-0.07434575,0.01316079,0.01094631,-0.01556127,0.02016011,0.0193903,-0.05675791,0.02144413,-0.02448043,0.02152953,-0.02416626,0.00775053,0.02249246,-0.02556278,0.04324878,0.05097042,0.04195284,-0.03912165,-0.06579211,0.02907968,-0.01825585,0.13828272,0.00902882,0.03888347,0.07173084,0.04712731,0.07016981,-0.03100345,-0.1761049,0.02642295,0.03334282,0.07008308,0.02436428,-0.00158964,-0.02669015,-0.04736562,0.04491652,0.01519435,-0.07483805,-0.05451793,-0.02026648,-0.00618656,-0.06666619,0.01581993,-0.00034045,-0.01497554,0.02939696,-0.00999344,0.0568515,-0.01879556,-0.02908655,-0.01876598,-0.02575218,-0.02133669,0.0172805,0.03459653,-0.00689369,0.01140166,0.00333475,0.07568534,0.00820607,0.00517438,-0.04998098,0.03161408,-0.0284557,0.01848617,0.06660491,-0.01989252,-0.0244241,0.00904964,-0.02412198,-0.04275382,0.00058957,0.01565343,-0.04512972,-0.00560386,0.04093624,0.02987962,-0.02467027,0.06264896,0.04476596,0.04334081,-0.06333377,0.06113405,-0.01247998,0.01348095,0.00455313,0.00765159,-0.00661213,0.11387626,0.0353043,-0.22824089,0.03888544,0.09856853,-0.02411935,-0.08356404,0.01413895,0.04592006,-0.05988509,-0.02792533,-0.02233496,-0.05621194,-0.01385033,-0.01379217,-0.00741504,-0.01312727,0.02430126,0.0383872,0.01603403,0.08075424,-0.09069905,0.00122837,0.07260258,0.2369917,-0.04111176,0.03040886,0.01290005,-0.00801133,-0.05187693,0.04711452,0.04679858,-0.0178963,0.03249165,0.07600619,0.00776389,0.03577694,0.0689756,0.03423902,-0.01531305,0.03393577,0.0605231,0.03020182,0.03176298,0.00466752,0.00028971,0.09442151,0.01678003,-0.01343546,-0.00665631,-0.03809039,0.00574097,-0.01127648,-0.07665959,-0.02035068,-0.01267267,0.03288854,0.0304017,-0.01596431,-0.02639298,-0.03807575,0.00988056,0.01341118,-0.00787995,0.06450573,0.02017689,-0.04365427],"last_embed":{"hash":"1vpffa5","tokens":97}}},"text":null,"length":0,"last_read":{"hash":"1vpffa5","at":1751095117249},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes","lines":[13,20],"size":298,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1vpffa5","at":1751095117249}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0935462,-0.04195705,0.03300592,-0.02206785,0.00017088,-0.00283308,-0.03397518,0.04355598,-0.01117554,-0.01767196,0.03612278,-0.02123349,0.03458765,0.03848835,0.00998674,-0.0105056,-0.04558143,0.00844221,-0.05488407,-0.01377122,0.02896873,0.00330889,0.0162238,0.02020193,-0.02979057,0.03382016,-0.04740274,-0.0719261,-0.03756533,-0.1917863,0.03719263,0.02925021,0.01886474,-0.02107122,-0.00232394,0.03170826,-0.01111194,0.03255926,-0.01630844,0.00725966,0.06258681,0.03005337,-0.00665687,-0.02654707,0.00681852,-0.03583153,-0.03388315,-0.02245057,-0.0075722,-0.01949533,-0.00771141,-0.03411087,0.04883943,-0.00165253,0.04307242,0.01838569,0.06640415,0.10948374,0.0807801,0.00194622,0.02858512,0.0376773,-0.19197513,0.10457368,-0.01483804,0.00586816,-0.07287618,-0.07098611,-0.00261465,0.03418941,-0.08875306,0.0498645,0.00956768,0.06930174,0.03788142,-0.00801318,0.01756028,-0.03384716,0.01941161,0.01754656,-0.04842538,-0.02388187,-0.03237298,-0.0076404,-0.01162881,-0.00793922,-0.01017741,0.00340286,0.01535484,-0.02911508,-0.02365167,-0.07912544,0.01043053,0.07336306,-0.04588547,0.03222651,0.03964785,0.03092229,-0.06562706,0.12812494,-0.01407396,0.01259976,-0.03700028,-0.04782592,0.05258439,-0.01660719,0.02234539,-0.03129381,-0.04123018,-0.01418685,-0.01217518,0.00679808,0.02102116,-0.01501792,0.05952024,0.06993128,0.00149877,0.02305509,-0.08280654,0.00440889,0.04093259,0.03763486,0.02196884,0.0187577,-0.04235595,-0.0261975,0.05870785,0.05398826,0.01591491,0.04469535,0.05644916,0.02848754,-0.08908428,0.02815419,-0.01811153,0.02195425,-0.04873936,-0.0997085,-0.00400216,-0.02126931,-0.04708348,0.02511992,-0.02797039,-0.0907689,-0.05559579,0.18043743,0.01504351,0.05463906,-0.04168987,-0.0587638,-0.0409651,0.04959211,-0.03887523,-0.00170966,0.01429203,0.02702451,0.0584104,-0.01425967,-0.05257893,0.06386362,-0.00516048,-0.05219312,-0.07617361,0.13449408,-0.00674528,-0.07498288,-0.02932407,-0.02562962,0.02128306,0.04553046,-0.01679586,0.01943456,0.00585327,0.04319699,0.06217317,0.01965014,-0.02544276,-0.02022336,0.02782126,0.00488234,0.05435122,-0.04671146,-0.0323659,-0.03061111,0.02063322,-0.04777693,0.04054073,-0.00509996,0.08017851,0.00137103,-0.05534917,-0.00242281,0.08236503,0.00912862,-0.02471387,-0.03439699,0.00302421,-0.00435859,-0.01785361,-0.00783444,0.04099563,0.06243994,0.01604286,-0.00205186,0.01049986,0.0060866,-0.04533247,-0.0386316,0.05860433,0.08671257,-0.04487769,-0.04493343,0.09277889,0.01034389,-0.05323746,0.01180013,-0.0241363,0.07547878,0.01567157,0.01454094,-0.01881675,0.11030526,-0.05918024,-0.23557016,-0.01645938,0.0263763,-0.07994701,0.01667666,-0.08794075,0.0112166,-0.03224228,-0.05839092,0.06471643,0.02847806,-0.07743738,0.00482722,-0.03351581,0.01168074,0.00247014,-0.0027208,-0.03419999,-0.00688129,0.00675309,-0.01580208,0.06619173,-0.03196747,-0.05726176,0.05770421,-0.04070609,0.13540378,-0.08052789,0.03159828,0.04064447,-0.01146368,0.04580524,-0.02401107,-0.21883009,0.02606144,0.05903105,0.00417187,0.03412837,-0.0041908,-0.02119802,-0.02286405,0.06783748,0.0043026,-0.1009731,-0.0312392,-0.03783774,0.00609362,-0.08232382,-0.02634332,0.02116075,-0.0238456,0.01138292,-0.01266253,0.01505031,0.02096846,-0.0369237,-0.05170911,0.04146583,-0.03447734,0.00546179,0.00525934,-0.0466739,-0.00442623,-0.00511124,0.06418797,0.01163651,-0.01078925,-0.02016252,-0.01231088,-0.03155395,0.02035193,0.07219578,-0.00736783,-0.01456283,0.0434296,-0.026616,-0.04859057,-0.03765307,0.01087058,-0.05699605,0.00495164,0.00583284,0.00463112,0.03867349,0.03565693,0.05255879,0.01067467,0.0039913,0.03658242,0.00198296,-0.0035651,-0.05133522,-0.00637032,0.02427204,0.09621332,0.00425236,-0.22376551,0.0265491,0.02689925,-0.00839907,-0.02316378,0.01190088,0.04590881,-0.0231873,-0.04273639,0.027892,-0.11691605,0.03039539,-0.01919358,0.04702982,-0.04251432,0.02023269,0.0494068,0.00908948,0.05956391,-0.07788622,-0.03241235,0.04789115,0.22419792,-0.02290926,0.045839,0.02228657,-0.0212773,-0.01520406,0.11863977,0.01580569,0.0260659,0.02705167,0.09561372,0.01306328,0.0216912,0.04674237,-0.00198792,0.01382142,0.00826165,0.03550619,-0.00581715,0.00545201,-0.04467799,-0.00732892,0.08501237,0.05800595,-0.00791665,-0.07355698,-0.06897399,0.03401198,-0.01655816,-0.04207711,0.00740837,0.01401845,0.03221224,0.02126056,0.03155588,0.00214221,-0.0308747,0.04123731,0.02711337,-0.00563323,0.0732991,0.02623789,-0.05558535],"last_embed":{"hash":"1vl9w3d","tokens":97}}},"text":null,"length":0,"last_read":{"hash":"1vl9w3d","at":1751095117313},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow","lines":[21,28],"size":268,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1vl9w3d","at":1751095117313}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08232121,-0.00997984,0.01223153,-0.0168469,-0.07297952,0.0412361,0.02208224,0.04204582,0.03848175,0.01373422,0.06559831,-0.02762631,0.04383766,0.04162662,-0.00686889,-0.00251586,-0.03050859,0.00213895,-0.00871772,-0.02114679,0.01854506,-0.0433976,0.0139445,-0.00372229,-0.01129517,0.04064675,-0.06338996,-0.08780441,-0.02357437,-0.15860406,0.01596281,-0.001898,0.09596736,-0.00922962,-0.03557784,0.02047222,-0.0484755,0.04116311,0.00372571,0.01228808,0.01639551,0.01576387,-0.01710008,-0.02122095,0.00567496,-0.03334302,-0.05399372,-0.03260658,-0.02735787,-0.0083465,0.0090226,-0.07493819,0.03685175,0.03874734,0.04357179,0.02829363,0.02468479,0.07249486,0.01695861,-0.0267847,0.02277269,-0.03694075,-0.1877576,0.08750816,-0.02309149,0.04932818,-0.05754118,-0.07129776,-0.00280969,0.07328655,-0.04859209,0.04613067,0.02963883,0.05915982,0.05033311,0.00513842,0.0377727,-0.05646959,-0.01363737,-0.02537405,-0.02740745,-0.02602564,-0.01564815,-0.00567702,-0.00328862,0.02755243,-0.04178054,-0.03579226,0.06888068,0.01600112,-0.02764839,-0.01693429,-0.01716509,0.07203289,-0.03391493,0.00622573,0.01945544,0.02482974,-0.10744825,0.12291569,-0.05574161,0.00479072,0.00108001,-0.05061364,0.05869558,0.00783495,0.00945909,-0.06215144,-0.05171362,-0.0080959,-0.02167801,0.03506457,-0.02978002,-0.01204914,0.01572864,0.05946727,0.00039417,0.05635658,-0.00886208,-0.03226731,0.03519455,0.04627348,0.04917455,-0.03980187,-0.03740502,-0.00731014,0.06424699,0.06790455,-0.021711,0.04054313,0.04269972,0.00842827,-0.0656115,0.02129978,-0.02606464,0.02943163,-0.01712317,-0.09176669,0.008123,0.03598927,0.01535548,0.08141742,0.02638397,-0.14352113,-0.04657863,0.15943731,0.06139331,0.04153167,-0.01897733,-0.05643774,-0.00233528,0.01266781,-0.0392805,-0.01982103,-0.02505091,0.00654243,0.04538723,-0.03742305,-0.04995889,0.03959944,-0.01536356,-0.03497117,-0.09073842,0.11462735,0.0244749,-0.11569875,-0.04570881,-0.02855215,-0.01189866,0.00340697,-0.02227044,0.01500934,-0.02719632,0.04932301,0.09856675,0.0056155,-0.0715933,0.01765409,0.03403579,-0.00844795,0.08599569,-0.05171019,-0.04349308,-0.03917856,0.01167997,-0.05589231,0.03808514,-0.05618432,0.08921421,-0.01067943,-0.04185133,0.03280111,0.02823066,0.05106894,0.01297765,-0.07499204,-0.01804237,-0.00689353,-0.0556176,0.02528588,0.01652398,-0.00498181,0.00917977,-0.03870018,0.04051857,0.01834341,-0.04878167,-0.03541464,0.05615498,0.05664978,-0.04647764,-0.04387124,0.10597655,0.02884901,-0.03305883,0.02042883,-0.00304879,0.03888935,-0.00569706,0.04089938,-0.0392861,0.10488925,-0.02105327,-0.20704159,-0.02994513,0.06228948,0.0121882,0.00144603,-0.07020614,0.00680356,-0.04664901,-0.02961298,0.02809536,0.07390811,-0.06990475,0.00241882,-0.02764352,0.02903328,0.00721238,-0.02532507,-0.01461265,0.00557137,-0.00478133,0.0084748,0.05147623,0.0010745,-0.08049045,0.03372905,-0.05653629,0.13685867,-0.02533972,-0.05874722,0.07178307,0.00082203,0.07192881,-0.08438437,-0.18427205,0.00281643,0.0397303,0.04859953,0.02216387,0.00385519,-0.04780735,-0.03620584,0.03735432,0.00235896,-0.08567982,-0.06899568,-0.00119289,-0.00759309,-0.09694688,0.01554854,0.00361659,0.01042025,0.01361857,0.00453181,0.03305111,0.03522482,-0.0133183,-0.06042434,0.06367527,-0.03579888,0.0548415,-0.01186396,-0.03975291,-0.01970433,-0.00578269,0.06690438,0.03606548,-0.01273554,-0.00802662,0.00025359,-0.03970847,0.03838153,0.07214233,-0.01502421,-0.0348927,0.03399072,-0.03221153,-0.03469716,-0.02126832,0.01001234,-0.03556494,-0.0023887,-0.02546419,-0.02459775,-0.00599405,0.03520194,0.01618329,0.02428425,-0.02758396,0.03726052,-0.01677852,0.03601743,0.00768342,0.00065752,-0.00360388,0.1054152,0.05497005,-0.24551721,0.02953009,0.04753259,0.01472015,-0.02450945,0.01850446,0.057618,-0.01825637,-0.05037814,0.04183722,-0.03035623,-0.02670431,-0.01609722,0.00681502,-0.0222755,0.033962,0.05777205,0.01049842,0.07008926,-0.107531,-0.00727358,0.09116861,0.21936738,-0.03657462,0.03587231,0.00289246,-0.00804126,-0.03463056,0.06614176,0.02507171,-0.01303807,0.03831653,0.07642568,0.05031751,-0.02465863,0.08037329,0.00793176,-0.02014484,-0.02152719,0.03555856,0.00455374,0.02656527,-0.03211154,-0.01738507,0.12255209,0.00973166,-0.00918826,-0.05372575,-0.01109156,0.01053222,-0.01222845,-0.02558229,-0.00639267,-0.00532788,0.08864003,0.03298817,-0.01970876,-0.00447934,-0.04523111,0.02175101,-0.0062267,0.04468364,0.08007547,0.00293133,-0.03924567],"last_embed":{"hash":"1bkp8w1","tokens":96}}},"text":null,"length":0,"last_read":{"hash":"1bkp8w1","at":1751095117378},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards","lines":[29,36],"size":313,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1bkp8w1","at":1751095117378}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Technical Development": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08253185,-0.0316927,0.01726523,-0.01892785,-0.02047309,-0.03877129,-0.03777938,0.04745471,-0.00025612,-0.01374168,0.09378801,-0.06977496,0.05883326,0.03935156,0.01392124,-0.00181656,-0.01297574,0.02105547,0.04394404,-0.06020657,0.01196227,-0.01230194,0.00536971,-0.00979991,-0.02557782,0.07551444,-0.043334,-0.087408,-0.04403472,-0.20663399,0.02382002,0.0008563,0.05155748,0.01299381,0.04072976,0.08031383,-0.0193864,0.00405667,-0.03159265,0.02566402,0.0574443,0.04436586,-0.0281458,-0.0242874,0.00615589,-0.03391004,-0.02575135,-0.04737981,-0.02746912,-0.01569443,-0.01893676,-0.05082867,0.04489502,0.01587716,0.00914983,0.05662897,0.03212945,0.08100324,0.05494897,0.01545485,0.02347295,0.02161081,-0.17969826,0.08583152,0.06951885,0.03643856,-0.06429214,-0.03814078,0.04677606,0.05106511,-0.05072333,0.03922337,0.04727971,0.07567397,0.0430081,0.04788659,0.01546886,-0.02367306,0.02941649,-0.03990878,-0.03597023,-0.00581162,-0.01847795,0.05975474,-0.0252021,0.01479297,-0.02270793,0.0302015,0.10707784,-0.03708571,-0.00259003,-0.03177532,0.00703102,0.05006523,-0.01931036,0.03912169,0.01882534,0.00765178,-0.05225531,0.12877761,-0.03071632,-0.0133613,-0.02300186,-0.04297389,0.02838979,-0.00587694,0.02405277,-0.05435742,-0.02752577,-0.05308146,-0.02227256,0.01014422,-0.02444114,-0.02131969,0.00450938,0.00039055,0.00051503,0.00776596,-0.07282759,0.00102483,0.05084157,0.00611751,0.05085963,-0.01244449,-0.02102035,-0.01156716,0.02768466,0.06509509,0.01981838,0.06790517,0.04899737,0.03888426,-0.07388071,0.01492259,-0.02460239,0.0542012,0.01070283,-0.08283512,-0.04895149,-0.01135135,-0.01912353,0.00046179,0.00164704,-0.07767878,-0.0616271,0.14106743,0.01133743,0.02611719,-0.01758521,-0.10835499,-0.01154081,0.05498819,-0.03963401,-0.01069185,0.03478886,-0.03248978,0.04465379,0.00699834,-0.04112229,0.03362191,0.00214509,-0.01464797,-0.04700793,0.15030961,0.00175282,-0.08937459,-0.07114079,-0.00855724,-0.01279361,0.02138806,0.00181674,0.01407279,-0.01450147,0.00394556,0.0285012,-0.03647382,-0.04841467,0.0101803,0.04039862,0.03577989,0.03607811,-0.05596389,-0.03831462,-0.01135824,0.02385453,-0.04802044,0.00432497,-0.0309506,0.02120849,-0.02484653,-0.06776575,0.00269857,0.02777226,0.04247714,0.02568968,-0.01884244,-0.00773178,0.01332334,-0.02390891,0.00723908,0.07731136,0.06688044,-0.01944656,0.02649599,-0.04512462,-0.02173419,-0.05733928,-0.01360003,0.02378708,0.06657142,-0.02778052,0.00132273,0.07998336,0.03056565,-0.0626216,-0.03340057,-0.01083692,0.04675547,0.03854632,0.08068202,-0.01263182,0.09024119,-0.06525891,-0.23022944,-0.01385748,0.02689653,-0.04366328,-0.03003163,-0.07802274,0.02185551,-0.06904736,-0.00911791,0.03466994,0.07370109,-0.03818614,-0.00218939,-0.07004701,0.00224858,-0.0070588,-0.0581454,-0.0062974,-0.04816506,0.01668268,0.03482516,0.04231944,-0.02399893,-0.03409103,0.01743798,-0.05274128,0.10774754,-0.02647843,0.03048305,0.05655419,-0.00701997,0.04108816,0.00750984,-0.19858533,0.04039115,0.04480546,0.04864356,0.03902995,0.00706292,-0.02388039,-0.04374709,0.04815781,-0.00826637,-0.09308998,-0.05580493,-0.00092564,-0.03768401,-0.07997593,-0.00667432,-0.00145695,0.01832836,0.02236691,-0.00460445,0.06633381,-0.00246512,-0.00720925,-0.03394778,0.01532205,-0.0019399,-0.02840048,0.04207817,-0.02530011,-0.03215504,0.00099988,0.09856866,0.0095793,0.00593084,-0.042649,-0.00088252,-0.05966045,0.02992858,0.09071805,-0.01854504,-0.01785835,0.00205469,-0.02993059,-0.02663423,-0.06808908,0.00950342,-0.04123106,0.03036822,0.01535062,0.02741398,-0.00663983,0.02799857,0.0434698,0.03425914,-0.03186839,0.0817944,0.00713464,0.00420582,0.0134504,-0.01863666,-0.00656194,0.12908079,0.0612168,-0.24110812,0.04936017,0.05578025,-0.04516831,-0.07344253,-0.01620689,0.03367764,-0.0659352,-0.01326627,-0.00345822,-0.02113819,0.00499073,-0.06221686,-0.00839951,-0.01309087,0.02860597,0.05967636,-0.01510537,0.05348168,-0.0733128,-0.01533933,0.04117919,0.21782443,-0.03768375,0.05219465,0.03594308,-0.00852579,-0.02713049,0.09898911,0.03727133,0.01608855,0.02133627,0.11229788,-0.02855685,-0.01697925,0.05413958,0.05618916,-0.03179123,0.0041062,0.03827053,0.02065427,0.02187435,-0.00657334,0.00873793,0.09122781,-0.01104678,-0.08343792,-0.08492625,-0.02726598,-0.00689724,-0.02467725,-0.07809993,0.01048072,0.00768901,0.07812953,0.03379221,0.01452634,0.00864897,-0.04258366,0.00843498,0.01205996,-0.0226474,0.04307036,0.0515914,-0.04549199],"last_embed":{"hash":"ol9kp4","tokens":146}}},"text":null,"length":0,"last_read":{"hash":"ol9kp4","at":1751095117445},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Technical Development","lines":[37,54],"size":570,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"ol9kp4","at":1751095117445}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Technical Development##core-functionality": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06226894,-0.03435621,0.01247678,-0.03192012,-0.02072391,-0.01919894,-0.00764993,0.06364369,0.03255629,-0.0109004,0.09733076,-0.06416439,0.05716357,0.04942225,0.00340657,0.00112612,-0.00465543,0.03716211,0.02765397,-0.08708397,0.01496772,0.00034923,-0.00669296,-0.0315355,-0.01419016,0.07539325,-0.04677562,-0.06716449,-0.05257888,-0.18899049,0.02750353,-0.01201391,0.06603841,-0.00265589,0.03980682,0.06904577,-0.00664657,0.02395403,-0.018834,0.01582627,0.06283096,0.02856007,-0.04740032,-0.03524998,0.01186083,-0.0259568,-0.02579145,-0.03839318,-0.05932955,-0.0268936,-0.03637495,-0.05822661,0.04174317,0.02509974,0.02474486,0.04814487,0.0179995,0.08920866,0.0607477,0.03774776,0.00517536,0.0246632,-0.17182638,0.0843918,0.06488565,0.01977191,-0.05373976,-0.0559061,0.01523267,0.06252707,-0.02887656,0.04050514,0.06511108,0.06075637,0.04813451,0.05507333,0.01270481,-0.02543432,0.03198024,-0.02119625,-0.02567914,-0.04678131,-0.05116086,0.04061379,-0.03439934,0.01911013,-0.02661103,0.01056072,0.10329399,-0.02908521,-0.00956819,-0.02050377,-0.01325452,0.04733305,-0.02378066,0.03256656,0.00572866,0.00388792,-0.05245849,0.14754371,-0.04378721,-0.02003528,-0.00977538,-0.06085365,0.01260829,-0.02072055,0.017265,-0.04495492,-0.024398,-0.05166014,-0.01369261,0.01698791,-0.00952804,-0.00561254,0.00087514,-0.00179747,0.0071401,0.0004167,-0.08430662,-0.01469316,0.02656229,0.00616462,0.04124006,0.00545525,-0.02717357,-0.00327998,0.0210893,0.09433921,0.01415358,0.05196309,0.04794775,0.00435886,-0.04809438,0.0246859,-0.01619173,0.0497591,0.00788577,-0.07297418,-0.04251622,-0.01005661,-0.02526733,0.00084329,0.0126386,-0.0700614,-0.06503914,0.15220089,0.01161614,0.01411484,-0.0140357,-0.09753287,-0.05878283,0.03887445,-0.04169759,-0.01444431,0.01855856,-0.04261916,0.04160025,-0.01471209,-0.05315119,0.02031916,-0.01018285,0.0104642,-0.03532815,0.16035473,-0.0007994,-0.08840858,-0.06749748,-0.01199476,-0.02837315,0.01221647,-0.01355402,0.00608691,-0.03177729,0.00450288,0.02273283,-0.0425555,-0.07601099,0.00883997,0.00963234,0.01053347,0.04921101,-0.04951109,-0.02438785,-0.02932722,0.03037579,-0.05254566,0.0052849,-0.01759774,0.0451504,-0.04818966,-0.07645415,0.01179748,0.04465258,0.02205461,0.02387444,-0.02128591,0.00095087,0.02257235,-0.03648361,0.02580297,0.11020598,0.07131254,-0.01140649,0.0292222,-0.02473681,-0.0169405,-0.05625127,-0.01354851,0.00006343,0.080722,-0.02479504,-0.01671276,0.08684094,0.03621816,-0.04953799,-0.01756226,-0.01687999,0.03784601,0.00910773,0.06343809,-0.0121935,0.09380284,-0.05960094,-0.22825906,-0.01580756,0.03550842,-0.04194899,-0.03654253,-0.08134329,0.0136408,-0.04940189,-0.02147789,0.02658524,0.03705933,-0.05657936,-0.00522074,-0.05704686,0.02006624,-0.00092542,-0.03568823,-0.01084632,-0.04813273,0.04352997,0.0461908,0.05916541,-0.02986672,-0.05005307,0.014962,-0.04458144,0.13158144,-0.03343557,0.04510909,0.05257699,-0.01043474,0.04189371,-0.02857893,-0.17898659,0.04690887,0.03757151,0.07762422,0.04307738,0.00580368,-0.04290603,-0.05159252,0.04452891,-0.00813446,-0.08577184,-0.04318815,-0.00135818,-0.02315792,-0.07894883,-0.00384974,-0.00280411,0.01931721,0.01430785,-0.00329723,0.05930295,0.00330426,-0.01298487,-0.03395649,0.02556053,-0.00780893,0.00141216,0.04342587,-0.02835032,-0.03905104,0.01038869,0.09833417,0.03577257,0.01717722,-0.05149059,-0.02165513,-0.0585283,0.02740172,0.10342231,-0.03274671,-0.01689746,0.01988617,-0.01705462,-0.02894118,-0.0542066,0.02132934,-0.03109004,0.04408786,0.0045025,0.0248672,-0.00268951,0.04080846,0.01417029,0.0597266,-0.03099546,0.05956331,-0.00001367,0.00409023,0.01543344,-0.02276669,0.00581766,0.130146,0.04638487,-0.24283743,0.04100785,0.06278976,-0.04922166,-0.0576563,-0.02128449,0.03867863,-0.0523844,-0.01240506,-0.00245603,-0.04519809,0.01347887,-0.03832738,0.02457833,-0.02752686,0.01635914,0.06956609,0.00043913,0.05738874,-0.0553476,-0.00834548,0.04317151,0.22738686,-0.03888251,0.05110502,0.03501561,-0.0038178,-0.04902996,0.07111086,0.01744088,0.01664506,0.02818962,0.09590781,-0.03279142,-0.00108228,0.04016674,0.0514095,-0.03710357,0.00407892,0.03747037,0.0343744,0.01987521,0.00443175,0.00268899,0.09686218,0.01713988,-0.09148803,-0.06533944,-0.02418916,0.0035642,-0.02837942,-0.06907374,0.01334766,-0.00709274,0.08057054,0.04527006,-0.00169502,0.00387547,-0.05627923,0.02060454,0.01474006,-0.01006754,0.03757465,0.05358405,-0.03953619],"last_embed":{"hash":"851bix","tokens":86}}},"text":null,"length":0,"last_read":{"hash":"851bix","at":1751095117543},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Technical Development##core-functionality","lines":[39,46],"size":274,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"851bix","at":1751095117543}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Technical Development##github-maintenance": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09478082,-0.02810465,0.03862314,-0.00289032,-0.02517898,-0.02717808,-0.04934234,0.02076503,-0.03598794,-0.00597844,0.05749343,-0.08312494,0.04658289,0.03276247,0.04600003,0.01209401,-0.03005223,0.01804554,0.04370091,-0.01493932,0.03611463,-0.00042499,0.02659188,0.02248334,-0.04394651,0.06470919,-0.01284821,-0.09086013,-0.01223575,-0.17069864,0.01204456,0.01315267,0.01021774,0.02793183,0.03679307,0.07829171,-0.05347205,-0.02720681,-0.02982197,0.02858269,0.03395856,0.03958851,-0.00747901,-0.02672399,0.00062373,-0.03524967,-0.00560113,-0.03808054,0.03468153,-0.01451483,0.01175994,-0.0189061,0.04172061,0.00654948,-0.01496718,0.06199632,0.0592064,0.06392663,0.05257324,-0.0173412,0.03836945,0.02129903,-0.18733589,0.0852493,0.06473998,0.06720982,-0.05421396,-0.01109218,0.06862716,0.02724954,-0.05897639,0.01811378,0.00214962,0.07512396,0.05694983,0.02795228,0.03377357,-0.02966992,-0.00398163,-0.05242112,-0.05939903,0.02515246,0.02100895,0.048439,-0.01088703,0.00858925,-0.02605414,0.05161225,0.09037989,-0.03903765,-0.01920665,-0.05493856,0.06066397,0.05364626,-0.02734411,0.01707766,0.02742684,0.02795869,-0.03887757,0.12568279,-0.01536584,-0.00505638,-0.0289559,-0.01465243,0.04478237,0.02184785,0.00456188,-0.05200151,-0.04220776,-0.03088948,-0.03881113,0.00202408,-0.02099767,-0.04779881,0.00672011,-0.01224105,-0.02167664,0.02926767,-0.03840971,0.01835063,0.03729419,0.01155569,0.05182264,-0.01117533,-0.01622345,-0.02352699,0.04024835,0.02869799,0.01865719,0.06476794,0.04008966,0.06287586,-0.11772282,-0.00985451,-0.02508528,0.05111153,-0.00990759,-0.12834381,-0.03565066,-0.01663855,-0.01993174,0.00234016,-0.01219485,-0.08614469,-0.03615169,0.13218525,0.01741278,0.04389338,-0.02299714,-0.08397456,0.04503895,0.05027783,-0.03017321,-0.00658655,0.02715109,0.00807494,0.04799445,0.01040472,-0.0326596,0.05656219,0.02553451,-0.04980544,-0.08538588,0.1584069,-0.00281088,-0.07821374,-0.05205637,0.00232256,0.01622462,0.02232999,0.02289945,0.03455573,0.01646902,0.00812498,0.0570193,-0.01870091,-0.01558347,-0.00680719,0.07051235,0.04303402,0.02345511,-0.05707639,-0.04864408,0.01844126,0.00249043,-0.02452192,-0.000399,-0.06898448,0.00157763,0.01232325,-0.04051975,0.02008561,0.02029523,0.04892035,0.02992832,-0.01302064,-0.03071002,-0.00759685,0.00635439,-0.03051802,0.01063317,0.04747326,-0.01845314,0.00823218,-0.08164995,-0.02941067,-0.05169906,-0.02198179,0.07663153,0.03862604,-0.03026777,-0.00086183,0.06809677,0.02217498,-0.08278754,-0.02341921,-0.00039116,0.04417899,0.07257327,0.09206129,-0.0165635,0.05760118,-0.06538814,-0.22373733,-0.01611019,0.00497418,-0.05992331,-0.01292866,-0.07105894,0.02071636,-0.06426508,-0.0327429,0.05703887,0.11672704,-0.03161411,0.00056768,-0.06775928,-0.02667267,-0.02722049,-0.04543686,-0.00417925,-0.02862304,-0.01135847,0.00603134,0.01263433,-0.02548609,-0.0460421,0.02823147,-0.04219403,0.09366348,-0.01744111,-0.0076314,0.044637,0.00495128,0.043724,0.01969926,-0.21433729,0.01924546,0.02285929,0.0065323,0.02845782,0.01238492,0.00814551,-0.02506013,0.05218968,-0.00493544,-0.08973002,-0.04586238,-0.00800383,-0.03810752,-0.07382535,-0.00915725,-0.00295105,-0.01105975,0.02240822,-0.01497206,0.08028586,0.00180534,0.0049736,-0.03347848,0.00051635,0.02190994,-0.04846302,0.04884581,-0.009206,-0.02504217,-0.01725129,0.07653236,-0.03276562,-0.0126305,-0.02496303,0.035828,-0.03974392,0.02573907,0.06770123,0.02041546,-0.01467029,-0.0331636,-0.05134143,-0.0253772,-0.04793597,-0.00662457,-0.04885531,0.01258349,0.01316039,0.01915579,0.01580463,0.01566923,0.06626134,-0.0250785,-0.0323124,0.1066808,0.02092046,0.00315683,0.00566115,-0.0028588,0.00829539,0.12792085,0.0523478,-0.23630928,0.0555264,0.05568318,-0.01828649,-0.08111375,0.00549087,0.028003,-0.06798691,-0.03828248,0.01204337,0.00373023,-0.01390543,-0.06257191,-0.03961084,0.00974918,0.037442,0.05995248,-0.03467739,0.03235066,-0.07425665,-0.03073025,0.04883055,0.21198292,-0.04344551,0.05842608,0.01673665,-0.02834602,0.01152486,0.10099138,0.06737492,0.0321242,0.00811477,0.10951043,-0.0305543,-0.02192486,0.0848951,0.03833474,0.00105604,-0.00933644,0.0346601,-0.01027286,0.03609765,-0.05638699,0.01633847,0.0759116,-0.01938078,-0.06593344,-0.08281995,-0.06738167,-0.01679544,-0.02963132,-0.05785969,-0.01265262,0.01313248,0.07853818,0.03145887,0.04219564,-0.00419163,-0.03143445,-0.00989967,0.01078402,-0.03243212,0.03848942,0.03291789,-0.05149381],"last_embed":{"hash":"dreeui","tokens":85}}},"text":null,"length":0,"last_read":{"hash":"dreeui","at":1751095117605},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Technical Development##github-maintenance","lines":[47,54],"size":269,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"dreeui","at":1751095117605}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Research & Testing": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07429878,-0.029572,0.00030901,-0.00976504,-0.02555103,0.0375543,-0.01329319,0.0526705,-0.0469252,-0.00336343,0.04124156,-0.04862,0.03038094,0.04673267,0.05246803,0.02386071,-0.02638697,-0.00623936,0.00133573,0.01812523,0.0055278,-0.04320744,0.02399071,-0.02990022,-0.03345454,0.03087969,-0.03128868,-0.07887393,-0.01812456,-0.18066065,0.01312704,0.04181902,0.09774689,-0.00331761,0.02580993,0.04957294,-0.01474668,0.00526585,-0.04435252,0.00672325,0.05428587,-0.02008263,0.00887441,-0.01865001,0.02418328,-0.00406768,0.00555934,-0.05687106,2.1e-7,0.00863685,-0.00685685,-0.0985797,0.01803437,0.00375804,0.05627493,0.01333392,0.04152369,0.059171,0.0523807,0.01915171,0.00501674,0.00869715,-0.17849937,0.08539548,0.01084657,0.04175292,-0.05665787,-0.0249203,0.0405187,0.03481776,-0.06762446,0.06211549,0.02708636,0.11372498,0.04604306,0.02899352,0.0108116,-0.03300093,0.01139005,-0.02326905,-0.08081668,0.03513886,-0.00855202,0.03946644,-0.02414173,-0.00485373,0.01168006,0.03634919,0.00235377,0.02297779,-0.02951492,-0.05155778,0.01008346,0.03309249,-0.03960168,-0.0007526,0.04789141,0.0003804,-0.1335952,0.12996426,-0.05208834,-0.04220556,0.00599338,-0.05662133,0.00629292,-0.0053263,0.01490818,-0.08249563,0.00134223,0.00440169,-0.04201176,0.02352625,-0.05837072,-0.03342989,0.02777556,0.00703553,0.01423158,0.0393257,-0.02171531,-0.02171697,0.04678782,0.011455,0.05662502,-0.04889894,0.00375879,-0.02382001,0.03884496,0.01851689,0.02912076,0.04653133,0.00578308,0.04518751,-0.09131024,0.03912751,-0.0307956,0.04111601,-0.02305038,-0.09757379,0.00590411,0.030024,0.00485938,0.01433548,0.00451428,-0.09466675,-0.06256976,0.1141276,-0.01282887,0.04436408,-0.04499074,-0.05155236,0.03708967,0.02409338,-0.08996291,-0.00541889,0.0220145,0.01297685,0.06479143,-0.01695505,-0.03859215,0.04425792,0.00622407,-0.0469625,-0.07744947,0.13036297,0.00750896,-0.14054123,-0.03455062,0.01641041,0.01726787,0.02647252,0.01859263,0.02783545,-0.01143043,0.03399802,0.07806413,-0.02319182,-0.04220232,0.04299437,0.04155318,0.05783845,0.06288324,0.00076591,-0.03785549,-0.03562699,-0.01303156,-0.03171975,0.02301456,-0.05952395,0.02451986,-0.00632966,-0.05576979,-0.04843523,-0.01492609,0.05636584,-0.0063637,-0.00346486,0.02523177,-0.03224903,-0.04919976,0.03440293,0.02634459,0.01922758,0.00467,0.01108065,-0.03662801,0.03183739,-0.00786061,-0.00073635,0.07621778,0.05422318,-0.02245877,0.00221857,0.05503101,0.00401529,-0.02504908,-0.0336374,0.01656552,0.02872109,0.04766343,0.07885911,-0.02775933,0.06912576,-0.05219771,-0.2106027,-0.02784232,0.03384389,0.03217386,0.0046508,-0.08041795,0.04286604,-0.04083964,-0.02016409,0.06100443,0.14414501,-0.05031727,-0.02541677,-0.04368474,0.05038944,-0.00307613,-0.0565343,-0.00933744,-0.04150093,-0.00247205,-0.01512519,0.00081663,0.03255052,-0.07496802,0.01695158,-0.0390237,0.10286635,0.00095582,-0.01495076,0.04239036,0.02004904,0.02655274,-0.05306759,-0.21513262,0.0093101,0.04453286,0.04051449,0.03003696,0.01495019,-0.02501173,-0.04579724,0.06026564,0.01944787,-0.1162962,-0.07486343,-0.03146237,-0.00091536,-0.05514169,-0.05052299,-0.03293938,-0.00231291,-0.00692321,0.01456142,0.04180746,-0.02659955,-0.04931671,-0.04081688,0.032043,0.01906219,0.0363095,0.02461139,-0.00042281,0.01591078,-0.03211589,0.07348589,-0.00792067,0.00246904,0.01616526,0.04840343,-0.05022285,0.04623438,0.09280971,-0.00437675,0.0365617,0.03137548,-0.02655491,0.01040357,-0.07523226,0.0009268,-0.08351868,-0.00945437,0.02187248,-0.00976884,-0.01868742,0.04028003,0.04565423,0.02020381,-0.02981669,0.10158178,-0.03700272,0.01624193,0.03905678,-0.03172344,-0.02484618,0.0777676,0.06933922,-0.25971168,0.01307646,0.05121378,0.03954212,-0.04114204,-0.00608029,0.10404164,-0.073399,-0.04907107,0.03489751,0.01792716,0.00067719,-0.00721168,0.00333022,0.01360561,0.01550014,0.03744337,-0.00652162,0.03477107,-0.10767916,-0.05306575,0.08443037,0.20152915,-0.08199273,0.03899153,0.02397231,0.0050301,-0.0361109,0.09062725,0.01698058,-0.0186201,0.00149064,0.0569801,-0.00714784,-0.01038237,0.05100985,0.0143396,-0.03617762,-0.0103243,0.02258161,0.01330343,-0.00594905,0.02855763,-0.02314863,0.06853439,0.01877584,-0.01850827,-0.06655429,-0.03033414,0.02739594,-0.03308352,-0.05909153,-0.01802771,0.01323736,0.04830375,0.07509461,0.03390662,0.00148682,-0.03455833,-0.02371188,-0.00367623,-0.00928204,0.08650122,0.01109937,-0.03518247],"last_embed":{"hash":"14dlujj","tokens":145}}},"text":null,"length":0,"last_read":{"hash":"14dlujj","at":1751095117668},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Research & Testing","lines":[55,72],"size":542,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"14dlujj","at":1751095117668}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Research & Testing##user-feedback": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06418201,-0.01488114,0.00967341,-0.01161049,-0.02594419,0.02224758,0.02009353,0.06240815,-0.02140284,-0.00953041,0.04092976,-0.03200262,0.04126397,0.04345668,0.04377987,0.02356717,-0.0406105,-0.01368001,0.0154033,0.01770716,0.00431068,-0.04387369,0.02267757,-0.0261302,-0.03171653,0.03925841,-0.05467216,-0.08965214,-0.03513784,-0.16046213,0.02328084,0.0342109,0.11244209,0.0066881,0.03228825,0.05175846,0.00183106,-0.00294238,-0.02200884,0.01641404,0.05748631,-0.02456739,0.00272867,-0.00380189,0.01867187,-0.008942,-0.02371914,-0.05182499,-0.02051896,0.01591795,0.010077,-0.09588945,0.02609975,0.02767916,0.06223591,0.01580684,0.03502943,0.0523265,0.03749313,0.02143776,0.00284068,0.01821541,-0.17254619,0.07615402,0.02133549,0.04324895,-0.07088229,-0.03788016,0.01524983,0.03703209,-0.06477161,0.06560679,0.02462183,0.11897678,0.05640015,0.03459649,0.01783959,-0.04061789,0.00639702,-0.02252516,-0.07417619,0.03915859,-0.01305366,0.02912078,-0.02730567,-0.01325925,0.01125093,0.03194746,0.01816579,0.01891959,-0.03093717,-0.04230366,0.01296497,0.03966603,-0.06302652,-0.00659577,0.05420816,0.01331693,-0.12786719,0.1390986,-0.0664444,-0.02520255,-0.0037336,-0.04439515,0.00537952,0.01022948,0.01947348,-0.08819993,-0.00185847,0.00779903,-0.03157007,0.00500592,-0.04127029,-0.02808498,0.02117238,0.01490433,0.02206565,0.04424281,-0.02216594,-0.04351654,0.03376483,0.02838515,0.04285461,-0.04190943,0.01450862,-0.01343389,0.06245987,0.02650387,0.01432026,0.03518051,0.0131699,0.02742096,-0.09748874,0.0261339,-0.03408651,0.03750771,-0.02633681,-0.10389651,-0.00446877,0.05629767,0.01288183,0.0198906,0.00069633,-0.09611099,-0.07548066,0.10522087,0.0039659,0.05227113,-0.05010742,-0.04842526,0.03213917,0.03201698,-0.09444431,-0.01745581,0.01470815,0.03239989,0.0371948,-0.02849269,-0.03817688,0.05763915,0.01584123,-0.03533356,-0.07643277,0.13048999,-0.00049031,-0.12205927,-0.02630719,0.01372825,0.01114407,0.03966931,0.0154944,0.02519169,-0.0130057,0.02721621,0.06838609,-0.01655847,-0.034452,0.03823467,0.04735685,0.05893631,0.06137557,0.01877581,-0.04354558,-0.02018537,-0.01721753,-0.0245989,0.0084131,-0.06186974,0.03812551,-0.01340345,-0.04938321,-0.05867739,-0.01645805,0.05726498,-0.00724375,-0.02262614,0.01829239,-0.03120179,-0.06142573,0.03369737,0.02352141,0.02461365,-0.00301147,-0.00721248,-0.01036649,0.05106279,-0.0202398,0.00848102,0.05601446,0.05172681,-0.0132826,-0.00210234,0.05767746,0.01138569,-0.02073275,-0.03869267,0.01730004,0.01974506,0.05318014,0.07915786,-0.02283617,0.07708402,-0.04892277,-0.21640988,-0.02763949,0.03183322,0.03090901,0.00873312,-0.0914743,0.04360028,-0.04160252,-0.00995018,0.07028238,0.12956831,-0.03576051,-0.04331684,-0.02815724,0.05606694,-0.00597568,-0.07229464,-0.02309313,-0.04666572,-0.01703882,-0.01080808,0.01821655,0.033455,-0.05495966,0.02611609,-0.03965848,0.11732192,0.00759058,-0.04378414,0.06671349,0.01780337,0.02954946,-0.04850614,-0.230995,0.00797939,0.05440335,0.02841198,0.02468613,-0.00044125,-0.02129862,-0.04332649,0.04925274,0.01808297,-0.10761122,-0.05486077,-0.02453228,0.02153136,-0.07600465,-0.05591328,-0.02730647,-0.01563022,-0.02162351,0.01353598,0.04974556,-0.01286403,-0.06574733,-0.05001949,0.02635517,0.01589912,0.04151366,0.02250965,-0.00471009,0.00462425,-0.03290476,0.0524416,-0.01195918,-0.00398154,0.01675918,0.04005401,-0.04924661,0.0527631,0.10447253,-0.00326326,0.02937152,0.00770477,-0.02627077,0.01969098,-0.05951271,-0.01389686,-0.07426336,-0.00286503,0.02989429,0.00303814,0.00074234,0.02839714,0.02103436,0.01674568,-0.0320606,0.1027666,-0.04083978,0.01774485,0.0375776,-0.03288265,-0.04048752,0.075161,0.05270631,-0.26396516,0.02083303,0.03259745,0.03136779,-0.04359524,0.00816757,0.09396715,-0.05998514,-0.05111886,0.03357001,0.02664563,0.01391961,-0.01733957,-0.00189129,0.00578355,0.02456241,0.03807341,-0.01849588,0.02842836,-0.11996024,-0.04108517,0.07955293,0.20264918,-0.07247935,0.04975862,0.02532824,0.0131419,-0.04476469,0.06770684,0.01908994,-0.03096503,0.00830479,0.06850049,-0.03486712,-0.01678409,0.05034974,0.01096169,-0.03396414,0.0024354,-0.00104776,0.00791267,0.00341955,0.01560422,-0.0386815,0.07571406,0.03338139,-0.01465337,-0.06098884,-0.012551,0.01729315,-0.01836114,-0.05923359,-0.01844306,0.000502,0.06282856,0.06101704,0.02151762,-0.00203863,-0.02823476,-0.0336628,0.00016285,-0.00465717,0.08040308,-0.00416634,-0.04099621],"last_embed":{"hash":"1v2tjsw","tokens":86}}},"text":null,"length":0,"last_read":{"hash":"1v2tjsw","at":1751095117755},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Research & Testing##user-feedback","lines":[57,64],"size":266,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1v2tjsw","at":1751095117755}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Research & Testing##documentation": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07586554,-0.0509176,-0.00718395,-0.01704863,-0.00941418,0.05382165,-0.05323169,0.03012573,-0.03978335,0.00287021,0.04342942,-0.08793674,0.02192811,0.05330193,0.05141288,0.02110244,-0.02625744,0.00126097,-0.01969072,0.00445542,0.0408578,-0.01257049,0.02550131,-0.03980391,-0.04620078,0.03288786,0.00126838,-0.05006316,0.00561462,-0.15989156,-0.00361447,0.02991342,0.04608396,-0.00692833,0.02018916,0.03934088,-0.05287698,0.03487672,-0.05115343,-0.00732967,0.03992304,-0.01030366,0.0116656,-0.03837624,0.02753009,0.01460508,0.022004,-0.05497969,0.02616023,-0.02446993,-0.04128945,-0.06655283,0.01598248,-0.02300382,0.01596943,0.02875844,0.05382932,0.07451938,0.07733131,0.0151101,-0.00054045,-0.01447017,-0.2027649,0.10292701,0.00000298,0.04493168,-0.04770714,-0.01933126,0.06047156,0.04641165,-0.04111854,0.04460867,0.01463579,0.0773873,0.04177989,0.02337344,0.01867904,-0.04387476,0.0239002,-0.04062392,-0.08970211,0.00838519,-0.00735337,0.05062833,-0.01325095,0.01737998,-0.01636067,0.02954393,0.01927267,0.01320348,-0.03101723,-0.07013273,0.00269307,0.05244019,-0.00702503,0.00844556,0.02728122,-0.00013788,-0.08586348,0.14216831,-0.04257236,-0.05268809,0.03140639,-0.06482349,0.01455935,-0.02692292,-0.0137623,-0.04776073,-0.00923303,-0.0034712,-0.03166326,0.04711484,-0.05214568,-0.04560699,0.01715275,-0.01483829,-0.01132407,0.03137553,-0.03384229,0.00990442,0.0324099,-0.0073914,0.06029041,-0.03345244,-0.0159591,-0.04309723,0.01019506,0.03633016,0.03785788,0.03700215,0.01679205,0.0530752,-0.08031838,0.05122298,-0.02964288,0.04257997,-0.01139333,-0.09860858,0.00998479,-0.03104944,-0.01031586,0.01770477,0.02468081,-0.07414535,-0.03221215,0.1443177,-0.01765459,0.03562925,-0.02940705,-0.05108436,0.02576845,0.01368904,-0.06884993,0.00150323,0.02443268,-0.01616506,0.07943591,-0.0003015,-0.04972615,0.02260415,-0.00388992,-0.05780555,-0.07194474,0.13784587,0.00403229,-0.13421984,-0.04720539,0.02744748,0.01611941,-0.00995444,0.00765384,0.02017346,-0.02544391,0.04043965,0.08293941,-0.01967999,-0.05365218,0.03324836,0.02086184,0.04276478,0.04514373,-0.07896494,-0.01899341,-0.03916664,0.00470916,-0.0247588,0.01796422,-0.05928964,0.0231202,0.01469029,-0.05611803,0.01129327,0.02029281,0.03424052,0.00959717,0.02607963,0.00692642,-0.00312851,-0.03112554,0.01855917,0.01965162,0.01762102,0.00635154,0.0530354,-0.07838374,-0.01206292,-0.00715767,-0.03092756,0.09715658,0.05879301,-0.04845557,0.00026562,0.06144277,0.00083172,-0.06245302,-0.02131213,-0.00477065,0.03811415,0.03595065,0.05717963,-0.03825621,0.03330059,-0.07123139,-0.20332047,-0.03025569,0.02736254,0.00692017,-0.016585,-0.06509124,0.02508743,-0.02786966,-0.04797843,0.05863233,0.12646741,-0.08671751,-0.00785256,-0.06970039,0.02834798,-0.00079337,-0.01317065,0.00378392,-0.02736812,0.04393,-0.014451,-0.02063322,0.0105378,-0.11123824,0.01663478,-0.03954757,0.09834056,-0.01369895,0.03421396,0.01534181,0.02624602,0.03180236,-0.06420686,-0.185296,0.00969532,0.01625544,0.06422768,0.03826896,0.03250681,-0.0320049,-0.04140899,0.06164014,0.01297665,-0.10914336,-0.06311338,-0.04216284,-0.03858102,-0.05040773,-0.02140216,-0.02568474,-0.00140021,0.01329835,0.00626,0.04475794,-0.03135369,0.00228417,-0.01987705,0.02753402,0.00769769,0.02441653,0.0353678,0.012246,0.00741443,-0.01121486,0.07925945,0.00634524,0.00767937,-0.00297259,0.02573285,-0.02771822,0.04939264,0.08099351,-0.00300659,0.04481714,0.05187962,-0.02808027,-0.01465594,-0.07375797,0.0210585,-0.06899413,-0.01129866,-0.00883754,-0.01121731,-0.02471355,0.07631868,0.0713183,0.00486284,-0.03060775,0.10204021,-0.01372752,0.0120417,0.02535495,-0.00963891,0.0259208,0.10081195,0.07535495,-0.24486148,0.02706161,0.08341503,0.03906977,-0.0446204,-0.02512153,0.10506189,-0.08676095,-0.04225098,0.0399581,-0.02051177,-0.03775499,0.00879503,0.02386414,-0.00376826,0.01230721,0.06266108,0.02606828,0.03733483,-0.03001095,-0.07405174,0.06750348,0.21534495,-0.08592858,0.02858281,0.00244967,-0.03561002,-0.00364407,0.09174522,0.04001172,0.02349544,-0.0051771,0.04114057,0.02097301,0.00236188,0.08373698,0.02662896,-0.03013619,-0.0017645,0.05076664,0.01258689,-0.01930038,-0.01471272,0.0159872,0.07264459,0.01152062,-0.03620341,-0.05993484,-0.07227623,0.03202549,-0.05806139,-0.05800911,-0.02786713,0.03510652,0.04102456,0.08439962,0.0581232,-0.01661405,-0.05063011,-0.01021254,-0.00240241,-0.00900969,0.069653,0.01829215,-0.03146872],"last_embed":{"hash":"1x62tt1","tokens":86}}},"text":null,"length":0,"last_read":{"hash":"1x62tt1","at":1751095117803},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Research & Testing##documentation","lines":[65,72],"size":247,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1x62tt1","at":1751095117803}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Success Metrics": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03492742,-0.02481318,-0.01020586,-0.02803731,-0.04545984,0.01810291,0.01293527,0.04417825,0.02843606,0.01206148,0.06164649,-0.04510859,0.02234782,0.07203705,0.01057121,0.03284342,-0.01636627,0.02114072,-0.00447777,-0.0079712,-0.02166934,-0.07591776,0.02510951,-0.02511953,0.01081809,0.04875917,-0.08074541,-0.13248432,-0.05604654,-0.17501658,-0.01087569,-0.00187517,0.14797935,0.00222751,0.0189152,0.06439257,-0.05352585,-0.00091744,0.00015171,-0.02056495,0.07123481,-0.00223193,0.0126241,-0.00245333,0.01641572,-0.00389104,-0.01485198,-0.03848898,-0.0178064,0.02557701,0.02226711,-0.05822008,-0.00026655,0.05977357,0.05393559,0.06761092,0.02361555,0.05385209,0.04548447,0.02047005,-0.00061993,0.00573378,-0.16151175,0.05241052,0.03535605,0.02216977,-0.06121914,-0.0926685,-0.03944096,0.02276812,-0.05026191,0.05605843,0.03557161,0.11088009,0.03461931,0.05040159,0.03572649,-0.02875867,-0.00245049,-0.02303938,-0.0249595,-0.00382969,-0.063031,0.00851777,-0.03025349,0.03891183,-0.01855355,0.00902497,0.08390871,-0.01306822,-0.01961811,-0.02023466,-0.01290287,0.06895634,-0.05794473,-0.00304182,0.05624367,0.00735944,-0.06057386,0.1409748,-0.05629523,-0.03011781,-0.00316744,-0.05779691,0.07389131,-0.04101532,0.00862628,-0.07725156,-0.03051049,0.0178464,-0.02377366,0.00044385,-0.01800925,-0.02602,0.02572714,0.00368581,-0.03137665,0.02853114,0.00617988,-0.03430595,0.00687489,0.04980158,0.04310226,-0.02400493,-0.02999867,-0.01023082,0.10167446,0.04547235,0.01225458,0.04622134,0.03558316,0.01307114,-0.09487828,0.01253566,-0.06237405,0.02139241,0.02260043,-0.07631713,-0.04302428,0.0145323,0.00626147,0.04163761,0.04744902,-0.09249274,-0.03641617,0.16530433,0.06553356,0.05711932,-0.03846338,-0.03578024,-0.03372354,0.04051427,-0.08054491,-0.02848965,-0.00980401,0.03284992,0.0309283,0.00659327,-0.07631898,0.02953223,-0.0074271,-0.04992502,-0.07981497,0.13366945,-0.00150552,-0.12101763,0.0000094,0.00390451,-0.01344111,0.04466572,-0.01667917,0.0294231,-0.02124513,0.00950405,0.07846692,-0.03701693,-0.02903756,0.00585705,0.03669874,0.02816367,0.04945735,-0.01851361,-0.03262793,-0.02396345,-0.00056151,-0.04674322,0.03075793,-0.06872368,0.03582903,-0.03948567,-0.04366355,-0.02230334,0.03341932,0.04281172,0.0428181,-0.02394421,-0.0120053,-0.01502853,-0.04153215,0.00744028,-0.00752474,0.00244971,0.00299804,0.0253803,0.00531421,0.00113077,-0.02865597,-0.00654683,0.05012602,0.07415886,-0.0350429,-0.03018056,0.08283005,0.00883469,-0.0518144,-0.01595457,0.01912096,0.05037533,0.0069136,0.06788855,-0.03434422,0.10381005,-0.04226478,-0.20349126,-0.00739457,0.03943582,-0.00174745,-0.00796094,-0.09145585,0.01284697,-0.01311588,-0.00705084,0.05367747,0.11097576,-0.07895883,-0.03862627,-0.0155097,0.05241246,-0.0178504,-0.04543155,-0.01381355,-0.02463411,-0.01971937,0.02272598,0.04251602,-0.01927391,-0.02948826,0.0066874,-0.04246868,0.11300149,-0.00114196,-0.05004634,0.03665888,-0.02652024,0.04937614,-0.07302132,-0.15960428,0.03111027,0.0206989,0.07546314,-0.02334431,-0.00799558,0.00884559,-0.07907909,0.07729249,0.00158042,-0.11984155,-0.04378742,-0.00403575,0.02467666,-0.0730097,-0.04616044,-0.01918359,-0.00041228,-0.00769831,0.03647679,0.0297095,0.01448869,0.00005523,-0.03120218,0.03465276,-0.02622795,0.07054657,0.02459433,-0.05855062,-0.0517031,-0.00375645,0.05251722,0.01901506,-0.00641679,0.0133497,-0.02194861,-0.07240719,0.04176644,0.10683787,-0.02487477,-0.00004745,0.03116436,-0.06637198,0.00428315,-0.04665525,0.02209493,-0.03242945,0.05603302,-0.0017317,0.00854166,0.00593816,0.0559183,0.02430942,0.06508446,-0.02871457,0.04542425,0.00430255,0.05406195,0.03267048,0.00491849,-0.02722318,0.07226493,0.05472017,-0.23829305,0.02873759,0.0162492,-0.02060756,-0.01621636,-0.00380824,0.08425227,-0.04970061,-0.04045584,0.02510466,-0.00972612,0.00960842,-0.03773816,-0.04065479,-0.0126821,0.02058188,0.07111847,-0.02473307,0.02870512,-0.06179124,-0.02126279,0.04637159,0.19391006,-0.03564028,0.03031061,0.02783557,-0.0293373,-0.03364262,0.06262512,0.00949091,-0.02065283,0.030163,0.07412828,-0.02547171,-0.03329537,0.10154572,0.05449575,-0.01212536,0.01135406,0.02108066,0.03874822,0.01162112,0.01199215,-0.01325347,0.08176716,0.00201694,-0.03207584,-0.08674168,-0.01907372,0.02209582,-0.01032303,-0.06188403,-0.03433711,-0.0348368,0.06193141,0.07539082,-0.0159527,0.01654966,-0.05350892,0.00334485,-0.04629214,0.00676625,0.0725307,0.02171256,-0.07227773],"last_embed":{"hash":"15215bp","tokens":59}}},"text":null,"length":0,"last_read":{"hash":"15215bp","at":1751095117853},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Success Metrics","lines":[73,79],"size":221,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"15215bp","at":1751095117853}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Repository Links": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09906086,-0.05648178,-0.00868633,0.00362851,-0.02358217,-0.00323827,-0.05137372,0.04329957,-0.01714119,-0.02851205,0.05922104,-0.06101968,0.05520857,0.11929758,0.05399066,0.02473621,-0.01934541,-0.00077031,-0.01289994,-0.00304465,0.03295211,-0.03522483,0.04736934,-0.02930536,-0.04408549,0.03529563,-0.02514878,-0.08367667,-0.02154415,-0.18012238,0.03854441,0.03316623,0.06587896,-0.00136832,0.03131754,0.07108246,-0.01327606,-0.00985512,0.00144734,0.01093661,0.06991903,0.02549171,-0.02117202,-0.06065289,0.02107542,-0.01925378,-0.01412753,-0.01158088,-0.00065774,-0.01266253,-0.02342206,-0.06274631,0.06503408,0.00890938,0.04982533,0.05695562,0.04018364,0.08643017,0.09456825,0.03315775,0.03816041,0.01255656,-0.16769299,0.10936379,0.06412628,0.03022121,-0.01526451,-0.03341303,0.02907444,0.00969426,-0.05115863,0.03255048,0.0190046,0.06468759,0.04555342,0.01042336,0.0256398,-0.01353762,0.02639399,-0.04968578,-0.07231567,-0.00254153,-0.02481145,0.01831054,-0.01096142,-0.00120222,-0.02880557,0.0611384,0.03952648,-0.00573608,-0.05176762,-0.06474334,0.07501545,0.03836459,-0.02460359,0.04156779,0.04667559,0.01221129,-0.09695284,0.1386116,-0.05536479,-0.0129463,0.0086969,-0.03047092,0.02665894,-0.00337535,-0.00223246,-0.06836073,-0.02054151,0.00198322,-0.0084697,-0.01710551,0.00372984,-0.0165372,0.02523606,-0.03236188,0.02164708,0.02399277,-0.04837586,-0.01532906,0.00568481,0.02775768,0.06547625,-0.00532304,0.00104567,-0.00064982,0.00778665,0.0462645,0.04336558,0.01848418,0.0331068,0.01394552,-0.05660382,-0.00200398,-0.04707007,0.02403853,-0.00061371,-0.13706964,0.01029108,-0.00755638,-0.03130063,-0.03869306,0.01565023,-0.06994893,-0.06180556,0.13425116,0.0040728,0.03472875,-0.01753611,-0.04380777,0.00479681,0.02768746,-0.06487003,-0.00537593,0.03318347,0.02755413,0.02949864,0.03741215,-0.04282563,0.02907529,0.00712527,-0.03796887,-0.06323655,0.14285538,0.02147206,-0.111088,-0.02962027,-0.02194706,-0.05326426,0.02675022,0.00205946,0.02551982,0.01605367,0.01613739,0.07004848,-0.02276043,-0.02697069,-0.01045014,0.02433163,0.05015339,0.00056743,-0.04161547,-0.03370884,-0.03697201,0.01640891,-0.03948762,-0.02624081,-0.06178752,0.02392892,-0.02615703,-0.07217488,-0.06160672,0.06333572,0.03922284,0.02577259,-0.02314363,0.01956381,0.01387825,-0.00406848,0.00994653,0.04735934,0.06093787,-0.00432166,0.03448293,-0.06341308,-0.02094454,-0.04848373,-0.00589783,0.05645207,0.0482397,-0.04418835,0.00049666,0.04693773,0.00553627,-0.05338433,-0.03418406,0.00947889,0.0336117,0.04936522,0.05396752,-0.05403702,0.04874412,-0.07329636,-0.22347146,-0.0155833,-0.00020793,-0.02596719,-0.03366694,-0.10859042,0.02031,-0.06771476,-0.02108932,0.08922681,0.14152238,-0.02158681,-0.02168847,-0.08676089,0.0189744,-0.00323112,-0.0164167,0.0027969,-0.02243019,0.02556936,0.00722212,0.03447601,-0.02920217,-0.06598797,-0.003649,-0.04602372,0.12641849,0.04010197,0.00317159,0.03240255,0.00121191,0.05814214,-0.02190181,-0.20471123,0.00809178,0.02876686,0.02468991,0.00124582,0.00754931,-0.00503803,-0.08886684,0.06501062,0.00221468,-0.08072162,-0.05239949,0.00346058,-0.0014232,-0.09845334,-0.00079832,0.00112795,-0.02271053,0.0505259,-0.01491907,0.0771376,0.00916067,0.03294883,-0.00386696,-0.01687346,0.01291459,0.00480615,0.04661863,-0.001788,-0.02042172,-0.0359441,0.066925,0.01273568,0.02198,-0.0180363,0.0065271,-0.01236805,0.05064759,0.09568986,0.01806284,0.00161734,-0.00571356,-0.0333303,-0.00784956,-0.05539258,0.03123009,-0.06851288,0.02788018,0.02722102,0.00749118,0.00509489,0.05197334,0.0591025,0.00666873,-0.05394056,0.09624884,-0.00392389,0.03206014,0.02584217,-0.02010884,-0.01496877,0.1129142,0.07014779,-0.23698427,0.03867207,0.095208,-0.00792263,-0.0446821,-0.00961009,0.08502125,-0.07618665,-0.05884571,0.01604035,-0.00742621,-0.01203011,-0.0364701,-0.02318662,-0.02439533,0.02642493,0.03603576,0.00151137,0.01342813,-0.01741396,-0.06566057,0.05305019,0.19703731,-0.05042331,0.04560758,0.00106131,-0.05343201,-0.0081591,0.05878282,0.03273271,-0.00841473,-0.03986134,0.06019538,-0.01312869,-0.00152856,0.05006623,0.02371081,-0.00676741,0.00121855,0.04188128,-0.01034808,0.03359202,-0.03150263,-0.00259042,0.08029081,0.03147012,-0.04024122,-0.02284893,-0.04403559,0.01675878,-0.01758617,-0.0788014,-0.02998983,-0.00947076,0.06320521,0.0552675,0.00914386,-0.022125,-0.08486212,0.00404356,0.02222921,-0.01517573,0.03923909,-0.00644714,-0.06701382],"last_embed":{"hash":"s643th","tokens":88}}},"text":null,"length":0,"last_read":{"hash":"s643th","at":1751095117891},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Repository Links","lines":[80,85],"size":204,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"s643th","at":1751095117891}},
