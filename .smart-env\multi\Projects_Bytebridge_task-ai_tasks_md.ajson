"smart_sources:Projects/Bytebridge/task-ai/tasks.md": {"path":"Projects/Bytebridge/task-ai/tasks.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08818076,-0.03257781,0.01520474,-0.04013559,-0.05104161,0.00273826,-0.03201558,0.03850737,0.01990391,-0.00594108,0.0656485,-0.01552621,0.04916392,0.06030954,0.00711526,-0.01717735,-0.03373131,0.01850539,0.02467568,-0.03415378,-0.00375662,-0.02985869,0.00284507,-0.01509707,-0.03625585,0.0765328,-0.03348406,-0.07657781,-0.02495872,-0.21419837,0.03154343,0.00584025,0.07368048,-0.02114806,-0.00370033,0.05669218,-0.02353977,0.03123403,0.00092828,-0.01509763,0.0753062,0.02999861,-0.0017855,-0.00800059,0.001323,-0.03684048,-0.03753575,-0.03731849,-0.01077224,-0.02307384,-0.02860586,-0.06844568,0.03894741,0.01997241,0.03616283,0.03631891,0.02591023,0.10020672,0.02581732,-0.00956068,0.02565576,-0.00347341,-0.19675221,0.10861785,0.02332397,0.0211919,-0.07351113,-0.06012909,0.01923011,0.02926983,-0.06824677,0.05014936,0.01353221,0.05446823,0.04354321,0.01836132,0.0237023,-0.00933349,0.03830216,-0.02250942,-0.02054425,-0.0029166,-0.04068449,0.05040745,-0.03435706,0.00176857,-0.00246842,-0.00530312,0.05824612,-0.02829834,-0.00555941,-0.07650927,-0.02103236,0.04925281,-0.0313375,0.01294113,0.02651323,0.01821632,-0.09078238,0.10384817,-0.02437831,0.03498591,-0.01472148,-0.05230106,0.04922473,-0.01777668,0.0585352,-0.04273817,-0.04913544,-0.02389282,-0.03466142,0.02269627,-0.01891517,-0.01301522,-0.00784243,0.04929836,-0.01708559,0.03599474,-0.04670512,-0.02152161,0.02801481,0.01248243,0.05596159,0.02027158,-0.04711152,-0.00643236,0.03397459,0.06959394,-0.01049844,0.03761068,0.05062947,0.04948963,-0.05142873,0.04956781,-0.01825077,0.03991046,-0.00525585,-0.06205093,-0.01406159,-0.01308435,-0.04869189,0.05202744,0.0122342,-0.09830242,-0.06540846,0.14163141,0.02413225,0.0104101,-0.00624145,-0.07600603,-0.01655607,0.03587966,-0.05369832,-0.00333767,0.00033807,-0.01522254,0.05088539,-0.00628882,-0.05481093,0.04333949,-0.00477005,-0.04826753,-0.07583583,0.14831293,0.01370985,-0.11775173,-0.04294288,-0.00864904,-0.01501554,0.02017677,-0.02199384,0.01424406,-0.0188234,0.03603676,0.06256919,-0.01039859,-0.07531993,0.02146051,0.04424096,0.01490681,0.06418505,-0.05133718,-0.03559332,-0.03909868,0.00831711,-0.06476068,0.01599781,-0.02902303,0.03818962,-0.0347851,-0.03291979,0.00353562,0.06557667,0.05366208,-0.01061865,-0.05383711,0.01770657,-0.00104969,-0.02316208,-0.0084323,0.08974566,0.05632477,0.00699661,0.01642375,-0.02412222,-0.01880492,-0.02510905,-0.02874617,0.05703373,0.0614044,-0.0534007,-0.01377078,0.08835125,0.00742361,-0.03933888,-0.01820736,-0.01319429,0.07082991,0.02036477,0.04525971,-0.01524281,0.12362473,-0.02195871,-0.22305551,-0.02042782,0.01477184,-0.01852509,-0.03364538,-0.06551313,0.03182022,-0.03517329,-0.03656309,-0.01283373,0.05134915,-0.06261354,0.00685425,-0.01217217,0.01927828,-0.01685726,0.00117599,0.00243593,-0.00617998,-0.00799276,0.04332332,0.05056807,-0.0257455,-0.05939149,0.02599986,-0.03320459,0.14569277,-0.03960654,0.01937136,0.03465784,0.01761561,0.05082559,-0.04009909,-0.19853622,0.02694846,0.05445008,0.06259556,0.02125934,0.00283657,-0.03375379,-0.04399013,0.04637539,0.01407716,-0.11385295,-0.08654179,-0.03002422,-0.02502852,-0.1103533,0.00658855,0.00759575,0.00035095,0.00381477,-0.00744832,0.0264534,-0.01204716,-0.03739474,-0.02499022,0.01631754,-0.04131664,0.0272604,0.00908703,-0.04489391,0.00786253,-0.01389496,0.0698814,0.02734675,-0.01748144,-0.02792457,0.02898672,-0.07374894,0.00079859,0.08910729,-0.01238256,-0.02548204,0.04041862,-0.0350189,-0.03887422,-0.02385405,0.01597737,-0.0414743,-0.03051096,0.01518742,0.00874657,0.00591111,0.04440899,0.05358102,0.05120378,-0.03623564,0.05385605,-0.02948215,-0.01131058,-0.00407884,-0.00218024,-0.00913247,0.1024794,0.04327058,-0.23403066,0.0252109,0.04555558,-0.02661033,-0.05067148,0.00369165,0.05948839,-0.05339009,-0.04486581,-0.00138161,-0.0683822,0.01687618,-0.01372012,0.02743245,-0.00694491,0.03031066,0.06212249,0.01297348,0.06638177,-0.11245878,-0.00091622,0.05674509,0.23006834,-0.0086586,0.05578524,0.0214209,-0.00861596,-0.03757595,0.10110184,0.05401799,0.01052434,0.0229685,0.0971262,0.0126629,0.00841789,0.06579699,0.00775205,-0.01100177,0.00006621,0.05579548,0.00900155,0.03709165,0.009462,-0.00095806,0.09388773,0.02722102,-0.01225027,-0.06529308,-0.03601383,0.0167786,-0.01408813,-0.05015035,-0.01077355,-0.01892072,0.04127252,0.02261116,0.00848801,0.00363257,-0.03858613,0.02942227,0.0023958,0.00220008,0.08675223,0.04908897,-0.04713201],"last_embed":{"hash":"1hyuc1q","tokens":439}}},"last_read":{"hash":"1hyuc1q","at":1751134777886},"class_name":"SmartSource","last_import":{"mtime":1751100030795,"size":3056,"at":1751134757746,"hash":"1hyuc1q"},"blocks":{"#__task.ai - Project Tasks":[1,85],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}":[3,36],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development":[5,12],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development#{1}":[6,6],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development#{2}":[7,7],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development#{3}":[8,8],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development#{4}":[9,9],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development#{5}":[10,10],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development#{6}":[11,12],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes":[13,20],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes#{1}":[14,14],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes#{2}":[15,15],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes#{3}":[16,16],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes#{4}":[17,17],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes#{5}":[18,18],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes#{6}":[19,20],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow":[21,28],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow#{1}":[22,22],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow#{2}":[23,23],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow#{3}":[24,24],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow#{4}":[25,25],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow#{5}":[26,26],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow#{6}":[27,28],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards":[29,36],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards#{1}":[30,30],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards#{2}":[31,31],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards#{3}":[32,32],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards#{4}":[33,33],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards#{5}":[34,34],"#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards#{6}":[35,36],"#__task.ai - Project Tasks#Technical Development":[37,54],"#__task.ai - Project Tasks#Technical Development##core-functionality":[39,46],"#__task.ai - Project Tasks#Technical Development##core-functionality#{1}":[40,40],"#__task.ai - Project Tasks#Technical Development##core-functionality#{2}":[41,41],"#__task.ai - Project Tasks#Technical Development##core-functionality#{3}":[42,42],"#__task.ai - Project Tasks#Technical Development##core-functionality#{4}":[43,43],"#__task.ai - Project Tasks#Technical Development##core-functionality#{5}":[44,44],"#__task.ai - Project Tasks#Technical Development##core-functionality#{6}":[45,46],"#__task.ai - Project Tasks#Technical Development##github-maintenance":[47,54],"#__task.ai - Project Tasks#Technical Development##github-maintenance#{1}":[48,48],"#__task.ai - Project Tasks#Technical Development##github-maintenance#{2}":[49,49],"#__task.ai - Project Tasks#Technical Development##github-maintenance#{3}":[50,50],"#__task.ai - Project Tasks#Technical Development##github-maintenance#{4}":[51,51],"#__task.ai - Project Tasks#Technical Development##github-maintenance#{5}":[52,52],"#__task.ai - Project Tasks#Technical Development##github-maintenance#{6}":[53,54],"#__task.ai - Project Tasks#User Research & Testing":[55,72],"#__task.ai - Project Tasks#User Research & Testing##user-feedback":[57,64],"#__task.ai - Project Tasks#User Research & Testing##user-feedback#{1}":[58,58],"#__task.ai - Project Tasks#User Research & Testing##user-feedback#{2}":[59,59],"#__task.ai - Project Tasks#User Research & Testing##user-feedback#{3}":[60,60],"#__task.ai - Project Tasks#User Research & Testing##user-feedback#{4}":[61,61],"#__task.ai - Project Tasks#User Research & Testing##user-feedback#{5}":[62,62],"#__task.ai - Project Tasks#User Research & Testing##user-feedback#{6}":[63,64],"#__task.ai - Project Tasks#User Research & Testing##documentation":[65,72],"#__task.ai - Project Tasks#User Research & Testing##documentation#{1}":[66,66],"#__task.ai - Project Tasks#User Research & Testing##documentation#{2}":[67,67],"#__task.ai - Project Tasks#User Research & Testing##documentation#{3}":[68,68],"#__task.ai - Project Tasks#User Research & Testing##documentation#{4}":[69,69],"#__task.ai - Project Tasks#User Research & Testing##documentation#{5}":[70,70],"#__task.ai - Project Tasks#User Research & Testing##documentation#{6}":[71,72],"#__task.ai - Project Tasks#Success Metrics":[73,79],"#__task.ai - Project Tasks#Success Metrics#{1}":[74,74],"#__task.ai - Project Tasks#Success Metrics#{2}":[75,75],"#__task.ai - Project Tasks#Success Metrics#{3}":[76,76],"#__task.ai - Project Tasks#Success Metrics#{4}":[77,77],"#__task.ai - Project Tasks#Success Metrics#{5}":[78,79],"#__task.ai - Project Tasks#Repository Links":[80,85],"#__task.ai - Project Tasks#Repository Links#{1}":[81,81],"#__task.ai - Project Tasks#Repository Links#{2}":[82,82],"#__task.ai - Project Tasks#Repository Links#{3}":[83,83],"#__task.ai - Project Tasks#Repository Links#{4}":[84,85],"#taskmaster #opensource #github #agent #ux #frontend #navigation":[86,87]},"outlinks":[],"last_embed":{"hash":"1hyuc1q","at":1751134776856},"key":"Projects/Bytebridge/task-ai/tasks.md"},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08785535,-0.03290265,0.01559757,-0.04172862,-0.05164723,0.00220792,-0.03816653,0.03721621,0.01669543,-0.00639818,0.06675877,-0.0162526,0.05034116,0.05909158,0.00846228,-0.01861739,-0.03202128,0.01869659,0.02400447,-0.03337916,-0.00229644,-0.03034861,0.00623946,-0.01096754,-0.0337835,0.0776405,-0.032161,-0.07640878,-0.02367643,-0.21583898,0.03035937,0.00137794,0.0712093,-0.01764066,-0.00582357,0.05465914,-0.02531628,0.03328997,-0.00075503,-0.01504493,0.07319536,0.03237911,-0.0030021,-0.00679568,-0.00128405,-0.03845612,-0.03529708,-0.03714671,-0.01130736,-0.02468972,-0.03128212,-0.067238,0.03827847,0.01950476,0.03404908,0.03617026,0.02414993,0.09599517,0.01892197,-0.01325697,0.02673172,-0.00011588,-0.19568017,0.10911141,0.02136785,0.02381075,-0.07603256,-0.05521529,0.01934101,0.02587746,-0.07121277,0.05013591,0.01283128,0.05864877,0.0466543,0.01716817,0.02315362,-0.01118703,0.03531989,-0.02127791,-0.02059271,0.00141671,-0.03743349,0.04752883,-0.03528784,0.00392909,0.00112349,-0.00536758,0.05959391,-0.02913156,-0.00444249,-0.07830861,-0.01875891,0.05125357,-0.02920521,0.01106857,0.02753553,0.01726804,-0.08820914,0.09710343,-0.02285395,0.03661966,-0.01478367,-0.05109422,0.04965896,-0.01295893,0.06237971,-0.04334579,-0.04583794,-0.02471297,-0.03507165,0.02160118,-0.0218803,-0.01499656,-0.00619608,0.04909965,-0.01606205,0.0343239,-0.04940206,-0.02082068,0.03063031,0.01208756,0.05406995,0.02066524,-0.04538726,-0.00320491,0.03473394,0.06860629,-0.00808698,0.0400764,0.05036519,0.05365475,-0.05532262,0.04631777,-0.01784791,0.04034433,-0.00857098,-0.06204828,-0.01175349,-0.01186582,-0.04648393,0.05532151,0.00949715,-0.10057782,-0.06090789,0.14415459,0.02426316,0.01221372,-0.01009777,-0.07958703,-0.01263716,0.0378343,-0.05445129,-0.0024358,0.00096412,-0.01485184,0.05539364,-0.00961128,-0.05256924,0.04388885,-0.00523646,-0.05117839,-0.07964191,0.14706534,0.01284459,-0.12187278,-0.04412878,-0.00655685,-0.01298794,0.0176323,-0.01698313,0.01080269,-0.01543912,0.03836391,0.06462942,-0.0078733,-0.07568026,0.01823582,0.04665971,0.01350397,0.06608924,-0.05141063,-0.04075171,-0.03597261,0.00764497,-0.06169704,0.01790502,-0.02798897,0.03887011,-0.03417218,-0.03430722,0.00298177,0.06357691,0.0508537,-0.01089278,-0.05430508,0.01230766,-0.00167367,-0.02014293,-0.01412261,0.08850358,0.05764224,0.00361124,0.0136753,-0.02596451,-0.01881819,-0.02193596,-0.03172252,0.0607014,0.05991726,-0.05285668,-0.01396567,0.08603302,0.01131738,-0.03759462,-0.01720557,-0.00996737,0.0715042,0.0228251,0.04593852,-0.01251546,0.12075727,-0.02570137,-0.22252342,-0.02131349,0.017764,-0.01721761,-0.03722536,-0.06487732,0.03394274,-0.03303938,-0.03703535,-0.00956655,0.05332308,-0.05788629,0.00467031,-0.01411985,0.02061885,-0.01489411,-0.00513207,0.00147443,-0.00808622,-0.00778971,0.03955889,0.05233368,-0.0228993,-0.05632751,0.02531014,-0.03293164,0.1451548,-0.04028722,0.01797439,0.03594948,0.0202395,0.05096225,-0.03870794,-0.19914033,0.02549603,0.05810032,0.06136537,0.02029983,0.00585242,-0.03365902,-0.03737662,0.04564513,0.01430397,-0.11698432,-0.08855824,-0.03140547,-0.02716028,-0.10785315,0.00652946,0.00891396,0.00002983,0.00527758,-0.0045421,0.02537423,-0.01430527,-0.03855747,-0.0282154,0.01678428,-0.04037813,0.02397635,0.00785994,-0.04456126,0.00978463,-0.01356704,0.0699551,0.02747302,-0.01990583,-0.02607463,0.03043674,-0.0730513,-0.0017402,0.08614808,-0.01197897,-0.02295727,0.03822275,-0.03410722,-0.0407419,-0.02541287,0.01201419,-0.04283426,-0.0320293,0.0199715,0.00716065,0.0077976,0.03846824,0.05804929,0.04648525,-0.03624781,0.05187865,-0.03223282,-0.01618666,-0.00675556,-0.00345941,-0.00842491,0.09818695,0.04269054,-0.23344159,0.02186831,0.04477517,-0.02371469,-0.05010967,0.00309934,0.05543321,-0.05687332,-0.04309798,-0.00420083,-0.06828535,0.01773152,-0.02065697,0.02374524,-0.00687221,0.03307719,0.05979408,0.01452845,0.06641577,-0.11987376,0.00311629,0.05487111,0.23402593,-0.00707566,0.05110529,0.0241241,-0.00299223,-0.03673083,0.10255509,0.058652,0.01196175,0.0252542,0.10006899,0.0158706,0.00716465,0.06639124,0.00959361,-0.00788594,0.00048313,0.05414084,0.01051035,0.03663731,0.00348137,-0.00056377,0.09206183,0.02292398,-0.009964,-0.06753254,-0.03524581,0.01637321,-0.01376794,-0.04712068,-0.00910634,-0.01325329,0.04202732,0.02005679,0.01231876,0.00474945,-0.03655861,0.03108709,0.00613114,0.00075514,0.08695901,0.05062337,-0.04555957],"last_embed":{"hash":"14u5bl0","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"14u5bl0","at":1751134777146},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks","lines":[1,85],"size":2990,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"14u5bl0","at":1751134777146}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08203925,-0.02778284,0.02535495,-0.03771603,-0.05246742,0.01996028,-0.02625216,0.03772302,0.02040473,-0.01246965,0.0565788,-0.00201434,0.03560795,0.0548531,0.01114176,-0.01480985,-0.033744,0.00343587,0.01275609,-0.02202688,-0.00258746,-0.02638431,0.00665066,-0.02129719,-0.03307916,0.06828123,-0.04590024,-0.07173004,-0.01469094,-0.21270224,0.03399245,0.00168816,0.07797252,-0.02826166,-0.01283504,0.03908857,-0.03236444,0.03681108,0.01199858,-0.01490246,0.07612453,0.02591255,0.00236755,0.00081339,-0.00004734,-0.03763132,-0.02995126,-0.04137635,-0.01027472,-0.02580739,-0.02445837,-0.06955378,0.03494336,0.02127294,0.04002175,0.02207666,0.02579523,0.10369307,0.01495258,-0.01149159,0.0208831,-0.00441991,-0.20600015,0.10844604,0.00923574,0.02046903,-0.07498694,-0.06522743,0.00210868,0.03000464,-0.07657819,0.05276699,0.00718803,0.0516891,0.04120902,0.00967332,0.01822974,-0.02127832,0.03168443,-0.00579295,-0.025693,-0.00061356,-0.04544748,0.02810141,-0.02785556,0.00126409,0.00194265,-0.00857874,0.03383084,-0.01326392,-0.00555886,-0.08223499,-0.01853413,0.05686338,-0.02922991,0.00621333,0.02477303,0.02473064,-0.10493235,0.09929218,-0.01258116,0.03109505,-0.00742502,-0.05583166,0.05144583,-0.00853149,0.06629755,-0.03408672,-0.04357075,-0.02460796,-0.03093244,0.03001836,-0.01781463,-0.00414003,-0.00541482,0.06011886,-0.01046119,0.03602279,-0.0437235,-0.01731648,0.03356676,0.0170003,0.05171978,0.01588299,-0.04415568,-0.00788855,0.04695672,0.06808538,-0.01731129,0.03592982,0.05439287,0.05228403,-0.05035788,0.05500149,-0.01233706,0.02888274,-0.02318874,-0.05078371,0.0003581,-0.00494993,-0.03994237,0.06128711,0.00778966,-0.10275172,-0.05502579,0.14044818,0.03106177,0.00966245,-0.01036248,-0.06381242,-0.00680462,0.02633796,-0.05784439,-0.00895705,-0.0099203,-0.01460971,0.05423544,-0.02149972,-0.06291264,0.04957563,-0.00499171,-0.05163752,-0.08564922,0.14731707,0.01431995,-0.12483392,-0.03917858,-0.00969643,-0.00240694,0.0192476,-0.02029359,0.00725051,-0.0199826,0.05384988,0.0787764,0.00186399,-0.08195361,0.01780171,0.03914219,0.00573064,0.07512003,-0.054258,-0.03920174,-0.03955716,0.00570191,-0.05843798,0.02343863,-0.02442276,0.04951657,-0.03588647,-0.02874241,0.01175553,0.0473268,0.05421098,-0.01448557,-0.05723658,0.01554963,-0.01341394,-0.02833577,-0.02520406,0.07298871,0.04544567,0.00928146,0.0058802,-0.01202388,-0.0069183,-0.02000326,-0.03481725,0.06212292,0.06106964,-0.05557943,-0.02862442,0.09086765,0.01327446,-0.02728568,-0.00515042,-0.01162177,0.07128391,0.01769152,0.04219341,-0.01672203,0.1298551,-0.01470454,-0.21655507,-0.01561063,0.02283915,-0.01717448,-0.0368158,-0.07200813,0.02877411,-0.02306475,-0.03919605,-0.01173505,0.04539944,-0.07283153,0.01000252,0.00329409,0.02273786,-0.01709624,0.00545227,0.00668502,0.00481661,-0.00801041,0.01978074,0.06041064,-0.01934215,-0.06210247,0.03040113,-0.03377431,0.1477799,-0.04602078,0.01082984,0.0341578,0.01941139,0.05078765,-0.04792943,-0.20002705,0.01986283,0.06063667,0.06967436,0.01617059,0.00481651,-0.03453654,-0.02562218,0.04006338,0.02540242,-0.10935161,-0.08937629,-0.03321809,-0.01779323,-0.10610083,-0.00497579,0.00534177,-0.00939847,0.003658,-0.00190279,0.02228997,-0.00742891,-0.0494776,-0.03851704,0.02621027,-0.04744957,0.04965249,0.00430266,-0.03746684,0.00857883,-0.02873515,0.06239642,0.02329439,-0.02608456,-0.02250254,0.03593335,-0.07723013,-0.0010049,0.07622676,-0.01865635,-0.02707887,0.04591091,-0.04423911,-0.0433393,-0.01776582,0.00866201,-0.03703417,-0.04396469,0.01344187,0.00112176,0.00467535,0.04600352,0.05039218,0.03965406,-0.02935214,0.03714992,-0.03433511,-0.01371158,-0.01182383,-0.00442247,-0.00773773,0.08880197,0.02968165,-0.23413154,0.01750633,0.0475221,-0.01282298,-0.04294053,0.02064049,0.05739997,-0.0445173,-0.05117227,0.00256509,-0.07215984,0.0049521,-0.00533135,0.03166635,0.0012694,0.03423492,0.05989865,0.01975697,0.06919775,-0.12653463,0.00626275,0.06563332,0.2315197,-0.00641149,0.04464041,0.01859435,-0.00172106,-0.04516734,0.09657978,0.05759171,0.00884984,0.0352142,0.09413033,0.03111997,0.00974337,0.06901772,0.00221524,-0.01467411,-0.00702714,0.05357414,0.00907497,0.02841199,0.00338971,-0.00302826,0.09310783,0.03152885,0.01619408,-0.06331775,-0.03543317,0.02027428,-0.01120839,-0.04526324,-0.0063856,-0.00812656,0.04244607,0.03230314,0.00048323,0.00310636,-0.02901847,0.03248958,-0.0016136,0.00916176,0.09101965,0.03874462,-0.04767773],"last_embed":{"hash":"bk8263","tokens":350}}},"text":null,"length":0,"last_read":{"hash":"bk8263","at":1751134777400},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}","lines":[3,36],"size":1420,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"bk8263","at":1751134777400}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05795189,-0.02862647,0.0141423,-0.05124723,-0.03506882,0.02189348,-0.0258557,0.02712435,0.02360762,0.00333918,0.03495022,-0.01452999,0.02989786,0.04721986,0.01564047,0.01376301,-0.02372804,0.00606802,0.01619826,-0.03194203,0.01730355,-0.0177162,0.00041363,-0.05451313,-0.03590301,0.0681093,-0.02105163,-0.0651465,-0.00371821,-0.16332816,0.03061542,-0.00646034,0.07573511,-0.03865084,0.0135711,0.03305581,-0.02725752,0.04348135,-0.00680539,-0.00211164,0.07497091,0.01606797,0.00008266,-0.01547811,0.01036773,-0.03356206,-0.02041116,-0.0459893,-0.00110973,-0.0317733,0.00958441,-0.05124718,0.01758502,0.00779344,0.04729015,0.01562955,0.02735142,0.08536506,0.00923745,-0.0021769,-0.00422102,-0.00893191,-0.20372719,0.11251732,0.01720848,0.02749017,-0.04798014,-0.06724691,0.01765196,0.0193219,-0.06894506,0.04152539,0.00686287,0.04687249,0.04214977,0.01306151,0.02463476,-0.01834751,0.03184607,-0.00791149,-0.02493561,-0.01179485,-0.0594011,0.03166878,-0.03134983,0.03035683,0.00451943,-0.00227447,0.02486493,-0.00348139,-0.02748076,-0.10575963,-0.02014602,0.04401508,-0.03094624,0.02331356,0.03955673,-0.00490155,-0.12246116,0.11395416,-0.00771073,0.01271576,-0.00932529,-0.03522978,0.04940175,-0.01320886,0.03653873,-0.06032484,-0.02557114,-0.03376684,-0.04021832,0.03062661,-0.02078189,-0.01254331,0.01153381,0.0204558,-0.02024904,0.02569089,-0.05039513,-0.00362989,0.03389239,0.00535887,0.02805,0.02661092,-0.03320352,-0.01958228,0.05490078,0.06315096,-0.02051254,0.0264499,0.03866725,0.05731928,-0.05277579,0.04656975,0.00434136,0.02445461,-0.00465694,-0.04849273,0.0128628,-0.01498073,-0.05511576,0.05880573,0.02227864,-0.10293917,-0.02575929,0.11203656,0.0316458,0.02364242,-0.02358894,-0.0189241,-0.01357802,0.01274524,-0.06663522,-0.00512149,-0.00582119,-0.00057172,0.03317925,-0.01021235,-0.0309245,0.05028908,0.0047712,-0.05251824,-0.06525922,0.16438641,-0.00749632,-0.15006359,-0.02843229,0.00708773,0.03171203,0.01723961,-0.02345781,0.0228447,-0.00655478,0.04781376,0.07686957,-0.00305439,-0.06932028,0.02617407,0.04201901,0.01327226,0.09160478,-0.05189904,-0.03236829,-0.04445753,-0.01766261,-0.07099105,0.04031164,-0.03711928,0.03713474,-0.02291497,-0.05730002,0.02737095,0.03020028,0.05729555,0.00518579,-0.04461765,0.01686491,-0.00924695,-0.02015524,-0.03110715,0.05744321,0.0619914,-0.00055313,0.02452795,-0.06765333,-0.0126153,-0.01652895,-0.04493491,0.08003177,0.07326655,-0.05433286,-0.02206019,0.10569271,0.025063,-0.02224289,0.02531369,-0.01859445,0.07950645,0.03508262,0.03034504,-0.00724093,0.1079361,-0.00460538,-0.22729245,0.00273096,0.03479095,-0.03961015,-0.05976833,-0.07310419,0.03384112,-0.02025341,-0.0683642,-0.00010786,0.0660973,-0.07654767,0.00767901,0.01952937,0.0049151,-0.00810352,0.00346741,-0.01687259,-0.00691112,-0.013779,-0.00931401,0.06176393,-0.01391947,-0.06280912,0.02880385,-0.01677396,0.13895485,-0.04092514,0.03852513,0.01215841,0.01746007,0.0285458,-0.05511836,-0.19446316,0.02701478,0.05402055,0.07998569,0.00810355,0.01363589,-0.02234592,-0.03701951,0.03288896,0.01869115,-0.13670853,-0.06566159,-0.04360178,-0.0142678,-0.11928525,-0.02759754,-0.00200747,-0.03938764,-0.00883596,0.00789402,0.0491663,-0.01126365,-0.04037206,-0.05370425,0.03461056,-0.02876907,0.07039893,0.02761884,-0.02164701,-0.02340939,-0.05070106,0.03762717,0.02893157,-0.03074427,-0.03432386,0.05262564,-0.08824176,0.0057288,0.080274,0.0019223,-0.01779504,0.04410191,-0.05351507,-0.05315508,-0.01355711,-0.00162198,-0.01871363,-0.03497629,0.01039937,0.01339334,0.01011088,0.05148127,0.03793344,0.03691462,-0.02348056,0.05924498,-0.03484287,-0.03467555,-0.01077792,-0.01035591,0.01338847,0.09706871,0.00632981,-0.24876797,0.02683272,0.0417087,0.00395129,-0.05522222,0.04455085,0.07525939,-0.04236633,-0.06509814,0.00224794,-0.06653002,-0.01773736,0.0229073,0.06951718,0.00740985,0.03707587,0.08483707,-0.0033525,0.02971743,-0.11186042,0.00441261,0.05448047,0.21339992,0.00137956,0.04628082,0.01464048,-0.03553462,-0.02177666,0.08831986,0.07486389,0.02199752,0.03143438,0.06094294,0.01719004,0.00258819,0.09177072,0.02879105,-0.02177202,-0.02068423,0.05451135,-0.01274223,0.01697761,-0.00555447,0.01161129,0.05468975,0.04589654,-0.00659198,-0.06525453,-0.04266506,0.00994123,-0.02822942,-0.03818308,-0.01571506,-0.00699875,0.05511405,0.05904378,0.01353528,0.00495016,-0.02498464,0.04196649,-0.00389587,0.0038551,0.08978564,0.03645793,-0.04038149],"last_embed":{"hash":"xzylla","tokens":105}}},"text":null,"length":0,"last_read":{"hash":"xzylla","at":1751095117135},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##frontend-development","lines":[5,12],"size":322,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"xzylla","at":1751095117135}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07396998,-0.05960165,0.05514104,-0.04537429,-0.0411469,-0.00487179,-0.04391704,0.02238407,-0.00341674,-0.03188699,0.08595315,-0.05298772,0.05181148,0.06798581,0.03238894,-0.00243102,-0.03965315,0.03785537,0.02492642,-0.00871087,-0.0343937,0.01169629,0.01129148,-0.01520851,-0.04562987,0.11595779,-0.03167249,-0.06497458,0.00549252,-0.21034195,0.00967285,-0.00317721,0.04141365,0.01479029,0.01656822,0.05729159,-0.04258784,0.01793519,0.0361877,-0.01926678,0.07645765,0.02767711,-0.02521588,-0.02206295,-0.00008256,-0.0186399,-0.01512865,-0.02390795,-0.01796177,-0.03914615,-0.06017051,-0.08228204,0.03511368,0.0347927,0.03197002,0.0488682,0.01933877,0.09467911,0.05451806,0.03482569,0.01358815,0.02596478,-0.19922742,0.11670862,0.05364408,0.02638043,-0.05935759,-0.07247356,-0.01443487,0.06693536,-0.07221815,0.03689943,0.01520223,0.0576849,0.05136558,0.01991403,0.02670006,-0.0318734,0.05293788,-0.02954592,-0.02432655,0.00575292,-0.02111811,0.03788697,-0.03252766,-0.00475004,-0.0242687,0.0243205,0.05368013,-0.02363949,-0.00678158,-0.06052165,-0.00015797,0.06857645,-0.0322089,-0.0112564,-0.01867222,0.0250301,-0.05683725,0.12537606,-0.04834427,0.0192314,-0.01656528,-0.03234583,0.02306455,-0.01627144,0.04444854,-0.00920349,-0.04084535,-0.01008218,0.00040006,0.01518213,0.00960584,-0.01435279,-0.05874317,0.02220868,-0.02225576,0.02489791,-0.07942259,-0.02792994,0.01659133,0.00427181,0.06631222,0.00916606,-0.05402793,-0.00152702,0.03667426,0.076731,-0.00497213,0.03623472,0.05160907,0.00137731,-0.02575575,0.04181178,-0.02467865,0.04579975,-0.01765939,-0.05286336,-0.03671723,-0.0110351,-0.03937368,0.00038163,-0.00252483,-0.05766029,-0.07531701,0.13835688,-0.02918719,0.00091639,-0.00087695,-0.10789772,-0.0167741,0.03744991,-0.06027059,-0.02271146,-0.01082278,-0.00949813,0.06379877,-0.02510034,-0.11248325,0.03132748,0.02197369,-0.04104092,-0.08501995,0.15124476,0.03075584,-0.08783555,-0.0677022,-0.03120795,-0.05298954,0.02427158,-0.02414624,0.01246279,-0.02313866,0.04788368,0.04212628,-0.00704586,-0.06041395,0.02491482,-0.01252549,0.01127929,0.03117879,-0.0660506,-0.0338837,-0.0269945,0.00724353,-0.04226788,-0.05022867,-0.01646648,0.03290344,-0.07034291,-0.02568753,-0.00650562,0.07322095,0.02254357,0.00642594,-0.02635971,0.00625233,0.00296505,-0.0234161,-0.01597484,0.11204113,0.04725945,0.00685552,0.03344909,-0.03665489,-0.00090697,-0.03362282,-0.01232089,0.02603763,0.04459161,-0.05064086,-0.02226417,0.07347856,0.00945686,-0.04789359,-0.03380806,-0.01468783,0.01617121,0.02601286,0.07582796,-0.02363948,0.11295502,-0.03509638,-0.22315392,-0.02053917,-0.0234815,0.00827613,-0.07679087,-0.07178593,0.01537395,0.00882553,-0.02387493,0.0295929,0.02821865,-0.06003845,0.02189855,-0.02911859,0.01900873,-0.02284129,0.00659081,0.02857571,-0.02292463,0.0397001,0.04487868,0.03272078,-0.0461083,-0.06402612,0.02405936,-0.01665344,0.14593421,0.01257295,0.03852476,0.06309611,0.04282224,0.06849509,-0.03086999,-0.18152894,0.02507812,0.03428933,0.07088833,0.01806243,0.00162818,-0.02888619,-0.04779635,0.03626651,0.02085547,-0.06975117,-0.05848365,-0.01682609,-0.00901918,-0.0674892,0.01526228,0.0042455,-0.01262184,0.03591672,-0.00945703,0.05294252,-0.01464447,-0.02236297,-0.01951801,-0.02690704,-0.01470489,0.02089493,0.03025406,-0.00560559,0.01425433,0.00231478,0.07144392,0.01383039,0.00500202,-0.04991885,0.03417657,-0.02612845,0.02229296,0.07632337,-0.01991572,-0.03027163,0.00893458,-0.02828488,-0.03784424,0.00129678,0.02175556,-0.04265707,-0.0079059,0.03417045,0.02788767,-0.02574981,0.06786899,0.04493596,0.04101215,-0.06156837,0.05872635,-0.01080819,0.01614649,0.010207,-0.00072456,-0.00419062,0.11389899,0.0308514,-0.2234707,0.03597587,0.10350703,-0.02420347,-0.08290619,0.01448938,0.04550982,-0.0577274,-0.02398255,-0.02640411,-0.0528415,-0.01326299,-0.01516062,-0.00587113,-0.00736887,0.02084904,0.03845266,0.0172039,0.08019581,-0.08615987,0.00040091,0.07058518,0.23704074,-0.04076133,0.02739759,0.0131271,-0.01032227,-0.04988372,0.04597264,0.04594077,-0.02671544,0.02915332,0.07520736,0.0053378,0.03878553,0.06463852,0.03625053,-0.01611687,0.03614769,0.06141448,0.02862172,0.03663746,0.00625314,-0.00058257,0.10177741,0.02092192,-0.00862239,-0.0102504,-0.0393703,0.01177343,-0.01115315,-0.07535747,-0.02371273,-0.00810389,0.02485925,0.028061,-0.01540174,-0.02612897,-0.04357741,0.01171692,0.00921449,-0.01230144,0.06837805,0.01625261,-0.04335829],"last_embed":{"hash":"1h8wl0v","tokens":104}}},"text":null,"length":0,"last_read":{"hash":"1h8wl0v","at":1751134777661},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##navigation-fixes","lines":[13,20],"size":340,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1h8wl0v","at":1751134777661}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09552014,-0.0354951,0.03117862,-0.02287129,0.00060568,-0.00099981,-0.03524502,0.03872551,-0.01137458,-0.01663552,0.03554937,-0.01305718,0.03890716,0.03743743,0.00460163,-0.00212825,-0.03754908,-0.00083276,-0.05314533,-0.01352829,0.02385822,0.00320566,0.01772703,0.02256561,-0.02680749,0.03534875,-0.04454923,-0.07375425,-0.03851369,-0.20367394,0.0415556,0.02469516,0.01557939,-0.02037381,-0.00125226,0.02837243,-0.00847346,0.03350196,-0.00948062,0.00245561,0.06367291,0.03604079,-0.0089112,-0.02985995,0.00886638,-0.03205641,-0.03561163,-0.02933141,-0.00092709,-0.01589847,-0.01165384,-0.03818699,0.04974744,-0.00890132,0.03983781,0.02380844,0.07426658,0.10627929,0.08349092,-0.00096015,0.03023561,0.04335243,-0.18428744,0.10457076,-0.01250467,-0.00133632,-0.0676259,-0.07015696,0.00278769,0.04019641,-0.09757085,0.05092505,0.00565154,0.06833036,0.03871576,-0.01027309,0.02039379,-0.02953526,0.02015389,0.02674813,-0.05276753,-0.0212819,-0.03394148,-0.00719649,-0.011116,-0.01223108,-0.01108837,-0.00523222,0.01810443,-0.03232119,-0.020642,-0.08004279,0.01020263,0.07054098,-0.04870057,0.03457903,0.03525043,0.04162476,-0.06670403,0.12737046,-0.0207526,0.0144527,-0.03180429,-0.05144543,0.05057339,-0.01416329,0.03063578,-0.01636503,-0.04881675,-0.01052327,-0.01398968,0.00341548,0.02404625,-0.01324034,0.06326037,0.0667861,0.00241695,0.02675384,-0.07414858,0.00503438,0.04428841,0.04595493,0.0208545,0.01758301,-0.03922201,-0.02313698,0.05584601,0.05475204,0.0166546,0.04159259,0.0595119,0.0367834,-0.09105428,0.03268556,-0.01639682,0.02717323,-0.04616103,-0.10245583,0.00032012,-0.02432846,-0.04607184,0.02660079,-0.02738732,-0.09303086,-0.05247251,0.18039761,0.0176084,0.05338061,-0.04207758,-0.06332481,-0.04505979,0.04601324,-0.03471158,-0.00332865,0.0163986,0.02019093,0.05549949,-0.00959663,-0.04312988,0.06295259,-0.0134682,-0.04576366,-0.0798434,0.12618198,-0.00381578,-0.07597519,-0.02116328,-0.02079671,0.02006552,0.04257884,-0.01603672,0.02325845,0.00828958,0.03018845,0.06345388,0.0206631,-0.03436946,-0.02378428,0.03777545,0.00592238,0.05599771,-0.03772497,-0.03354362,-0.0266709,0.01608488,-0.05722697,0.04303991,-0.00411376,0.08125328,0.0018553,-0.04958447,-0.0096988,0.08288959,0.01701674,-0.02879997,-0.03249111,0.00111919,-0.00315288,-0.01310224,-0.00721866,0.03430466,0.05859593,0.01753862,-0.00015597,0.0194678,0.005806,-0.03888439,-0.04814586,0.06883344,0.09254283,-0.03731826,-0.04032442,0.0868445,0.00302917,-0.05438954,0.01002141,-0.02155277,0.08015097,0.01321198,0.01947881,-0.01303981,0.10747501,-0.0532148,-0.23409009,-0.01261678,0.02428802,-0.08213455,0.04257292,-0.08886501,0.01151448,-0.04167682,-0.05234875,0.04982832,0.02662294,-0.07636012,-0.00107808,-0.03839047,0.01111196,-0.00217928,0.00110996,-0.02713654,-0.00615924,0.00619763,-0.01270882,0.06759367,-0.04253758,-0.05592262,0.05678033,-0.04155732,0.13617916,-0.0898445,0.02878937,0.0398877,-0.00828687,0.04424147,-0.0259898,-0.229029,0.02468535,0.056724,0.01130324,0.02901444,-0.00498043,-0.02590549,-0.01662385,0.06550489,0.00404875,-0.10369279,-0.03609651,-0.02848576,-0.0029727,-0.08491128,-0.03134637,0.01707562,-0.0173751,0.002112,-0.01122623,0.01218363,0.01666186,-0.0424564,-0.04553225,0.03895939,-0.03666497,0.00707605,0.00153633,-0.04816623,-0.00836795,0.00191681,0.06153287,0.01402096,-0.00219228,-0.02296712,-0.01760681,-0.04087807,0.0179229,0.07795537,-0.0116248,-0.02154873,0.04787855,-0.02220275,-0.04796638,-0.0337926,0.00859594,-0.05958441,0.01018346,0.00123574,0.00554334,0.04793464,0.03400533,0.04603421,0.00826445,0.00793004,0.02751514,0.00042289,-0.00572581,-0.05366587,-0.00744709,0.02266821,0.08641364,0.00225906,-0.22060968,0.03053738,0.01787903,-0.00834212,-0.01328937,0.01749999,0.04008313,-0.0116745,-0.03923171,0.02945741,-0.11396267,0.03904227,-0.02139574,0.05025655,-0.03841263,0.02119361,0.05150386,0.00381102,0.06125518,-0.07726981,-0.02798022,0.038055,0.21908428,-0.01772418,0.04980982,0.02347118,-0.01751714,-0.01859826,0.12152136,0.00582418,0.02067411,0.01482202,0.08595864,0.01203913,0.01870815,0.03305702,-0.01271514,0.02434408,0.00475382,0.02821323,-0.00293919,0.00841484,-0.04892817,-0.00956822,0.08562628,0.0616944,-0.01496128,-0.07267837,-0.06930401,0.03513469,-0.01555268,-0.04460953,0.01957283,0.00972263,0.03197169,0.0199475,0.02475338,-0.00467269,-0.03315096,0.04691171,0.02869096,-0.0119132,0.07089742,0.03214957,-0.05818194],"last_embed":{"hash":"1ynn2ce","tokens":115}}},"text":null,"length":0,"last_read":{"hash":"1ynn2ce","at":1751134777807},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##memo-workflow","lines":[21,28],"size":336,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1ynn2ce","at":1751134777807}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09345195,-0.01435697,0.0068532,-0.00172653,-0.07763611,0.037853,0.00839343,0.03417052,0.04190487,-0.00335999,0.04316367,-0.00249152,0.05176814,0.03497362,-0.00452921,-0.02151308,-0.0204722,-0.00912295,-0.00995785,-0.00496819,0.03588291,-0.04032583,0.0021491,0.00753716,-0.00477697,0.04090463,-0.06455568,-0.07597107,-0.01530586,-0.16671889,0.02461482,0.01131853,0.08723833,-0.01708147,-0.0320098,0.02698133,-0.0468019,0.04881029,0.00117329,0.01844208,0.01007305,0.02673821,-0.01813257,-0.02806253,0.01805561,-0.0434771,-0.07578192,-0.03655711,-0.00876317,-0.00488718,0.01855095,-0.06964288,0.04049585,0.02475002,0.02412234,0.01640776,0.02103499,0.07996639,0.01506996,-0.0272009,0.02138093,-0.04155317,-0.18260795,0.08955884,-0.00803746,0.04879234,-0.05523701,-0.04740646,-0.01505651,0.07627442,-0.0465871,0.05144991,0.01860309,0.06144051,0.04878549,0.01006548,0.01361903,-0.05457697,-0.0155666,-0.02753588,-0.04130402,-0.05929284,-0.01598505,0.01425049,-0.00263755,0.01933272,-0.05414567,-0.03084331,0.07312793,0.01817886,-0.02433462,-0.02913213,-0.00018277,0.06039815,-0.02015277,-0.00494575,0.00799289,0.03461763,-0.12182927,0.11925074,-0.05011406,0.0060965,0.00791062,-0.05560229,0.06097152,0.00637087,0.01760687,-0.05127974,-0.05574727,-0.01561176,-0.01598536,0.03278869,-0.02767226,-0.00765618,-0.0042393,0.05498387,-0.01205797,0.0576314,-0.00382648,-0.02872756,0.01487305,0.04912603,0.04644075,-0.01845899,-0.03934098,-0.00778291,0.03787357,0.05955484,-0.0113042,0.03428809,0.03330609,-0.00882293,-0.06264982,0.01507314,-0.02966352,0.01793306,-0.01742696,-0.09051356,0.02065221,0.02194998,0.02217875,0.07036767,0.02610333,-0.12296902,-0.05013848,0.148478,0.05964793,0.03637082,-0.01300273,-0.05487884,-0.0006689,-0.00367137,-0.04245112,-0.019634,-0.02563514,0.00223715,0.04839831,-0.03716896,-0.04745654,0.03024231,-0.00383957,-0.0272107,-0.09577424,0.11572296,0.02438833,-0.08579644,-0.05170847,-0.02507177,-0.02652108,0.00037161,-0.02907619,0.00026698,-0.03467104,0.0635372,0.10806717,0.00819916,-0.067392,0.00790419,0.04596316,-0.0102519,0.07949491,-0.03396421,-0.05436306,-0.04808686,0.02254923,-0.0401879,0.03445247,-0.06047118,0.08752955,-0.01769173,-0.03864686,0.02833312,0.03266186,0.05362266,-0.00139459,-0.081228,-0.01373511,-0.00296368,-0.05761941,0.02298786,0.01936946,0.00498876,0.00775384,-0.03848946,0.04367052,0.00689015,-0.03043416,-0.03877274,0.0642706,0.04153316,-0.04925677,-0.04129598,0.09799198,0.04367952,-0.04393653,0.01593644,0.00275612,0.04740311,-0.00284393,0.0296924,-0.04066829,0.09520739,-0.03210186,-0.19864884,-0.04202144,0.06084471,-0.00025733,0.02232037,-0.06169932,0.01692176,-0.04741994,-0.03936538,0.01369651,0.05110011,-0.06344723,-0.00183331,-0.0315594,0.0198578,0.01185909,-0.00820216,-0.01115022,0.02187341,0.00132681,0.0219575,0.06608623,0.01710811,-0.08109085,0.04015773,-0.05433145,0.14835162,-0.02987887,-0.03815394,0.09039527,0.00443256,0.06522793,-0.07426133,-0.19902541,0.00042895,0.04999495,0.0450471,0.02791108,0.01309204,-0.05423729,-0.03406151,0.02587465,-0.00206757,-0.07954641,-0.08523014,0.00247747,-0.01989425,-0.11193565,0.02025427,0.02503874,0.01598992,0.03274493,-0.01188394,0.04618536,0.04934521,-0.00869832,-0.07598592,0.05873662,-0.02163189,0.04733127,-0.00800058,-0.05348488,-0.006508,-0.01368413,0.05829006,0.04099655,-0.0185918,-0.00804267,-0.00756136,-0.01656726,0.02430024,0.06791987,-0.02236434,-0.03612944,0.02597956,-0.02753545,-0.03127816,-0.02675698,0.02710859,-0.03387152,-0.03222932,-0.03715263,-0.02767177,-0.00948259,0.02465761,0.03411591,0.01233101,-0.02105008,0.01938099,-0.03538614,0.0282328,0.01178538,-0.015057,0.01937369,0.10241568,0.05915067,-0.24632484,0.03080675,0.04686659,0.00479514,-0.02618872,0.02201255,0.03701971,-0.01982316,-0.05416233,0.05577261,-0.02637868,-0.02494101,-0.0172025,0.0005021,-0.02342931,0.03263944,0.06213017,0.0146522,0.08502538,-0.11622427,-0.02725585,0.09787264,0.22975178,-0.02784946,0.0247531,0.0015865,0.01522245,-0.02871869,0.06559904,0.05528246,0.00597661,0.02800561,0.08827894,0.05160277,-0.0233417,0.07078028,-0.00335127,-0.02648001,-0.00322219,0.04090065,0.00320684,0.02425443,-0.0503988,-0.02288063,0.12215734,0.0187378,-0.0220693,-0.05740903,-0.01321376,0.01815019,-0.005488,-0.02188835,-0.00308701,-0.0053287,0.07890277,0.02808629,-0.01210933,0.01169721,-0.02540203,0.02135576,-0.00448429,0.05119165,0.08145619,0.02972776,-0.03330888],"last_embed":{"hash":"1imcnb6","tokens":111}}},"text":null,"length":0,"last_read":{"hash":"1imcnb6","at":1751134777886},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Experience Improvements {#improvements}##note-taking-rewards","lines":[29,36],"size":370,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1imcnb6","at":1751134777886}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Technical Development": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08253185,-0.0316927,0.01726523,-0.01892785,-0.02047309,-0.03877129,-0.03777938,0.04745471,-0.00025612,-0.01374168,0.09378801,-0.06977496,0.05883326,0.03935156,0.01392124,-0.00181656,-0.01297574,0.02105547,0.04394404,-0.06020657,0.01196227,-0.01230194,0.00536971,-0.00979991,-0.02557782,0.07551444,-0.043334,-0.087408,-0.04403472,-0.20663399,0.02382002,0.0008563,0.05155748,0.01299381,0.04072976,0.08031383,-0.0193864,0.00405667,-0.03159265,0.02566402,0.0574443,0.04436586,-0.0281458,-0.0242874,0.00615589,-0.03391004,-0.02575135,-0.04737981,-0.02746912,-0.01569443,-0.01893676,-0.05082867,0.04489502,0.01587716,0.00914983,0.05662897,0.03212945,0.08100324,0.05494897,0.01545485,0.02347295,0.02161081,-0.17969826,0.08583152,0.06951885,0.03643856,-0.06429214,-0.03814078,0.04677606,0.05106511,-0.05072333,0.03922337,0.04727971,0.07567397,0.0430081,0.04788659,0.01546886,-0.02367306,0.02941649,-0.03990878,-0.03597023,-0.00581162,-0.01847795,0.05975474,-0.0252021,0.01479297,-0.02270793,0.0302015,0.10707784,-0.03708571,-0.00259003,-0.03177532,0.00703102,0.05006523,-0.01931036,0.03912169,0.01882534,0.00765178,-0.05225531,0.12877761,-0.03071632,-0.0133613,-0.02300186,-0.04297389,0.02838979,-0.00587694,0.02405277,-0.05435742,-0.02752577,-0.05308146,-0.02227256,0.01014422,-0.02444114,-0.02131969,0.00450938,0.00039055,0.00051503,0.00776596,-0.07282759,0.00102483,0.05084157,0.00611751,0.05085963,-0.01244449,-0.02102035,-0.01156716,0.02768466,0.06509509,0.01981838,0.06790517,0.04899737,0.03888426,-0.07388071,0.01492259,-0.02460239,0.0542012,0.01070283,-0.08283512,-0.04895149,-0.01135135,-0.01912353,0.00046179,0.00164704,-0.07767878,-0.0616271,0.14106743,0.01133743,0.02611719,-0.01758521,-0.10835499,-0.01154081,0.05498819,-0.03963401,-0.01069185,0.03478886,-0.03248978,0.04465379,0.00699834,-0.04112229,0.03362191,0.00214509,-0.01464797,-0.04700793,0.15030961,0.00175282,-0.08937459,-0.07114079,-0.00855724,-0.01279361,0.02138806,0.00181674,0.01407279,-0.01450147,0.00394556,0.0285012,-0.03647382,-0.04841467,0.0101803,0.04039862,0.03577989,0.03607811,-0.05596389,-0.03831462,-0.01135824,0.02385453,-0.04802044,0.00432497,-0.0309506,0.02120849,-0.02484653,-0.06776575,0.00269857,0.02777226,0.04247714,0.02568968,-0.01884244,-0.00773178,0.01332334,-0.02390891,0.00723908,0.07731136,0.06688044,-0.01944656,0.02649599,-0.04512462,-0.02173419,-0.05733928,-0.01360003,0.02378708,0.06657142,-0.02778052,0.00132273,0.07998336,0.03056565,-0.0626216,-0.03340057,-0.01083692,0.04675547,0.03854632,0.08068202,-0.01263182,0.09024119,-0.06525891,-0.23022944,-0.01385748,0.02689653,-0.04366328,-0.03003163,-0.07802274,0.02185551,-0.06904736,-0.00911791,0.03466994,0.07370109,-0.03818614,-0.00218939,-0.07004701,0.00224858,-0.0070588,-0.0581454,-0.0062974,-0.04816506,0.01668268,0.03482516,0.04231944,-0.02399893,-0.03409103,0.01743798,-0.05274128,0.10774754,-0.02647843,0.03048305,0.05655419,-0.00701997,0.04108816,0.00750984,-0.19858533,0.04039115,0.04480546,0.04864356,0.03902995,0.00706292,-0.02388039,-0.04374709,0.04815781,-0.00826637,-0.09308998,-0.05580493,-0.00092564,-0.03768401,-0.07997593,-0.00667432,-0.00145695,0.01832836,0.02236691,-0.00460445,0.06633381,-0.00246512,-0.00720925,-0.03394778,0.01532205,-0.0019399,-0.02840048,0.04207817,-0.02530011,-0.03215504,0.00099988,0.09856866,0.0095793,0.00593084,-0.042649,-0.00088252,-0.05966045,0.02992858,0.09071805,-0.01854504,-0.01785835,0.00205469,-0.02993059,-0.02663423,-0.06808908,0.00950342,-0.04123106,0.03036822,0.01535062,0.02741398,-0.00663983,0.02799857,0.0434698,0.03425914,-0.03186839,0.0817944,0.00713464,0.00420582,0.0134504,-0.01863666,-0.00656194,0.12908079,0.0612168,-0.24110812,0.04936017,0.05578025,-0.04516831,-0.07344253,-0.01620689,0.03367764,-0.0659352,-0.01326627,-0.00345822,-0.02113819,0.00499073,-0.06221686,-0.00839951,-0.01309087,0.02860597,0.05967636,-0.01510537,0.05348168,-0.0733128,-0.01533933,0.04117919,0.21782443,-0.03768375,0.05219465,0.03594308,-0.00852579,-0.02713049,0.09898911,0.03727133,0.01608855,0.02133627,0.11229788,-0.02855685,-0.01697925,0.05413958,0.05618916,-0.03179123,0.0041062,0.03827053,0.02065427,0.02187435,-0.00657334,0.00873793,0.09122781,-0.01104678,-0.08343792,-0.08492625,-0.02726598,-0.00689724,-0.02467725,-0.07809993,0.01048072,0.00768901,0.07812953,0.03379221,0.01452634,0.00864897,-0.04258366,0.00843498,0.01205996,-0.0226474,0.04307036,0.0515914,-0.04549199],"last_embed":{"hash":"ol9kp4","tokens":146}}},"text":null,"length":0,"last_read":{"hash":"ol9kp4","at":1751095117445},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Technical Development","lines":[37,54],"size":570,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"ol9kp4","at":1751095117445}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Technical Development##core-functionality": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06226894,-0.03435621,0.01247678,-0.03192012,-0.02072391,-0.01919894,-0.00764993,0.06364369,0.03255629,-0.0109004,0.09733076,-0.06416439,0.05716357,0.04942225,0.00340657,0.00112612,-0.00465543,0.03716211,0.02765397,-0.08708397,0.01496772,0.00034923,-0.00669296,-0.0315355,-0.01419016,0.07539325,-0.04677562,-0.06716449,-0.05257888,-0.18899049,0.02750353,-0.01201391,0.06603841,-0.00265589,0.03980682,0.06904577,-0.00664657,0.02395403,-0.018834,0.01582627,0.06283096,0.02856007,-0.04740032,-0.03524998,0.01186083,-0.0259568,-0.02579145,-0.03839318,-0.05932955,-0.0268936,-0.03637495,-0.05822661,0.04174317,0.02509974,0.02474486,0.04814487,0.0179995,0.08920866,0.0607477,0.03774776,0.00517536,0.0246632,-0.17182638,0.0843918,0.06488565,0.01977191,-0.05373976,-0.0559061,0.01523267,0.06252707,-0.02887656,0.04050514,0.06511108,0.06075637,0.04813451,0.05507333,0.01270481,-0.02543432,0.03198024,-0.02119625,-0.02567914,-0.04678131,-0.05116086,0.04061379,-0.03439934,0.01911013,-0.02661103,0.01056072,0.10329399,-0.02908521,-0.00956819,-0.02050377,-0.01325452,0.04733305,-0.02378066,0.03256656,0.00572866,0.00388792,-0.05245849,0.14754371,-0.04378721,-0.02003528,-0.00977538,-0.06085365,0.01260829,-0.02072055,0.017265,-0.04495492,-0.024398,-0.05166014,-0.01369261,0.01698791,-0.00952804,-0.00561254,0.00087514,-0.00179747,0.0071401,0.0004167,-0.08430662,-0.01469316,0.02656229,0.00616462,0.04124006,0.00545525,-0.02717357,-0.00327998,0.0210893,0.09433921,0.01415358,0.05196309,0.04794775,0.00435886,-0.04809438,0.0246859,-0.01619173,0.0497591,0.00788577,-0.07297418,-0.04251622,-0.01005661,-0.02526733,0.00084329,0.0126386,-0.0700614,-0.06503914,0.15220089,0.01161614,0.01411484,-0.0140357,-0.09753287,-0.05878283,0.03887445,-0.04169759,-0.01444431,0.01855856,-0.04261916,0.04160025,-0.01471209,-0.05315119,0.02031916,-0.01018285,0.0104642,-0.03532815,0.16035473,-0.0007994,-0.08840858,-0.06749748,-0.01199476,-0.02837315,0.01221647,-0.01355402,0.00608691,-0.03177729,0.00450288,0.02273283,-0.0425555,-0.07601099,0.00883997,0.00963234,0.01053347,0.04921101,-0.04951109,-0.02438785,-0.02932722,0.03037579,-0.05254566,0.0052849,-0.01759774,0.0451504,-0.04818966,-0.07645415,0.01179748,0.04465258,0.02205461,0.02387444,-0.02128591,0.00095087,0.02257235,-0.03648361,0.02580297,0.11020598,0.07131254,-0.01140649,0.0292222,-0.02473681,-0.0169405,-0.05625127,-0.01354851,0.00006343,0.080722,-0.02479504,-0.01671276,0.08684094,0.03621816,-0.04953799,-0.01756226,-0.01687999,0.03784601,0.00910773,0.06343809,-0.0121935,0.09380284,-0.05960094,-0.22825906,-0.01580756,0.03550842,-0.04194899,-0.03654253,-0.08134329,0.0136408,-0.04940189,-0.02147789,0.02658524,0.03705933,-0.05657936,-0.00522074,-0.05704686,0.02006624,-0.00092542,-0.03568823,-0.01084632,-0.04813273,0.04352997,0.0461908,0.05916541,-0.02986672,-0.05005307,0.014962,-0.04458144,0.13158144,-0.03343557,0.04510909,0.05257699,-0.01043474,0.04189371,-0.02857893,-0.17898659,0.04690887,0.03757151,0.07762422,0.04307738,0.00580368,-0.04290603,-0.05159252,0.04452891,-0.00813446,-0.08577184,-0.04318815,-0.00135818,-0.02315792,-0.07894883,-0.00384974,-0.00280411,0.01931721,0.01430785,-0.00329723,0.05930295,0.00330426,-0.01298487,-0.03395649,0.02556053,-0.00780893,0.00141216,0.04342587,-0.02835032,-0.03905104,0.01038869,0.09833417,0.03577257,0.01717722,-0.05149059,-0.02165513,-0.0585283,0.02740172,0.10342231,-0.03274671,-0.01689746,0.01988617,-0.01705462,-0.02894118,-0.0542066,0.02132934,-0.03109004,0.04408786,0.0045025,0.0248672,-0.00268951,0.04080846,0.01417029,0.0597266,-0.03099546,0.05956331,-0.00001367,0.00409023,0.01543344,-0.02276669,0.00581766,0.130146,0.04638487,-0.24283743,0.04100785,0.06278976,-0.04922166,-0.0576563,-0.02128449,0.03867863,-0.0523844,-0.01240506,-0.00245603,-0.04519809,0.01347887,-0.03832738,0.02457833,-0.02752686,0.01635914,0.06956609,0.00043913,0.05738874,-0.0553476,-0.00834548,0.04317151,0.22738686,-0.03888251,0.05110502,0.03501561,-0.0038178,-0.04902996,0.07111086,0.01744088,0.01664506,0.02818962,0.09590781,-0.03279142,-0.00108228,0.04016674,0.0514095,-0.03710357,0.00407892,0.03747037,0.0343744,0.01987521,0.00443175,0.00268899,0.09686218,0.01713988,-0.09148803,-0.06533944,-0.02418916,0.0035642,-0.02837942,-0.06907374,0.01334766,-0.00709274,0.08057054,0.04527006,-0.00169502,0.00387547,-0.05627923,0.02060454,0.01474006,-0.01006754,0.03757465,0.05358405,-0.03953619],"last_embed":{"hash":"851bix","tokens":86}}},"text":null,"length":0,"last_read":{"hash":"851bix","at":1751095117543},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Technical Development##core-functionality","lines":[39,46],"size":274,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"851bix","at":1751095117543}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Technical Development##github-maintenance": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09478082,-0.02810465,0.03862314,-0.00289032,-0.02517898,-0.02717808,-0.04934234,0.02076503,-0.03598794,-0.00597844,0.05749343,-0.08312494,0.04658289,0.03276247,0.04600003,0.01209401,-0.03005223,0.01804554,0.04370091,-0.01493932,0.03611463,-0.00042499,0.02659188,0.02248334,-0.04394651,0.06470919,-0.01284821,-0.09086013,-0.01223575,-0.17069864,0.01204456,0.01315267,0.01021774,0.02793183,0.03679307,0.07829171,-0.05347205,-0.02720681,-0.02982197,0.02858269,0.03395856,0.03958851,-0.00747901,-0.02672399,0.00062373,-0.03524967,-0.00560113,-0.03808054,0.03468153,-0.01451483,0.01175994,-0.0189061,0.04172061,0.00654948,-0.01496718,0.06199632,0.0592064,0.06392663,0.05257324,-0.0173412,0.03836945,0.02129903,-0.18733589,0.0852493,0.06473998,0.06720982,-0.05421396,-0.01109218,0.06862716,0.02724954,-0.05897639,0.01811378,0.00214962,0.07512396,0.05694983,0.02795228,0.03377357,-0.02966992,-0.00398163,-0.05242112,-0.05939903,0.02515246,0.02100895,0.048439,-0.01088703,0.00858925,-0.02605414,0.05161225,0.09037989,-0.03903765,-0.01920665,-0.05493856,0.06066397,0.05364626,-0.02734411,0.01707766,0.02742684,0.02795869,-0.03887757,0.12568279,-0.01536584,-0.00505638,-0.0289559,-0.01465243,0.04478237,0.02184785,0.00456188,-0.05200151,-0.04220776,-0.03088948,-0.03881113,0.00202408,-0.02099767,-0.04779881,0.00672011,-0.01224105,-0.02167664,0.02926767,-0.03840971,0.01835063,0.03729419,0.01155569,0.05182264,-0.01117533,-0.01622345,-0.02352699,0.04024835,0.02869799,0.01865719,0.06476794,0.04008966,0.06287586,-0.11772282,-0.00985451,-0.02508528,0.05111153,-0.00990759,-0.12834381,-0.03565066,-0.01663855,-0.01993174,0.00234016,-0.01219485,-0.08614469,-0.03615169,0.13218525,0.01741278,0.04389338,-0.02299714,-0.08397456,0.04503895,0.05027783,-0.03017321,-0.00658655,0.02715109,0.00807494,0.04799445,0.01040472,-0.0326596,0.05656219,0.02553451,-0.04980544,-0.08538588,0.1584069,-0.00281088,-0.07821374,-0.05205637,0.00232256,0.01622462,0.02232999,0.02289945,0.03455573,0.01646902,0.00812498,0.0570193,-0.01870091,-0.01558347,-0.00680719,0.07051235,0.04303402,0.02345511,-0.05707639,-0.04864408,0.01844126,0.00249043,-0.02452192,-0.000399,-0.06898448,0.00157763,0.01232325,-0.04051975,0.02008561,0.02029523,0.04892035,0.02992832,-0.01302064,-0.03071002,-0.00759685,0.00635439,-0.03051802,0.01063317,0.04747326,-0.01845314,0.00823218,-0.08164995,-0.02941067,-0.05169906,-0.02198179,0.07663153,0.03862604,-0.03026777,-0.00086183,0.06809677,0.02217498,-0.08278754,-0.02341921,-0.00039116,0.04417899,0.07257327,0.09206129,-0.0165635,0.05760118,-0.06538814,-0.22373733,-0.01611019,0.00497418,-0.05992331,-0.01292866,-0.07105894,0.02071636,-0.06426508,-0.0327429,0.05703887,0.11672704,-0.03161411,0.00056768,-0.06775928,-0.02667267,-0.02722049,-0.04543686,-0.00417925,-0.02862304,-0.01135847,0.00603134,0.01263433,-0.02548609,-0.0460421,0.02823147,-0.04219403,0.09366348,-0.01744111,-0.0076314,0.044637,0.00495128,0.043724,0.01969926,-0.21433729,0.01924546,0.02285929,0.0065323,0.02845782,0.01238492,0.00814551,-0.02506013,0.05218968,-0.00493544,-0.08973002,-0.04586238,-0.00800383,-0.03810752,-0.07382535,-0.00915725,-0.00295105,-0.01105975,0.02240822,-0.01497206,0.08028586,0.00180534,0.0049736,-0.03347848,0.00051635,0.02190994,-0.04846302,0.04884581,-0.009206,-0.02504217,-0.01725129,0.07653236,-0.03276562,-0.0126305,-0.02496303,0.035828,-0.03974392,0.02573907,0.06770123,0.02041546,-0.01467029,-0.0331636,-0.05134143,-0.0253772,-0.04793597,-0.00662457,-0.04885531,0.01258349,0.01316039,0.01915579,0.01580463,0.01566923,0.06626134,-0.0250785,-0.0323124,0.1066808,0.02092046,0.00315683,0.00566115,-0.0028588,0.00829539,0.12792085,0.0523478,-0.23630928,0.0555264,0.05568318,-0.01828649,-0.08111375,0.00549087,0.028003,-0.06798691,-0.03828248,0.01204337,0.00373023,-0.01390543,-0.06257191,-0.03961084,0.00974918,0.037442,0.05995248,-0.03467739,0.03235066,-0.07425665,-0.03073025,0.04883055,0.21198292,-0.04344551,0.05842608,0.01673665,-0.02834602,0.01152486,0.10099138,0.06737492,0.0321242,0.00811477,0.10951043,-0.0305543,-0.02192486,0.0848951,0.03833474,0.00105604,-0.00933644,0.0346601,-0.01027286,0.03609765,-0.05638699,0.01633847,0.0759116,-0.01938078,-0.06593344,-0.08281995,-0.06738167,-0.01679544,-0.02963132,-0.05785969,-0.01265262,0.01313248,0.07853818,0.03145887,0.04219564,-0.00419163,-0.03143445,-0.00989967,0.01078402,-0.03243212,0.03848942,0.03291789,-0.05149381],"last_embed":{"hash":"dreeui","tokens":85}}},"text":null,"length":0,"last_read":{"hash":"dreeui","at":1751095117605},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Technical Development##github-maintenance","lines":[47,54],"size":269,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"dreeui","at":1751095117605}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Research & Testing": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07429878,-0.029572,0.00030901,-0.00976504,-0.02555103,0.0375543,-0.01329319,0.0526705,-0.0469252,-0.00336343,0.04124156,-0.04862,0.03038094,0.04673267,0.05246803,0.02386071,-0.02638697,-0.00623936,0.00133573,0.01812523,0.0055278,-0.04320744,0.02399071,-0.02990022,-0.03345454,0.03087969,-0.03128868,-0.07887393,-0.01812456,-0.18066065,0.01312704,0.04181902,0.09774689,-0.00331761,0.02580993,0.04957294,-0.01474668,0.00526585,-0.04435252,0.00672325,0.05428587,-0.02008263,0.00887441,-0.01865001,0.02418328,-0.00406768,0.00555934,-0.05687106,2.1e-7,0.00863685,-0.00685685,-0.0985797,0.01803437,0.00375804,0.05627493,0.01333392,0.04152369,0.059171,0.0523807,0.01915171,0.00501674,0.00869715,-0.17849937,0.08539548,0.01084657,0.04175292,-0.05665787,-0.0249203,0.0405187,0.03481776,-0.06762446,0.06211549,0.02708636,0.11372498,0.04604306,0.02899352,0.0108116,-0.03300093,0.01139005,-0.02326905,-0.08081668,0.03513886,-0.00855202,0.03946644,-0.02414173,-0.00485373,0.01168006,0.03634919,0.00235377,0.02297779,-0.02951492,-0.05155778,0.01008346,0.03309249,-0.03960168,-0.0007526,0.04789141,0.0003804,-0.1335952,0.12996426,-0.05208834,-0.04220556,0.00599338,-0.05662133,0.00629292,-0.0053263,0.01490818,-0.08249563,0.00134223,0.00440169,-0.04201176,0.02352625,-0.05837072,-0.03342989,0.02777556,0.00703553,0.01423158,0.0393257,-0.02171531,-0.02171697,0.04678782,0.011455,0.05662502,-0.04889894,0.00375879,-0.02382001,0.03884496,0.01851689,0.02912076,0.04653133,0.00578308,0.04518751,-0.09131024,0.03912751,-0.0307956,0.04111601,-0.02305038,-0.09757379,0.00590411,0.030024,0.00485938,0.01433548,0.00451428,-0.09466675,-0.06256976,0.1141276,-0.01282887,0.04436408,-0.04499074,-0.05155236,0.03708967,0.02409338,-0.08996291,-0.00541889,0.0220145,0.01297685,0.06479143,-0.01695505,-0.03859215,0.04425792,0.00622407,-0.0469625,-0.07744947,0.13036297,0.00750896,-0.14054123,-0.03455062,0.01641041,0.01726787,0.02647252,0.01859263,0.02783545,-0.01143043,0.03399802,0.07806413,-0.02319182,-0.04220232,0.04299437,0.04155318,0.05783845,0.06288324,0.00076591,-0.03785549,-0.03562699,-0.01303156,-0.03171975,0.02301456,-0.05952395,0.02451986,-0.00632966,-0.05576979,-0.04843523,-0.01492609,0.05636584,-0.0063637,-0.00346486,0.02523177,-0.03224903,-0.04919976,0.03440293,0.02634459,0.01922758,0.00467,0.01108065,-0.03662801,0.03183739,-0.00786061,-0.00073635,0.07621778,0.05422318,-0.02245877,0.00221857,0.05503101,0.00401529,-0.02504908,-0.0336374,0.01656552,0.02872109,0.04766343,0.07885911,-0.02775933,0.06912576,-0.05219771,-0.2106027,-0.02784232,0.03384389,0.03217386,0.0046508,-0.08041795,0.04286604,-0.04083964,-0.02016409,0.06100443,0.14414501,-0.05031727,-0.02541677,-0.04368474,0.05038944,-0.00307613,-0.0565343,-0.00933744,-0.04150093,-0.00247205,-0.01512519,0.00081663,0.03255052,-0.07496802,0.01695158,-0.0390237,0.10286635,0.00095582,-0.01495076,0.04239036,0.02004904,0.02655274,-0.05306759,-0.21513262,0.0093101,0.04453286,0.04051449,0.03003696,0.01495019,-0.02501173,-0.04579724,0.06026564,0.01944787,-0.1162962,-0.07486343,-0.03146237,-0.00091536,-0.05514169,-0.05052299,-0.03293938,-0.00231291,-0.00692321,0.01456142,0.04180746,-0.02659955,-0.04931671,-0.04081688,0.032043,0.01906219,0.0363095,0.02461139,-0.00042281,0.01591078,-0.03211589,0.07348589,-0.00792067,0.00246904,0.01616526,0.04840343,-0.05022285,0.04623438,0.09280971,-0.00437675,0.0365617,0.03137548,-0.02655491,0.01040357,-0.07523226,0.0009268,-0.08351868,-0.00945437,0.02187248,-0.00976884,-0.01868742,0.04028003,0.04565423,0.02020381,-0.02981669,0.10158178,-0.03700272,0.01624193,0.03905678,-0.03172344,-0.02484618,0.0777676,0.06933922,-0.25971168,0.01307646,0.05121378,0.03954212,-0.04114204,-0.00608029,0.10404164,-0.073399,-0.04907107,0.03489751,0.01792716,0.00067719,-0.00721168,0.00333022,0.01360561,0.01550014,0.03744337,-0.00652162,0.03477107,-0.10767916,-0.05306575,0.08443037,0.20152915,-0.08199273,0.03899153,0.02397231,0.0050301,-0.0361109,0.09062725,0.01698058,-0.0186201,0.00149064,0.0569801,-0.00714784,-0.01038237,0.05100985,0.0143396,-0.03617762,-0.0103243,0.02258161,0.01330343,-0.00594905,0.02855763,-0.02314863,0.06853439,0.01877584,-0.01850827,-0.06655429,-0.03033414,0.02739594,-0.03308352,-0.05909153,-0.01802771,0.01323736,0.04830375,0.07509461,0.03390662,0.00148682,-0.03455833,-0.02371188,-0.00367623,-0.00928204,0.08650122,0.01109937,-0.03518247],"last_embed":{"hash":"14dlujj","tokens":145}}},"text":null,"length":0,"last_read":{"hash":"14dlujj","at":1751095117668},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Research & Testing","lines":[55,72],"size":542,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"14dlujj","at":1751095117668}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Research & Testing##user-feedback": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06418201,-0.01488114,0.00967341,-0.01161049,-0.02594419,0.02224758,0.02009353,0.06240815,-0.02140284,-0.00953041,0.04092976,-0.03200262,0.04126397,0.04345668,0.04377987,0.02356717,-0.0406105,-0.01368001,0.0154033,0.01770716,0.00431068,-0.04387369,0.02267757,-0.0261302,-0.03171653,0.03925841,-0.05467216,-0.08965214,-0.03513784,-0.16046213,0.02328084,0.0342109,0.11244209,0.0066881,0.03228825,0.05175846,0.00183106,-0.00294238,-0.02200884,0.01641404,0.05748631,-0.02456739,0.00272867,-0.00380189,0.01867187,-0.008942,-0.02371914,-0.05182499,-0.02051896,0.01591795,0.010077,-0.09588945,0.02609975,0.02767916,0.06223591,0.01580684,0.03502943,0.0523265,0.03749313,0.02143776,0.00284068,0.01821541,-0.17254619,0.07615402,0.02133549,0.04324895,-0.07088229,-0.03788016,0.01524983,0.03703209,-0.06477161,0.06560679,0.02462183,0.11897678,0.05640015,0.03459649,0.01783959,-0.04061789,0.00639702,-0.02252516,-0.07417619,0.03915859,-0.01305366,0.02912078,-0.02730567,-0.01325925,0.01125093,0.03194746,0.01816579,0.01891959,-0.03093717,-0.04230366,0.01296497,0.03966603,-0.06302652,-0.00659577,0.05420816,0.01331693,-0.12786719,0.1390986,-0.0664444,-0.02520255,-0.0037336,-0.04439515,0.00537952,0.01022948,0.01947348,-0.08819993,-0.00185847,0.00779903,-0.03157007,0.00500592,-0.04127029,-0.02808498,0.02117238,0.01490433,0.02206565,0.04424281,-0.02216594,-0.04351654,0.03376483,0.02838515,0.04285461,-0.04190943,0.01450862,-0.01343389,0.06245987,0.02650387,0.01432026,0.03518051,0.0131699,0.02742096,-0.09748874,0.0261339,-0.03408651,0.03750771,-0.02633681,-0.10389651,-0.00446877,0.05629767,0.01288183,0.0198906,0.00069633,-0.09611099,-0.07548066,0.10522087,0.0039659,0.05227113,-0.05010742,-0.04842526,0.03213917,0.03201698,-0.09444431,-0.01745581,0.01470815,0.03239989,0.0371948,-0.02849269,-0.03817688,0.05763915,0.01584123,-0.03533356,-0.07643277,0.13048999,-0.00049031,-0.12205927,-0.02630719,0.01372825,0.01114407,0.03966931,0.0154944,0.02519169,-0.0130057,0.02721621,0.06838609,-0.01655847,-0.034452,0.03823467,0.04735685,0.05893631,0.06137557,0.01877581,-0.04354558,-0.02018537,-0.01721753,-0.0245989,0.0084131,-0.06186974,0.03812551,-0.01340345,-0.04938321,-0.05867739,-0.01645805,0.05726498,-0.00724375,-0.02262614,0.01829239,-0.03120179,-0.06142573,0.03369737,0.02352141,0.02461365,-0.00301147,-0.00721248,-0.01036649,0.05106279,-0.0202398,0.00848102,0.05601446,0.05172681,-0.0132826,-0.00210234,0.05767746,0.01138569,-0.02073275,-0.03869267,0.01730004,0.01974506,0.05318014,0.07915786,-0.02283617,0.07708402,-0.04892277,-0.21640988,-0.02763949,0.03183322,0.03090901,0.00873312,-0.0914743,0.04360028,-0.04160252,-0.00995018,0.07028238,0.12956831,-0.03576051,-0.04331684,-0.02815724,0.05606694,-0.00597568,-0.07229464,-0.02309313,-0.04666572,-0.01703882,-0.01080808,0.01821655,0.033455,-0.05495966,0.02611609,-0.03965848,0.11732192,0.00759058,-0.04378414,0.06671349,0.01780337,0.02954946,-0.04850614,-0.230995,0.00797939,0.05440335,0.02841198,0.02468613,-0.00044125,-0.02129862,-0.04332649,0.04925274,0.01808297,-0.10761122,-0.05486077,-0.02453228,0.02153136,-0.07600465,-0.05591328,-0.02730647,-0.01563022,-0.02162351,0.01353598,0.04974556,-0.01286403,-0.06574733,-0.05001949,0.02635517,0.01589912,0.04151366,0.02250965,-0.00471009,0.00462425,-0.03290476,0.0524416,-0.01195918,-0.00398154,0.01675918,0.04005401,-0.04924661,0.0527631,0.10447253,-0.00326326,0.02937152,0.00770477,-0.02627077,0.01969098,-0.05951271,-0.01389686,-0.07426336,-0.00286503,0.02989429,0.00303814,0.00074234,0.02839714,0.02103436,0.01674568,-0.0320606,0.1027666,-0.04083978,0.01774485,0.0375776,-0.03288265,-0.04048752,0.075161,0.05270631,-0.26396516,0.02083303,0.03259745,0.03136779,-0.04359524,0.00816757,0.09396715,-0.05998514,-0.05111886,0.03357001,0.02664563,0.01391961,-0.01733957,-0.00189129,0.00578355,0.02456241,0.03807341,-0.01849588,0.02842836,-0.11996024,-0.04108517,0.07955293,0.20264918,-0.07247935,0.04975862,0.02532824,0.0131419,-0.04476469,0.06770684,0.01908994,-0.03096503,0.00830479,0.06850049,-0.03486712,-0.01678409,0.05034974,0.01096169,-0.03396414,0.0024354,-0.00104776,0.00791267,0.00341955,0.01560422,-0.0386815,0.07571406,0.03338139,-0.01465337,-0.06098884,-0.012551,0.01729315,-0.01836114,-0.05923359,-0.01844306,0.000502,0.06282856,0.06101704,0.02151762,-0.00203863,-0.02823476,-0.0336628,0.00016285,-0.00465717,0.08040308,-0.00416634,-0.04099621],"last_embed":{"hash":"1v2tjsw","tokens":86}}},"text":null,"length":0,"last_read":{"hash":"1v2tjsw","at":1751095117755},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Research & Testing##user-feedback","lines":[57,64],"size":266,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1v2tjsw","at":1751095117755}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Research & Testing##documentation": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07586554,-0.0509176,-0.00718395,-0.01704863,-0.00941418,0.05382165,-0.05323169,0.03012573,-0.03978335,0.00287021,0.04342942,-0.08793674,0.02192811,0.05330193,0.05141288,0.02110244,-0.02625744,0.00126097,-0.01969072,0.00445542,0.0408578,-0.01257049,0.02550131,-0.03980391,-0.04620078,0.03288786,0.00126838,-0.05006316,0.00561462,-0.15989156,-0.00361447,0.02991342,0.04608396,-0.00692833,0.02018916,0.03934088,-0.05287698,0.03487672,-0.05115343,-0.00732967,0.03992304,-0.01030366,0.0116656,-0.03837624,0.02753009,0.01460508,0.022004,-0.05497969,0.02616023,-0.02446993,-0.04128945,-0.06655283,0.01598248,-0.02300382,0.01596943,0.02875844,0.05382932,0.07451938,0.07733131,0.0151101,-0.00054045,-0.01447017,-0.2027649,0.10292701,0.00000298,0.04493168,-0.04770714,-0.01933126,0.06047156,0.04641165,-0.04111854,0.04460867,0.01463579,0.0773873,0.04177989,0.02337344,0.01867904,-0.04387476,0.0239002,-0.04062392,-0.08970211,0.00838519,-0.00735337,0.05062833,-0.01325095,0.01737998,-0.01636067,0.02954393,0.01927267,0.01320348,-0.03101723,-0.07013273,0.00269307,0.05244019,-0.00702503,0.00844556,0.02728122,-0.00013788,-0.08586348,0.14216831,-0.04257236,-0.05268809,0.03140639,-0.06482349,0.01455935,-0.02692292,-0.0137623,-0.04776073,-0.00923303,-0.0034712,-0.03166326,0.04711484,-0.05214568,-0.04560699,0.01715275,-0.01483829,-0.01132407,0.03137553,-0.03384229,0.00990442,0.0324099,-0.0073914,0.06029041,-0.03345244,-0.0159591,-0.04309723,0.01019506,0.03633016,0.03785788,0.03700215,0.01679205,0.0530752,-0.08031838,0.05122298,-0.02964288,0.04257997,-0.01139333,-0.09860858,0.00998479,-0.03104944,-0.01031586,0.01770477,0.02468081,-0.07414535,-0.03221215,0.1443177,-0.01765459,0.03562925,-0.02940705,-0.05108436,0.02576845,0.01368904,-0.06884993,0.00150323,0.02443268,-0.01616506,0.07943591,-0.0003015,-0.04972615,0.02260415,-0.00388992,-0.05780555,-0.07194474,0.13784587,0.00403229,-0.13421984,-0.04720539,0.02744748,0.01611941,-0.00995444,0.00765384,0.02017346,-0.02544391,0.04043965,0.08293941,-0.01967999,-0.05365218,0.03324836,0.02086184,0.04276478,0.04514373,-0.07896494,-0.01899341,-0.03916664,0.00470916,-0.0247588,0.01796422,-0.05928964,0.0231202,0.01469029,-0.05611803,0.01129327,0.02029281,0.03424052,0.00959717,0.02607963,0.00692642,-0.00312851,-0.03112554,0.01855917,0.01965162,0.01762102,0.00635154,0.0530354,-0.07838374,-0.01206292,-0.00715767,-0.03092756,0.09715658,0.05879301,-0.04845557,0.00026562,0.06144277,0.00083172,-0.06245302,-0.02131213,-0.00477065,0.03811415,0.03595065,0.05717963,-0.03825621,0.03330059,-0.07123139,-0.20332047,-0.03025569,0.02736254,0.00692017,-0.016585,-0.06509124,0.02508743,-0.02786966,-0.04797843,0.05863233,0.12646741,-0.08671751,-0.00785256,-0.06970039,0.02834798,-0.00079337,-0.01317065,0.00378392,-0.02736812,0.04393,-0.014451,-0.02063322,0.0105378,-0.11123824,0.01663478,-0.03954757,0.09834056,-0.01369895,0.03421396,0.01534181,0.02624602,0.03180236,-0.06420686,-0.185296,0.00969532,0.01625544,0.06422768,0.03826896,0.03250681,-0.0320049,-0.04140899,0.06164014,0.01297665,-0.10914336,-0.06311338,-0.04216284,-0.03858102,-0.05040773,-0.02140216,-0.02568474,-0.00140021,0.01329835,0.00626,0.04475794,-0.03135369,0.00228417,-0.01987705,0.02753402,0.00769769,0.02441653,0.0353678,0.012246,0.00741443,-0.01121486,0.07925945,0.00634524,0.00767937,-0.00297259,0.02573285,-0.02771822,0.04939264,0.08099351,-0.00300659,0.04481714,0.05187962,-0.02808027,-0.01465594,-0.07375797,0.0210585,-0.06899413,-0.01129866,-0.00883754,-0.01121731,-0.02471355,0.07631868,0.0713183,0.00486284,-0.03060775,0.10204021,-0.01372752,0.0120417,0.02535495,-0.00963891,0.0259208,0.10081195,0.07535495,-0.24486148,0.02706161,0.08341503,0.03906977,-0.0446204,-0.02512153,0.10506189,-0.08676095,-0.04225098,0.0399581,-0.02051177,-0.03775499,0.00879503,0.02386414,-0.00376826,0.01230721,0.06266108,0.02606828,0.03733483,-0.03001095,-0.07405174,0.06750348,0.21534495,-0.08592858,0.02858281,0.00244967,-0.03561002,-0.00364407,0.09174522,0.04001172,0.02349544,-0.0051771,0.04114057,0.02097301,0.00236188,0.08373698,0.02662896,-0.03013619,-0.0017645,0.05076664,0.01258689,-0.01930038,-0.01471272,0.0159872,0.07264459,0.01152062,-0.03620341,-0.05993484,-0.07227623,0.03202549,-0.05806139,-0.05800911,-0.02786713,0.03510652,0.04102456,0.08439962,0.0581232,-0.01661405,-0.05063011,-0.01021254,-0.00240241,-0.00900969,0.069653,0.01829215,-0.03146872],"last_embed":{"hash":"1x62tt1","tokens":86}}},"text":null,"length":0,"last_read":{"hash":"1x62tt1","at":1751095117803},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#User Research & Testing##documentation","lines":[65,72],"size":247,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1x62tt1","at":1751095117803}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Success Metrics": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03492742,-0.02481318,-0.01020586,-0.02803731,-0.04545984,0.01810291,0.01293527,0.04417825,0.02843606,0.01206148,0.06164649,-0.04510859,0.02234782,0.07203705,0.01057121,0.03284342,-0.01636627,0.02114072,-0.00447777,-0.0079712,-0.02166934,-0.07591776,0.02510951,-0.02511953,0.01081809,0.04875917,-0.08074541,-0.13248432,-0.05604654,-0.17501658,-0.01087569,-0.00187517,0.14797935,0.00222751,0.0189152,0.06439257,-0.05352585,-0.00091744,0.00015171,-0.02056495,0.07123481,-0.00223193,0.0126241,-0.00245333,0.01641572,-0.00389104,-0.01485198,-0.03848898,-0.0178064,0.02557701,0.02226711,-0.05822008,-0.00026655,0.05977357,0.05393559,0.06761092,0.02361555,0.05385209,0.04548447,0.02047005,-0.00061993,0.00573378,-0.16151175,0.05241052,0.03535605,0.02216977,-0.06121914,-0.0926685,-0.03944096,0.02276812,-0.05026191,0.05605843,0.03557161,0.11088009,0.03461931,0.05040159,0.03572649,-0.02875867,-0.00245049,-0.02303938,-0.0249595,-0.00382969,-0.063031,0.00851777,-0.03025349,0.03891183,-0.01855355,0.00902497,0.08390871,-0.01306822,-0.01961811,-0.02023466,-0.01290287,0.06895634,-0.05794473,-0.00304182,0.05624367,0.00735944,-0.06057386,0.1409748,-0.05629523,-0.03011781,-0.00316744,-0.05779691,0.07389131,-0.04101532,0.00862628,-0.07725156,-0.03051049,0.0178464,-0.02377366,0.00044385,-0.01800925,-0.02602,0.02572714,0.00368581,-0.03137665,0.02853114,0.00617988,-0.03430595,0.00687489,0.04980158,0.04310226,-0.02400493,-0.02999867,-0.01023082,0.10167446,0.04547235,0.01225458,0.04622134,0.03558316,0.01307114,-0.09487828,0.01253566,-0.06237405,0.02139241,0.02260043,-0.07631713,-0.04302428,0.0145323,0.00626147,0.04163761,0.04744902,-0.09249274,-0.03641617,0.16530433,0.06553356,0.05711932,-0.03846338,-0.03578024,-0.03372354,0.04051427,-0.08054491,-0.02848965,-0.00980401,0.03284992,0.0309283,0.00659327,-0.07631898,0.02953223,-0.0074271,-0.04992502,-0.07981497,0.13366945,-0.00150552,-0.12101763,0.0000094,0.00390451,-0.01344111,0.04466572,-0.01667917,0.0294231,-0.02124513,0.00950405,0.07846692,-0.03701693,-0.02903756,0.00585705,0.03669874,0.02816367,0.04945735,-0.01851361,-0.03262793,-0.02396345,-0.00056151,-0.04674322,0.03075793,-0.06872368,0.03582903,-0.03948567,-0.04366355,-0.02230334,0.03341932,0.04281172,0.0428181,-0.02394421,-0.0120053,-0.01502853,-0.04153215,0.00744028,-0.00752474,0.00244971,0.00299804,0.0253803,0.00531421,0.00113077,-0.02865597,-0.00654683,0.05012602,0.07415886,-0.0350429,-0.03018056,0.08283005,0.00883469,-0.0518144,-0.01595457,0.01912096,0.05037533,0.0069136,0.06788855,-0.03434422,0.10381005,-0.04226478,-0.20349126,-0.00739457,0.03943582,-0.00174745,-0.00796094,-0.09145585,0.01284697,-0.01311588,-0.00705084,0.05367747,0.11097576,-0.07895883,-0.03862627,-0.0155097,0.05241246,-0.0178504,-0.04543155,-0.01381355,-0.02463411,-0.01971937,0.02272598,0.04251602,-0.01927391,-0.02948826,0.0066874,-0.04246868,0.11300149,-0.00114196,-0.05004634,0.03665888,-0.02652024,0.04937614,-0.07302132,-0.15960428,0.03111027,0.0206989,0.07546314,-0.02334431,-0.00799558,0.00884559,-0.07907909,0.07729249,0.00158042,-0.11984155,-0.04378742,-0.00403575,0.02467666,-0.0730097,-0.04616044,-0.01918359,-0.00041228,-0.00769831,0.03647679,0.0297095,0.01448869,0.00005523,-0.03120218,0.03465276,-0.02622795,0.07054657,0.02459433,-0.05855062,-0.0517031,-0.00375645,0.05251722,0.01901506,-0.00641679,0.0133497,-0.02194861,-0.07240719,0.04176644,0.10683787,-0.02487477,-0.00004745,0.03116436,-0.06637198,0.00428315,-0.04665525,0.02209493,-0.03242945,0.05603302,-0.0017317,0.00854166,0.00593816,0.0559183,0.02430942,0.06508446,-0.02871457,0.04542425,0.00430255,0.05406195,0.03267048,0.00491849,-0.02722318,0.07226493,0.05472017,-0.23829305,0.02873759,0.0162492,-0.02060756,-0.01621636,-0.00380824,0.08425227,-0.04970061,-0.04045584,0.02510466,-0.00972612,0.00960842,-0.03773816,-0.04065479,-0.0126821,0.02058188,0.07111847,-0.02473307,0.02870512,-0.06179124,-0.02126279,0.04637159,0.19391006,-0.03564028,0.03031061,0.02783557,-0.0293373,-0.03364262,0.06262512,0.00949091,-0.02065283,0.030163,0.07412828,-0.02547171,-0.03329537,0.10154572,0.05449575,-0.01212536,0.01135406,0.02108066,0.03874822,0.01162112,0.01199215,-0.01325347,0.08176716,0.00201694,-0.03207584,-0.08674168,-0.01907372,0.02209582,-0.01032303,-0.06188403,-0.03433711,-0.0348368,0.06193141,0.07539082,-0.0159527,0.01654966,-0.05350892,0.00334485,-0.04629214,0.00676625,0.0725307,0.02171256,-0.07227773],"last_embed":{"hash":"15215bp","tokens":59}}},"text":null,"length":0,"last_read":{"hash":"15215bp","at":1751095117853},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Success Metrics","lines":[73,79],"size":221,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"15215bp","at":1751095117853}},
"smart_blocks:Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Repository Links": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09906086,-0.05648178,-0.00868633,0.00362851,-0.02358217,-0.00323827,-0.05137372,0.04329957,-0.01714119,-0.02851205,0.05922104,-0.06101968,0.05520857,0.11929758,0.05399066,0.02473621,-0.01934541,-0.00077031,-0.01289994,-0.00304465,0.03295211,-0.03522483,0.04736934,-0.02930536,-0.04408549,0.03529563,-0.02514878,-0.08367667,-0.02154415,-0.18012238,0.03854441,0.03316623,0.06587896,-0.00136832,0.03131754,0.07108246,-0.01327606,-0.00985512,0.00144734,0.01093661,0.06991903,0.02549171,-0.02117202,-0.06065289,0.02107542,-0.01925378,-0.01412753,-0.01158088,-0.00065774,-0.01266253,-0.02342206,-0.06274631,0.06503408,0.00890938,0.04982533,0.05695562,0.04018364,0.08643017,0.09456825,0.03315775,0.03816041,0.01255656,-0.16769299,0.10936379,0.06412628,0.03022121,-0.01526451,-0.03341303,0.02907444,0.00969426,-0.05115863,0.03255048,0.0190046,0.06468759,0.04555342,0.01042336,0.0256398,-0.01353762,0.02639399,-0.04968578,-0.07231567,-0.00254153,-0.02481145,0.01831054,-0.01096142,-0.00120222,-0.02880557,0.0611384,0.03952648,-0.00573608,-0.05176762,-0.06474334,0.07501545,0.03836459,-0.02460359,0.04156779,0.04667559,0.01221129,-0.09695284,0.1386116,-0.05536479,-0.0129463,0.0086969,-0.03047092,0.02665894,-0.00337535,-0.00223246,-0.06836073,-0.02054151,0.00198322,-0.0084697,-0.01710551,0.00372984,-0.0165372,0.02523606,-0.03236188,0.02164708,0.02399277,-0.04837586,-0.01532906,0.00568481,0.02775768,0.06547625,-0.00532304,0.00104567,-0.00064982,0.00778665,0.0462645,0.04336558,0.01848418,0.0331068,0.01394552,-0.05660382,-0.00200398,-0.04707007,0.02403853,-0.00061371,-0.13706964,0.01029108,-0.00755638,-0.03130063,-0.03869306,0.01565023,-0.06994893,-0.06180556,0.13425116,0.0040728,0.03472875,-0.01753611,-0.04380777,0.00479681,0.02768746,-0.06487003,-0.00537593,0.03318347,0.02755413,0.02949864,0.03741215,-0.04282563,0.02907529,0.00712527,-0.03796887,-0.06323655,0.14285538,0.02147206,-0.111088,-0.02962027,-0.02194706,-0.05326426,0.02675022,0.00205946,0.02551982,0.01605367,0.01613739,0.07004848,-0.02276043,-0.02697069,-0.01045014,0.02433163,0.05015339,0.00056743,-0.04161547,-0.03370884,-0.03697201,0.01640891,-0.03948762,-0.02624081,-0.06178752,0.02392892,-0.02615703,-0.07217488,-0.06160672,0.06333572,0.03922284,0.02577259,-0.02314363,0.01956381,0.01387825,-0.00406848,0.00994653,0.04735934,0.06093787,-0.00432166,0.03448293,-0.06341308,-0.02094454,-0.04848373,-0.00589783,0.05645207,0.0482397,-0.04418835,0.00049666,0.04693773,0.00553627,-0.05338433,-0.03418406,0.00947889,0.0336117,0.04936522,0.05396752,-0.05403702,0.04874412,-0.07329636,-0.22347146,-0.0155833,-0.00020793,-0.02596719,-0.03366694,-0.10859042,0.02031,-0.06771476,-0.02108932,0.08922681,0.14152238,-0.02158681,-0.02168847,-0.08676089,0.0189744,-0.00323112,-0.0164167,0.0027969,-0.02243019,0.02556936,0.00722212,0.03447601,-0.02920217,-0.06598797,-0.003649,-0.04602372,0.12641849,0.04010197,0.00317159,0.03240255,0.00121191,0.05814214,-0.02190181,-0.20471123,0.00809178,0.02876686,0.02468991,0.00124582,0.00754931,-0.00503803,-0.08886684,0.06501062,0.00221468,-0.08072162,-0.05239949,0.00346058,-0.0014232,-0.09845334,-0.00079832,0.00112795,-0.02271053,0.0505259,-0.01491907,0.0771376,0.00916067,0.03294883,-0.00386696,-0.01687346,0.01291459,0.00480615,0.04661863,-0.001788,-0.02042172,-0.0359441,0.066925,0.01273568,0.02198,-0.0180363,0.0065271,-0.01236805,0.05064759,0.09568986,0.01806284,0.00161734,-0.00571356,-0.0333303,-0.00784956,-0.05539258,0.03123009,-0.06851288,0.02788018,0.02722102,0.00749118,0.00509489,0.05197334,0.0591025,0.00666873,-0.05394056,0.09624884,-0.00392389,0.03206014,0.02584217,-0.02010884,-0.01496877,0.1129142,0.07014779,-0.23698427,0.03867207,0.095208,-0.00792263,-0.0446821,-0.00961009,0.08502125,-0.07618665,-0.05884571,0.01604035,-0.00742621,-0.01203011,-0.0364701,-0.02318662,-0.02439533,0.02642493,0.03603576,0.00151137,0.01342813,-0.01741396,-0.06566057,0.05305019,0.19703731,-0.05042331,0.04560758,0.00106131,-0.05343201,-0.0081591,0.05878282,0.03273271,-0.00841473,-0.03986134,0.06019538,-0.01312869,-0.00152856,0.05006623,0.02371081,-0.00676741,0.00121855,0.04188128,-0.01034808,0.03359202,-0.03150263,-0.00259042,0.08029081,0.03147012,-0.04024122,-0.02284893,-0.04403559,0.01675878,-0.01758617,-0.0788014,-0.02998983,-0.00947076,0.06320521,0.0552675,0.00914386,-0.022125,-0.08486212,0.00404356,0.02222921,-0.01517573,0.03923909,-0.00644714,-0.06701382],"last_embed":{"hash":"s643th","tokens":88}}},"text":null,"length":0,"last_read":{"hash":"s643th","at":1751095117891},"key":"Projects/Bytebridge/task-ai/tasks.md#__task.ai - Project Tasks#Repository Links","lines":[80,85],"size":204,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"s643th","at":1751095117891}},