# Afyalink - Project Tasks

## Strategic Planning with <PERSON> (Co-Founder)

### #simon-meeting
**Next Phase Planning Meeting**
- [ ] Schedule meeting with <PERSON>
- [ ] Prepare MVP performance report
- [ ] Review current user analytics
- [ ] Discuss expansion opportunities
- [ ] Plan next development phase timeline

### #performance-review
**MVP Performance Analysis**
- [ ] Analyze website traffic and SEO performance
- [ ] Review care worker booking conversions
- [ ] Assess NHIMA facility directory usage
- [ ] Evaluate medication price guide engagement
- [ ] Generate comprehensive performance report

### #feature-planning
**Next Phase Feature Development**
- [ ] Identify high-priority feature requests
- [ ] Plan additional care worker onboarding
- [ ] Consider payment integration options
- [ ] Evaluate mobile app development
- [ ] Plan content expansion strategy

### #content-strategy {#content-strategy}
**Unified MVP Content & SEO Blueprint Implementation**
- [ ] Implement three-pillar content strategy from strategic memo
- [ ] Set up Pillar 1: Factual & Directory Content (The "Lists")
- [ ] Create NHIMA-Accredited Facilities directory structure
- [ ] Build procedure costs & NHIMA coverage system with pricing
- [ ] Develop detailed facility profiles with comprehensive data
- [ ] Implement Pillar 2: Educational & Guide Content (The "Explainers")
- [ ] Create "Ultimate Guide to NHIMA in Zambia" comprehensive article
- [ ] Set up Pillar 3: High-Intent Thematic Content (The "Hubs")
- [ ] Build cancer treatment guide as hub-and-spoke model example

## Technical Development

### #mvp-maintenance
**Current MVP Maintenance**
- [ ] Monitor site performance and uptime
- [ ] Update Sanity CMS content
- [ ] Review and fix any technical issues
- [ ] Ensure SEO content remains current
- [ ] Backup and security maintenance

### #seo-optimization
**SEO Content Strategy**
- [ ] Analyze current keyword performance
- [ ] Expand NHIMA facility listings
- [ ] Update medication pricing data
- [ ] Create additional healthcare guides
- [ ] Monitor search ranking improvements

### #technical-implementation
**Content Strategy Technical Setup**
- [ ] Set up URL structure: `/nhima/[city]/[facilityType]` (e.g., `/nhima/lusaka/hospitals`)
- [ ] Create cost pages: `/costs/[procedure-slug]/[city]` (e.g., `/costs/mri-scan/lusaka`)
- [ ] Build facility profiles: `/facility/[facility-slug]` (e.g., `/facility/coptic-hospital-lusaka`)
- [ ] Implement NHIMA status badges ("Covered," "Not Covered," "Co-payment Required")
- [ ] Add toggle switch for "Show only NHIMA-covered options"
- [ ] Set up price history tracking with enhanced Sanity schema
- [ ] Implement procedureCost data model with priceHistory field
- [ ] Create cross-linking system between content pillars

### #user-experience
**UX/UI Improvements**
- [ ] Review user feedback and analytics
- [ ] Identify navigation improvements
- [ ] Optimize booking flow
- [ ] Enhance mobile responsiveness
- [ ] Improve page load speeds

## Business Development

### #partnership-opportunities
**Strategic Partnerships**
- [ ] Identify potential healthcare partners
- [ ] Explore NHIMA collaboration opportunities
- [ ] Consider pharmacy partnerships
- [ ] Evaluate care worker training programs
- [ ] Research government health initiatives

### #monetization-strategy
**Revenue Model Development**
- [ ] Analyze current lead generation effectiveness
- [ ] Plan premium service offerings
- [ ] Consider subscription model for care workers
- [ ] Evaluate advertising opportunities
- [ ] Develop pricing strategy for expanded services

## Archive Links
- [MVP Development History](./archive/mvp-development.md)
- [Performance Reports](./archive/performance-reports.md)
- [Feature Requests](./archive/feature-requests.md)
- [Partnership Discussions](./archive/partnerships.md)

## Team Assignments
- **Mwila**: Technical development, performance analysis, feature implementation
- **Simon**: Business strategy, partnerships, market development, co-founder decisions
- **Task AI**: Full Stack Developer - Content strategy implementation, technical architecture, schema development

## Success Metrics
- Website traffic growth
- Care worker booking conversions
- SEO ranking improvements
- User engagement metrics
- Lead generation effectiveness

#afyalink #strategic #mvp #simon #cofounder #healthcare #seo
