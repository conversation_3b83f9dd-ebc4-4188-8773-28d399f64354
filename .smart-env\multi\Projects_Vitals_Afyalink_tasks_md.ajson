
"smart_sources:Projects/Vitals/Afyalink/tasks.md": {"path":"Projects/Vitals/Afyalink/tasks.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05232798,-0.01928849,-0.03844541,-0.01869103,-0.03903562,0.07407255,-0.00544059,0.04653032,-0.03204865,0.05150738,0.03432007,-0.04081288,0.03724507,0.05191988,0.06598736,0.00761603,-0.02182716,0.02713693,-0.02540366,-0.01319824,0.0368604,-0.02154942,0.0040768,0.00498491,0.01520705,0.07483893,-0.04473664,-0.08440199,-0.04641955,-0.19074132,-0.04386105,-0.01187502,0.03539537,-0.00331667,-0.03036504,0.01861327,-0.02902416,0.08238649,-0.02370793,0.02056587,0.00665966,0.03862905,-0.03905916,-0.03426899,-0.01804563,-0.0746979,-0.05661981,-0.04816819,-0.04975302,-0.04724815,-0.04379017,-0.08875538,0.03806227,0.01554106,0.06382219,0.02870329,0.04685874,0.02255139,0.00678972,0.07302587,0.01362206,-0.006584,-0.2240458,0.08292703,-0.01560592,0.0625821,-0.08028352,-0.09254666,-0.04071993,0.02705416,-0.01865983,0.00575177,0.03228047,0.00536083,0.06477209,0.03495782,0.00270714,-0.00632822,0.00555752,-0.04278349,0.03103776,0.02259819,-0.02499568,0.00447654,0.01070418,0.02131707,0.03653444,0.01214182,0.04306527,0.02106236,0.03243661,-0.02328596,-0.0056751,0.01941823,0.00655135,0.00819873,-0.0154353,0.0288413,-0.06199699,0.12117695,-0.03519119,0.00459415,0.01450172,-0.05517947,0.01387554,0.00823571,-0.00394408,-0.03334372,0.01407106,0.06269877,-0.00899439,-0.01717303,0.0070896,-0.03022121,-0.00059074,0.05415737,-0.00781312,0.04043685,-0.04254396,-0.04470721,0.06751431,0.02662159,0.08175693,0.00188956,-0.04236403,0.02020612,0.04790826,0.0736177,0.03330938,0.03997834,0.04521144,0.03437584,-0.0776177,0.0172797,-0.0580896,0.00508997,-0.00379946,-0.03108075,-0.01560395,0.0576725,-0.00081713,0.02626826,-0.0526531,-0.10885801,-0.05453521,0.17822365,0.02518658,0.06537002,-0.00946231,-0.07440176,-0.01599559,0.0200072,-0.03379476,-0.04202787,-0.02761624,-0.01698373,-0.00415749,0.00275593,-0.05458219,0.00032654,-0.01656473,-0.09640352,-0.07838749,0.15797046,-0.01623667,-0.11733913,0.00385083,-0.01822813,-0.00868228,-0.02415193,-0.01078989,-0.00304971,-0.04518479,-0.00072334,0.061133,-0.03442674,0.01587053,-0.00721302,-0.00278476,-0.01762087,0.01960161,-0.02325519,-0.04907458,-0.00425081,-0.01661096,-0.10786041,0.0339812,-0.05519794,0.01149088,-0.04225345,-0.09022498,-0.01789769,-0.01079902,0.04260253,-0.02112341,-0.07990346,-0.00929422,0.01212799,0.05036914,-0.03487109,0.0524586,0.00680307,0.00756725,0.05374186,0.00105561,0.05483829,-0.02493515,-0.00186989,0.07933231,0.10803548,-0.00557647,0.03343171,0.0091115,-0.00522813,-0.03726513,0.01266879,0.00412616,0.0573904,-0.00444922,0.08712583,0.00203002,0.11105559,-0.01907809,-0.23551327,0.05161579,0.01295492,0.00095455,-0.00004194,-0.06570029,0.01236749,-0.0002111,-0.00194752,0.05684899,0.16715991,-0.00634168,0.01160012,0.05781656,-0.01449045,-0.00481764,-0.02317451,-0.04190659,-0.02242396,-0.00853805,0.04705551,0.06448456,0.00833587,-0.07772476,0.03441558,-0.05226544,0.13383366,0.00052745,0.0088027,0.0116123,0.0521212,0.02253833,-0.06593027,-0.15611061,-0.01470743,-0.00278459,0.05709519,-0.02826949,-0.0191171,-0.00415472,-0.02128688,0.06257943,-0.03122981,-0.08356281,-0.06663955,-0.05958581,-0.03531923,-0.0197097,-0.0311186,0.01160684,0.02644292,-0.04087128,0.03430761,-0.03007176,0.03293381,0.00598266,-0.03150208,-0.03001935,-0.03456492,0.01762826,-0.02754357,-0.0064753,0.04252382,0.03673846,0.04810079,-0.0334626,-0.00982272,0.03134705,-0.05546505,-0.04584699,0.03976396,0.06102435,-0.03153056,-0.02786644,0.01494918,-0.02569157,0.00893564,-0.05019729,0.02034249,-0.01988561,0.00353672,-0.02503789,0.03146571,0.00643376,0.0280266,0.04240344,0.03310686,-0.0408747,0.04469067,-0.0018385,0.02649284,-0.02922826,-0.03062813,-0.00575666,0.115572,0.03924286,-0.24845608,0.00063605,-0.00723628,-0.039487,-0.02270041,-0.0372472,0.03239929,-0.02016204,0.01364546,0.095591,-0.00255137,-0.01307652,0.03098624,-0.04094375,0.01472777,0.02720327,0.05601373,0.00309374,0.0195899,-0.05149581,-0.02020058,-0.00096636,0.21733569,-0.043656,0.0132055,0.02114414,-0.04870823,-0.00179124,-0.00440083,0.04218401,0.05716271,0.00668653,0.04495265,0.03751589,-0.00497223,0.06494591,-0.01583024,0.02945255,-0.01408219,0.04591569,0.03431045,0.0112146,-0.00985066,0.01567783,0.10571307,-0.04478367,-0.01287558,-0.02870757,-0.00285613,-0.00015546,-0.04113968,-0.04465273,0.00328661,-0.04306004,0.03182644,0.07088804,0.06024682,0.03639796,0.0069373,-0.00827614,0.01927124,0.02644869,0.03709215,0.06307019,0.0294594],"last_embed":{"hash":"kzvhc5","tokens":415}}},"last_read":{"hash":"kzvhc5","at":1751243576182},"class_name":"SmartSource","last_import":{"mtime":1751099929734,"size":4361,"at":1751243557685,"hash":"kzvhc5"},"blocks":{"#Afyalink - Project Tasks":[1,113],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)":[3,40],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##simon-meeting":[5,12],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##simon-meeting#{1}":[6,6],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##simon-meeting#{2}":[7,7],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##simon-meeting#{3}":[8,8],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##simon-meeting#{4}":[9,9],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##simon-meeting#{5}":[10,10],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##simon-meeting#{6}":[11,12],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##performance-review":[13,20],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##performance-review#{1}":[14,14],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##performance-review#{2}":[15,15],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##performance-review#{3}":[16,16],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##performance-review#{4}":[17,17],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##performance-review#{5}":[18,18],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##performance-review#{6}":[19,20],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##feature-planning":[21,28],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##feature-planning#{1}":[22,22],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##feature-planning#{2}":[23,23],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##feature-planning#{3}":[24,24],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##feature-planning#{4}":[25,25],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##feature-planning#{5}":[26,26],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##feature-planning#{6}":[27,28],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}":[29,40],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}#{1}":[30,30],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}#{2}":[31,31],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}#{3}":[32,32],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}#{4}":[33,33],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}#{5}":[34,34],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}#{6}":[35,35],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}#{7}":[36,36],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}#{8}":[37,37],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}#{9}":[38,38],"#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}#{10}":[39,40],"#Afyalink - Project Tasks#Technical Development":[41,77],"#Afyalink - Project Tasks#Technical Development##mvp-maintenance":[43,50],"#Afyalink - Project Tasks#Technical Development##mvp-maintenance#{1}":[44,44],"#Afyalink - Project Tasks#Technical Development##mvp-maintenance#{2}":[45,45],"#Afyalink - Project Tasks#Technical Development##mvp-maintenance#{3}":[46,46],"#Afyalink - Project Tasks#Technical Development##mvp-maintenance#{4}":[47,47],"#Afyalink - Project Tasks#Technical Development##mvp-maintenance#{5}":[48,48],"#Afyalink - Project Tasks#Technical Development##mvp-maintenance#{6}":[49,50],"#Afyalink - Project Tasks#Technical Development##seo-optimization":[51,58],"#Afyalink - Project Tasks#Technical Development##seo-optimization#{1}":[52,52],"#Afyalink - Project Tasks#Technical Development##seo-optimization#{2}":[53,53],"#Afyalink - Project Tasks#Technical Development##seo-optimization#{3}":[54,54],"#Afyalink - Project Tasks#Technical Development##seo-optimization#{4}":[55,55],"#Afyalink - Project Tasks#Technical Development##seo-optimization#{5}":[56,56],"#Afyalink - Project Tasks#Technical Development##seo-optimization#{6}":[57,58],"#Afyalink - Project Tasks#Technical Development##technical-implementation":[59,69],"#Afyalink - Project Tasks#Technical Development##technical-implementation#{1}":[60,60],"#Afyalink - Project Tasks#Technical Development##technical-implementation#{2}":[61,61],"#Afyalink - Project Tasks#Technical Development##technical-implementation#{3}":[62,62],"#Afyalink - Project Tasks#Technical Development##technical-implementation#{4}":[63,63],"#Afyalink - Project Tasks#Technical Development##technical-implementation#{5}":[64,64],"#Afyalink - Project Tasks#Technical Development##technical-implementation#{6}":[65,65],"#Afyalink - Project Tasks#Technical Development##technical-implementation#{7}":[66,66],"#Afyalink - Project Tasks#Technical Development##technical-implementation#{8}":[67,67],"#Afyalink - Project Tasks#Technical Development##technical-implementation#{9}":[68,69],"#Afyalink - Project Tasks#Technical Development##user-experience":[70,77],"#Afyalink - Project Tasks#Technical Development##user-experience#{1}":[71,71],"#Afyalink - Project Tasks#Technical Development##user-experience#{2}":[72,72],"#Afyalink - Project Tasks#Technical Development##user-experience#{3}":[73,73],"#Afyalink - Project Tasks#Technical Development##user-experience#{4}":[74,74],"#Afyalink - Project Tasks#Technical Development##user-experience#{5}":[75,75],"#Afyalink - Project Tasks#Technical Development##user-experience#{6}":[76,77],"#Afyalink - Project Tasks#Business Development":[78,95],"#Afyalink - Project Tasks#Business Development##partnership-opportunities":[80,87],"#Afyalink - Project Tasks#Business Development##partnership-opportunities#{1}":[81,81],"#Afyalink - Project Tasks#Business Development##partnership-opportunities#{2}":[82,82],"#Afyalink - Project Tasks#Business Development##partnership-opportunities#{3}":[83,83],"#Afyalink - Project Tasks#Business Development##partnership-opportunities#{4}":[84,84],"#Afyalink - Project Tasks#Business Development##partnership-opportunities#{5}":[85,85],"#Afyalink - Project Tasks#Business Development##partnership-opportunities#{6}":[86,87],"#Afyalink - Project Tasks#Business Development##monetization-strategy":[88,95],"#Afyalink - Project Tasks#Business Development##monetization-strategy#{1}":[89,89],"#Afyalink - Project Tasks#Business Development##monetization-strategy#{2}":[90,90],"#Afyalink - Project Tasks#Business Development##monetization-strategy#{3}":[91,91],"#Afyalink - Project Tasks#Business Development##monetization-strategy#{4}":[92,92],"#Afyalink - Project Tasks#Business Development##monetization-strategy#{5}":[93,93],"#Afyalink - Project Tasks#Business Development##monetization-strategy#{6}":[94,95],"#Afyalink - Project Tasks#Archive Links":[96,101],"#Afyalink - Project Tasks#Archive Links#{1}":[97,97],"#Afyalink - Project Tasks#Archive Links#{2}":[98,98],"#Afyalink - Project Tasks#Archive Links#{3}":[99,99],"#Afyalink - Project Tasks#Archive Links#{4}":[100,101],"#Afyalink - Project Tasks#Team Assignments":[102,106],"#Afyalink - Project Tasks#Team Assignments#{1}":[103,103],"#Afyalink - Project Tasks#Team Assignments#{2}":[104,104],"#Afyalink - Project Tasks#Team Assignments#{3}":[105,106],"#Afyalink - Project Tasks#Success Metrics":[107,113],"#Afyalink - Project Tasks#Success Metrics#{1}":[108,108],"#Afyalink - Project Tasks#Success Metrics#{2}":[109,109],"#Afyalink - Project Tasks#Success Metrics#{3}":[110,110],"#Afyalink - Project Tasks#Success Metrics#{4}":[111,111],"#Afyalink - Project Tasks#Success Metrics#{5}":[112,113],"#afyalink #strategic #mvp #simon #cofounder #healthcare #seo":[114,115]},"outlinks":[{"title":"MVP Development History","target":"./archive/mvp-development.md","line":97},{"title":"Performance Reports","target":"./archive/performance-reports.md","line":98},{"title":"Feature Requests","target":"./archive/feature-requests.md","line":99},{"title":"Partnership Discussions","target":"./archive/partnerships.md","line":100}],"last_embed":{"hash":"kzvhc5","at":1751243574493}},"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05244681,-0.02957001,-0.03853362,-0.01605021,-0.03424021,0.07445753,-0.00216117,0.05138384,-0.03216359,0.04836722,0.03631185,-0.03308469,0.04088598,0.05109447,0.06511054,0.00502692,-0.01999069,0.02827227,-0.01760335,-0.01648284,0.04052619,-0.02064252,0.00653614,0.00723485,0.02026005,0.07193463,-0.0432265,-0.07818799,-0.04389442,-0.18970522,-0.03443051,-0.00972276,0.03598721,-0.00857406,-0.0336069,0.01745848,-0.02925463,0.08249201,-0.01673608,0.02333526,0.00316977,0.03798292,-0.03908237,-0.02774774,-0.01676206,-0.07213265,-0.0589718,-0.05246288,-0.04504198,-0.04659007,-0.03767489,-0.09059642,0.0360701,0.01940897,0.06766125,0.02663128,0.04581823,0.02752013,0.00043988,0.06516176,0.01518922,0.00112778,-0.23185474,0.08340897,-0.01980289,0.05871088,-0.08194784,-0.09042045,-0.04426529,0.03058879,-0.01859465,0.00351204,0.03642652,0.00480077,0.06124466,0.03588377,0.00396341,-0.0089174,0.01037075,-0.04544503,0.03115319,0.02254197,-0.02507913,-0.00174302,0.01711219,0.01746812,0.0376949,0.01357397,0.04676596,0.0228686,0.03712166,-0.01603446,-0.01473396,0.01328177,0.0064676,0.01587313,-0.02205702,0.02557133,-0.06022748,0.11728704,-0.02805431,0.0080084,0.0094066,-0.05370971,0.01254686,0.00550981,-0.00848219,-0.02842809,0.01497793,0.06341144,-0.00637728,-0.01602617,0.01203375,-0.03171045,-0.00322387,0.05822051,-0.00810156,0.03598648,-0.03516482,-0.04349964,0.06770799,0.02506502,0.07858657,0.00066975,-0.03540081,0.01814607,0.04595805,0.0739195,0.03334811,0.03363594,0.04830016,0.02525531,-0.07618112,0.01955205,-0.04980717,0.00500066,-0.00340466,-0.03333356,-0.0099718,0.06849491,-0.00643063,0.01933321,-0.05688062,-0.1057459,-0.0553091,0.18281126,0.02095376,0.06826466,-0.01286954,-0.07845745,-0.01511589,0.02527538,-0.04369349,-0.04259589,-0.03229591,-0.01709337,0.00276696,0.00241632,-0.0631934,0.00013523,-0.01268429,-0.09768943,-0.08516771,0.15755382,-0.01896834,-0.11338595,0.00259846,-0.0201134,-0.01091427,-0.02176564,-0.01916167,-0.0015494,-0.04892907,-0.00256942,0.06320275,-0.03967484,0.0149791,-0.00796568,-0.00088611,-0.02364945,0.01802109,-0.02374994,-0.0523957,-0.00439969,-0.01855059,-0.10738474,0.03154393,-0.0530713,0.01533583,-0.04088574,-0.09054526,-0.00982089,-0.00985599,0.04407063,-0.02081483,-0.07843499,-0.01361183,0.01302934,0.05232225,-0.03386211,0.06230032,0.00580445,0.00716498,0.05789404,0.00439366,0.05345433,-0.02259707,0.00168708,0.0817374,0.09914263,-0.00223265,0.03464834,0.00693577,-0.00552995,-0.0366616,0.01041785,0.00373682,0.06172039,-0.00303726,0.08298317,0.00453483,0.11434631,-0.02761908,-0.23449202,0.05119872,0.01481817,0.0043582,-0.00455774,-0.06364943,0.01782016,-0.00174803,-0.00188186,0.06099999,0.16053334,-0.00496507,0.01652436,0.05103533,-0.01435679,-0.01216017,-0.01383024,-0.03834268,-0.02334067,-0.00647857,0.04697857,0.06439464,0.01120376,-0.07949332,0.03575492,-0.05121526,0.13138595,-0.00521105,0.0045283,0.01043699,0.048869,0.01326209,-0.06873118,-0.14647585,-0.01623116,-0.00413934,0.05531075,-0.02675796,-0.02422035,-0.00576871,-0.02854384,0.0661912,-0.03080258,-0.08968669,-0.06485616,-0.05486187,-0.03593314,-0.01202582,-0.03463129,0.01438777,0.02949699,-0.04158301,0.03611161,-0.02860249,0.03258288,-0.00147021,-0.0328646,-0.0271757,-0.03746854,0.01433185,-0.0249659,-0.00548236,0.03940605,0.0412904,0.0526628,-0.03834103,-0.00561692,0.03423567,-0.05498307,-0.04572219,0.03938109,0.06336962,-0.02843144,-0.01580897,0.02134924,-0.02009352,0.00316275,-0.05274462,0.01282394,-0.01560285,-0.00328972,-0.02614375,0.02974764,0.01276615,0.02721895,0.04650106,0.03819017,-0.04271811,0.04258925,-0.00513417,0.01775218,-0.02629067,-0.03121793,-0.01110578,0.10317878,0.04244159,-0.25579262,0.000787,-0.01264992,-0.03284816,-0.02005855,-0.03706925,0.02577495,-0.01689027,0.01557776,0.08857222,-0.00288039,-0.01123446,0.03070593,-0.0460773,0.01204298,0.02395436,0.06120335,0.00055286,0.02128882,-0.05281959,-0.01710832,-0.00363396,0.22259547,-0.04367134,0.01087623,0.01864876,-0.05030644,0.00152423,-0.00352486,0.03973379,0.04846476,0.00817359,0.04864191,0.03853088,0.00144838,0.06788731,-0.01629567,0.02985873,-0.01422425,0.04206314,0.03285835,0.01590235,-0.01783344,0.02600968,0.1067123,-0.04854885,-0.01038491,-0.03072361,0.0001259,0.00293466,-0.03962643,-0.04480697,0.00196363,-0.04200226,0.03535358,0.07157133,0.05508045,0.03077572,0.00656114,-0.01222909,0.01557968,0.02559947,0.03163424,0.06624984,0.02684131],"last_embed":{"hash":"i8w1bd","tokens":445}}},"text":null,"length":0,"last_read":{"hash":"i8w1bd","at":1751243574728},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks","lines":[1,113],"size":4299,"outlinks":[{"title":"MVP Development History","target":"./archive/mvp-development.md","line":97},{"title":"Performance Reports","target":"./archive/performance-reports.md","line":98},{"title":"Feature Requests","target":"./archive/feature-requests.md","line":99},{"title":"Partnership Discussions","target":"./archive/partnerships.md","line":100}],"class_name":"SmartBlock","last_embed":{"hash":"i8w1bd","at":1751243574728}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05121283,-0.02261803,-0.03306592,-0.01595276,-0.04133159,0.0746324,-0.0048065,0.0473986,-0.03395775,0.04619755,0.04218344,-0.03862368,0.03970222,0.04256225,0.07179173,0.01182751,-0.02794628,0.02415195,-0.01422545,-0.01202535,0.03523763,-0.01964704,0.00387541,0.00163426,0.02692367,0.07734582,-0.04287262,-0.08870637,-0.04332862,-0.19270736,-0.03057516,-0.00238914,0.03913817,-0.01175053,-0.03409019,0.0128581,-0.02305457,0.08974565,-0.01966389,0.02577964,0.00445924,0.03506724,-0.02617615,-0.02505619,-0.02041497,-0.07069644,-0.05479115,-0.04488324,-0.0482653,-0.05353126,-0.04025413,-0.092426,0.03236881,0.00547984,0.0628736,0.03155813,0.04404259,0.02806391,0.00215577,0.06633771,0.00635139,-0.00483089,-0.22914211,0.08543315,-0.01972839,0.0671188,-0.09120765,-0.083781,-0.04037183,0.03156473,-0.0148774,0.00671786,0.04010174,0.00135148,0.06650128,0.03295054,-0.00607837,-0.00383107,0.01565312,-0.04672657,0.02037069,0.03453242,-0.03083392,0.00297292,0.01100938,0.02046446,0.03346162,0.00512814,0.03718334,0.02184239,0.03793421,-0.01745719,-0.02076142,0.02242219,-0.00278343,0.00987459,-0.01607245,0.02146557,-0.05436349,0.11897913,-0.03101817,0.00236711,0.01252907,-0.04685854,0.0122604,0.00186079,-0.00603191,-0.03384777,0.01774699,0.06511535,-0.00756817,-0.01478579,0.0035566,-0.03596351,-0.0032463,0.05421719,-0.00723376,0.03617313,-0.03969418,-0.04177833,0.0684408,0.02192882,0.07351074,-0.00087393,-0.04862249,0.02085434,0.05354964,0.0678228,0.03387114,0.02838496,0.04287272,0.03043189,-0.07593441,0.01524242,-0.05713882,-0.00399332,-0.00238348,-0.03013251,-0.01413021,0.06863855,0.00524484,0.02708098,-0.06107677,-0.10966746,-0.05465422,0.18005219,0.02454769,0.06571818,-0.01293349,-0.06961431,-0.00630798,0.01291142,-0.04344957,-0.04159569,-0.02976481,-0.0216123,0.00335465,0.01254562,-0.05400058,0.01083675,-0.02129745,-0.10155573,-0.08024637,0.16240329,-0.02411144,-0.12040011,0.00395851,-0.00961192,0.00432678,-0.03232532,-0.0163561,-0.0021709,-0.04757478,0.00261405,0.07331868,-0.02607928,0.00392396,-0.00891115,-0.00197511,-0.01836311,0.02393021,-0.02178011,-0.04818512,-0.01248535,-0.02258682,-0.11059917,0.03475183,-0.05198904,0.01261135,-0.04169415,-0.09484937,0.00614009,-0.01945595,0.04475643,-0.02954241,-0.08555883,-0.0036705,0.01045891,0.04852987,-0.03749647,0.0498313,0.00217508,0.00175854,0.06305461,-0.00130696,0.05543822,-0.02224263,0.00094344,0.08438308,0.09360145,-0.00018686,0.03831985,0.01566011,0.00173207,-0.03675467,0.01190589,0.00255215,0.0536773,-0.00971236,0.08162215,0.01136705,0.1112692,-0.0265877,-0.2321021,0.05647459,0.02340978,0.00735995,-0.01072733,-0.05884368,0.01365144,0.0044557,-0.01010424,0.06806006,0.16525254,-0.00395711,0.01111903,0.06572074,-0.01440581,-0.00735111,-0.02440342,-0.03667093,-0.02131973,-0.00743123,0.04231665,0.06760259,-0.00254932,-0.0721215,0.03165608,-0.05072413,0.13084126,-0.00052606,-0.00078508,0.01349151,0.05265133,0.01240495,-0.06945051,-0.14094929,-0.01936022,-0.00184471,0.0657257,-0.02969329,-0.01948773,-0.01022294,-0.02175403,0.06326031,-0.02773907,-0.08576068,-0.06259288,-0.05238311,-0.03670219,-0.00451858,-0.02427273,0.01096189,0.02163457,-0.04211316,0.04024674,-0.02553244,0.02930061,-0.00066905,-0.0348147,-0.01986333,-0.0378454,0.02026874,-0.02377808,0.00154411,0.03926235,0.04221131,0.04203683,-0.0271234,-0.00751198,0.03027972,-0.05503521,-0.04381937,0.04787496,0.05256622,-0.02948836,-0.02777051,0.02262103,-0.02592018,0.00568011,-0.05357348,0.01446703,-0.01767305,0.00213816,-0.03305697,0.02986729,0.00561579,0.02730386,0.04334207,0.03215576,-0.05125527,0.03950147,-0.00707155,0.01972732,-0.03164021,-0.03871111,0.00079907,0.10085929,0.03637593,-0.25246376,0.00471755,-0.01090699,-0.03502263,-0.01875577,-0.03686749,0.0273848,-0.02534899,0.01824453,0.0831925,0.00627766,-0.02006995,0.04206637,-0.03811177,0.01573951,0.0227776,0.06424321,0.00202599,0.02082942,-0.05390187,-0.01936521,-0.00731123,0.21764365,-0.04246691,0.00816546,0.02204842,-0.05001693,-0.00098376,-0.00656896,0.04010946,0.05073811,0.00551275,0.03925192,0.0426215,0.0005286,0.06110513,-0.00958467,0.02672774,-0.01745762,0.04376481,0.0389105,0.00542271,-0.01070312,0.01308851,0.1049863,-0.03599153,-0.01530214,-0.03404685,-0.00322812,-0.00025392,-0.04189065,-0.04883511,0.0031945,-0.04512455,0.03899064,0.08047043,0.06305624,0.02897184,0.00822896,-0.00890706,0.01463937,0.02524984,0.04025679,0.06594161,0.03192546],"last_embed":{"hash":"1p5acel","tokens":346}}},"text":null,"length":0,"last_read":{"hash":"1p5acel","at":1751243574985},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)","lines":[3,40],"size":1539,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1p5acel","at":1751243574985}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##simon-meeting": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03704607,-0.02686192,-0.0214959,-0.06477044,-0.06152347,0.03882159,0.01487291,0.02057963,-0.02100197,0.00407868,0.02776671,-0.06373403,0.02795316,0.06144878,0.0498814,0.02363809,-0.05571451,0.02237484,-0.04079434,-0.00581872,0.03404382,-0.04199326,-0.02155869,-0.00128457,0.01598987,0.06797303,-0.03436618,-0.10266087,-0.03920088,-0.17646074,-0.06150725,-0.01088159,0.04234844,-0.01792656,-0.02124034,-0.00609931,-0.01938436,0.11099653,-0.01267585,0.06695823,0.00092353,0.01942724,-0.03030696,-0.02627438,-0.02554514,-0.04941501,-0.04342959,-0.02285209,-0.05862891,-0.01918863,-0.02880118,-0.06076382,0.0139053,0.00404918,0.02486762,0.03670365,0.0392788,0.00520847,0.04064595,0.04672371,-0.00332361,-0.02936495,-0.23226072,0.08128019,-0.01521255,0.08076642,-0.06911012,-0.08195031,-0.02967622,0.05554744,-0.00062011,0.02010443,0.058217,0.00448865,0.06899932,0.03042031,-0.00969264,-0.02493777,0.01818383,-0.03694741,0.02245777,0.01886011,-0.02492392,-0.00559697,-0.00402143,0.01780757,0.01274371,0.02265524,0.05166453,0.0428802,0.00610513,-0.04386639,-0.01135132,0.02471873,-0.01886001,0.01229833,-0.0114845,0.01960686,-0.04624543,0.15111452,-0.06516858,0.02426172,0.01045306,0.00544289,0.01244813,-0.01282618,-0.0316124,-0.05778241,0.02640257,0.05428065,-0.01848019,-0.02282224,0.06053371,-0.04826792,0.02523751,0.0337149,0.01890864,0.02721568,-0.04714887,-0.04745262,0.08000935,0.03905449,0.06989284,0.00696917,-0.04914016,0.05675836,0.05698526,0.06938244,0.0125048,0.02575598,0.06158408,0.05115173,-0.07024036,0.0139052,-0.06321049,-0.00194255,-0.02251303,-0.04758631,-0.0409908,0.02347621,0.02718504,0.05048113,-0.05853527,-0.11366414,0.00472071,0.16553605,0.01440783,0.03539687,0.01700869,-0.07224052,-0.02788893,-0.01292025,-0.01734493,-0.07073116,-0.01495051,0.00557787,0.02410104,-0.01021126,-0.05410158,0.02720077,-0.02802867,-0.0424004,-0.03709403,0.13987409,-0.02928105,-0.09376993,0.01852189,-0.02590769,0.03739337,-0.03508468,-0.02299864,-0.01166628,-0.02038368,0.02165471,0.08666717,0.01057595,0.03161418,-0.0471347,-0.04337175,-0.01042208,0.03899928,-0.01897581,-0.03042418,-0.00600789,-0.03262196,-0.12258384,0.03465716,-0.0779678,-0.00630546,-0.00922107,-0.1224827,0.02542526,-0.03984103,0.01764924,-0.04393415,-0.06883888,0.01330196,0.00524579,0.03151089,-0.04294384,-0.00260449,-0.02091642,-0.01852994,0.0649031,0.00095354,0.05142776,-0.01693123,-0.02216113,0.09208591,0.09222749,-0.01114092,0.02778232,0.00839399,-0.02226197,-0.06666674,-0.00088679,0.00274344,0.01564828,-0.0407853,0.05026554,0.0093054,0.13819101,-0.002672,-0.21731642,0.07155276,0.00222407,0.00348277,-0.04286797,-0.04762584,0.02826117,0.02346703,-0.04084081,0.08914074,0.14667058,0.00696701,0.01192371,0.06321404,-0.01482046,0.0054312,-0.00693597,-0.03724218,-0.01777086,-0.01198235,0.00246908,0.05833485,-0.00120024,-0.07550152,-0.01259448,-0.04674921,0.11760535,0.05081376,0.0165274,0.01120113,0.06859844,0.02201049,-0.0653018,-0.12950945,-0.0214575,0.01554215,0.0860645,-0.04022739,0.00741751,-0.00046055,-0.01625312,0.03766628,-0.02461583,-0.07742623,-0.06487776,-0.05126538,-0.01949387,-0.02603205,-0.01581651,0.02786878,0.01588677,-0.03510601,0.07450135,-0.01407114,0.04074088,-0.0056683,-0.03742164,-0.05171286,-0.02873158,0.04994176,-0.02074076,0.02176303,-0.01243624,0.05447444,0.0477518,0.01237703,-0.0215273,-0.01831706,-0.03477478,-0.05369548,0.06058063,0.02789141,-0.01826041,-0.07925609,0.00363619,-0.04340996,-0.00983963,-0.05120252,0.02244261,-0.01309962,0.04424917,-0.05325741,0.06490051,-0.02425926,0.01784151,0.01180225,0.02296866,-0.01268593,0.02509437,0.00067642,0.02088868,-0.02487021,-0.02677755,-0.00183476,0.12705879,0.02622806,-0.24054,0.02571787,0.03656674,-0.04570178,-0.01847511,-0.01761327,0.045891,0.00339852,0.00560136,0.06413537,0.02272256,-0.02467007,0.02679451,-0.00588978,0.00077484,0.04925787,0.06836631,0.01036149,-0.00111989,-0.03044386,-0.01993193,-0.00407108,0.20907134,-0.05264499,0.05381149,0.02836637,-0.02004669,0.02810769,-0.02326104,0.03316899,0.05145282,-0.02444969,0.0146645,0.00331715,-0.00332538,0.07657831,0.00982665,0.01242685,0.01471067,0.04759338,0.0364084,-0.0165721,0.00174864,-0.02632206,0.12959707,-0.0108355,-0.00999138,-0.01596981,-0.02148542,-0.00789619,-0.05773401,-0.0550875,0.00778607,-0.02902346,0.01320327,0.07454838,0.07541354,0.02298723,0.03354389,-0.00429103,0.01554481,0.00168467,0.02448555,0.00738162,0.03767414],"last_embed":{"hash":"4lphlr","tokens":80}}},"text":null,"length":0,"last_read":{"hash":"4lphlr","at":1751243575180},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##simon-meeting","lines":[5,12],"size":239,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"4lphlr","at":1751243575180}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##performance-review": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03311872,-0.03528993,-0.03961723,-0.01390807,-0.04972404,0.04582923,0.04941364,0.0627238,-0.04390772,0.04720474,-0.0015873,-0.0369409,0.04018014,0.0597198,0.06173765,0.00510691,-0.02576343,0.03546185,0.00518987,0.00010901,0.04378237,-0.0227195,0.00252156,0.0013208,0.01618865,0.05351334,-0.01698927,-0.0921897,-0.04762888,-0.15902928,-0.03747644,-0.02588249,0.05351487,-0.00352245,-0.02318844,0.02202638,-0.03304774,0.08348657,-0.01070432,0.02455456,0.015281,0.02751726,-0.01427133,-0.00652924,0.00525735,-0.06693419,-0.05558717,-0.06342224,-0.04807496,-0.004627,-0.04410517,-0.06439378,0.03551104,0.0149306,0.06185283,-0.00210069,0.02653144,0.03873771,0.0195944,0.03883598,0.01434776,0.02293379,-0.2368291,0.04924212,-0.0322253,0.06254811,-0.0728977,-0.09466728,-0.06887951,0.02823637,-0.00981942,-0.00008148,0.03394034,0.00139759,0.07919846,0.06323131,0.00743414,-0.03333542,0.01514366,-0.04201885,0.04835721,-0.0032587,-0.02543702,-0.02785753,0.03577632,0.02199676,0.0526611,0.03551527,0.0873074,0.02464384,0.02308034,-0.00985603,-0.03248421,0.01797719,0.00782757,0.026958,-0.01813059,0.02269354,-0.04046901,0.14019868,-0.03312352,0.01629546,0.01312159,-0.0363631,-0.00307954,-0.00914983,-0.02130595,-0.01015661,0.02323362,0.07489443,-0.02672326,-0.00630384,0.04578495,-0.03415286,-0.00489361,0.04620859,-0.0153607,0.05302691,-0.04727536,-0.05657048,0.05905779,0.02067865,0.07219018,0.01628296,-0.053788,0.02232007,0.06182615,0.08203314,0.01346212,0.03841485,0.04211276,0.03121998,-0.065365,0.02373299,-0.06760056,-0.00970398,-0.01146766,-0.02719988,-0.03039157,0.06268939,0.00523687,0.01748166,-0.07733081,-0.04677049,-0.04553631,0.17022894,0.00467594,0.08048742,-0.02163171,-0.04338384,-0.02231017,0.03696088,-0.05132885,-0.07968971,-0.03680056,0.01874706,0.01029579,-0.0230601,-0.05736517,0.02034307,0.00332788,-0.08096658,-0.07669902,0.16625796,-0.05342206,-0.08443958,0.03564037,-0.02049589,-0.04266514,-0.01169324,-0.02479677,-0.002747,-0.09039818,0.02804617,0.03126607,-0.05837499,0.04839497,-0.01705135,-0.02480643,-0.06347884,0.04039408,-0.02195047,-0.07005692,0.02433295,0.00199358,-0.09343097,0.01503485,-0.05107052,0.01786747,-0.00488136,-0.10377552,-0.02974618,-0.02015688,0.02826412,-0.01075902,-0.04533248,-0.01346291,0.00525026,0.04699506,-0.04244159,0.05346707,-0.00829581,-0.00989523,0.04631855,0.01640601,0.04841255,-0.02894507,0.00424997,0.05583517,0.1164286,-0.01086112,0.01075923,-0.01515998,0.00425646,-0.03584216,0.01050891,0.00459534,0.07424036,-0.00587602,0.05808051,-0.00401476,0.09447175,-0.04908392,-0.23053414,0.0396306,-0.00924735,0.04749436,0.02597003,-0.07423924,0.02714865,-0.00755307,-0.02079618,0.09578445,0.15308931,-0.00510217,-0.01009553,0.07842021,-0.01173711,-0.00091349,-0.03416043,-0.05811948,-0.04182681,-0.01462232,0.02717663,0.04873414,0.04777037,-0.0635483,0.03238175,-0.06754865,0.13659336,0.01371951,0.04127463,0.03200632,0.05368824,-0.0047003,-0.0584319,-0.14538316,-0.0133948,-0.00643099,0.0872351,-0.04422243,-0.01171793,-0.01439421,-0.0559442,0.03369788,-0.0275377,-0.07682543,-0.04081273,-0.04224774,-0.00036514,-0.04205211,-0.06553901,0.04094778,0.0140982,-0.04461819,0.05946467,-0.03073817,0.02435414,0.00716115,-0.04067571,-0.0397139,-0.05268628,0.02023179,-0.02312915,-0.00438057,0.0391456,0.05467893,0.05259028,-0.02725716,-0.02684063,0.01308136,-0.04264804,-0.02623144,0.04872911,0.05200832,-0.05916348,-0.03692136,0.01524594,-0.02540106,0.00581726,-0.06866004,0.01321484,-0.0347644,0.01314692,0.00757778,0.01558628,-0.00655014,0.0219411,0.03498154,0.0409181,-0.03593862,0.04413848,0.0042517,0.03839015,-0.00718206,-0.0214467,-0.01108249,0.09687424,0.03794208,-0.23875056,-0.00184288,-0.00732024,-0.00788296,-0.0156886,-0.05280662,0.00934648,-0.01495889,0.03715971,0.05860548,0.04173978,-0.02567777,0.02808972,-0.05691354,-0.01030825,0.01897319,0.06114514,0.00035296,0.03042852,-0.03434421,-0.03231408,0.00166666,0.22285795,-0.02029723,0.01454655,0.01379364,-0.04454762,-0.0143659,-0.0208632,0.00519987,0.05699236,-0.01166239,0.04949445,0.01401561,-0.00015135,0.07055583,-0.03076528,0.02200023,0.00269935,0.02773567,0.05664924,0.00640503,-0.03394511,0.0074563,0.11059283,-0.01981608,0.01150534,-0.00552382,0.00620608,-0.00376089,-0.02966401,-0.04003631,0.002969,-0.03412733,0.03506393,0.05321141,0.03883403,0.00016429,-0.00251221,-0.02078858,0.01507688,0.00378681,0.00787719,0.05853954,0.02605879],"last_embed":{"hash":"1tprhbc","tokens":85}}},"text":null,"length":0,"last_read":{"hash":"1tprhbc","at":1751243575241},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##performance-review","lines":[13,20],"size":289,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1tprhbc","at":1751243575241}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##feature-planning": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05709411,-0.02597133,-0.01143402,-0.06492231,-0.06852791,0.01387967,0.00459905,0.03081797,-0.01434482,0.00559823,0.00880402,-0.03753252,0.0301522,0.07029598,0.05881089,0.01991943,-0.00344903,0.0322529,-0.055487,0.0245635,0.0351564,-0.03948945,-0.00856947,-0.01748135,0.0217035,0.05424269,-0.04531455,-0.09485687,-0.03053769,-0.18158913,-0.03872051,0.01049957,0.02805798,-0.01945267,-0.02877372,0.02312449,-0.04475375,0.10124977,-0.01924635,0.04794886,-0.01057623,0.03179386,-0.0612866,-0.04075107,-0.06594821,-0.06698963,-0.02656151,-0.04179841,-0.05129357,-0.01870281,0.01380294,-0.06251428,-0.0149229,0.01446167,0.03024351,0.01740444,0.00528676,0.0134087,0.02974763,0.11446521,0.01350086,-0.02492131,-0.18798117,0.09074537,-0.01525094,0.0850354,-0.06848279,-0.08397269,-0.05655711,0.05285447,-0.00493693,0.01284233,0.03652319,0.01055539,0.06805089,0.02585887,-0.00356335,-0.00917245,-0.01032582,-0.03702173,0.01375334,0.00523461,-0.02463081,0.03960541,-0.02635426,0.02061813,0.00359079,-0.04417486,0.03741851,0.0097355,0.01653502,-0.06431631,-0.00044899,0.00902701,-0.02518898,0.01279355,-0.01664462,0.01511556,-0.08415418,0.1765947,-0.01132044,-0.01670706,0.02418502,-0.02368052,0.03032451,-0.00952675,-0.01522183,-0.04692487,0.01149102,0.06769968,-0.05449474,0.00013185,0.01309971,-0.03360734,-0.00001731,0.02418873,-0.01945172,0.0219369,-0.02695882,-0.03143353,0.08119719,0.01356256,0.03637693,-0.00731687,-0.09783413,0.02556003,0.06058048,0.05774513,-0.0057094,0.02907525,0.04401326,0.01307889,-0.04133701,0.00402332,-0.06678556,0.0066892,-0.0077314,-0.01322921,-0.06285161,0.04305815,0.00256027,0.04895053,-0.03247927,-0.10146447,-0.01146674,0.13251087,0.03575363,0.04616595,-0.01631818,-0.05686254,-0.02389336,-0.00159862,0.00599979,-0.03563803,-0.01415684,0.02125183,0.04101793,-0.01179744,-0.05327356,0.01119666,0.02737976,-0.05400478,-0.07422242,0.16667071,-0.03781486,-0.11049065,-0.0073358,-0.00896283,0.00715201,-0.03387862,-0.01782322,0.008035,-0.02337423,0.04915945,0.09484029,0.0109044,-0.01802216,-0.0079251,0.00052288,-0.00838383,0.02980161,-0.02879563,-0.02881232,-0.03724843,-0.0315755,-0.10774353,0.04591967,-0.06258394,-0.01436793,-0.06777007,-0.0788234,0.02130728,-0.02036969,0.00242058,-0.04791092,-0.08085601,-0.0070073,0.01888314,0.05335638,-0.06538647,0.03045297,0.01191898,-0.00115637,0.04532433,-0.01411776,0.06182868,-0.01516036,-0.00567129,0.10792235,0.08798245,0.00240557,0.09182292,0.04605282,-0.01496771,-0.02684653,0.01316178,0.00303018,0.02007686,-0.02159839,0.0497567,-0.02480884,0.14062802,-0.00260799,-0.2160666,0.03799318,0.01219715,-0.00022581,-0.06127058,-0.05968928,0.00098883,0.02633129,-0.03229927,0.0774025,0.1590118,-0.00690578,-0.00524545,0.08074405,0.0131676,-0.00264156,0.00533752,-0.07146143,-0.024923,-0.00392889,0.02660376,0.04885894,0.01082955,-0.06020502,0.00783194,-0.01791878,0.13782986,0.03837688,0.00801789,0.01195921,0.03519171,0.03522433,-0.03282589,-0.13313544,-0.00479744,0.01980926,0.06816477,-0.05551975,-0.0168213,-0.00522499,-0.01348139,0.03914043,-0.03531177,-0.06841592,-0.04711098,-0.04806657,-0.02636749,-0.01074936,-0.03381564,0.00347427,0.00642648,0.00107981,0.03250618,-0.01603811,0.04478564,0.02358527,-0.03547128,-0.01177068,-0.05396136,0.008934,-0.02887668,-0.00551652,0.01265499,0.03405502,0.03107677,-0.02117333,-0.01986333,0.00468502,-0.03573013,-0.04759145,0.06709331,0.03311096,-0.01079431,-0.04216702,-0.00867273,-0.03799573,0.04056006,-0.0028502,0.01776427,-0.05016682,0.05760767,-0.03400677,0.04296899,-0.0034496,0.00783165,0.03348057,0.05604911,-0.04403837,0.0159748,0.00454687,0.00233376,-0.01143934,-0.0134327,-0.02808894,0.12622547,0.00730532,-0.22675066,0.03547331,-0.01380619,-0.04643259,-0.04173952,-0.01561595,0.04813278,-0.00741125,-0.01412019,0.06921776,0.01200725,-0.02454549,0.08038317,-0.01558537,-0.00851754,0.02918678,0.06846271,-0.01084102,0.00816139,-0.07270858,-0.01503221,0.00412858,0.23249505,-0.06190552,0.0166129,-0.00255299,-0.02791319,0.03946561,0.00295934,0.03163422,0.07717925,-0.00564147,-0.00780199,0.01784096,-0.01389374,0.09364612,0.02796266,0.00579334,-0.01417914,0.04964475,0.0301843,-0.02272105,0.01839939,-0.03521977,0.08801727,-0.02321361,-0.04204059,-0.00640392,-0.0306576,0.01616458,-0.03998368,-0.07288518,-0.02196949,-0.03352313,0.04157641,0.07109296,0.06883825,0.04264058,0.03786515,0.01080493,0.04097341,0.02377387,0.07360029,0.06441601,0.05145973],"last_embed":{"hash":"anwe93","tokens":84}}},"text":null,"length":0,"last_read":{"hash":"anwe93","at":1751243575313},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##feature-planning","lines":[21,28],"size":267,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"anwe93","at":1751243575313}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05444264,-0.01011895,-0.02949814,0.00678683,-0.05052524,0.07425132,-0.03699008,0.02287157,-0.02352268,0.05152949,0.03160612,-0.03480019,0.06295336,0.031214,0.10684051,0.001687,-0.03427963,0.0361057,0.01801103,0.00214848,0.08146661,-0.02720975,0.00684181,0.03319823,0.03449349,0.0766314,-0.0308692,-0.06948406,-0.01423163,-0.19284001,-0.00973077,-0.0291562,0.00067463,-0.02587247,-0.03061149,0.00462162,-0.02865128,0.0739703,-0.02767591,0.02173359,0.01703822,0.05079985,-0.01259691,-0.02983911,-0.0185055,-0.07987086,-0.02922853,-0.02425205,-0.0148421,-0.07268198,-0.01863325,-0.04005839,0.03172701,-0.01931136,0.03090111,0.06370278,0.07033592,0.06862747,-0.00863546,0.05251571,-0.02515228,0.00635287,-0.23256987,0.08160811,-0.00960245,0.07274554,-0.08421081,-0.04749317,-0.01921318,0.05331649,-0.01476599,0.02195882,0.04713363,0.01149718,0.06997491,0.04375016,-0.00872587,-0.0060617,-0.00539465,-0.05820648,-0.00453675,0.03066191,-0.029342,0.03659502,0.00672346,0.01596632,0.00261163,-0.02806067,0.02162687,-0.00302548,0.03349488,-0.00675951,0.01202624,0.00597221,-0.0211176,-0.01915422,-0.04887924,-0.00776001,-0.04297048,0.09603647,-0.02604728,-0.02925799,0.00563005,-0.02726739,0.01036123,0.01442037,-0.01451552,0.00874609,0.01571466,0.05338228,0.01009755,-0.01539426,-0.02431097,-0.05561947,-0.01948988,0.05015163,-0.01357764,0.02721839,-0.04639761,-0.02955911,0.04762527,-0.01921518,0.06889673,0.02785031,-0.06114841,0.02212898,0.03628578,0.02230529,0.06531039,0.02461158,0.00443236,0.02366746,-0.07745717,-0.01101009,-0.05914821,0.00395053,0.02776216,-0.0245824,-0.02299574,0.07336754,0.0139285,0.03332871,-0.02336364,-0.09040058,-0.04949272,0.15470882,0.02515548,0.07795551,-0.01315329,-0.03387061,-0.00608082,0.00521508,-0.00216195,-0.00031509,-0.03498072,-0.01211546,-0.01263197,0.03187784,-0.05817975,0.01116468,-0.05713362,-0.11941294,-0.06650223,0.18162072,-0.04062539,-0.14124276,0.00305452,-0.00047249,0.0028006,-0.05201628,-0.018029,0.00340487,-0.02867855,0.00225101,0.08545212,-0.00468685,-0.03538528,-0.00892664,0.02184314,0.02279636,0.0438857,-0.08225878,-0.05692699,0.00367341,-0.0794128,-0.0838645,0.02773724,-0.03976179,0.04934188,-0.03188551,-0.09980167,0.06053834,0.00698022,0.01432129,-0.04560929,-0.08131509,0.00701824,0.01690767,0.03949385,-0.01275756,0.01363538,-0.00498733,-0.03070697,0.02995808,-0.05302963,0.03894146,0.00263532,-0.01798111,0.09674417,0.05350745,0.0122089,0.04753314,0.00413677,-0.00392157,-0.05102631,-0.00864238,0.01982763,0.0467065,-0.01451929,0.06964531,0.00131989,0.06498709,-0.06609854,-0.21535367,0.03291195,-0.00239763,-0.01950009,0.00866271,-0.02214722,0.02875867,0.0118376,-0.03361147,0.05241325,0.15133934,-0.05487296,0.01986678,0.05114583,-0.04756133,-0.02263605,-0.00718336,-0.04457033,0.00362465,-0.03382306,0.02317866,0.0465029,-0.04774083,-0.07608358,0.06253626,-0.02630146,0.14473715,-0.00104521,-0.00427421,0.02713386,0.04994706,0.01969316,-0.04866939,-0.13788646,-0.00194082,0.00896822,0.0571682,-0.02744513,0.00070431,-0.01321051,0.01605147,0.07057111,-0.03496631,-0.09747602,-0.03769056,-0.04818824,-0.03239784,0.04192061,0.01632081,0.01837949,0.01950427,-0.0093807,0.03441723,0.01717821,0.00225264,0.00753439,-0.04574134,0.00593586,-0.01963402,-0.00342436,-0.01934457,-0.00676523,0.06191416,0.04283036,0.02974644,-0.03445251,0.00788042,0.01379101,-0.03761558,-0.03234366,0.0497344,0.0278122,-0.02722155,-0.04721678,0.01356201,0.00295713,0.01485564,-0.05391244,-0.00528432,-0.02945517,0.02107174,-0.0410374,0.01940502,0.00205938,0.03841671,0.05874517,0.01596923,-0.07561809,0.0459923,0.00362227,-0.02663489,-0.03913486,-0.03919826,0.00989139,0.09816957,0.02004949,-0.26877505,0.01416076,-0.0450087,-0.01862678,-0.03138485,-0.04268698,0.01463375,-0.02130557,0.02249228,0.06570072,0.01115571,-0.01776299,0.03101505,-0.02562128,0.01069081,0.02189383,0.03997811,0.00113792,0.04685265,-0.02885882,-0.01743662,0.00454667,0.22932079,-0.02407067,-0.01715774,0.00463801,-0.04672674,0.00320449,0.00672966,0.0547394,0.06580421,0.00748508,0.03385305,0.05050477,0.02305558,0.0606457,0.00683298,0.03473385,-0.00511527,0.03369516,-0.01497389,-0.01575795,-0.00167336,0.01760426,0.09313721,-0.02866618,-0.01947464,-0.05122402,-0.03512288,-0.00984898,-0.03643731,-0.03234428,0.00001076,-0.03363757,0.06529606,0.1004099,0.05096582,0.00017998,-0.02046189,-0.00708813,-0.00505789,0.03780465,0.04149041,0.12250786,0.02999036],"last_embed":{"hash":"v1e53g","tokens":192}}},"text":null,"length":0,"last_read":{"hash":"v1e53g","at":1751243575380},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Strategic Planning with Simon (Co-Founder)##content-strategy {#content-strategy}","lines":[29,40],"size":694,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"v1e53g","at":1751243575380}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Technical Development": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08210285,-0.00373639,-0.02129718,0.00078922,-0.00559561,0.06677951,-0.05064614,0.04469747,-0.03346815,0.06432576,0.01837751,-0.0583664,0.02810502,0.07730896,0.06818102,0.00637037,0.00089711,0.02071284,-0.03232979,-0.04212599,0.02402482,-0.00302031,0.02819826,0.00216487,0.00142038,0.07497911,-0.02127789,-0.03689609,-0.02431767,-0.1702958,-0.06317879,-0.05185244,0.01949742,0.03336811,-0.0100634,0.02352946,-0.04796927,0.04411824,-0.0254009,0.01884925,0.01937662,0.05318647,-0.03579531,-0.06441283,-0.00813323,-0.10214724,-0.0334326,-0.01658168,-0.02901405,-0.02608596,-0.03232667,-0.0533964,0.04392128,0.03393861,0.06306128,0.01290156,0.06550797,0.02910509,-0.00474626,0.05743316,0.04038106,0.00574104,-0.20786922,0.05739133,-0.01615212,0.02330291,-0.05345976,-0.09614346,-0.00606602,0.01248007,-0.00117628,0.00673543,-0.00057101,-0.0016758,0.06499185,0.03622038,0.04114121,-0.0096345,-0.00663391,-0.02851259,0.0448495,-0.00530468,-0.00714718,0.00414817,0.00804062,-0.01938971,0.0403183,0.0239627,0.0216923,0.00423774,0.01009914,-0.02879669,0.04336976,0.00256383,-0.0050611,-0.00632308,-0.01279165,0.05142228,-0.06032981,0.10211977,-0.02990638,-0.00586838,0.00867625,-0.06486169,0.02331051,0.01821811,-0.00752512,0.00475802,0.00769798,0.0203054,0.00802552,0.00246731,0.00222818,0.00772191,-0.03093697,0.05503871,-0.04108097,0.04021337,-0.03252186,-0.01163624,0.03310676,-0.00659294,0.09034806,0.00526111,-0.01142359,0.02103535,0.04264671,0.08785591,0.024828,0.06375389,0.03325338,0.03701646,-0.07732107,-0.02141669,-0.04867032,0.02291986,-0.01811679,-0.03632761,0.01341667,0.05823376,-0.0438678,-0.00370366,-0.03924761,-0.11135078,-0.0931504,0.17487095,-0.00249871,0.06605661,-0.02439629,-0.08327542,-0.0332691,0.04850632,-0.0288632,-0.00185634,-0.01272303,-0.01276527,-0.01418859,0.01903063,-0.05812316,-0.02448473,0.0106717,-0.09784912,-0.08100755,0.15639278,0.00599211,-0.12055133,-0.00925609,-0.04751251,-0.02876388,-0.01299188,-0.00635668,0.01336938,-0.04451559,-0.01060151,0.04577388,-0.06740166,0.01009448,0.03782779,0.01932473,-0.03046991,0.01089996,-0.0323083,-0.04342463,0.02800908,-0.00073448,-0.07344699,0.0288355,-0.03964419,0.03322754,-0.04228958,-0.06894082,-0.04147036,0.03572781,0.03978501,0.01526175,-0.07342245,-0.01816313,0.00842833,0.05596178,-0.0129691,0.05607496,0.03215167,0.01432898,0.01270255,-0.02010623,0.04276519,-0.00057449,0.0050105,0.04227712,0.1091339,-0.02818356,0.027077,0.01609057,-0.00767779,-0.05303585,0.00484129,0.01363283,0.06099559,0.00959347,0.09635345,-0.0222963,0.08975303,-0.02070359,-0.24535117,0.051477,-0.02306775,-0.02246909,0.02180047,-0.0877516,0.01533101,-0.01415078,0.01508414,0.04470117,0.16091043,-0.03465493,0.00605792,-0.00690205,-0.04886591,-0.00770609,-0.01445341,-0.03202597,-0.02369835,-0.00392257,0.04544152,0.07094128,0.01676171,-0.08364448,0.06144515,-0.04261581,0.13179745,-0.02564914,0.02047804,-0.01579524,0.04990918,0.0429109,-0.03984948,-0.18367872,0.01206519,0.00861251,0.01277006,0.0308604,-0.02863816,0.00003741,0.00838526,0.05591069,-0.0309766,-0.08909167,-0.04506316,-0.07229742,-0.02984669,-0.04068913,-0.06837532,-0.01618168,0.02831308,-0.04579506,0.01088245,-0.02707765,0.01081953,0.00856377,-0.01417081,-0.03160749,-0.01754127,0.00990199,-0.02084134,-0.00129799,0.05203372,-0.01399291,0.07935245,-0.08620389,0.00668158,0.03724449,-0.03679555,-0.02537522,0.01597739,0.08938694,-0.04233579,0.02928046,0.01146698,0.01364881,-0.00093317,-0.02259234,-0.02309518,0.02075013,0.0080789,0.00897441,0.03323989,0.01196533,0.03249487,0.0459779,0.02751828,-0.06837647,0.05285905,0.03160159,0.01397241,-0.02055733,-0.03056721,-0.01906606,0.12995,0.03904293,-0.25599805,0.02307902,-0.00217016,-0.04747662,-0.0618572,-0.02342233,0.01644249,0.00052853,-0.00447889,0.10709911,-0.01474393,0.02428459,-0.00581898,-0.09808359,0.03101829,0.02241692,0.03883081,-0.03110755,0.05856955,-0.04764857,-0.00149926,-0.00807644,0.19733568,-0.01727453,0.01496012,0.00500643,-0.06237638,0.00855544,0.00077766,0.02383301,0.05192487,0.02832855,0.07178081,0.02345686,-0.00195348,0.0577838,-0.03979906,0.04356768,-0.01817374,0.03721284,-0.00898245,0.05267518,-0.03845226,0.0791873,0.07679803,-0.04903975,-0.01190435,-0.00643438,-0.00282735,-0.01616341,-0.00389302,-0.01387488,0.02511875,-0.02287305,0.02929956,0.0553876,0.02744853,0.06505645,0.01854783,-0.03337843,0.01281413,0.02302484,-0.01924736,0.05362447,0.00666687],"last_embed":{"hash":"16bpsi1","tokens":390}}},"text":null,"length":0,"last_read":{"hash":"16bpsi1","at":1751243575471},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Technical Development","lines":[41,77],"size":1427,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"16bpsi1","at":1751243575471}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Technical Development##mvp-maintenance": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04565075,0.00319255,-0.014474,-0.04770597,-0.01905213,0.02386925,-0.02226792,0.03111232,-0.03941812,0.03783042,0.01162523,-0.0835309,0.0167976,0.05154686,0.03903397,0.01767073,0.03563416,0.03193562,-0.01982103,-0.02884487,0.03871642,-0.00621035,0.00461827,0.01621566,-0.02228224,0.04959556,-0.03769112,-0.05588399,-0.03596105,-0.14669935,-0.09892064,-0.09314701,0.01051583,0.0314168,-0.00123021,-0.00894051,-0.03157173,0.01807052,-0.01121349,0.02644283,0.01101117,0.04844249,-0.06997082,-0.06650162,-0.01639243,-0.07670093,-0.00229477,-0.02331833,0.00216635,-0.01212777,-0.01449417,-0.01340395,0.06136635,0.04186724,0.01720185,0.0432145,0.06935819,0.05068377,0.03311859,0.06906573,0.05064928,0.00169129,-0.18883374,0.03943098,-0.00327808,0.05478768,-0.01787138,-0.1113607,-0.01314516,0.01955677,-0.01958632,-0.00390102,-0.02249183,0.0241394,0.07300559,0.03637393,0.06466392,-0.01964299,-0.03764741,-0.01975627,0.03214139,-0.02623322,0.01574375,-0.00437556,-0.00736402,-0.01855454,0.03275653,0.03706349,0.06110709,0.00741209,-0.01930166,-0.07344731,0.07934962,0.00936034,0.00640609,-0.0182221,0.01062911,0.02322672,-0.06079928,0.12639751,-0.06916123,0.0065618,-0.01605157,-0.07421498,0.01951857,0.01861091,0.0091768,0.00335169,0.0139399,0.0285901,-0.02322684,0.00233943,0.02638232,0.00930819,-0.00483866,0.04008158,-0.01466854,0.06191465,-0.05625429,-0.01833746,0.03668183,0.03805209,0.11970585,0.00452879,-0.00243549,0.0132029,0.0499066,0.08459989,0.03143856,0.0903544,0.05826778,0.07442943,-0.09197649,-0.01244789,-0.05211041,0.02404498,-0.03124756,-0.05569601,-0.00928245,0.01293103,-0.04318999,-0.00357034,-0.01147335,-0.07952354,-0.04065648,0.14656402,0.01031969,0.04590305,0.01653486,-0.09968889,-0.04162053,0.02571361,0.00053518,-0.0169983,0.0029511,0.02785547,-0.03070156,-0.02500471,-0.05355236,0.00343867,0.02017534,-0.03716448,-0.0642221,0.16820347,0.00874406,-0.09558243,-0.02733876,-0.05609908,-0.03501846,0.012913,-0.00654171,-0.00364941,-0.02061562,-0.02538022,0.04175554,-0.05466308,0.04573315,-0.01521526,0.01055973,-0.03333494,0.03940116,-0.03860388,-0.05690095,0.05831953,0.01683553,-0.06465475,0.02370434,-0.05840728,0.04493665,0.00117291,-0.07540097,-0.08511066,-0.00032328,-0.00378541,0.02178419,-0.0546234,-0.03371442,-0.01273301,0.05472761,-0.03447822,0.00652835,0.03724779,-0.01388819,0.00978112,-0.02124238,0.06019801,-0.02739698,-0.0081425,0.03081045,0.1080966,-0.01872594,-0.0061929,0.0065066,-0.02686676,-0.05097594,0.01068306,0.02637845,0.04258926,-0.00360264,0.07516985,-0.01610068,0.12735823,0.00698909,-0.22138484,0.00207646,-0.04468136,-0.03583426,0.02803765,-0.05404481,0.03512657,-0.00012073,0.01580042,-0.00906313,0.14214505,-0.0232477,0.01767385,-0.0262451,-0.02539741,0.01597053,-0.01056064,-0.05388508,-0.03343543,-0.0158256,0.02631657,0.04821417,0.04949997,-0.08566193,0.07646429,-0.04943154,0.13000758,0.00084013,0.0435704,-0.02688134,0.02454166,0.08414994,-0.00172738,-0.23043154,0.02725112,0.0097323,0.00233141,0.00146324,-0.02366672,0.00786694,-0.01121273,0.04014087,-0.0331931,-0.06984961,-0.05577563,-0.0931565,-0.03102476,-0.05231087,-0.06456136,-0.00675982,0.02919097,-0.00792823,0.00786706,-0.0011429,0.03810902,0.0065835,0.01966136,-0.04281617,-0.02285354,0.01268885,-0.02585608,-0.01349203,0.04417875,-0.00674047,0.10850666,-0.06125327,-0.0294292,0.00260872,-0.00273095,-0.05477609,-0.02161096,0.07863358,-0.04670199,-0.04515537,-0.04725092,-0.00037264,-0.01170797,-0.03709447,0.00473204,-0.00442647,0.0149836,0.01288486,0.03831471,-0.00116552,0.01715706,-0.00193175,-0.03575883,-0.00284442,0.05951548,-0.03014215,0.04176629,-0.01263634,-0.00149072,-0.05636917,0.15581964,0.07357642,-0.21603136,-0.00957379,0.04085597,-0.05368713,-0.06940453,-0.01884001,0.02687818,0.02144613,-0.01997466,0.10648825,-0.00197255,0.04585812,-0.04332102,-0.05310985,0.00852793,0.04698849,0.04337011,-0.04497733,0.02545031,-0.0659088,0.01370293,0.04543065,0.19512674,-0.00594253,0.03539371,0.00755082,-0.03002557,0.02726234,0.02464796,0.04577529,0.04802733,0.00469557,0.08133812,0.00004573,0.00937758,0.06558973,-0.01913507,0.04445735,0.01275267,0.04187607,0.03492008,0.03323002,-0.03640614,0.0289016,0.10701543,-0.04138656,-0.01091761,-0.00460742,-0.02607414,-0.01416811,-0.03660936,-0.02428634,0.00921307,-0.00831893,-0.0166672,0.0291413,0.02815152,0.07541034,-0.00814409,-0.04162693,0.03368928,0.00591317,0.02015028,0.04493964,0.01044894],"last_embed":{"hash":"1nw10si","tokens":78}}},"text":null,"length":0,"last_read":{"hash":"1nw10si","at":1751243575699},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Technical Development##mvp-maintenance","lines":[43,50],"size":244,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1nw10si","at":1751243575699}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Technical Development##seo-optimization": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03452278,-0.01961849,-0.01968614,-0.01690777,-0.01509817,0.07082561,-0.03448174,0.06713801,-0.04500181,0.0364729,0.01330451,-0.03180864,0.02551896,0.05651679,0.09191793,0.0040845,0.02583523,0.02857618,-0.06422537,-0.04423863,0.06169812,0.024962,0.02682874,-0.01990894,-0.00662944,0.03027873,-0.03418032,-0.05974698,-0.01162211,-0.15060189,-0.0310282,0.00048241,0.02541238,0.00614766,0.00167552,0.04923359,-0.04817007,0.09009566,-0.05329028,0.03990683,-0.00089629,0.04275073,-0.04306046,-0.02809203,0.00118294,-0.06378316,-0.04062878,-0.01663614,-0.05865426,-0.03425886,-0.0624315,-0.06198573,0.01608586,0.05208091,0.09400168,-0.03173505,0.05122649,0.02642257,-0.01571805,0.07293494,0.05317314,0.02917993,-0.21321689,0.04664614,-0.00859062,0.04635169,-0.04694145,-0.08529364,-0.04712529,0.04884082,0.0200519,0.0290923,0.00974004,0.02705595,0.04702072,0.03532637,0.01460245,-0.03086643,-0.01355365,-0.03523876,0.0503315,-0.02566888,-0.0244222,0.00032347,0.02241875,-0.01733468,-0.00106409,0.01301589,0.07032298,0.0488011,0.01963637,-0.05419163,-0.03818988,0.00054866,0.0069189,0.02509886,-0.02581718,0.04084948,-0.02583286,0.14573906,-0.05293559,-0.00782373,0.00751366,-0.09345118,-0.00151154,0.02477391,-0.00420984,0.00612728,0.01933854,0.06114417,0.0113215,0.01391628,0.00733733,-0.03068193,-0.02821275,0.03199492,-0.04595514,0.0525881,-0.0612348,-0.02257843,0.01978787,0.00692985,0.07049532,0.00488242,0.01161213,0.00889903,0.01214514,0.0969523,0.01400496,0.05121165,0.03950607,0.02147293,-0.0567939,0.01407982,-0.04303939,0.01680443,-0.01158574,-0.04200295,-0.02641267,0.08219633,-0.05157166,-0.00362027,-0.02242474,-0.10823541,-0.08906537,0.20845622,-0.00085665,0.08847377,-0.04105513,-0.06997172,-0.02401076,0.03082294,-0.0447208,-0.02891654,-0.02121073,-0.01572862,0.01249583,-0.02588794,-0.08991262,-0.02188496,0.01846726,-0.05793926,-0.07358025,0.15643786,-0.01664258,-0.09715779,-0.02911137,-0.04096946,-0.0111638,-0.00503637,0.01772639,0.00155684,-0.02629858,0.03078547,0.03880391,-0.058932,0.0324027,0.00402508,0.00013903,-0.04131546,0.03235809,-0.04292374,-0.05653923,0.02219319,0.00238153,-0.08737469,0.02090955,-0.02203811,0.03119537,-0.02292956,-0.09236128,-0.02217661,0.02446024,0.02057121,0.00775719,-0.05428885,0.00532805,0.01159277,0.05975038,-0.01680967,0.06884849,0.02509784,0.0244666,0.06084633,0.0195918,0.04040229,-0.01284047,-0.02413708,0.06701065,0.11185489,-0.03695463,0.00845337,0.02455019,-0.00756687,-0.03887228,0.00471495,-0.01818411,0.06309238,-0.01592703,0.0805245,-0.03135716,0.07952568,-0.0498978,-0.23766586,0.03356101,-0.00826176,-0.04094721,0.02344669,-0.04837055,0.0282904,-0.03068939,-0.01195737,0.0746389,0.13158415,-0.02785788,0.02684733,0.02588715,-0.0243628,-0.04668971,0.00228963,-0.0575604,-0.02868758,0.00118045,0.02778442,0.02358243,0.04168234,-0.10169348,0.04414561,-0.03994591,0.12567946,-0.00678218,0.0298413,-0.00833756,0.02260341,0.02855766,-0.04601713,-0.16957514,0.00663135,-0.01636094,0.0318693,0.0331756,-0.04248621,0.00132148,-0.02787254,0.0512828,-0.02571948,-0.06541347,-0.04245189,-0.08897862,-0.00792813,-0.05098677,-0.0636979,0.01603652,0.01849327,-0.01197955,0.0323984,-0.02652266,0.01504504,0.00409129,-0.02491222,-0.03932659,-0.0322024,0.03499271,-0.02198268,0.00104587,0.03446477,0.01548241,0.05493196,-0.05165964,0.00843127,0.01997194,-0.0537314,-0.03225825,0.01255127,0.08405537,-0.02918416,0.02311783,0.02057803,0.01249543,-0.02491697,-0.03081856,-0.00238644,-0.00136637,0.0079503,-0.01954015,0.02852507,0.00483362,0.0046383,0.00987643,0.05080513,-0.05175404,0.04437791,0.01074558,0.02687872,-0.01451644,-0.02669341,-0.01789105,0.10147777,0.04075541,-0.24713302,0.02975405,0.02450741,-0.04327018,-0.03667588,-0.01401934,0.03128379,-0.01459597,0.02977936,0.08726513,-0.0118719,-0.00206622,0.00881394,-0.08100288,-0.0046498,0.01066321,0.07745295,-0.01483326,0.05532373,-0.0274866,0.00349004,0.00426853,0.2264339,-0.03262007,0.02079108,-0.00015595,-0.06082413,-0.00799544,-0.03444483,0.01883824,0.06086358,0.01960471,0.05194128,-0.00357468,-0.00598406,0.07513678,-0.00767179,0.04560068,-0.01425856,0.05632811,0.00801368,0.0340716,-0.05959986,0.07594927,0.09165574,-0.03346804,0.00460376,-0.02047742,-0.02833424,-0.00660693,-0.01968238,-0.02268704,0.0281477,-0.01212371,0.05486306,0.04811485,0.04515231,0.01186375,0.0063632,-0.02501402,0.00955533,-0.00649871,-0.02157587,0.0259277,0.01365108],"last_embed":{"hash":"1gxuu7j","tokens":74}}},"text":null,"length":0,"last_read":{"hash":"1gxuu7j","at":1751243575751},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Technical Development##seo-optimization","lines":[51,58],"size":247,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1gxuu7j","at":1751243575751}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Technical Development##technical-implementation": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07179746,-0.01342043,-0.01202949,0.00696696,-0.02386567,0.05285697,-0.06970342,0.04273321,-0.01471793,0.04956643,0.02005699,-0.04703545,0.01803744,0.07889097,0.09053109,0.02488119,-0.04157325,0.02780129,-0.00656247,0.00481626,0.06258686,-0.01355904,0.0404401,-0.01386232,-0.00155657,0.10207657,0.01602979,-0.03478326,-0.0078969,-0.15799105,-0.04282952,-0.03412317,-0.01211519,0.02896036,0.01859943,0.00255689,-0.05261537,0.03710148,-0.03578925,0.02024715,0.00653708,0.03598227,-0.02634718,-0.07310578,-0.0024342,-0.11290473,-0.04219309,0.00400585,-0.00675941,-0.02599262,-0.02360165,-0.03340366,0.01647923,0.00208207,0.05817405,0.01674853,0.05974822,0.00635256,5e-8,0.0335111,0.0085354,0.01031908,-0.21320471,0.06764174,-0.00929004,0.01251348,-0.04992022,-0.06041985,0.0307046,0.00832733,-0.01006383,0.02385932,0.01086543,-0.01438981,0.07444692,-0.00113756,0.02711582,0.00902941,-0.00165539,-0.04241645,0.01662441,0.0188875,-0.01746491,0.05031665,-0.03050712,-0.00598674,0.0538394,-0.01829785,-0.02282987,-0.02458056,0.00361752,-0.02535449,0.02404842,0.01823218,-0.02804099,-0.01914529,-0.01348127,0.03802332,-0.03547506,0.1125885,-0.01980603,-0.02984336,0.02129209,-0.03154749,0.01446268,0.00891407,-0.02787837,-0.0086261,-0.01527636,0.01447907,0.01390494,0.0098469,-0.04218522,-0.0323002,-0.04016858,0.04270149,-0.03382896,0.00219811,-0.01710753,-0.01688105,0.02352168,-0.02767625,0.04130912,0.02416753,-0.0289377,0.01549216,0.05645024,0.07077813,0.03730565,0.04039743,0.01089143,0.0050979,-0.06775615,-0.03383941,-0.03072538,0.02607619,0.00950669,-0.02123709,-0.00919245,0.07024125,-0.05188639,0.00116469,-0.04458903,-0.10385859,-0.10369346,0.170454,-0.00232871,0.06573447,-0.04176368,-0.06333795,-0.0310146,0.02859107,-0.03738391,0.01315043,-0.01988941,-0.03270283,-0.0009616,0.04310073,-0.04910388,-0.03384759,-0.00153718,-0.11433128,-0.06970276,0.19954175,-0.00369044,-0.12853128,0.00554589,-0.02457093,0.00614353,-0.02932525,-0.02675808,0.02057875,-0.02138523,0.00979124,0.0828773,-0.03757056,-0.0323055,0.03704834,0.01040848,-0.01209421,0.03458666,-0.04108851,-0.04450487,0.00363537,-0.03349744,-0.10145374,-0.00585528,-0.03139149,0.01849535,-0.03521431,-0.05964286,0.00600414,0.03037316,0.02730184,-0.01635314,-0.06572448,0.00290785,0.02241489,0.04457686,-0.0045898,0.06097417,0.01553694,-0.00076056,0.00144363,-0.05109712,0.02942398,0.02621344,0.02335067,0.05372408,0.05597017,-0.0209054,0.04674563,0.02297897,-0.00112349,-0.07595681,0.01200027,0.02594159,0.05819267,0.03491322,0.06711493,-0.04138999,0.05349367,-0.02488371,-0.2451513,0.07758042,-0.00620503,-0.02325996,0.01921034,-0.07582492,0.00228561,-0.00670199,-0.03084698,0.09383588,0.1946719,-0.06833271,-0.00461746,0.00558457,-0.06224712,-0.00235503,-0.00069847,-0.04532304,-0.00280195,0.00712999,0.0215175,0.0405359,-0.04161957,-0.07471532,0.05915,-0.01378761,0.13296209,-0.02963526,0.01188424,-0.01473984,0.0788838,0.01299189,-0.04599927,-0.13855796,-0.00605905,0.02061358,0.00261686,0.04917988,0.01736248,-0.00835059,0.00484966,0.02930901,-0.03527519,-0.093504,-0.0016807,-0.03917075,-0.02227622,-0.02541305,-0.04916404,-0.02721427,0.02449968,-0.0669158,0.01745638,-0.00332354,-0.00586821,-0.00219669,-0.02812489,-0.02973121,-0.01312704,-0.00101271,0.0036181,0.02628296,0.06526598,-0.01153789,0.0655541,-0.06413323,0.01455542,0.01332172,-0.01676376,0.00199686,0.04240147,0.07865267,-0.02100682,0.03886328,0.03901824,0.017132,-0.01337296,-0.00199955,-0.03391943,0.03335221,0.03937393,-0.0063994,-0.00070126,0.0275983,0.03695391,0.07917713,0.06337404,-0.10545239,0.05609387,0.01615987,-0.02999157,-0.01299468,-0.02777475,0.03544474,0.11804499,-0.00584594,-0.28232539,0.06455661,-0.01674192,-0.0206274,-0.02865518,-0.04501127,0.01336662,-0.02047856,0.01534758,0.06907553,0.02224732,-0.00439036,0.00160712,-0.06833336,0.04190721,0.02568097,0.04003204,-0.00449554,0.07144513,-0.02079859,-0.01709967,-0.02179734,0.20543832,-0.01231565,0.00667015,-0.01133606,-0.08437116,0.02815629,0.03135419,0.03725134,0.05197726,0.03515266,0.04973925,0.02971098,-0.00693902,0.05370062,-0.00190585,0.06741195,-0.03359173,0.03542523,-0.04249801,0.03317392,-0.05337441,0.05747122,0.05696369,-0.02224385,-0.02305397,-0.03497696,-0.00075866,-0.01125642,-0.01053908,-0.00386317,0.03547569,-0.03043171,0.05855053,0.0785216,0.02914662,0.04124785,0.00239122,-0.02002192,-0.00848396,0.02928573,-0.02220243,0.06011651,0.02121346],"last_embed":{"hash":"vzps46","tokens":232}}},"text":null,"length":0,"last_read":{"hash":"vzps46","at":1751243575801},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Technical Development##technical-implementation","lines":[59,69],"size":688,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"vzps46","at":1751243575801}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Technical Development##user-experience": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03568852,-0.02284126,0.03290769,-0.03323549,-0.0682592,0.04163636,-0.05382475,0.07143144,-0.02620455,0.01996373,-0.01921397,-0.04125179,0.01801276,0.06065011,0.06364866,0.00055348,0.03200982,0.00624733,-0.05264971,-0.02480319,0.0197706,-0.0150595,0.01915897,-0.0260278,0.00106823,0.0422679,-0.07512446,-0.08576424,-0.00799834,-0.16920048,-0.04568271,0.00997479,0.02317685,0.00538901,-0.01457269,0.02307335,-0.03772694,0.03015112,0.00119565,0.04301343,0.0212415,0.08391627,-0.04567171,-0.02709776,-0.00748267,-0.06979444,-0.01358537,0.02357998,-0.07296173,0.01216864,-0.02047629,-0.07528652,0.01877536,0.02489875,0.05487356,0.01690585,0.01054782,0.0137729,0.01471669,0.08872911,0.03344958,0.00244676,-0.19866528,0.1006096,0.00548223,0.03651064,-0.06180932,-0.07949055,-0.03638824,0.01627208,-0.04196147,0.03556425,0.02985151,0.04448325,0.06812342,-0.01581962,0.05358573,-0.0282547,-0.00673369,0.0047657,0.05653117,-0.03186672,-0.02148722,-0.03131156,0.00055089,-0.03958614,0.02842592,-0.00877468,0.02445733,0.03223724,0.00961607,-0.06782552,-0.00963608,0.0449674,0.00131645,-0.01223534,0.02226288,0.04289045,-0.05196647,0.1369089,-0.04127579,0.0442718,-0.00226117,-0.03694293,0.01205627,-0.02510379,0.04709292,-0.01664359,-0.02543409,0.04256503,-0.01994741,-0.00127931,0.00349341,0.01507105,-0.00358162,0.0192298,-0.03454436,0.01194826,-0.03604721,-0.00354135,0.00575976,-0.00166505,0.04052844,0.00166105,-0.02031112,0.01887922,0.07584359,0.09505913,-0.05076399,0.08925989,0.05080009,0.04890447,-0.06989401,0.01863521,-0.06702837,0.0049275,-0.03158824,-0.03104836,-0.05110383,0.03645192,-0.05868649,0.02913144,-0.01897492,-0.1483216,-0.08290595,0.16901639,0.04559171,0.04650249,-0.00263564,-0.087727,-0.03197899,0.00152821,-0.01900789,-0.03260741,-0.03106558,0.05411986,0.01083243,-0.01444395,-0.06472208,0.02236645,0.03702369,-0.04662181,-0.05260405,0.11127114,-0.00921461,-0.14351386,-0.007125,-0.02571526,-0.01742082,-0.01678985,-0.03198376,-0.0283808,-0.03125177,0.05965348,0.06740708,-0.02226079,0.00973558,-0.00254063,0.01549225,-0.02934431,0.06113569,-0.01473681,-0.02312759,-0.00069058,0.01071539,-0.06075426,0.03098431,-0.01631968,-0.00431799,-0.02530725,-0.06837351,-0.09636059,0.01115398,0.03044543,-0.00958899,-0.07336521,0.02766151,-0.01171105,0.00852514,-0.06386344,0.09581718,0.02267538,0.01984587,0.03989297,0.00446975,0.09573966,-0.03859732,-0.00115879,0.03899712,0.09652159,-0.0411686,0.01499324,0.07414285,0.00484281,-0.04510216,0.00775558,-0.00773062,0.05192154,-0.00758337,0.06748239,-0.01494368,0.10667033,-0.01414837,-0.25900775,0.03783365,0.01828997,0.00336392,-0.00107639,-0.05972485,-0.01190031,0.01230614,0.00831008,0.04649862,0.11711905,-0.02484473,0.01950282,0.02996992,-0.00504525,-0.00506155,-0.03083744,-0.05142368,-0.02393064,-0.03539385,-0.01730716,0.06373289,0.01583775,-0.09857869,0.02635448,0.00612302,0.14022636,-0.03483044,0.02155992,-0.01664364,0.03423163,0.0471069,-0.01716345,-0.19908278,0.00385117,0.04690211,0.05415488,0.01257632,0.01030686,-0.02709025,-0.01996043,0.03330522,0.02467518,-0.07285828,-0.06741078,-0.058835,0.02640074,-0.08269733,-0.06546981,-0.01644397,0.00123698,-0.02396062,0.04919179,-0.00676145,0.02903499,0.00675158,-0.0125984,0.03391598,-0.06057998,0.04247364,-0.01958569,-0.01585967,0.02398499,-0.00444231,0.07426974,-0.00214771,-0.03315476,-0.02683406,-0.03103753,-0.02060108,0.01140795,0.07688174,-0.01456741,0.00133655,0.00656968,-0.02952752,-0.00006935,0.03491712,0.00443252,-0.02345419,-0.00181991,0.02809102,0.05737238,-0.00807729,-0.02327418,0.01422421,0.05034397,-0.04065331,-0.00877842,-0.00313529,0.01790148,-0.00491011,-0.02540243,-0.0533126,0.11432759,0.04026994,-0.23909207,-0.01563079,0.03205785,-0.0377626,-0.07284547,0.02591725,0.0714384,-0.00462365,-0.00154517,0.07706931,-0.0383217,0.00152093,0.02298773,-0.03959967,0.04391827,0.04897416,0.08706157,-0.0356125,0.03775971,-0.06510483,0.01883904,0.04718209,0.21553268,-0.03423427,0.04674061,0.04350631,-0.034268,-0.00984555,0.03544007,0.02420692,0.04204056,-0.00575466,0.0442776,0.00018591,-0.03369796,0.0352885,-0.00404323,0.02010727,0.00020024,0.04112497,0.02292248,0.0108641,0.00862468,0.02633958,0.07336876,-0.02296657,-0.00743175,-0.01840741,-0.02797485,-0.00857359,-0.02270019,-0.06830646,0.0066621,0.00269464,0.06051789,0.04253151,0.00706062,0.02396386,-0.02964383,0.01790755,0.02611299,-0.00243688,0.03016467,-0.00187985,0.01733891],"last_embed":{"hash":"1da1b2j","tokens":75}}},"text":null,"length":0,"last_read":{"hash":"1da1b2j","at":1751243575929},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Technical Development##user-experience","lines":[70,77],"size":219,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1da1b2j","at":1751243575929}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Business Development": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06633197,-0.01790817,-0.03056522,-0.0490317,-0.02088891,0.04620929,-0.02930768,0.07891519,-0.04455809,0.00028806,-0.01466361,-0.03691693,0.01881857,0.05349736,0.07586749,0.01582785,-0.01050342,0.03210612,-0.0557314,0.0160157,0.01636663,-0.03626007,0.00254658,0.00292766,-0.0059319,0.06248344,-0.00356999,-0.06849879,-0.00866508,-0.14557242,-0.02829339,0.03989109,0.02422462,-0.00527587,-0.01194996,0.09731397,-0.0086872,0.07210215,-0.01500588,0.01345039,0.01619361,0.01116227,-0.0174432,-0.04356754,-0.04580714,-0.07391275,-0.08450715,-0.03434097,-0.07834943,-0.02346347,-0.02260266,-0.0959506,-0.00248903,0.04839478,0.06914026,0.04028666,0.02454993,-0.04248703,-0.00928994,0.10110395,0.0308129,0.03492808,-0.23394489,0.05323874,-0.00946767,0.06843254,-0.04199162,-0.04255258,-0.03900084,0.0082688,-0.00461645,0.02501938,-0.01091951,0.00205503,0.07335313,0.04712305,0.01630085,-0.03739325,-0.00129316,-0.06894006,0.02275511,0.04568981,-0.01874474,0.04201599,-0.02385764,0.02436635,0.03406055,-0.05760168,0.05580396,-0.00441444,-0.00466037,-0.00451021,-0.04633817,0.01391006,-0.01171512,0.02374667,0.02512611,0.0148737,-0.05002587,0.14734742,-0.06861837,-0.03317332,0.06094344,-0.10632288,-0.00072976,0.01672029,-0.02438654,-0.03967646,0.04479837,0.05223822,0.00826216,0.02374811,-0.03665094,-0.06922749,-0.00337971,0.06193739,-0.0250739,0.0477623,-0.05367957,-0.05420511,0.02984613,0.00217563,0.06074864,-0.01582654,-0.06433852,-0.02622574,0.02637855,0.11526974,0.01134109,0.02744119,0.03181098,-0.00172903,-0.02165994,-0.01129999,-0.07742919,0.01202869,-0.00383487,-0.03320099,-0.02779702,0.12915367,-0.00122645,-0.00956008,-0.02185131,-0.130577,-0.05845836,0.17116956,0.04208214,0.08310375,-0.03402939,0.01011205,0.00078283,0.03800989,-0.04003264,0.00788855,0.00372985,-0.01573779,0.04592498,-0.00090819,-0.05716133,0.02309933,0.01195153,-0.09284549,-0.07619114,0.19604686,0.00154081,-0.11476681,-0.02296397,-0.04241574,-0.0023291,-0.01240919,-0.00042616,0.02304793,-0.00428422,0.04065825,0.05963415,-0.01760582,0.00349606,0.00986884,0.00928478,-0.03016515,-0.00588416,-0.02716381,-0.02412253,-0.01946236,-0.00907568,-0.09702925,0.01979633,-0.02694476,0.00693838,-0.04036963,-0.08658109,-0.00752852,-0.0114592,0.04593764,0.00684004,-0.05416258,-0.01304093,-0.03508078,0.07396676,-0.0105804,0.01475101,0.00811122,0.02475081,0.01584355,0.01064381,0.00612604,0.00086278,0.01649335,0.08241832,0.04922171,0.00131787,0.03375638,0.05323311,0.03336513,-0.03149219,0.06490868,-0.00123867,0.04228765,-0.01643005,0.08115552,0.01261781,0.10402983,0.02341513,-0.23032758,0.05333101,0.00638933,-0.02506963,0.02159804,-0.03877217,0.00469808,-0.01235643,-0.04196136,0.11298736,0.16434969,-0.00945422,0.0250477,0.03129572,0.02187384,0.00850946,-0.02701871,-0.04084885,-0.02315477,0.02184867,0.01693363,0.03206201,0.0141124,-0.03362064,0.06231445,-0.02741515,0.13483794,-0.00873365,-0.02838169,0.02526406,0.03101527,0.06727613,-0.06023673,-0.13692273,-0.01187607,-0.01414361,0.01924655,-0.00644318,-0.05940989,-0.00049988,-0.0647797,0.02450801,-0.033196,-0.06055011,-0.02388047,-0.03604342,-0.0390033,-0.01963994,-0.04774502,0.04682443,0.01431283,-0.00834444,0.02608153,-0.04393822,0.00155983,0.01570908,-0.0251394,-0.00735739,-0.04233538,0.02662471,-0.01586517,0.03259799,0.00907066,0.00172945,0.04580272,-0.042429,-0.01642231,0.02684939,-0.04060747,0.00841846,0.05521061,0.05498234,-0.05214918,0.00427615,-0.01453597,-0.01436231,0.0080759,-0.07333139,-0.00563234,-0.02633849,0.00104675,-0.04902378,-0.00993283,0.01148126,0.01119464,0.01862488,0.02393269,-0.00490988,0.05393405,0.00062693,-0.00907715,-0.04881424,-0.01660162,0.01174026,0.09156729,0.01166038,-0.25111446,0.0293481,0.00501462,-0.03171938,-0.05035202,-0.04468211,-0.00339554,-0.03276136,-0.01248134,0.00570232,0.06008567,-0.01813625,0.0265334,-0.04634887,0.00097951,0.02927034,0.04683389,-0.0119284,0.05567012,-0.05354407,0.01412551,-0.01521311,0.18924583,-0.01661998,0.01522112,-0.02269752,-0.06886739,-0.02007774,-0.02024193,0.03982939,0.05767531,0.03710159,0.05170976,-0.00632652,0.01721971,0.0895499,-0.02049519,0.06118564,0.00114044,0.06420368,-0.00825175,0.0236661,0.00349985,-0.00006248,0.10449419,-0.01738039,0.01783439,-0.02819332,-0.01990258,0.01376999,-0.02046119,-0.06192535,0.02038891,0.0015378,0.06189337,0.00925181,0.03500908,0.02713389,-0.01091209,0.00185154,0.0113713,0.00874475,-0.02392785,0.0554327,0.01690815],"last_embed":{"hash":"1na4ouc","tokens":127}}},"text":null,"length":0,"last_read":{"hash":"1na4ouc","at":1751243575977},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Business Development","lines":[78,95],"size":595,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1na4ouc","at":1751243575977}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Business Development##partnership-opportunities": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04526906,-0.01235831,-0.00823376,-0.04336653,-0.02466783,0.05466317,0.00127212,0.06332728,-0.05369189,-0.0137311,-0.03222281,-0.04688278,0.02977233,0.06098479,0.0650303,0.01396548,-0.03308692,0.0450182,-0.06161534,0.01663205,-0.0044416,-0.019282,0.0155969,0.0110945,-0.02358823,0.0818953,0.00505575,-0.08110208,-0.02507679,-0.11335236,-0.03688618,0.06432138,0.00571551,0.00948857,-0.01137798,0.09553967,-0.00438858,0.09239525,-0.0238113,-0.0048127,0.02692002,0.00776871,0.00543647,-0.0514218,-0.03960828,-0.06922251,-0.08360855,-0.04205931,-0.05257584,-0.03933972,-0.01454269,-0.10225715,0.01899464,0.04843582,0.05609464,0.04499928,0.02966122,-0.03378643,0.00799274,0.09718619,0.03715587,0.05117647,-0.21911675,0.02930003,0.01285987,0.04254911,-0.04626399,-0.05202613,-0.01715533,-0.00883865,0.00094444,0.04726192,-0.00572283,0.01252154,0.07214475,0.05444939,0.01458622,-0.05176377,0.01485014,-0.06671827,0.0191754,0.0615348,-0.01290441,0.03797623,-0.01119528,0.02800105,0.00255773,-0.07028443,0.04331554,-0.00718683,-0.03547415,-0.0321738,-0.03814355,0.01758546,-0.00257755,0.02235788,0.00374411,0.02531699,-0.044079,0.16826278,-0.09848842,-0.04054924,0.06512339,-0.08454299,0.0026264,0.02464397,-0.01366138,-0.06079271,0.04555867,0.0535322,0.0362749,0.01110789,-0.03899572,-0.05608236,-0.0010972,0.06313093,-0.0347749,0.05227168,-0.05753519,-0.03274123,0.02328677,0.01137185,0.06563976,-0.01282611,-0.05334185,-0.03673529,-0.00034488,0.08472172,0.02769005,0.02996357,0.03677579,0.02084069,-0.01125372,-0.02149597,-0.07283493,0.02869598,0.0031121,-0.05963019,-0.04325457,0.09722201,0.02658292,-0.03868341,0.00464396,-0.13215011,-0.05903712,0.16834828,0.02182799,0.08871443,-0.05192284,0.02011057,-0.02066508,0.02464271,-0.04059195,0.01353567,0.01390847,-0.0354971,0.04981462,-0.01247635,-0.04222471,0.02160066,0.0163571,-0.0909311,-0.0650059,0.17212979,-0.00082319,-0.10679694,-0.01350708,-0.03184001,-0.00603277,0.00818299,0.0099955,0.03467382,0.00440571,0.0340552,0.05368218,-0.02017928,0.00685507,0.0077147,0.00086379,-0.02789437,0.00887747,-0.02052833,-0.02733412,-0.0167565,-0.00296246,-0.06888577,0.00707078,-0.01750229,0.01623,-0.02966889,-0.0822094,0.00172129,0.01606973,0.03864919,0.01620328,-0.04609444,-0.00786733,-0.03727255,0.03841314,-0.01407457,0.00866993,0.01133872,0.02678443,0.03981771,0.00325171,-0.01694397,-0.00846572,0.00636699,0.06893803,0.05551993,-0.00376568,0.0238351,0.03598791,0.02742918,-0.02879787,0.04646411,-0.0204376,0.05876256,-0.02223457,0.09172947,0.01183344,0.07653393,0.03010608,-0.24259967,0.05792583,0.0048857,-0.03497216,0.02604187,-0.01955147,0.00063694,0.00350544,-0.06838435,0.11428307,0.17664623,-0.01586051,0.01797407,0.04607903,0.00532682,0.03080461,-0.00396696,-0.03849661,-0.01949567,0.01397214,-0.00144972,0.03713768,-0.01066462,-0.01781464,0.07446738,-0.02069184,0.14407088,-0.0006719,-0.03537256,0.02492126,0.01677244,0.05584744,-0.05708845,-0.17130046,-0.01286065,-0.01194395,0.00677717,-0.030657,-0.0499815,0.00814639,-0.06167269,0.02989546,-0.02296905,-0.0523996,-0.00148915,-0.0522503,-0.03885894,-0.03220857,-0.03415749,0.05292087,0.01097975,0.00028278,0.00394739,-0.0371782,-0.01362491,0.04347941,-0.02365202,-0.01546727,-0.03653833,0.03007068,-0.0082336,0.04429673,0.00925201,0.00154868,0.04093985,-0.036988,-0.00735257,0.02280851,-0.01300142,0.01688953,0.05180217,0.06531963,-0.06153668,-0.00207963,-0.00155949,-0.02686597,0.02245465,-0.07170213,0.00773916,-0.03812006,0.0095453,-0.06586184,-0.01074827,0.0042765,0.02772846,0.00742332,0.02438863,-0.00986466,0.05746221,0.01447263,0.01030092,-0.04282936,0.00753273,0.01183977,0.09600792,0.01060339,-0.25467896,0.04753716,0.02075737,-0.04722581,-0.06661154,-0.05548239,0.00122896,-0.04458612,-0.00406697,-0.00264983,0.07278305,-0.05265009,0.0244542,-0.05087955,-0.00064622,0.0241846,0.0574517,-0.00863132,0.04116354,-0.03912773,0.00726459,-0.00581094,0.17274146,-0.01654593,0.0218028,-0.02900663,-0.08063588,-0.01789493,-0.02981027,0.03636978,0.06796843,0.03668492,0.02871756,-0.01513989,0.02439852,0.11324675,-0.02869488,0.04910048,-0.00111684,0.0695655,-0.02348808,0.00409026,-0.0093103,-0.0105464,0.09773251,-0.02961015,0.00453418,-0.02583998,-0.02903202,0.01056602,-0.00935381,-0.04177174,0.01796887,-0.00823119,0.05304182,0.00853148,0.05101512,0.00734901,-0.01521836,0.00531526,-0.00465662,-0.00551406,-0.0260388,0.0410682,0.01077289],"last_embed":{"hash":"313fst","tokens":72}}},"text":null,"length":0,"last_read":{"hash":"313fst","at":1751243576038},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Business Development##partnership-opportunities","lines":[80,87],"size":278,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"313fst","at":1751243576038}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Business Development##monetization-strategy": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05771599,-0.03723095,-0.04174286,-0.0655781,-0.0211727,0.01513135,-0.04578809,0.06665624,-0.01253738,0.0297522,0.01205572,-0.02983092,-0.00736928,0.02529495,0.07015491,0.02285266,0.02957511,0.02056035,-0.0665455,0.01405153,0.05762847,-0.03639887,-0.02324622,-0.02084884,0.01754756,0.03657417,-0.01992783,-0.05572761,0.00294711,-0.15624118,-0.0301315,-0.01145325,0.02942264,-0.02151928,0.004171,0.06057115,-0.02685894,0.05120965,-0.00747877,0.05680627,0.00800545,0.02265834,-0.06628204,-0.02602268,-0.0626975,-0.07451538,-0.06453911,0.01116433,-0.07949436,-0.0120548,-0.02733179,-0.04876949,-0.03116276,0.03048878,0.04813787,0.02314184,0.01366704,-0.03175235,-0.01275642,0.0906832,0.02050808,-0.00673194,-0.22017005,0.07050509,-0.01507053,0.11255246,-0.02616522,-0.05372505,-0.04843454,0.04723457,-0.01527625,-0.00791954,-0.0113956,0.00048401,0.07985393,0.00324094,0.03230909,-0.01310964,-0.02839359,-0.03202372,0.01943881,-0.00186272,-0.01371835,0.04772956,-0.07358456,0.00477831,0.06005922,-0.04164682,0.09142122,0.0092797,0.02235198,-0.01780744,-0.04247913,0.02955234,-0.02875822,-0.01540864,0.03934652,0.00951338,-0.04236716,0.1379748,-0.02667365,-0.00739355,0.03608046,-0.11514759,-0.00650723,-0.01627807,-0.03138928,0.01823066,0.02746653,0.05454848,-0.05330108,0.03608747,-0.0272808,-0.06520328,-0.01667609,0.01497474,-0.01277945,0.04137781,-0.05849748,-0.05029476,0.0327304,-0.00052129,0.04761311,-0.00312053,-0.06618968,0.00634683,0.08260944,0.1368551,-0.01007556,0.0288741,0.02053288,-0.01210472,-0.07369052,0.00089743,-0.09202384,-0.00638717,-0.00812632,0.00936458,-0.02144281,0.11881856,-0.04413596,0.04873203,-0.04935585,-0.12495223,-0.0567077,0.16072659,0.06761876,0.06124945,0.00103947,-0.03429206,0.01312013,0.05082301,-0.00618156,0.00503681,-0.01765562,0.03566395,0.03010001,0.0099001,-0.04619715,0.01585937,0.00119385,-0.05460707,-0.06493215,0.2094143,-0.00764111,-0.10081982,-0.04747056,-0.04705494,0.0065629,-0.04353316,-0.0176022,-0.00015901,-0.01431159,0.04389045,0.06314792,-0.00799763,-0.00164798,-0.01159196,0.0148491,-0.02913519,0.01662238,-0.0293761,-0.02453795,-0.00973828,-0.01410866,-0.1216277,0.0341927,-0.04451264,0.00471755,-0.04271189,-0.09604038,-0.00177179,-0.04847435,0.00745286,-0.0228953,-0.05769352,-0.0183571,-0.02315705,0.10207536,-0.02784628,0.02400115,0.02178111,-0.00432664,0.00671875,0.01211978,0.06354766,0.01138089,0.01164594,0.08232912,0.06090295,-0.02367099,0.05668527,0.07849165,0.0121292,-0.04822455,0.08084776,0.02773331,-0.00806801,-0.0136719,0.0556667,-0.00272604,0.11009519,-0.01148197,-0.21160123,0.02815362,-0.00186598,-0.0075693,0.00919263,-0.04352701,0.00202197,-0.01409335,-0.02371636,0.08451199,0.12264416,-0.00836097,0.02114859,0.03125616,0.03023477,-0.02672363,-0.05324599,-0.06713843,-0.03770613,0.00130224,0.02770436,0.01170614,0.05417454,-0.0497101,0.03793976,-0.02774709,0.12341479,-0.02472632,0.0031786,0.00107338,0.04855451,0.07281543,-0.02944981,-0.1030566,-0.00243422,-0.01073592,0.03231271,0.02626408,-0.04728353,0.00305738,-0.03325164,0.01122874,-0.04559696,-0.05696619,-0.0515619,-0.02261359,-0.01007241,-0.02543862,-0.0737783,0.01518363,0.00307733,-0.01643847,0.0723787,-0.02503598,0.02925652,-0.02024788,-0.02638044,0.007103,-0.05667173,0.01158234,-0.0262262,0.00229581,0.02727581,0.00654692,0.0593629,-0.03909359,-0.03204463,0.01051835,-0.05389249,-0.0178851,0.04647842,0.05375547,-0.02412955,-0.02401017,-0.03460108,0.00098106,-0.02620828,-0.04202684,-0.03755712,-0.00089912,0.00319045,0.00176633,0.00083628,0.0176697,-0.01110158,0.01552248,-0.00155298,-0.01307116,0.04260648,-0.02605109,-0.01821782,-0.05185685,-0.06314921,-0.00315566,0.10498054,0.02673576,-0.23667911,0.01743147,0.00269101,-0.01298568,-0.03029675,-0.01490954,0.01633902,-0.0040018,-0.00317055,0.06693394,0.04103865,0.0421451,0.0380932,-0.03105521,-0.00101647,0.02883118,0.06167413,-0.01733398,0.06338834,-0.049839,0.02476956,-0.02340423,0.23064956,-0.01268827,0.01858821,-0.00705547,-0.01757724,-0.00422809,-0.01112471,0.04678477,0.07500242,0.00706315,0.07228016,0.00202726,0.00571213,0.05246164,0.00288201,0.07247826,-0.00101484,0.05681361,0.0216897,0.02373362,-0.01442501,0.00567531,0.10903196,-0.01083353,0.01511033,-0.04050376,-0.01566203,-0.00133987,-0.03907327,-0.07261836,0.02285523,0.01176901,0.03982319,0.01379946,0.02801638,0.05113103,0.01487213,-0.01189126,0.03204196,0.01016175,0.00627441,0.06102332,0.03886366],"last_embed":{"hash":"unpjuq","tokens":78}}},"text":null,"length":0,"last_read":{"hash":"unpjuq","at":1751243576083},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Business Development##monetization-strategy","lines":[88,95],"size":291,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"unpjuq","at":1751243576083}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Archive Links": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02534156,0.00633215,-0.00145116,-0.04333716,-0.02908207,0.02533104,-0.04731391,0.03788636,-0.04671912,0.01350713,0.01875668,-0.05125933,0.00444012,0.04210229,0.07610517,0.03236403,0.00205207,0.0506523,-0.05233932,-0.00528775,0.04122943,-0.03857043,0.0300242,0.01247481,-0.02230279,0.05560071,-0.03534055,-0.11089387,-0.05620923,-0.16156633,-0.05130125,-0.02731833,0.02452003,0.02014197,0.0107644,0.01074006,-0.03040998,0.0798225,-0.05259822,-0.00343377,0.04299577,0.03903005,-0.04230918,-0.05896524,-0.02566653,-0.05821412,-0.02525005,-0.02600525,-0.01723173,-0.01949377,-0.02925332,-0.03748857,0.03173925,0.04904993,0.04018067,0.07380188,0.04197187,0.00845517,0.01679843,0.07072002,0.02277186,0.00405885,-0.20921041,0.05881396,0.00659657,0.0516358,-0.05386376,-0.08666083,-0.02689345,0.00392236,-0.0255659,0.05282725,-0.00224392,0.02859524,0.0507059,0.00320246,0.01768013,0.00119071,-0.03801957,-0.0575693,0.02133905,0.02056027,-0.03264799,-0.0092678,-0.00209909,-0.00307038,0.0461916,-0.00520003,0.0405816,0.01922737,-0.03246299,-0.05565797,0.04331087,0.05779701,-0.00922291,-0.00293372,0.0351984,0.01745846,-0.0079605,0.16318394,-0.08295501,0.01911732,0.00446988,-0.06792162,0.01018646,0.00464403,0.02771515,-0.06477612,-0.01034517,0.05609287,-0.00181452,0.01327601,-0.01199358,-0.01637588,0.02942317,0.02263432,0.00485396,0.04774947,-0.07209167,-0.0582564,0.02493441,0.01159749,0.08709405,-0.00032917,-0.0210999,0.02765329,0.03676626,0.08528642,0.08571795,0.05560595,0.04924722,0.0683668,-0.10115192,0.00209152,-0.09036785,0.01006757,0.02790289,-0.07711735,-0.02073583,0.02971173,-0.01015085,-0.0084094,-0.01680128,-0.09771226,-0.06684334,0.19538043,0.0139597,0.06538407,-0.00482294,-0.02474729,-0.00881047,-0.00707645,-0.02385736,-0.03413612,0.01967777,-0.02883861,0.00602941,0.00440524,-0.05158926,-0.00686775,0.00371249,-0.06152404,-0.04130224,0.18232909,0.00515774,-0.10834777,-0.03307615,-0.02455826,-0.01347785,0.00583018,0.02864517,0.00365083,0.00220387,-0.00156223,0.07709152,-0.02532005,0.04217979,-0.01306702,0.00204828,-0.03164733,0.01786589,-0.03474553,-0.0258775,-0.00466759,0.04295101,-0.08381853,0.02836891,-0.03020333,0.01677834,0.03927722,-0.07009877,-0.08183248,0.00271098,0.00680579,0.00483647,-0.04012441,0.03323359,-0.00151743,0.03841986,-0.05089525,0.01491523,0.02753132,-0.01293999,0.04940636,-0.02976547,0.0291612,-0.07119071,-0.00485244,0.03090025,0.06448092,-0.04493734,-0.00121063,0.01273612,0.0097427,-0.0721311,0.00202984,-0.01611195,0.01607776,-0.03902648,0.05311311,-0.03000458,0.0391449,0.02734028,-0.23692031,0.03730495,-0.04065484,-0.02521434,0.02053561,-0.05196084,0.00330062,0.00930927,-0.03767527,0.06696191,0.15033492,-0.0237744,0.00356564,0.02461498,0.01526718,0.04750656,-0.01136555,-0.04648907,-0.00815587,-0.00050432,0.0192067,0.00854129,0.05429933,-0.0224296,0.04342669,-0.0328535,0.12847702,0.02383488,0.01240651,-0.00055384,0.02882312,0.0897424,-0.01409038,-0.20085186,0.00003814,0.01708896,0.01202801,0.00997785,0.01243456,0.00298685,-0.06931967,0.03586007,0.01547275,-0.07961733,-0.04138922,-0.04345207,-0.00859623,-0.09160814,-0.03321351,0.05774589,0.01214009,-0.01943842,0.03239728,-0.03191607,0.02527241,0.02176835,-0.01152883,-0.045695,-0.04667456,0.03994328,-0.01289267,-0.01005684,0.03699077,-0.01926507,0.06692858,-0.01175915,-0.04150014,-0.02265612,-0.04896174,-0.03313083,-0.00275356,0.08172692,-0.05402305,-0.03527366,0.00234265,-0.00230416,0.02330974,-0.04804439,0.02417337,-0.05619456,0.00992537,-0.02341213,0.00967389,-0.00196836,0.01314739,0.01151332,-0.00177424,0.00871273,0.07509863,-0.02326852,0.0247467,-0.01919967,-0.00952136,-0.01535649,0.14750992,0.083001,-0.23392595,-0.00382905,0.0835112,-0.03284899,-0.03827884,-0.03044578,0.03767589,-0.04973022,0.01725117,0.0918183,0.03566489,0.00499246,-0.02329514,-0.04245367,-0.00966484,0.028277,0.05849963,-0.00449113,0.02380094,-0.03826715,0.00409945,0.02334216,0.19542536,-0.02642431,0.0087615,-0.00458661,-0.05986909,0.0187501,-0.01298731,0.05443207,0.06440249,0.00217701,0.03360115,0.01356741,-0.02127007,0.10720278,0.0244801,0.04100525,-0.0058994,0.08091971,-0.00513602,-0.01213083,-0.06209235,-0.0055368,0.11215888,0.0157793,-0.01190351,-0.03003037,-0.02168101,0.00275158,-0.04462122,-0.05612623,0.02796524,-0.01600676,0.00401754,0.03256993,0.04330537,0.04687151,-0.00883947,-0.00475433,0.02172359,0.00332365,0.01426807,0.01847807,0.02082943],"last_embed":{"hash":"tjf4fe","tokens":86}}},"text":null,"length":0,"last_read":{"hash":"tjf4fe","at":1751243576132},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Archive Links","lines":[96,101],"size":240,"outlinks":[{"title":"MVP Development History","target":"./archive/mvp-development.md","line":2},{"title":"Performance Reports","target":"./archive/performance-reports.md","line":3},{"title":"Feature Requests","target":"./archive/feature-requests.md","line":4},{"title":"Partnership Discussions","target":"./archive/partnerships.md","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"tjf4fe","at":1751243576132}},
"smart_blocks:Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Team Assignments": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02640739,-0.00894616,-0.04187495,-0.06171671,-0.05546045,0.04941354,0.00217733,0.04533382,-0.01188535,-0.00792645,-0.00243531,-0.06884994,0.01844335,0.05826495,0.07367077,0.02818957,-0.04921775,0.05360566,-0.0462759,-0.05753022,0.04206028,-0.0415265,-0.01936987,-0.00261599,0.00350941,0.06006435,-0.05143701,-0.09286541,-0.02033769,-0.17625391,-0.02370779,0.00829439,0.0599723,0.01044228,0.02338473,0.03343264,-0.05439167,0.03139013,-0.04498209,0.01056157,0.0195219,0.02907173,-0.02862194,-0.06579977,-0.00045561,-0.05828561,-0.03851531,0.01365447,-0.04302501,0.00236259,-0.00754186,-0.07444054,0.01480971,0.03405701,0.04280158,0.0240227,0.02245109,0.01964625,0.05821421,0.10480009,0.02406039,-0.02565233,-0.20734549,0.07166133,0.03755808,0.07966565,-0.0460262,-0.11417999,-0.03095681,0.04542157,-0.01390819,0.04058692,0.04735475,0.0405784,0.08742371,0.05094624,0.03666877,-0.02938916,-0.00634121,-0.06028201,0.03373763,-0.00900161,-0.01453207,0.03311609,-0.06996411,0.02573344,0.00067731,-0.00764589,0.06060857,-0.0104161,-0.01201344,-0.01983757,-0.0120425,0.02238827,-0.01563538,0.00953095,0.0093327,0.03413502,-0.06776166,0.14022599,-0.05053311,-0.04042741,-0.00327448,-0.04545211,0.00250655,0.0292076,-0.02112783,-0.06363429,0.0013787,0.05729505,-0.0338851,0.00042508,-0.02254489,-0.02475308,-0.01086696,-0.02602111,-0.01005138,0.03469064,-0.05597939,-0.02773356,0.07197478,0.03213433,0.06514873,0.0498802,-0.06346176,0.02657637,0.06429901,0.07822943,0.02499816,0.04035309,0.03543303,0.03120918,-0.06176036,-0.00242936,-0.04424977,0.00672152,-0.03722208,-0.03969092,-0.07175395,0.05879325,-0.02283246,0.03120147,0.00741764,-0.09285372,-0.0383624,0.16675697,0.03741682,0.04382567,-0.01305082,-0.0092076,-0.01935133,0.0202016,-0.00488141,-0.01222267,-0.01763406,0.02663947,0.00561841,-0.02485918,-0.07436194,0.00177681,0.00056041,-0.02746741,-0.07398744,0.18569992,-0.01635805,-0.10831907,-0.02539801,-0.02574163,-0.00869986,0.00004182,0.02013355,-0.01386392,-0.01604326,0.0298065,0.0449438,-0.02552891,0.03494686,-0.00610696,-0.00455128,-0.03499441,0.03307763,-0.03083126,-0.03352469,-0.00371602,0.01237057,-0.06459004,0.03808554,-0.05590827,-0.02078174,-0.02989134,-0.08684976,0.03271171,0.02763379,0.00152263,-0.01828988,-0.04204386,0.06081922,-0.01274521,0.02201781,-0.00511083,0.01255358,0.05491789,-0.01189203,0.00370244,-0.00252663,-0.01541677,-0.04727828,-0.00152895,0.05318191,0.09790342,-0.01668787,0.01890549,-0.00094406,-0.00437254,-0.03606513,0.00642266,0.02177656,0.01762532,-0.02982314,0.06685721,-0.0474972,0.09491742,-0.01141916,-0.21682286,0.02924358,-0.00499447,-0.01023271,-0.03300388,-0.03727817,-0.01668799,-0.04372207,-0.00253546,0.05794221,0.17372538,-0.02212211,-0.02492307,0.01044033,-0.00328659,-0.01456964,-0.03952513,-0.02735299,-0.03819741,-0.01424333,0.04229,0.03993385,-0.01518188,-0.09761906,0.01201919,-0.00488141,0.14401564,0.00678519,0.02662794,0.01478486,0.02917258,0.05057094,-0.02859629,-0.15513015,-0.01572935,0.0072862,0.0489408,-0.01819369,0.00540449,0.01681407,-0.01929163,0.0433039,0.0025958,-0.0910627,-0.06844224,-0.05066651,-0.00748886,-0.08342846,-0.02781193,0.00237299,0.01079598,0.00637786,0.02109314,-0.01553508,-0.00760115,0.04775677,-0.01970892,-0.01883535,-0.00655297,0.00580849,-0.02469826,-0.01880495,0.00393466,0.04192497,0.05396477,-0.01379392,-0.00259332,-0.01387727,-0.01979062,-0.04280406,0.04902683,0.06540685,0.00198395,-0.03073416,-0.01722065,-0.02790523,0.01281733,-0.04696284,0.04225897,-0.02490093,0.06156254,-0.01424693,0.03767783,0.00403437,0.07030136,0.03723584,0.05369553,-0.04580611,0.04460263,0.0122314,0.00648903,0.0166864,-0.02498152,0.00458745,0.11636885,0.04000125,-0.25191641,0.03110359,0.02374056,-0.03431165,-0.03427024,-0.055499,0.02784149,-0.032077,-0.02271721,0.06654157,0.00037592,-0.01290585,0.00370889,-0.04398058,-0.00746365,0.03813893,0.09796483,-0.00364011,0.06218809,-0.01462855,-0.03852017,0.03752385,0.22448328,-0.04632267,0.06488459,0.02460282,-0.04274134,-0.02806699,-0.00341539,0.02703028,0.09603529,-0.01238258,0.04664113,0.02619401,-0.03532755,0.09737998,0.02583513,0.04200722,0.004549,0.0611729,0.00917019,-0.00698566,0.00790042,-0.0245413,0.10997814,0.01714886,-0.03882336,-0.07441137,-0.04178394,-0.01985342,-0.03019373,-0.05820748,-0.01178727,-0.04613172,0.03990206,0.03690557,0.05161876,0.04233978,-0.03463412,-0.01225693,0.01793063,0.01820477,0.00365943,0.04025531,-0.00071028],"last_embed":{"hash":"cg217v","tokens":80}}},"text":null,"length":0,"last_read":{"hash":"cg217v","at":1751243576182},"key":"Projects/Vitals/Afyalink/tasks.md#Afyalink - Project Tasks#Team Assignments","lines":[102,106],"size":302,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"cg217v","at":1751243576182}},
