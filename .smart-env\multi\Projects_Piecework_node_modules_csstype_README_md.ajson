
"smart_sources:Projects/Piecework/node_modules/csstype/README.md": {"path":"Projects/Piecework/node_modules/csstype/README.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03968721,-0.03998672,0.02710209,-0.08437041,0.00137091,0.01308902,-0.03303653,-0.01926036,-0.03099072,0.01914443,-0.0121802,-0.03128326,-0.03246749,0.02314687,0.00952119,0.05080018,-0.02144624,0.09098277,-0.02104471,0.01215522,0.04922827,0.02175418,0.00648242,-0.02944377,0.01420595,0.04250409,0.0493506,-0.01885979,0.00092494,-0.21759926,-0.0402809,0.04391181,-0.02008538,0.00893657,0.02657717,-0.05186568,-0.02339457,0.02053728,-0.03402599,0.05352403,0.00712402,0.01551825,0.01801345,-0.03348977,-0.02800818,-0.01001152,-0.00866622,0.01057964,-0.02753838,-0.07249293,-0.04408732,-0.03961395,0.04556382,-0.01684267,0.00701782,0.09656867,0.08259088,0.03973715,0.04176564,0.03682998,0.01937838,0.01282318,-0.17322755,0.06874286,0.05667945,0.03147963,-0.05631666,-0.00721106,0.03555632,-0.01686703,0.0081964,0.02336842,-0.01918447,0.09970228,-0.00466431,-0.04282753,0.01244962,-0.05047989,0.01792561,-0.00480753,-0.04082136,-0.02064194,-0.0017605,0.0016193,0.02456368,0.02549666,0.00136122,0.01510791,0.03181818,0.00527271,0.00255619,-0.10726467,0.03509422,0.08145621,0.01090262,0.00486555,0.04021563,0.0106993,-0.00402196,0.11950441,-0.00722661,-0.00018312,0.00284828,0.03420018,0.04429501,0.02263944,-0.02825523,-0.05444044,-0.01878052,-0.05556924,-0.02531851,-0.02098374,-0.02090186,-0.08094489,-0.05486695,-0.00634716,-0.00563513,0.00268233,0.02330649,0.03369099,0.01071179,0.06285851,0.01596416,-0.0367782,-0.01639477,0.02084805,0.03465937,0.07603394,0.02256349,0.08188667,-0.00070051,0.09665804,-0.06436157,-0.01899846,0.01791223,0.00685647,-0.0324334,0.01069748,-0.00254874,-0.02176839,-0.00557186,-0.01424067,-0.02360783,-0.04806766,-0.02771058,0.0715009,-0.10435818,0.08662605,-0.02552458,0.00634012,-0.01892587,0.04787725,-0.07699692,-0.01293688,-0.02010557,0.01823466,0.03072487,0.00619468,-0.06735485,0.00844233,0.00059658,-0.01162736,-0.06275845,0.06715944,-0.00305318,-0.08512534,-0.03852042,0.01358408,0.00076178,-0.07136235,-0.02555796,0.00017211,-0.036815,0.00625178,0.0490183,-0.0048074,-0.06760437,-0.05258004,0.03971142,0.048352,0.03580927,0.00682884,-0.06390996,0.0524949,0.02744688,-0.02859847,0.03676014,-0.06073028,0.03137869,0.00789897,0.01537823,-0.04997619,0.03867336,-0.00260916,-0.02915775,0.02411867,-0.06470752,-0.04621951,0.03590837,-0.04961685,0.11435198,0.04039259,0.02647331,0.00314689,-0.05150719,0.02968728,-0.03235931,0.00039561,0.03031404,0.01915149,-0.08290531,0.02010622,0.03550074,0.05584067,0.03440527,-0.02336008,0.07064771,0.06734134,0.02082725,0.06564821,-0.03187365,0.00397825,-0.13980895,-0.21357077,-0.0362356,0.06244204,-0.05766667,0.00577059,-0.00237599,-0.00592304,0.0257851,-0.06620229,0.08690175,0.1107303,0.04573235,-0.04869692,-0.04790097,0.00077274,0.00210084,-0.01076889,-0.06392943,-0.06983262,-0.00167562,0.01354486,0.00167339,-0.11851704,-0.09207325,0.05131643,-0.03772879,0.14610344,-0.00208321,0.0377711,-0.01477602,0.03408127,0.00119752,0.02389641,-0.08172333,0.01742995,0.04148406,-0.03794344,0.02195126,0.01884811,-0.00521174,-0.0258125,0.02879863,0.02574702,-0.009475,0.0511223,-0.07304485,0.00280404,-0.00399622,-0.04290634,0.06349245,-0.03123677,0.03160298,0.00519321,0.11775433,-0.03000997,0.03949307,-0.05807858,-0.0516895,0.03389803,0.01126855,0.0233094,-0.03593687,0.0637072,-0.06903596,0.03058163,0.03966067,0.02650382,-0.05722764,0.04693177,-0.0078086,-0.0445765,0.1403444,0.01376028,-0.01316469,-0.03559706,0.02611084,-0.06795543,0.04965123,0.01969116,0.01949522,0.01865548,0.01694637,0.04576128,0.0339289,-0.03932273,-0.014046,-0.00752961,-0.06439151,0.0638559,-0.05284954,-0.03506423,0.01520736,-0.0400868,0.00587909,0.09532606,0.02137519,-0.23637053,0.00382194,0.05070705,-0.01452649,-0.03597635,-0.0035053,0.01801843,-0.09451034,-0.0361104,-0.0193626,0.00079663,0.02213605,0.02209928,-0.05312629,-0.00866674,-0.018175,0.07262054,-0.02749209,0.12068014,-0.07729022,0.04770183,0.01859523,0.25928074,-0.04776243,0.03107963,0.06618023,-0.01865475,0.0398042,0.08476162,0.07905056,0.02818014,0.02763341,0.12544176,-0.00073297,-0.00789213,0.02107289,0.00993242,-0.00576197,0.04370295,-0.00455006,-0.00714494,-0.00321482,-0.05151592,-0.00896005,0.02971164,-0.05887808,-0.05323875,-0.04561558,0.01861726,0.00397905,-0.03943114,0.03195239,-0.01807475,-0.02339102,0.0121459,-0.00267413,-0.03584263,0.01683458,-0.04518202,0.0075201,0.00744214,-0.08129715,0.00868475,0.05810255,-0.01976042],"last_embed":{"hash":"1qsj2qy","tokens":429}}},"last_read":{"hash":"1qsj2qy","at":1751288786235},"class_name":"SmartSource","last_import":{"mtime":1751244532677,"size":10518,"at":1751288765474,"hash":"1qsj2qy"},"blocks":{"#CSSType":[1,278],"#CSSType#{1}":[3,31],"#CSSType#Getting started":[32,37],"#CSSType#Getting started#{1}":[34,37],"#CSSType#Table of content":[38,48],"#CSSType#Table of content#{1}":[40,40],"#CSSType#Table of content#{2}":[41,41],"#CSSType#Table of content#{3}":[42,42],"#CSSType#Table of content#{4}":[43,43],"#CSSType#Table of content#{5}":[44,44],"#CSSType#Table of content#{6}":[45,45],"#CSSType#Table of content#{7}":[46,46],"#CSSType#Table of content#{8}":[47,48],"#CSSType#Style types":[49,74],"#CSSType#Style types#{1}":[51,62],"#CSSType#Style types#{2}":[63,63],"#CSSType#Style types#{3}":[64,64],"#CSSType#Style types#{4}":[65,65],"#CSSType#Style types#{5}":[66,66],"#CSSType#Style types#{6}":[67,68],"#CSSType#Style types#{7}":[69,70],"#CSSType#Style types#{8}":[71,71],"#CSSType#Style types#{9}":[72,72],"#CSSType#Style types#{10}":[73,74],"#CSSType#At-rule types":[75,87],"#CSSType#At-rule types#{1}":[77,87],"#CSSType#Pseudo types":[88,103],"#CSSType#Pseudo types#{1}":[90,91],"#CSSType#Pseudo types#{2}":[92,93],"#CSSType#Pseudo types#{3}":[94,103],"#CSSType#Generics":[104,120],"#CSSType#Generics#{1}":[106,107],"#CSSType#Generics#{2}":[108,113],"#CSSType#Generics#{3}":[109,113],"#CSSType#Generics#{4}":[114,120],"#CSSType#Generics#{5}":[115,120],"#CSSType#Usage":[121,184],"#CSSType#Usage#{1}":[123,184],"#CSSType#What should I do when I get type errors?":[185,250],"#CSSType#What should I do when I get type errors?#{1}":[187,190],"#CSSType#What should I do when I get type errors?#{2}":[191,195],"#CSSType#What should I do when I get type errors?#{3}":[196,196],"#CSSType#What should I do when I get type errors?#{4}":[197,250],"#CSSType#What should I do when I get type errors?#{5}":[201,250],"#CSSType#Version 3.0":[251,267],"#CSSType#Version 3.0#{1}":[253,253],"#CSSType#Version 3.0#{2}":[254,255],"#CSSType#Version 3.0#{3}":[256,256],"#CSSType#Version 3.0#{4}":[257,258],"#CSSType#Version 3.0#{5}":[259,259],"#CSSType#Version 3.0#{6}":[260,260],"#CSSType#Version 3.0#{7}":[261,261],"#CSSType#Version 3.0#{8}":[262,262],"#CSSType#Version 3.0#{9}":[263,263],"#CSSType#Version 3.0#{10}":[264,264],"#CSSType#Version 3.0#{11}":[265,265],"#CSSType#Version 3.0#{12}":[266,267],"#CSSType#Contributing":[268,278],"#CSSType#Contributing#{1}":[270,271],"#CSSType#Contributing#Commands":[272,278],"#CSSType#Contributing#Commands#{1}":[274,274],"#CSSType#Contributing#Commands#{2}":[275,275],"#CSSType#Contributing#Commands#{3}":[276,276],"#CSSType#Contributing#Commands#{4}":[277,278]},"outlinks":[{"title":"![npm","target":"https://img.shields.io/npm/v/csstype.svg","line":3},{"title":"data from MDN","target":"https://github.com/mdn/data","line":5},{"title":"Style types","target":"#style-types","line":40},{"title":"At-rule types","target":"#at-rule-types","line":41},{"title":"Pseudo types","target":"#pseudo-types","line":42},{"title":"Generics","target":"#generics","line":43},{"title":"Usage","target":"#usage","line":44},{"title":"What should I do when I get type errors?","target":"#what-should-i-do-when-i-get-type-errors","line":45},{"title":"Version 3.0","target":"#version-30","line":46},{"title":"Contributing","target":"#contributing","line":47},{"title":"length where the unit identifier is optional","target":"https://drafts.csswg.org/css-values-3/#lengths","line":108},{"title":"type widening","target":"https://blog.mariusschulz.com/2017/02/04/TypeScript-2-1-literal-type-widening","line":194},{"title":"issues","target":"https://github.com/frenic/csstype/issues","line":196},{"title":"related issue","target":"https://github.com/microsoft/TypeScript/issues/29729","line":262},{"title":"\"Generics\"","target":"#generics","line":264}],"last_embed":{"hash":"1qsj2qy","at":1751288782215}},"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04281273,-0.03268984,0.02583474,-0.08141675,-0.00439535,0.01407778,-0.03845797,-0.02074399,-0.02499388,0.01744533,-0.01532815,-0.03817286,-0.02831093,0.02204602,0.00052394,0.04640831,-0.02096082,0.09994704,-0.02245021,0.01290922,0.05149734,0.01516435,0.01171072,-0.02240594,0.01407909,0.04008684,0.04583548,-0.0139015,-0.00486844,-0.22287136,-0.03620679,0.04038698,-0.02512361,0.01502708,0.02768379,-0.04587843,-0.02093619,0.02188968,-0.02849622,0.05385089,0.00572826,0.01528084,0.02428542,-0.03493883,-0.02231643,-0.0162314,-0.01055084,0.01649516,-0.03316311,-0.07016232,-0.04859377,-0.03978396,0.04717381,-0.01916585,0.01228395,0.10009492,0.0819758,0.04253037,0.03169349,0.04448801,0.01879746,0.01325265,-0.17868845,0.0686135,0.05883275,0.02582136,-0.055563,-0.01238852,0.03678272,-0.02081299,0.00602792,0.01951972,-0.01479332,0.09764159,-0.00418456,-0.04913678,0.00976006,-0.05431612,0.02225542,-0.01181434,-0.03363124,-0.02009449,-0.00303894,0.0024455,0.02922299,0.02022508,0.00016357,0.02225946,0.03170156,-0.00426593,-0.0009734,-0.11624963,0.03572512,0.07798006,0.00994575,0.00584528,0.03968073,0.01609038,-0.00953785,0.1160249,-0.01004159,-0.0036165,-0.003045,0.0395139,0.04336353,0.02086233,-0.02569789,-0.05813639,-0.01693365,-0.05594299,-0.02532763,-0.02393291,-0.00682265,-0.07932261,-0.05557476,-0.00965026,-0.00491022,0.00365352,0.01933361,0.03100451,0.01133772,0.07012887,0.01830345,-0.03353219,-0.01421831,0.02617036,0.03512199,0.07505786,0.02665041,0.08150843,0.00100219,0.09164413,-0.05528237,-0.01965543,0.011789,0.00933734,-0.0353304,0.01495308,0.00287274,-0.02691278,-0.01162914,-0.0209177,-0.0286994,-0.04397561,-0.03523293,0.07595283,-0.09920233,0.08090473,-0.02002173,0.00979112,-0.02367333,0.04961953,-0.06949931,-0.01659645,-0.01951971,0.01677384,0.02937438,0.00619678,-0.06684127,0.01281132,0.0081158,-0.01371599,-0.06841446,0.0644045,-0.00993081,-0.0755749,-0.03047344,0.01726502,-0.00433269,-0.06175549,-0.03325098,0.00656928,-0.02586957,0.0084759,0.05068092,-0.00138584,-0.06768831,-0.05889361,0.03435036,0.04119189,0.03322343,0.00786044,-0.07489067,0.0440252,0.03039565,-0.02989012,0.03102979,-0.05723277,0.03585857,0.01156767,-0.00120085,-0.05224599,0.04169338,-0.00614329,-0.0246124,0.01974293,-0.06817652,-0.0430074,0.03167689,-0.04962319,0.12057846,0.04763164,0.03041709,0.00568228,-0.0560369,0.02924977,-0.03264254,-0.00779557,0.02745938,0.00712615,-0.07909378,0.03190045,0.03432915,0.05870604,0.04158977,-0.02783654,0.07183433,0.0724832,0.01809054,0.05385438,-0.03338856,-0.00431738,-0.13944627,-0.21739352,-0.0374289,0.05849604,-0.06977595,0.00149265,-0.00045956,-0.00950308,0.02278812,-0.05951348,0.08468987,0.11468506,0.04489523,-0.05456305,-0.04634513,0.00061758,0.00574321,-0.00719113,-0.06557429,-0.06245612,-0.00117781,0.01822175,0.00101192,-0.11670632,-0.09337401,0.04700297,-0.03210669,0.14413787,-0.00147471,0.04141199,-0.01195053,0.03355182,0.00366969,0.03138364,-0.07723328,0.01613826,0.04591819,-0.03530831,0.02328433,0.0162892,-0.01092682,-0.02604595,0.02782761,0.0291507,-0.00798785,0.05676285,-0.07121919,0.00009772,-0.00426663,-0.04676885,0.05968228,-0.01875381,0.03222513,-0.00208364,0.11869662,-0.03675312,0.04440093,-0.05107763,-0.04881174,0.02934494,0.01227572,0.02233901,-0.03291346,0.06091237,-0.06019108,0.02744856,0.03300872,0.02617414,-0.05990404,0.04728515,-0.00942517,-0.05287181,0.13430706,0.01764416,-0.0021289,-0.03298383,0.02552806,-0.07300327,0.03892418,0.01855887,0.02280645,0.02673915,0.01504066,0.04625242,0.04077502,-0.03749856,-0.00990122,-0.00868931,-0.06972006,0.06455808,-0.05576369,-0.03073017,0.01538046,-0.04379914,0.00762429,0.09401264,0.01655567,-0.24073395,0.00292327,0.0501526,-0.01248808,-0.04088711,-0.00754058,0.02364387,-0.09255956,-0.03884495,-0.02333267,-0.00666534,0.02383812,0.02057056,-0.04816738,-0.0112226,-0.01922983,0.07023582,-0.0245148,0.11723828,-0.074536,0.04959239,0.02253114,0.25563145,-0.04834639,0.02918023,0.06772117,-0.01433924,0.04329845,0.0841656,0.07778659,0.02664331,0.03644089,0.13243695,0.00250231,-0.00973518,0.01938149,-0.00042173,-0.00476481,0.04403907,-0.00134279,-0.00995057,0.00180336,-0.04237167,-0.01083193,0.03697499,-0.05709184,-0.04897333,-0.03529881,0.02265792,0.00779997,-0.03188905,0.02590267,-0.0142111,-0.02412072,0.00889569,0.00072093,-0.02531103,0.01408162,-0.04794785,0.00954752,0.01088118,-0.08049128,0.00955941,0.05948684,-0.02054226],"last_embed":{"hash":"1qsj2qy","tokens":498}}},"text":null,"length":0,"last_read":{"hash":"1qsj2qy","at":1751288782566},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType","lines":[1,278],"size":10518,"outlinks":[{"title":"![npm","target":"https://img.shields.io/npm/v/csstype.svg","line":3},{"title":"data from MDN","target":"https://github.com/mdn/data","line":5},{"title":"Style types","target":"#style-types","line":40},{"title":"At-rule types","target":"#at-rule-types","line":41},{"title":"Pseudo types","target":"#pseudo-types","line":42},{"title":"Generics","target":"#generics","line":43},{"title":"Usage","target":"#usage","line":44},{"title":"What should I do when I get type errors?","target":"#what-should-i-do-when-i-get-type-errors","line":45},{"title":"Version 3.0","target":"#version-30","line":46},{"title":"Contributing","target":"#contributing","line":47},{"title":"length where the unit identifier is optional","target":"https://drafts.csswg.org/css-values-3/#lengths","line":108},{"title":"type widening","target":"https://blog.mariusschulz.com/2017/02/04/TypeScript-2-1-literal-type-widening","line":194},{"title":"issues","target":"https://github.com/frenic/csstype/issues","line":196},{"title":"related issue","target":"https://github.com/microsoft/TypeScript/issues/29729","line":262},{"title":"\"Generics\"","target":"#generics","line":264}],"class_name":"SmartBlock","last_embed":{"hash":"1qsj2qy","at":1751288782566}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03442243,-0.03977118,0.02981115,-0.08341371,0.00445603,0.01634109,-0.02964232,-0.0148116,-0.02632819,0.02470546,-0.0231507,-0.04334772,-0.03021692,0.02961192,0.00424854,0.06034781,-0.03104059,0.10247255,-0.03581966,0.00843384,0.05517317,0.01222326,-0.00094118,-0.04258737,0.0254952,0.04588368,0.04809443,-0.01788866,0.00418498,-0.21999426,-0.05111892,0.03270742,-0.02239223,0.00414455,0.03970649,-0.05201358,-0.02105301,0.02713671,-0.03273217,0.05412582,0.01381627,0.01392103,0.01010293,-0.02512024,-0.03100347,-0.00648255,-0.00944023,0.00615334,-0.02994724,-0.07403034,-0.03675573,-0.04602544,0.03650241,-0.01798169,0.00672274,0.10134698,0.0807382,0.03233213,0.04052503,0.03732928,0.02453108,0.00425079,-0.17474759,0.06809369,0.0652993,0.02299888,-0.04934726,-0.01201415,0.02691911,-0.01604544,0.00513449,0.01027352,-0.00325064,0.08844548,0.00354612,-0.04793884,0.00519224,-0.05642395,0.01845452,-0.00031285,-0.03038049,-0.01236163,-0.0043338,0.00632653,0.03235727,0.02802843,-0.00141041,0.00775187,0.03086586,0.00740268,-0.00047976,-0.11586197,0.02795845,0.08345982,0.0016147,0.00405942,0.03469626,0.01317756,0.00431998,0.12931468,-0.0016437,0.00766467,0.00026734,0.03440418,0.04139812,0.0262256,-0.03372132,-0.04877194,-0.01724361,-0.05933205,-0.01863055,-0.0171861,-0.01934784,-0.07823256,-0.05852036,-0.00033589,-0.00075186,0.00116023,0.00038442,0.04102847,0.01667871,0.06749494,0.01810787,-0.04206605,-0.00485062,0.01947975,0.03554918,0.07959388,0.01831731,0.0822428,-0.00121412,0.07705192,-0.04903788,-0.0225774,0.02422546,0.00892687,-0.03297152,0.0151565,-0.00208745,-0.02108165,-0.01181393,-0.01066038,-0.04250168,-0.04245057,-0.02108269,0.05527274,-0.0945995,0.07998011,-0.01329491,0.00490082,-0.02088599,0.04492012,-0.0686656,-0.00623547,-0.02318319,0.0191623,0.01423242,0.02295434,-0.06631196,0.00965083,0.0055745,-0.00918801,-0.0644979,0.08804911,-0.00868277,-0.08301426,-0.02476739,0.01946052,0.01052679,-0.06206398,-0.03609691,-0.00122166,-0.03597945,0.00226407,0.05434541,-0.01085648,-0.0643383,-0.0459444,0.03341267,0.04944957,0.04033802,0.0019967,-0.0590596,0.05151551,0.03089657,-0.03072326,0.03700432,-0.0629837,0.02792915,0.00368673,0.01930207,-0.03388169,0.04039124,-0.00211867,-0.03617784,0.01942397,-0.05561434,-0.05241553,0.02705908,-0.04200655,0.13176256,0.03663894,0.02837695,0.00642275,-0.0493407,0.03431959,-0.03325711,0.00812297,0.00885784,0.03192985,-0.0826839,0.00840529,0.04065369,0.06377491,0.0384358,-0.01512272,0.06535304,0.06550328,0.01374972,0.05576371,-0.0393369,0.00141114,-0.14784952,-0.21699473,-0.0312547,0.05658937,-0.05838075,0.01084126,-0.00063895,-0.01080605,0.02801362,-0.05199008,0.08278744,0.12224495,0.06710703,-0.04879519,-0.0456562,-0.001491,-0.00056705,-0.0074517,-0.06727164,-0.06059804,-0.00869295,0.01174115,-0.00089546,-0.13347794,-0.08236343,0.05113605,-0.03591152,0.15766765,-0.00022727,0.03599503,-0.0135107,0.02952628,0.00794663,0.02250473,-0.06386496,0.00110302,0.04380062,-0.05205332,0.0258492,0.00537442,-0.0091466,-0.02636237,0.0301611,0.02763174,-0.01802874,0.06937893,-0.0753808,0.01450091,-0.00021331,-0.03759176,0.06430241,-0.03439389,0.0331688,0.00842328,0.11898027,-0.02153558,0.04673772,-0.04955442,-0.04805435,0.02304295,0.02219227,0.01505082,-0.03517314,0.06446845,-0.06431529,0.03678905,0.05531606,0.0366317,-0.06565715,0.04890618,-0.00515179,-0.03826657,0.13361917,0.01177834,-0.01504464,-0.02962285,0.03390483,-0.06825422,0.05119883,0.02109896,0.02457452,0.03195376,0.02213628,0.0487606,0.03090935,-0.03688468,-0.01472458,-0.00962937,-0.06354483,0.05737915,-0.05540136,-0.03463545,0.00750147,-0.04146517,0.01035636,0.08678129,0.02139165,-0.23518212,-0.00407262,0.03743173,-0.02684919,-0.04529186,0.00251703,0.01864028,-0.08585643,-0.05002648,-0.01802845,-0.0145385,0.01791673,0.02761003,-0.04427538,-0.00851355,-0.02125536,0.07020341,-0.02137993,0.12328586,-0.06032246,0.03417531,0.00998169,0.25720856,-0.04730904,0.02841666,0.07472701,-0.02593218,0.03867373,0.07539357,0.07156677,0.01853421,0.02652183,0.10813078,-0.00950033,-0.00800763,0.01744341,-0.00128915,-0.0062489,0.05387798,-0.00709069,-0.00740638,-0.01403678,-0.03889486,-0.01481674,0.01814786,-0.05920802,-0.05350094,-0.03905643,0.02707835,-0.00881084,-0.04102252,0.02763236,-0.01303566,-0.03432046,0.02190898,0.01061183,-0.0358633,0.01002167,-0.04579476,-0.00255791,0.00047617,-0.09049387,0.00475961,0.04432126,-0.03744623],"last_embed":{"hash":"sksbad","tokens":266}}},"text":null,"length":0,"last_read":{"hash":"sksbad","at":1751288782929},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#{1}","lines":[3,31],"size":705,"outlinks":[{"title":"![npm","target":"https://img.shields.io/npm/v/csstype.svg","line":1},{"title":"data from MDN","target":"https://github.com/mdn/data","line":3}],"class_name":"SmartBlock","last_embed":{"hash":"sksbad","at":1751288782929}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Table of content": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04848137,-0.03365292,0.06002111,-0.08436712,0.00949023,-0.02603299,-0.02436022,-0.02619295,-0.02934745,0.01820637,0.04635347,-0.02496628,-0.01132046,0.0155701,0.01102031,0.02484524,0.00089002,0.07021736,0.01436293,0.01920923,0.06078671,0.03613352,0.02779439,0.00311205,0.00300883,0.02719433,-0.00749459,-0.05079525,0.00571339,-0.21736518,-0.0122237,0.05757251,-0.04486189,0.00157706,0.03395574,-0.07599737,0.01199778,-0.01563905,-0.02974088,0.06764659,0.03948239,0.06735,-0.00798569,-0.03849233,-0.01410803,0.00174356,-0.01097602,0.03165226,0.03389763,-0.06236562,-0.05233696,-0.02417884,0.04960417,-0.01161696,0.00374252,0.07763437,0.10315206,0.02940788,0.03588942,0.05116295,-0.00581277,0.02750498,-0.17284593,0.04566348,0.00992179,0.03491624,-0.06307743,-0.00426847,0.01454101,0.03681647,0.00705649,0.00740538,-0.03486931,0.1089131,0.05813105,-0.01646801,0.02687792,-0.03352565,-0.00644654,-0.00986216,-0.07847758,-0.01787102,0.01967625,0.00429039,0.0221864,-0.00595248,0.02787312,0.04595439,0.06653234,0.00569506,-0.01909184,-0.06461129,0.06217047,0.06832394,-0.0024533,-0.00979934,0.04964967,0.00202221,-0.0289194,0.13624026,-0.0415181,-0.00595205,0.01718249,0.05789809,0.00945141,0.01460529,0.00198015,-0.05454797,-0.01878173,-0.01708872,-0.00409136,-0.01711283,-0.05132837,-0.08862898,-0.02887393,0.00109648,0.01250683,0.02232456,0.00576069,0.01430656,-0.00002119,0.05097572,0.04038947,0.01023362,-0.00410006,-0.00897671,0.02571425,0.05787162,-0.0071542,0.0611654,0.02192465,0.07044124,-0.09258886,-0.00510676,0.00963249,-0.02575388,0.00186273,-0.00930214,0.01102342,-0.04526209,0.02710747,-0.08869895,0.02358648,-0.05918234,-0.05497317,0.11363375,-0.06501242,0.08305699,-0.01368309,-0.0209011,-0.03326536,0.03528894,-0.04599214,0.01716302,0.00569278,-0.00380835,0.03939906,-0.02933798,-0.03253863,0.03007784,-0.01732772,-0.01192699,-0.07881939,0.08137829,0.01415663,-0.03536211,-0.06446411,0.00204922,0.01888009,-0.06486077,-0.0116043,-0.01587595,-0.03847719,0.00120845,0.06747076,0.0365148,-0.03944718,-0.05808579,0.0435388,0.03990033,0.05642849,0.02591358,-0.05605608,0.06455786,0.03400826,-0.03204707,0.04030433,-0.03230633,0.03761913,0.02479764,-0.0558597,-0.03505933,-0.0228352,-0.02157136,-0.05303598,-0.025793,-0.05702164,-0.02571911,0.03951401,-0.05718273,0.0942114,0.0407673,-0.00793262,0.01275322,-0.03647682,0.00571129,-0.05312955,-0.03618253,0.03239397,-0.00013013,-0.07739389,0.03044209,0.05128987,0.07057922,-0.01644626,-0.03157907,0.06164825,0.05275131,0.00838296,0.05107089,-0.03483045,0.00438155,-0.10225654,-0.22265221,-0.02487748,0.05373393,-0.06070118,0.00169993,0.02397624,-0.0040833,-0.00610828,-0.08533093,0.09819923,0.10633124,0.01007069,-0.04182583,-0.05440461,-0.01812145,-0.01682865,-0.03414882,-0.0983386,-0.06402194,0.00209385,0.02971601,-0.00879425,-0.06329986,-0.08497323,0.07982877,-0.03423618,0.12195857,0.04123279,0.03626658,-0.00376074,0.04535565,-0.01935454,0.01660138,-0.13591728,0.01723192,0.03853352,0.01137543,-0.02001247,0.03264507,-0.02340694,-0.01089623,-0.01031549,0.00923024,-0.01107941,0.03131942,-0.02750936,-0.02807619,-0.01774534,-0.03642933,0.07747255,-0.02052756,0.04132705,-0.01898661,0.10354918,-0.02634666,0.02185366,-0.09814214,-0.02031098,0.02172457,-0.01365469,0.02083173,-0.02132609,0.01808023,-0.07623401,0.03358118,0.0153843,0.00152067,-0.03848515,0.02650318,-0.01005509,0.00506413,0.11325323,-0.00274148,-0.03708516,-0.0307485,0.00409802,-0.05009601,0.00754207,0.04058177,-0.00489218,0.0057571,-0.01104155,0.05385312,0.02479287,-0.02963143,0.00689754,-0.01265026,-0.04628532,0.07511225,-0.10372652,-0.03388876,0.01404088,-0.03946833,-0.00131907,0.12353951,0.02335586,-0.23794235,0.03659775,0.07459206,-0.01763267,-0.02220967,0.00653974,0.01271796,-0.10578673,-0.03502228,-0.00677167,0.03535193,0.05717458,0.03018647,-0.0485725,-0.0120354,0.01747819,0.07603794,-0.04310306,0.0997463,-0.09681372,0.04007713,0.04806609,0.24233052,-0.02818567,0.04157517,0.0197122,0.0028751,0.01944472,0.06521522,0.09345972,0.01583585,0.00347775,0.12795179,-0.00548204,0.00472426,0.01439675,0.01774151,-0.03226207,0.00469905,0.00952266,-0.02227167,0.02283959,-0.06092563,-0.00123935,0.07343176,-0.03512156,-0.0650481,-0.05194984,0.01776187,-0.0042487,-0.05222386,0.00627972,-0.01048784,-0.01214621,-0.02409651,0.00209244,-0.01529755,0.01945663,-0.05864934,0.03293382,0.01552955,-0.02446266,0.03620801,0.05744585,0.00511226],"last_embed":{"hash":"1v4v99j","tokens":135}}},"text":null,"length":0,"last_read":{"hash":"1v4v99j","at":1751288783218},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Table of content","lines":[38,48],"size":307,"outlinks":[{"title":"Style types","target":"#style-types","line":3},{"title":"At-rule types","target":"#at-rule-types","line":4},{"title":"Pseudo types","target":"#pseudo-types","line":5},{"title":"Generics","target":"#generics","line":6},{"title":"Usage","target":"#usage","line":7},{"title":"What should I do when I get type errors?","target":"#what-should-i-do-when-i-get-type-errors","line":8},{"title":"Version 3.0","target":"#version-30","line":9},{"title":"Contributing","target":"#contributing","line":10}],"class_name":"SmartBlock","last_embed":{"hash":"1v4v99j","at":1751288783218}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Style types": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00553805,-0.00441695,0.02599422,-0.03560231,0.00631266,0.00504126,-0.05209614,-0.04109993,-0.02535247,0.00085243,-0.01137587,0.00124632,-0.00048868,0.0286989,0.03640146,-0.01198069,0.0442186,0.09593411,0.01426265,0.02477476,0.05453696,0.02352895,0.03625995,-0.01567884,0.01740945,0.01317463,0.02023382,0.00319355,-0.01753248,-0.21294789,0.01468977,0.07183041,-0.03126089,0.02080748,0.03943877,-0.05856757,-0.04054238,0.02395814,-0.08135122,0.06058785,0.02065161,0.01753323,-0.0088507,-0.03508009,-0.01736701,-0.03827763,-0.00589375,0.02416389,-0.02361562,-0.06047938,-0.02813182,-0.03629,0.03447973,0.01095907,0.00564716,0.06640928,0.08053915,0.03878193,0.03780825,0.02574945,0.01246539,0.02224187,-0.18768246,0.02164035,0.05589417,0.0393042,-0.07467139,-0.04154802,0.04432366,0.02303912,0.03122398,0.02209945,-0.02677943,0.0648,-0.00429089,-0.03511151,-0.0072287,-0.04407724,-0.03079947,-0.0035167,-0.02964112,-0.02021645,-0.01931681,-0.00705751,0.00097508,-0.01219684,0.0306872,0.00127459,0.05973443,-0.00695235,-0.0169122,-0.09097952,0.06690997,0.06129694,0.02278798,-0.02130559,0.08313301,0.01852272,0.00594237,0.1247167,-0.05727961,-0.01036486,0.02941756,0.02472065,0.05672758,0.02056497,0.00711789,-0.07912494,-0.00965122,-0.00992243,-0.03773741,0.00509541,-0.02981532,-0.07239717,-0.0659494,-0.05571558,-0.01579642,0.03454862,0.04015313,0.00677315,-0.03171981,0.08418888,0.00491473,-0.0097198,0.03447567,-0.00400961,0.02003157,0.03129653,0.0305952,0.08117978,0.0287262,0.08948731,-0.07177048,-0.01213757,-0.01051782,0.00962416,-0.03071692,-0.01770032,-0.00652743,-0.03372972,-0.01466839,-0.05125661,0.02883006,-0.05470074,-0.04490891,0.12320817,-0.07274128,0.05332552,0.00804055,0.06059347,-0.04875694,0.03392657,-0.03137015,-0.04413434,-0.00095205,-0.00931229,0.04744313,0.0038915,-0.09674047,0.02467583,0.02729282,-0.00897362,-0.07532751,0.06592522,0.02657457,-0.10011535,-0.03907318,0.0402755,-0.03123542,-0.07367131,-0.00898183,0.02079947,-0.01413064,0.01926655,0.07545669,0.03269197,-0.09773459,-0.10085209,-0.00951829,-0.01165584,0.00766078,0.01500159,-0.07159524,0.05586008,0.00364318,-0.03594517,0.00471827,-0.01078724,0.04472761,0.03608123,-0.0167357,-0.03427897,0.0160357,0.00096731,-0.01981996,0.00555673,-0.03875965,0.0084612,0.03467695,-0.03788872,0.06821608,0.06296327,0.00315357,0.03527135,-0.05264486,-0.00913695,-0.04415836,-0.05313922,0.05532271,-0.02123654,-0.07301661,0.04083217,0.04157589,0.03392796,0.00790488,-0.06536343,0.01953764,0.05525539,-0.00044102,0.02603392,-0.00794372,-0.05167069,-0.10505823,-0.19402276,-0.05630208,0.02587317,-0.09502298,0.0149374,0.0056714,-0.01739894,-0.01148011,-0.0485082,0.05886193,0.09164472,0.02754283,-0.06806088,-0.03621919,-0.00818719,0.0116856,0.02280551,-0.0620679,-0.04282978,-0.01023879,0.04614874,0.01818533,-0.08895186,-0.067221,0.0526408,-0.00852641,0.12567723,0.0025925,0.04485941,-0.00975421,0.01313328,0.00359191,0.01765802,-0.08287763,0.04141987,0.05070173,0.01511022,-0.00899794,-0.02133469,-0.02962621,-0.01892691,0.0384949,0.03511632,-0.01893691,0.04961596,-0.05127379,-0.00994901,0.00635358,-0.04421204,0.08545311,-0.02084935,0.03283411,-0.03813954,0.10750825,-0.04311332,0.00501021,-0.05693356,-0.02916871,0.03227865,0.01900271,0.02425921,-0.03511878,0.02914976,-0.03366322,0.05365554,-0.01754437,-0.01857208,-0.05790021,-0.02516437,-0.05697814,-0.04222965,0.07704271,0.00568466,-0.02198233,-0.03231172,0.0140413,-0.06703369,0.04430533,0.02375912,0.03949952,0.03256601,0.00027755,0.00911093,0.04058119,0.00335763,0.01006668,-0.00842974,-0.06197772,0.05488509,-0.02528947,-0.02391639,0.03035957,-0.0354487,-0.00897612,0.08537983,0.01744441,-0.26673514,0.03238896,0.09197298,-0.00960718,-0.04032099,0.0110098,0.00861329,-0.07754496,-0.02746227,0.01381227,0.0253686,0.04384559,0.03232931,-0.0188516,-0.03284936,-0.04792428,0.1074887,-0.0540454,0.11796957,-0.04896782,0.04603634,0.06802665,0.25793025,-0.03858396,0.0593898,0.00348731,0.02643196,0.05968409,0.0205907,0.08281839,0.0451722,0.00601044,0.14149436,0.01361644,-0.0558883,0.03703975,0.01700625,-0.01129822,0.01322753,0.0060302,-0.00532015,0.01412197,-0.06469517,-0.01448434,0.10926633,-0.05741993,-0.05953424,-0.0428868,0.02478,0.00651744,0.01596612,0.0362096,-0.02879672,-0.02313672,0.01134296,0.00919065,-0.03697199,0.01287366,-0.05310889,0.03417254,0.02002159,-0.04692084,-0.02515885,0.09380658,0.02684901],"last_embed":{"hash":"v4qswx","tokens":473}}},"text":null,"length":0,"last_read":{"hash":"v4qswx","at":1751288783317},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Style types","lines":[49,74],"size":1775,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"v4qswx","at":1751288783317}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Style types#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00557335,-0.00290332,0.02483123,-0.02942058,-0.00284674,0.00922553,-0.03880068,-0.04538926,-0.02968062,-0.00874725,-0.01152837,0.00136993,-0.00406534,0.02539838,0.03816609,-0.00989866,0.04892762,0.09176586,0.00623597,0.03144466,0.06404149,0.02663105,0.03476626,-0.01315619,0.02040613,0.00482439,0.02138083,0.0066567,-0.01776664,-0.20791906,0.01908528,0.07477675,-0.03196507,0.01887014,0.03678816,-0.05360639,-0.04854563,0.02647016,-0.08101646,0.06171313,0.01673959,0.0152727,-0.00185016,-0.04051763,-0.01563504,-0.0367779,-0.00965368,0.02874729,-0.02512549,-0.05648309,-0.02979017,-0.0381254,0.04235576,0.01258111,0.00924192,0.06164358,0.08255623,0.04759844,0.03588361,0.02863705,0.01700599,0.02259082,-0.18608917,0.02409358,0.05390352,0.0437445,-0.07318512,-0.04318144,0.03998043,0.02493791,0.03417869,0.02008912,-0.03211302,0.06298652,-0.01305929,-0.03716356,-0.00862962,-0.0432649,-0.03001763,-0.01080152,-0.02877853,-0.02983482,-0.02133706,-0.01167572,-0.00149765,-0.01171389,0.02944708,0.00062185,0.05101289,-0.00478906,-0.02214203,-0.08957978,0.06118286,0.06137538,0.02772319,-0.02034726,0.0789518,0.01966042,0.00696846,0.13051285,-0.05570097,-0.01465863,0.02777079,0.02458268,0.06199988,0.01987372,0.01292945,-0.07976879,-0.01780129,-0.00746931,-0.03348687,0.00242433,-0.03515313,-0.0679935,-0.07391075,-0.05439555,-0.01742643,0.03441397,0.04588375,0.00077704,-0.03146465,0.08123812,0.00081625,-0.01568313,0.02400197,-0.00585283,0.01841914,0.02815843,0.02596138,0.07692302,0.02185105,0.0889299,-0.07004268,-0.01239285,-0.01460501,0.00797315,-0.02519768,-0.02166469,-0.00721391,-0.03257363,-0.01410402,-0.05320576,0.02880251,-0.05832854,-0.04188619,0.12371398,-0.078293,0.0560057,0.00744957,0.06252545,-0.05595758,0.03720402,-0.04533686,-0.04091686,-0.00336725,-0.00530912,0.0513466,0.00272662,-0.0918108,0.02914543,0.0290619,-0.00167741,-0.07384073,0.05465733,0.02573735,-0.0937757,-0.04119105,0.03990082,-0.03009394,-0.06806992,-0.01187184,0.02118293,-0.01576901,0.02683847,0.07283898,0.02921132,-0.10320287,-0.10223734,-0.00771531,-0.01565764,0.01492061,0.01504593,-0.06728183,0.05621003,0.00606233,-0.03896121,0.00096074,-0.00829972,0.05224836,0.04007311,-0.01507583,-0.03614523,0.02378008,0.00629416,-0.01759829,0.00469447,-0.03767252,0.00444679,0.03002211,-0.03415795,0.06429051,0.06451598,0.00401369,0.0369649,-0.05611613,-0.00761977,-0.04165416,-0.05567909,0.05533149,-0.02522761,-0.08102439,0.04364108,0.03850328,0.03583357,0.00493248,-0.06372758,0.02556576,0.06373426,0.0067364,0.02234743,-0.00898226,-0.05899813,-0.10511889,-0.1912639,-0.05963354,0.02418592,-0.09254138,0.01497302,0.0064288,-0.01565612,-0.01038056,-0.05104004,0.05849236,0.08388173,0.01960654,-0.06351286,-0.03236082,-0.00830937,0.01467358,0.01907325,-0.05551839,-0.04665848,-0.00952347,0.04807319,0.01616582,-0.09245522,-0.07558005,0.05248585,0.00202115,0.12391426,0.00096627,0.05073772,-0.01005174,0.01884363,0.00002195,0.01828785,-0.08257928,0.05033434,0.04503576,0.01299422,-0.02078461,-0.02197979,-0.03433253,-0.01908303,0.03283786,0.03451685,-0.0159288,0.05101191,-0.05304834,-0.01560691,0.00558443,-0.05026915,0.08227929,-0.01891673,0.03307048,-0.03795991,0.1046459,-0.0397059,0.00320579,-0.0605232,-0.02823531,0.02953586,0.02111069,0.03051695,-0.03422474,0.03286903,-0.03659238,0.05031417,-0.01545359,-0.01558859,-0.05869317,-0.02393617,-0.05333497,-0.04185035,0.08033441,0.0049028,-0.01706793,-0.02802551,0.01347986,-0.06423566,0.04962739,0.02258288,0.03507626,0.03009658,-0.00520129,0.01095947,0.04529278,0.0059551,0.0064465,-0.01060708,-0.06593952,0.05869557,-0.02108199,-0.02647554,0.03367566,-0.03901929,-0.003662,0.08751959,0.01975689,-0.2609109,0.03518667,0.09326491,-0.00149428,-0.03653417,0.01161795,0.00530985,-0.07574183,-0.0250134,0.01084173,0.02811479,0.03377815,0.03173352,-0.01737221,-0.03377948,-0.04926251,0.11239384,-0.05486256,0.11991972,-0.0431194,0.05367313,0.065749,0.25976548,-0.04037694,0.06403633,0.00010107,0.02618012,0.055923,0.01383081,0.08299305,0.05324558,0.01115726,0.14032318,0.01137856,-0.05443309,0.03532024,0.01930943,-0.010161,0.01203721,0.00634394,-0.00648298,0.01351313,-0.06860165,-0.01948067,0.10839557,-0.04694038,-0.05647597,-0.03558572,0.02304803,0.01130729,0.01367946,0.0410515,-0.03367329,-0.0202304,0.0145369,0.00320581,-0.03703272,0.01297115,-0.05475059,0.03476223,0.02538729,-0.03807772,-0.0302067,0.08930635,0.02862248],"last_embed":{"hash":"x8ca11","tokens":425}}},"text":null,"length":0,"last_read":{"hash":"x8ca11","at":1751288783583},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Style types#{1}","lines":[51,62],"size":1119,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"x8ca11","at":1751288783583}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#At-rule types": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0380181,-0.01998291,0.02838383,-0.01742701,-0.01162323,-0.00254845,-0.04180054,-0.02274277,-0.02342682,-0.0076384,-0.02758629,-0.0335309,0.0112296,0.01999631,0.03206256,0.01179972,-0.05436371,0.11642695,0.00427857,0.02341101,0.11042649,0.01278504,0.03740828,-0.05335568,0.01635429,0.06994203,0.01205205,-0.06178361,0.00004763,-0.24102646,0.02403603,0.07517872,-0.04591557,-0.0141475,0.03409209,-0.07647578,-0.00238686,0.05055625,-0.04796893,0.04894897,0.04378165,0.03048303,0.02638396,-0.03132443,-0.01814732,-0.04826035,-0.02475414,0.01303348,-0.02484719,-0.11297139,-0.02989969,-0.05928271,0.00140809,-0.00272298,-0.01969782,0.04136286,0.07905179,0.00678249,0.05202175,0.04973406,0.02513092,-0.00485745,-0.1835192,0.07473219,0.01299184,0.04614091,-0.08197404,-0.03915286,0.00681747,0.01650457,0.00293144,-0.01653397,-0.01946026,0.05967266,0.03256017,-0.02180796,-0.02811764,-0.00982376,-0.01266179,-0.02603953,-0.02281253,0.00994212,0.0070243,0.01408582,0.00106672,-0.01954058,0.0167555,0.00609945,0.05236369,-0.02158938,-0.03198155,-0.12028318,0.03728591,0.04266521,0.00789561,-0.0279317,0.03133144,0.02230144,-0.02391963,0.15376572,-0.07094664,-0.00630885,-0.02964415,0.04506825,0.04155148,-0.02143987,0.01500591,-0.06326661,-0.01304853,-0.0116705,-0.0128418,0.00485501,-0.02511353,-0.05560702,-0.01830047,-0.01437618,0.01691314,0.02804417,0.01943429,0.0284821,0.01357321,0.04381112,0.02177068,-0.00734278,0.01914808,0.00128274,0.00603506,0.04312022,0.00939154,0.05666443,0.02942404,0.06695883,-0.05622492,-0.02261085,0.03322456,0.00336486,-0.00536592,0.00162203,-0.01530556,-0.0817309,-0.03881094,-0.06250159,0.02323815,-0.09114922,-0.02568035,0.09430853,-0.0681232,0.0508396,0.00366726,0.02451476,-0.03306587,0.05483224,-0.06352699,-0.03546694,-0.01394353,0.01965774,0.0035894,0.04957951,-0.04884255,0.03770808,-0.02987024,-0.00179597,-0.0102337,0.04933649,-0.01830224,-0.08807522,-0.05570786,0.03509989,-0.01552239,-0.04968045,-0.0365512,-0.02434513,0.01415563,0.01336446,0.07343552,0.06159936,-0.06113369,-0.10332787,0.00396669,0.03431338,0.0388752,0.00815581,-0.07458619,0.02267044,0.02001934,-0.03359016,-0.00925343,-0.0460283,0.00007736,0.04303668,-0.11448862,-0.00367419,-0.00976693,0.00564375,-0.04759512,0.00458524,-0.03770993,-0.02001418,0.00055392,-0.00590013,0.10565689,0.06098858,-0.0036957,0.00943098,-0.02109343,0.02099999,-0.08449424,-0.01932844,0.00434756,-0.03661248,-0.09376144,0.03369222,0.07031368,0.05005394,0.02070513,-0.05429915,0.05571339,0.02716546,-0.00417299,0.01085326,-0.0251879,-0.02570157,-0.06735314,-0.21375102,-0.02527895,0.02517285,-0.0330238,-0.01659197,0.03425818,0.01314387,-0.00557723,-0.02048,0.06209291,0.0945852,0.06995871,-0.0526329,-0.03215352,-0.03127053,0.0102625,-0.00525649,-0.06240942,-0.02814484,0.00011069,0.06039734,0.04011542,-0.07443914,-0.03605351,0.04622499,0.01130677,0.13527425,0.02861711,0.06927956,-0.01420408,0.03307234,-0.01525227,0.03392424,-0.06354988,0.0175268,0.03018948,0.02411058,-0.02114491,0.03240018,-0.001814,-0.04091324,-0.00163947,0.01953794,-0.00521838,0.04947461,-0.04498189,-0.00928686,-0.00693669,-0.05683851,0.06158567,0.00779463,0.0045843,-0.00949671,0.09354823,-0.03912648,0.02503506,-0.06034227,-0.06031944,-0.03155775,0.03995954,0.02138745,0.0079958,0.00874689,-0.05006411,0.02810775,0.0636552,-0.02588554,-0.05757493,0.04169092,-0.01483862,-0.00427318,0.07343533,-0.001216,-0.00334088,0.00085432,0.03290125,-0.05407144,0.01580898,0.01234654,-0.01151736,0.06462225,0.01679414,0.024708,0.00145457,0.00318112,0.07007791,-0.03250916,-0.07362831,0.09513742,-0.0391453,-0.00680588,0.03759589,-0.00588102,0.01007843,0.06737909,0.03987297,-0.29055899,0.02580723,0.05787365,0.00545934,-0.0411162,0.02338645,0.08505577,-0.08738836,-0.00735076,0.0011487,0.00304111,0.0518835,0.05544656,-0.03081164,-0.05381402,0.00808475,0.11371858,-0.03651147,0.08107658,-0.06751013,0.01597048,0.04847504,0.2463073,-0.04632249,0.06346024,0.00022063,-0.01250501,0.02392494,0.05152925,0.09758422,0.01190456,-0.00307367,0.12896447,-0.00203246,0.02023852,0.02271993,0.00288354,-0.00175468,0.02932261,-0.00487541,-0.01116963,0.0048646,-0.02465341,-0.0144869,0.04883504,-0.03322992,-0.07337207,0.01061068,0.04157702,-0.0145909,0.00146622,0.0214995,0.0112571,-0.02485396,0.00500654,0.01381378,-0.01424326,0.0172828,-0.08251489,0.00412447,-0.01818263,-0.02708976,-0.00297675,0.09617357,0.03533373],"last_embed":{"hash":"sb3p59","tokens":360}}},"text":null,"length":0,"last_read":{"hash":"sb3p59","at":1751288783963},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#At-rule types","lines":[75,87],"size":824,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"sb3p59","at":1751288783963}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#At-rule types#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04125454,-0.02265232,0.02369724,-0.01379775,-0.01185457,-0.00060832,-0.03575452,-0.02328112,-0.02639262,-0.01084463,-0.02868903,-0.03176314,0.00769456,0.01801926,0.03411339,0.01078831,-0.06002412,0.11415824,0.00569399,0.02660334,0.11553731,0.01381262,0.03401819,-0.05552638,0.02004344,0.07113149,0.00746128,-0.06829598,0.00131976,-0.24126475,0.0227602,0.07075296,-0.04786982,-0.01840834,0.03173042,-0.07948154,-0.00436956,0.04957013,-0.04917539,0.0446309,0.04485162,0.03208701,0.03030227,-0.03118627,-0.02010139,-0.05003209,-0.02658563,0.0105737,-0.02739844,-0.11616727,-0.03061174,-0.06017418,-0.00355008,0.00392483,-0.02163937,0.03399283,0.07429331,0.01091888,0.05188815,0.05347772,0.031873,-0.00601808,-0.18156801,0.07553253,0.01131015,0.0501588,-0.08002656,-0.03981741,0.0063277,0.01300607,0.00100705,-0.01668334,-0.02052402,0.05991767,0.02841511,-0.02093774,-0.0293309,-0.00953527,-0.01324558,-0.02385574,-0.01978074,0.00272507,0.00659903,0.01486581,-0.00667611,-0.0183505,0.01821804,0.00256674,0.05302479,-0.02056208,-0.03178319,-0.11737046,0.03723316,0.04537718,0.01192691,-0.02452095,0.03005247,0.02142846,-0.02418677,0.1567089,-0.07171656,-0.006222,-0.03540931,0.04404108,0.04029411,-0.02440607,0.01393412,-0.06459712,-0.01335168,-0.00995979,-0.01559278,0.00922217,-0.0252783,-0.05261249,-0.01700544,-0.01270785,0.01685706,0.02937975,0.02189826,0.02544052,0.0148845,0.035709,0.02228533,-0.00581134,0.01760428,0.00363216,0.00560688,0.03909731,0.00384169,0.05654361,0.02683483,0.06399836,-0.05815521,-0.02300745,0.03629837,0.00187298,-0.0033386,0.00355459,-0.01433647,-0.07665293,-0.04293607,-0.06406002,0.02328953,-0.08879381,-0.02405351,0.09401899,-0.06971515,0.04818695,0.00403325,0.0258113,-0.03165979,0.058123,-0.06641563,-0.035865,-0.01695417,0.02362766,0.00023893,0.0514509,-0.05060305,0.0300884,-0.03117672,0.00057457,-0.00778619,0.05188954,-0.02345485,-0.08666487,-0.0584024,0.03698025,-0.01637632,-0.04568725,-0.03548317,-0.0268736,0.01786349,0.01309317,0.07462989,0.06433827,-0.06077576,-0.10373645,-0.00186253,0.03534382,0.03911947,0.00815776,-0.0691946,0.01993511,0.02148807,-0.03009796,-0.01152836,-0.04236076,-0.00065244,0.04310111,-0.1157892,0.00057711,-0.01009843,0.00402877,-0.04368553,0.00696113,-0.03779872,-0.02328063,-0.00112686,-0.00625767,0.10917318,0.06808699,-0.00504391,0.00901674,-0.02050409,0.02594164,-0.08413097,-0.01911733,0.00272335,-0.03589372,-0.09757918,0.03603122,0.06932937,0.0487305,0.01529866,-0.05012918,0.05780697,0.01937398,-0.00489435,0.00951781,-0.02228834,-0.02461465,-0.06045152,-0.21260981,-0.02669124,0.02506169,-0.02873206,-0.0225953,0.03038327,0.01813544,-0.00297142,-0.01950311,0.0599828,0.08687776,0.06829023,-0.04636561,-0.02985681,-0.03000491,0.01050541,-0.00761923,-0.06181839,-0.02401942,0.00313189,0.05643945,0.04521493,-0.07466209,-0.03702891,0.04475026,0.01414417,0.13793409,0.02521635,0.07405813,-0.01475986,0.03408854,-0.02004819,0.03480081,-0.06207241,0.01977828,0.02759778,0.02488635,-0.02088145,0.03837112,-0.00198538,-0.04097904,-0.00102271,0.01598763,-0.00609627,0.04846479,-0.04536429,-0.0098094,-0.01253059,-0.05829386,0.05627073,0.0133523,0.00491094,-0.00634898,0.08590499,-0.0391659,0.02556648,-0.06068335,-0.05984828,-0.02790157,0.04338145,0.02602558,0.00778031,0.00177718,-0.05089555,0.02978416,0.06387763,-0.02663895,-0.05415988,0.04210863,-0.01247142,-0.00428611,0.07426432,-0.00336065,-0.00439941,0.00032167,0.03473667,-0.05769324,0.01673432,0.0095588,-0.01201449,0.06501607,0.01898036,0.025369,-0.00151043,0.00681667,0.07199544,-0.03531539,-0.06851441,0.10199957,-0.03479359,-0.00768374,0.03977622,-0.00099903,0.01531595,0.06787609,0.03832017,-0.29256645,0.02618337,0.05457139,0.00677938,-0.03932526,0.02601426,0.08806847,-0.08857855,-0.00745933,-0.00058596,0.00305842,0.05124387,0.05656966,-0.02767711,-0.05415877,0.01239096,0.11207569,-0.03313338,0.07830848,-0.07192349,0.01824749,0.0481541,0.24450177,-0.04639241,0.06380902,0.00079732,-0.01407376,0.02224384,0.05176159,0.0939614,0.01509259,-0.00268558,0.12936486,-0.00166659,0.02696109,0.02360472,-0.00351108,0.0009007,0.02674265,-0.00530681,-0.00955314,-0.00095897,-0.02585368,-0.01547835,0.05074059,-0.03007444,-0.07313982,0.01217244,0.04075358,-0.0114913,-0.00166155,0.02126538,0.01317024,-0.02749669,0.00702398,0.01221111,-0.01018475,0.02012846,-0.08336566,0.00259759,-0.01511074,-0.02053196,-0.00127616,0.09472053,0.03831179],"last_embed":{"hash":"6nr7gj","tokens":359}}},"text":null,"length":0,"last_read":{"hash":"6nr7gj","at":1751288784116},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#At-rule types#{1}","lines":[77,87],"size":806,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"6nr7gj","at":1751288784116}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Pseudo types": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04938956,-0.03831722,0.04899349,-0.07557831,-0.0114729,-0.06259561,-0.04885942,-0.01549537,-0.00836694,0.00094528,-0.0049205,-0.02106925,0.00429723,0.03941114,0.01546929,-0.01386054,0.02273644,0.10144351,-0.02096814,0.01517326,0.14310816,0.01685927,0.00229108,0.00051828,0.02496651,0.03167151,0.03698362,0.01335713,0.04485784,-0.21257675,-0.01609015,0.00836402,-0.03928488,-0.03982743,0.0277047,-0.04492555,0.00174913,0.00769292,-0.03866493,0.06777518,0.00172755,0.03480523,-0.00628266,-0.01193262,-0.05185565,-0.00906187,0.00070295,0.03474706,0.00181669,-0.04057123,-0.02884399,-0.0057802,0.02033479,-0.03442176,-0.0672332,0.06778897,0.08070143,0.02880379,0.01902642,0.05625736,-0.01771441,0.02133879,-0.11283772,0.04864441,0.01365976,0.04950659,-0.0259791,-0.03054039,0.01308377,0.04810885,0.03242852,0.01167788,-0.06910653,0.10096954,0.05545543,-0.06465294,0.01466224,-0.05200096,0.00260796,0.00943865,-0.08470781,-0.03840155,-0.0069164,0.03599102,0.01556852,0.00985538,0.01288166,-0.03504558,0.00604944,0.01753089,-0.04799033,-0.07849868,0.05497018,0.02127374,0.04971056,0.03036965,0.03871901,0.02888874,0.01923671,0.12269268,-0.05919649,0.01416861,0.02628256,-0.01832923,0.01383482,-0.02939773,0.00807807,-0.05279494,-0.02745609,-0.03702435,-0.01055215,-0.03498666,0.00276506,-0.07778277,-0.05492673,-0.02730377,0.05497188,-0.02728636,0.05179191,0.05784784,0.02324549,0.0477732,0.03495396,0.01226379,0.04730726,-0.02174838,-0.02151675,0.0580012,0.01850972,0.09416025,0.06662636,0.02004093,-0.05838284,-0.01233919,0.02864315,-0.00820521,-0.03388656,0.01729782,0.01059465,-0.02906411,0.01061572,-0.08567149,-0.02077379,-0.01796486,-0.05757532,0.05338894,-0.0881528,0.06216752,-0.0253884,0.05664296,-0.04493794,0.03897678,-0.00694331,-0.02533538,-0.02215604,-0.01552222,-0.01205878,0.00320969,-0.033294,-0.02529296,-0.02834753,0.00332356,-0.06304179,0.11477875,-0.01143773,-0.03039682,-0.03533652,0.02461891,0.04022288,-0.11533901,0.01096096,-0.01052763,-0.06991524,0.0008245,0.05527313,0.02869381,-0.08009247,-0.04434694,0.00892963,0.0646363,-0.00346368,0.00870171,-0.03637985,0.05241478,0.00004393,-0.04038314,-0.004528,-0.04990317,0.04028079,0.04129351,0.00970728,0.04153207,-0.0573833,-0.04237188,-0.01978923,-0.03104603,0.04201601,-0.01970803,0.02716126,-0.00660977,0.1093009,0.01950802,0.01602757,0.02996195,-0.04290355,-0.01885896,0.01165802,-0.01503013,0.02077047,0.04373734,-0.05927073,-0.01943649,0.02357839,0.07738466,-0.01189321,-0.03498155,0.01476862,0.04190445,0.01460852,0.02148858,-0.06859567,-0.03403625,-0.07410502,-0.23139265,-0.04072095,0.06281124,-0.08333838,0.0382515,0.02856123,0.00406816,-0.01068457,-0.01376912,0.04380334,0.03735504,0.01799271,-0.07193255,0.00642652,-0.033716,0.00469484,0.01520707,-0.08772302,-0.00132041,0.00966168,-0.00108569,0.01945986,-0.09538192,-0.08118419,0.03125696,-0.0257486,0.13198353,0.04407466,0.04801447,-0.01888429,0.01249478,-0.02664774,-0.00772412,-0.07102562,-0.0106559,0.02844951,-0.01599129,-0.05824509,0.0578592,-0.01983099,-0.00278258,0.00299999,-0.04108533,-0.03636571,0.05720374,-0.06444596,-0.02951888,0.01027176,0.01174188,0.10343491,-0.02417419,-0.00333828,0.01122759,0.05986549,0.00103902,-0.00982975,-0.0747316,-0.03394952,0.01257922,-0.01077547,-0.02638551,-0.03199286,0.03314166,-0.04571812,0.02127294,0.02400506,0.00934409,0.01116965,0.01127448,-0.02770751,-0.04079505,0.12632698,0.01012693,-0.05025027,-0.00522846,0.03829575,-0.02960566,0.00635943,-0.00785366,0.01254509,0.06305985,0.03823562,0.03670922,0.02856287,-0.01203225,-0.01117961,0.01266084,-0.05367595,0.04676409,-0.09705611,-0.04440695,0.0388474,-0.02196366,0.02013933,0.10335122,-0.04793996,-0.24968478,0.01894331,0.03594841,-0.04913417,0.03573365,0.0398156,0.06518376,-0.07095359,-0.07982384,0.00339716,-0.03175177,0.07395265,0.05949851,-0.03590672,-0.03909445,0.04351984,0.0956488,-0.04045148,0.10854182,-0.05306191,0.02598723,0.0138305,0.25657988,-0.04784047,0.06205419,0.00198776,-0.03734854,0.0462319,0.05427059,0.08004515,0.0472659,0.03256387,0.09992228,-0.0207158,-0.01301675,0.01138178,0.01211747,0.01984402,0.02575855,-0.04473764,-0.04344855,0.02082544,-0.07758354,0.00462788,0.07421448,-0.02497641,-0.00604445,-0.05916552,0.05256525,-0.0045633,-0.05681374,0.08210279,0.00775742,-0.00574094,0.00383809,0.0222437,-0.05959023,-0.05092614,-0.05805231,0.04023504,0.00448318,0.02003778,0.06607195,0.12619156,0.01133767],"last_embed":{"hash":"tzeoey","tokens":130}}},"text":null,"length":0,"last_read":{"hash":"tzeoey","at":1751288784295},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Pseudo types","lines":[88,103],"size":436,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"tzeoey","at":1751288784295}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Pseudo types#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06382449,-0.03887084,0.05579898,-0.07442706,-0.02840857,-0.05226914,-0.03764225,-0.01866744,0.00002196,0.0083138,-0.00601852,-0.01870694,0.0085772,0.04551619,0.0029797,-0.02411158,0.00884259,0.09592769,-0.01613039,0.02100148,0.13099785,0.02527455,0.00147571,-0.00880698,0.02097112,0.02674419,0.03629868,0.02287734,0.04544207,-0.22814919,0.00031214,0.00327073,-0.03835778,-0.03780444,0.02800623,-0.04376823,0.00263114,0.00021937,-0.03139425,0.07615982,0.00023835,0.03480266,-0.00701899,-0.00472534,-0.049308,-0.0035298,-0.00483046,0.0427209,0.00776904,-0.05126699,-0.02577496,-0.00551362,0.03519198,-0.0410703,-0.07100765,0.06490416,0.08020747,0.01908605,0.01929639,0.04865082,-0.0054686,0.0276339,-0.11710472,0.0377126,0.03125091,0.05398116,-0.02499552,-0.0322953,-0.0004085,0.0583835,0.03636786,0.00865081,-0.06729184,0.10197098,0.05177788,-0.06058906,0.00068842,-0.05261619,-0.01433719,0.02836495,-0.08874247,-0.03979988,-0.02645836,0.03948621,0.0075221,-0.00083484,0.01662065,-0.02860474,0.00463577,0.01727353,-0.03771795,-0.07275288,0.04690865,0.02124769,0.04885296,0.02363558,0.03744017,0.0267744,0.01910928,0.11967746,-0.05055497,0.0235586,0.01725558,-0.01033703,0.00188273,-0.03478121,0.00920161,-0.04823137,-0.02487611,-0.04845805,-0.0075483,-0.03828846,0.02155286,-0.07288037,-0.0486487,-0.02210704,0.04924443,-0.02384416,0.04890825,0.05135933,0.01812857,0.04196922,0.0441327,0.0196127,0.03696136,0.00003951,-0.0093152,0.0612444,0.0216984,0.0935208,0.08070285,0.01401482,-0.05948748,-0.00218102,0.0287754,-0.00510387,-0.03513842,0.02091247,0.00704458,-0.02346043,0.0141985,-0.08417847,-0.01285143,-0.01966138,-0.05360183,0.06333236,-0.0811476,0.05451054,-0.03371966,0.0513296,-0.04843976,0.03295828,-0.01596162,-0.03122018,-0.02912077,-0.01412148,-0.02032814,-0.00168862,-0.05571543,-0.01430659,-0.02565285,0.02124618,-0.0732483,0.11983981,-0.01534285,-0.02870749,-0.02562884,0.03000988,0.03499959,-0.10649345,0.01626318,-0.02653949,-0.07176913,-0.01479054,0.07024702,0.02054645,-0.08136384,-0.04491701,-0.00297147,0.06010696,-0.00182564,-0.0154824,-0.03933931,0.0545274,0.00302022,-0.04789201,0.0032355,-0.02768015,0.03153779,0.03981205,-0.00022518,0.03566569,-0.04627511,-0.0353049,-0.02039849,-0.03332161,0.0330313,-0.01354114,0.03071222,0.00353023,0.11306538,0.01782588,0.01349698,0.0419983,-0.04666484,-0.0204419,0.0071462,-0.01807313,0.02694368,0.03338591,-0.06039846,-0.0219603,0.02521757,0.08587684,-0.0074047,-0.01189288,0.01546631,0.0375996,0.02579498,-0.00019158,-0.06223904,-0.03210563,-0.0712102,-0.22314748,-0.03631631,0.03639378,-0.05679949,0.03184728,0.02731366,-0.00183618,-0.00805825,-0.01297684,0.04369541,0.04023292,0.01688279,-0.07587116,0.02606552,-0.02901625,-0.00132374,0.02509303,-0.09530308,-0.00542358,0.01513922,-0.00770123,0.01151818,-0.09981234,-0.06302343,0.03865134,-0.028174,0.13461292,0.04691435,0.05076133,-0.0178554,0.02631048,-0.01191496,-0.00132426,-0.07500818,-0.03692683,0.04807275,-0.01017338,-0.05535485,0.04516025,-0.00787124,-0.00068938,0.0040587,-0.03515276,-0.04144557,0.04807733,-0.04835844,-0.02645392,0.02779585,0.01326518,0.10505867,-0.02699294,0.00036632,0.00256454,0.06540899,0.01009936,-0.00617566,-0.08414625,-0.0549082,0.00766072,0.00117896,-0.03511734,-0.03110055,0.02187265,-0.04568676,0.02266702,0.03084648,0.00208919,0.00340216,0.01862713,-0.01510608,-0.02982203,0.11502258,-0.00087134,-0.04144486,-0.02044597,0.0448726,-0.01713865,0.00650337,-0.01709793,0.01619832,0.06315503,0.02752956,0.02331029,0.03187338,-0.02141947,-0.01558019,0.00385928,-0.05306393,0.051489,-0.11812843,-0.04986516,0.0141232,-0.01911947,0.02778247,0.10810139,-0.03940101,-0.2553643,0.03267806,0.01936233,-0.06649148,0.02366328,0.04752463,0.0549119,-0.06173031,-0.08697402,0.00715631,-0.0301273,0.08273108,0.05080958,-0.02194121,-0.04775195,0.03697037,0.07326008,-0.02922724,0.1198013,-0.05548368,0.03921129,0.02908832,0.26019832,-0.05604597,0.03931266,0.00873227,-0.03358551,0.03517635,0.04657609,0.08040144,0.04404236,0.03177967,0.09842069,-0.02887796,-0.00020123,0.01274635,0.03021726,0.02596965,0.03107257,-0.04873056,-0.05954019,0.0119924,-0.05802147,-0.00193955,0.08305189,-0.04003142,-0.01313846,-0.04319931,0.04258752,-0.00864981,-0.06966864,0.07359796,0.0087477,-0.01032952,0.01529405,0.01466595,-0.05056831,-0.03567275,-0.0701815,0.04892361,0.00461492,0.01552659,0.07681966,0.12958521,0.00619828],"last_embed":{"hash":"8el9rj","tokens":115}}},"text":null,"length":0,"last_read":{"hash":"8el9rj","at":1751288784361},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Pseudo types#{3}","lines":[94,103],"size":351,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"8el9rj","at":1751288784361}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Generics": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.039473,-0.0204405,0.02441228,-0.05185312,-0.0412159,0.02139545,-0.02255701,-0.02369811,0.02061232,-0.00619393,0.00765908,-0.01547822,0.01594296,0.04815478,0.0335581,-0.01444839,0.02243974,0.08515371,-0.0433239,0.02869227,0.12889512,0.02856122,0.00765311,-0.06918033,0.02937552,0.01909256,0.05644387,-0.01035909,0.00914534,-0.20780841,0.02450821,0.03247549,-0.00412021,0.00674437,0.01196658,-0.07440112,0.00018082,-0.00246748,-0.04917288,0.07091157,0.01149593,0.08499046,0.01879165,-0.02907337,-0.04902573,-0.04808735,-0.04078475,0.01167341,-0.03191188,0.02996961,-0.05778804,0.01775836,0.05485675,-0.00423488,-0.03479462,0.09870724,0.08867308,0.02000063,0.05464464,0.00729031,-0.01215591,-0.00230025,-0.15048856,0.1104351,0.0740435,0.03664257,-0.00478804,-0.00187406,0.01993991,0.02040549,0.02729091,-0.00853393,0.00901333,0.11370187,0.02205983,-0.02725293,0.00958462,-0.00668292,0.07186969,-0.00465806,-0.04624628,-0.08231175,0.01018024,0.00203974,-0.00084177,0.02409165,0.00405712,-0.02193803,0.03629346,0.018285,0.00227779,-0.08393146,0.00683592,0.03813228,0.00483524,-0.04182261,0.00186216,0.06379363,-0.03559503,0.13207671,-0.00987123,-0.01090823,-0.03878893,0.04121023,0.05241371,0.04032929,-0.02488428,-0.03639768,-0.0391265,-0.02050835,-0.01837054,-0.01327545,-0.05285009,-0.07929604,-0.02930691,0.02639111,-0.00003663,-0.02305188,0.05798176,0.01693849,0.00481305,0.02424071,-0.02816723,-0.02135848,0.02317203,-0.00645176,0.0316026,0.05272202,-0.00023651,0.11192518,0.07755337,0.03828521,-0.06052049,0.00143122,0.02906633,0.03568894,0.00754494,0.0114237,-0.02716438,-0.04597662,-0.02712116,-0.06210048,-0.01827228,-0.02111662,0.00188841,0.11227263,-0.06164759,0.07182577,-0.04399586,0.00092065,-0.00047418,-0.00942709,-0.03288957,-0.0146567,-0.00299745,-0.02953535,0.0513172,-0.03822111,-0.05164397,-0.00409782,-0.02794545,0.08582568,0.00757174,0.08307441,0.01753639,-0.10429844,-0.03489755,0.03169442,0.01823145,-0.06400692,-0.03392617,-0.01668982,0.00467343,-0.00155739,0.08507036,0.00157453,-0.07956149,-0.06956485,0.03254722,0.05993821,0.02573667,0.03372193,-0.00904104,0.02062542,0.01383914,-0.01061978,0.02622089,-0.06103827,0.01034917,0.02603007,-0.03458101,-0.00471635,0.02372448,-0.02170868,-0.03639638,-0.0367578,-0.03748846,0.00254318,0.03308326,-0.01823967,0.1494624,0.01481811,-0.00085035,0.05355373,0.01573865,-0.0331866,0.00889009,-0.02140277,0.06026087,0.02611958,-0.06226618,-0.02980303,0.01723213,0.01762414,0.01670378,-0.00011077,0.04671155,0.0350687,0.02505211,0.03389263,-0.04573876,-0.04221827,-0.09884595,-0.21240138,0.01009532,0.04603528,-0.06069302,0.0763859,-0.02107444,-0.03335957,0.0015302,-0.06773856,0.00459882,0.11445771,0.04492429,-0.08508756,-0.08820186,-0.06264606,-0.00641942,-0.03751329,-0.02665445,-0.03357609,0.02215756,0.02104731,0.06074595,-0.11609193,-0.04391508,-0.00034911,-0.02753679,0.15009436,-0.00082314,0.06321426,-0.02973681,0.046028,-0.03324309,-0.00018611,-0.06488704,0.01397514,0.05025402,-0.02837461,-0.00469334,0.00610565,-0.0219736,-0.04240025,0.01674197,0.04148039,-0.03446018,0.03605377,-0.0368924,0.01833406,0.01540599,-0.00352964,0.02283954,-0.04528169,0.0388285,-0.01770497,0.07896823,0.00257281,-0.00543203,-0.06865288,-0.03973487,0.04040008,-0.01712541,-0.01181572,-0.01482201,0.04980908,-0.04340706,0.003928,0.03054528,0.01059886,-0.04719828,-0.04143543,-0.05670552,-0.01231545,0.10257728,0.00161237,-0.01293439,-0.05346055,-0.02615543,-0.05607529,0.05390513,0.02727626,0.01618996,0.01390108,0.03142023,0.02658211,0.0511359,-0.01108039,-0.01686331,-0.01874591,-0.03151459,0.05851938,-0.05709365,-0.00241292,0.0007431,-0.02698677,-0.01295097,0.0656308,-0.03354099,-0.26440051,0.02960513,0.04312032,-0.06101901,-0.04275298,0.04343778,0.0437957,-0.09302793,-0.08402561,0.00132284,-0.01335438,0.01398002,0.02334497,-0.02862407,-0.00870026,-0.01372908,0.11054698,-0.03895199,0.06641645,-0.05018698,0.05990013,0.0105994,0.23430341,-0.05957381,0.07843425,0.03655022,0.02401716,0.01818481,0.07413422,0.05503067,0.01600143,0.0185458,0.13767453,-0.03953542,-0.01099984,-0.00644064,-0.01160401,-0.01993693,-0.00771884,0.0069545,-0.0208748,-0.03557622,-0.02565704,-0.01954961,0.07210813,-0.02672419,-0.07184663,-0.04965226,-0.01447977,0.02047913,-0.06838302,0.07838251,-0.01839809,-0.00577107,0.06609458,0.00734663,-0.02483841,-0.04619711,0.00339682,0.01035665,-0.03738263,-0.07932109,-0.00349543,0.05068508,0.02513269],"last_embed":{"hash":"7t6qam","tokens":266}}},"text":null,"length":0,"last_read":{"hash":"7t6qam","at":1751288784413},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Generics","lines":[104,120],"size":876,"outlinks":[{"title":"length where the unit identifier is optional","target":"https://drafts.csswg.org/css-values-3/#lengths","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"7t6qam","at":1751288784413}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Generics#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01293824,-0.01789352,0.01844911,-0.05869998,-0.05138027,0.02058412,-0.03587838,-0.00698562,0.01543633,-0.00113422,0.03609138,-0.01230007,0.01424131,0.05175621,0.01906495,-0.03934193,0.04119516,0.09473126,-0.06668798,0.02698764,0.13491619,0.03619321,0.00391135,-0.05866067,0.02545295,0.03510414,0.05705054,-0.02927927,0.00150871,-0.2039793,0.01989521,0.02688426,-0.00698079,0.01518851,0.02496447,-0.05139062,0.00172378,-0.01606871,-0.02755024,0.07443941,0.01345557,0.07986866,0.01211674,-0.01069943,-0.0461526,-0.04708457,-0.04271174,0.02298565,-0.01513961,0.00693181,-0.0639955,0.02783939,0.0554228,0.01572767,-0.03996759,0.09345226,0.07785222,0.00280419,0.05431325,0.00556894,-0.0182662,0.00871656,-0.15377998,0.09521756,0.05814907,0.04774414,-0.01390789,-0.02068073,0.0286401,0.01923446,0.03841629,-0.01110176,-0.00160498,0.09799363,0.01672615,-0.00927655,0.01579558,0.00061175,0.06493378,0.00648951,-0.05005522,-0.092761,0.0070052,-0.00413367,-0.01488174,0.02536622,0.00846416,-0.01662652,0.05411229,0.00930882,-0.01239691,-0.06444839,0.00533194,0.04310129,-0.01160908,-0.05191951,-0.00177406,0.0593809,-0.04041018,0.13727215,-0.00725353,0.01542598,-0.02673107,0.04040038,0.04146981,0.04733952,-0.03839747,-0.03168797,-0.03082927,-0.02769839,-0.01862329,-0.01980324,-0.05094515,-0.07545447,-0.02777868,0.00675955,-0.01157254,-0.00500911,0.04369272,0.01255635,0.0071369,0.01947481,-0.03667069,-0.01323978,0.03877536,0.00772324,0.02188006,0.03891484,0.00367269,0.10706101,0.07239668,0.0593531,-0.06765606,-0.00097128,0.02325142,0.03740634,0.01523858,0.01931275,-0.0275649,-0.04095209,-0.03159823,-0.05295698,-0.03334529,-0.0228925,-0.02089071,0.10858813,-0.08808864,0.06486117,-0.03205371,-0.0038802,0.00822262,-0.01152785,-0.03715131,-0.0180121,-0.00016811,-0.03200241,0.04240379,-0.04358215,-0.06377385,-0.00473587,-0.01575853,0.05964116,0.00434388,0.08289248,0.03747042,-0.10789467,-0.03266753,0.04389554,0.01948593,-0.0749381,-0.028716,-0.01764377,-0.00458562,0.00337909,0.07710571,0.00442465,-0.07679956,-0.08576672,0.00911216,0.05255828,0.03312359,0.02002359,-0.01249034,-0.00040421,0.02120984,-0.01402696,0.01718085,-0.06444434,0.03594947,0.01365008,-0.03164553,0.00626144,0.01183017,-0.02853235,-0.03443221,-0.00806624,-0.03860042,0.01105015,0.02981994,-0.03389332,0.15063523,0.03858511,0.00464852,0.06639078,0.00714406,-0.04260688,0.03010883,-0.03020029,0.05662172,0.01880327,-0.0827138,-0.01252804,0.02135425,0.02900951,0.0047226,-0.0132547,0.03479261,0.03736917,0.0341688,0.02927619,-0.04975192,-0.02920242,-0.11834452,-0.22413187,0.01219553,0.06178419,-0.07070028,0.06716885,-0.00825043,-0.02330851,0.01290756,-0.0631908,0.0025161,0.11525592,0.04678149,-0.07435401,-0.08801041,-0.0462578,0.0235297,-0.0238322,-0.0384497,-0.02889236,0.0445837,0.03201717,0.05450074,-0.13014658,-0.03845287,0.01346465,-0.03909711,0.14123785,0.00272767,0.05191438,-0.02443071,0.03985818,-0.03789595,0.01104842,-0.07150865,0.00592167,0.04976278,-0.03728822,-0.0103528,0.00104701,-0.01720329,-0.03790585,0.00433691,0.02440272,-0.03905564,0.05012908,-0.04527955,-0.01066342,0.01460805,0.00983933,0.04209685,-0.06932571,0.04946029,-0.0057496,0.08015987,0.02906331,-0.00092345,-0.06360474,-0.05074777,0.03060481,-0.02619328,-0.01811762,-0.0309554,0.05759435,-0.06194348,-0.01551839,0.02949351,0.00956438,-0.02539731,-0.00944448,-0.04934454,-0.01025985,0.09190359,0.01921359,-0.01393591,-0.05547449,-0.02684263,-0.06688821,0.03476075,0.029529,0.02390774,0.00341705,0.04398951,0.01518925,0.03695598,-0.0052428,0.00207161,-0.02722962,-0.01770338,0.06955094,-0.05654419,0.01706183,0.00237527,-0.03137393,-0.00827893,0.07543866,-0.04634473,-0.25574973,0.0327286,0.02997385,-0.04214185,-0.04633117,0.06162025,0.06896835,-0.08197806,-0.07162061,0.0059922,0.0175726,-0.0021589,0.02340559,-0.03382988,-0.013083,-0.00534407,0.08186453,-0.04330545,0.06938586,-0.04222585,0.05835968,0.00514628,0.23744923,-0.03422131,0.05658047,0.0200172,0.02303747,0.01073744,0.05900815,0.05406773,0.02359022,0.02989605,0.13267487,-0.03975916,0.00636769,-0.01022003,-0.00818742,-0.01268833,0.00988707,0.00982986,-0.01753354,-0.03022374,-0.03361796,-0.01945379,0.06968392,-0.02159943,-0.0677266,-0.05034419,-0.01717095,0.0153875,-0.06862782,0.07652047,-0.02194968,-0.00757563,0.06361134,0.012005,-0.03119545,-0.07224359,-0.0261673,0.01042772,-0.04434825,-0.06785027,-0.00239405,0.06326133,0.02576398],"last_embed":{"hash":"1hlobnn","tokens":147}}},"text":null,"length":0,"last_read":{"hash":"1hlobnn","at":1751288784539},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Generics#{2}","lines":[108,113],"size":416,"outlinks":[{"title":"length where the unit identifier is optional","target":"https://drafts.csswg.org/css-values-3/#lengths","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1hlobnn","at":1751288784539}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Generics#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05178156,-0.03521092,0.04178543,-0.06895238,-0.02523631,0.00655015,-0.01371878,-0.05079712,0.00676543,-0.00208053,0.00253778,-0.01618589,0.01736862,0.04394701,0.03632583,-0.00492302,0.0040619,0.05404921,-0.02885054,0.00869689,0.11166199,0.04865247,0.00773866,-0.04305009,0.00122729,0.02428887,0.02684866,0.00012524,0.01953712,-0.18942741,0.00418174,0.00811026,-0.02634997,0.01880823,0.01145866,-0.07385156,-0.00534339,-0.00917557,-0.04896151,0.07058486,0.01252388,0.07446082,-0.0338603,-0.02888381,-0.04829901,-0.04124774,-0.0312125,0.01702857,-0.0175021,0.00348547,-0.05450637,0.00584565,0.06774471,-0.02024895,-0.02140241,0.1042557,0.08793548,0.02186768,0.04844591,-0.00849352,-0.02159265,-0.0000494,-0.14533865,0.10932082,0.06761731,0.03714119,0.0056393,-0.00069433,0.01811976,0.02249463,0.02208397,-0.00729344,-0.00729796,0.11549012,0.04742664,-0.01292285,-0.00091545,-0.0399766,0.05043144,0.00602735,-0.03556325,-0.063279,0.00591704,-0.02478993,0.02020297,0.04041256,0.02653633,-0.01756726,0.05799327,0.0257452,-0.01590595,-0.05887696,0.04628358,0.05651612,0.00856552,-0.01572255,0.01524701,0.06882068,-0.01170194,0.13714662,-0.03199205,0.01809412,-0.02079158,0.04507282,0.04795909,0.01363168,-0.01379526,-0.0452701,-0.05364257,-0.02343473,0.00525715,-0.00909979,-0.03379241,-0.0927081,-0.03083926,0.02460356,0.01390372,0.00444488,0.05276078,0.04303147,0.02053841,0.05749774,-0.03020011,-0.02243822,0.02832462,0.00476185,0.05504647,0.05307843,-0.00509877,0.10373477,0.08032493,0.05628251,-0.05671873,-0.00234044,0.01698937,0.04588581,-0.0026714,-0.00186655,-0.01908603,-0.04243596,-0.0077923,-0.06555016,-0.01000745,-0.03240456,-0.01060706,0.10969906,-0.03703939,0.06253485,-0.02768027,0.0119054,-0.02699761,-0.00644993,-0.03409732,-0.01914387,-0.01088862,-0.01904225,0.04073149,-0.03633083,-0.03945925,-0.00698024,-0.04450442,0.08707955,-0.00654793,0.08647003,0.01671925,-0.09429027,-0.0462319,0.0280266,0.03215918,-0.08905371,-0.04570422,-0.01084781,0.01710528,0.00764663,0.09400803,0.00583729,-0.06873344,-0.09553775,0.03426047,0.03084738,0.04150702,0.02273864,-0.01581607,0.02910786,0.01712461,-0.00927708,0.02551003,-0.06599028,0.02570849,0.02253902,-0.00841566,-0.02062928,0.01684202,-0.01150821,-0.03302004,-0.0318815,-0.03214209,0.00615548,0.03539095,-0.02193158,0.13453396,0.00554609,-0.01998368,0.05495582,0.01229849,-0.02424693,-0.02501825,-0.03141173,0.0368133,0.01771005,-0.04536807,-0.01385969,0.03564204,0.0525248,0.00854733,-0.0236019,0.04581948,0.03220449,0.0206306,0.02823107,-0.0670554,-0.02105768,-0.12654731,-0.2213168,0.00874984,0.0417253,-0.02215949,0.07487974,0.00160931,-0.04154836,0.00767672,-0.04465352,0.03369748,0.10378021,0.05006612,-0.05307572,-0.08323499,-0.04534614,0.00524128,-0.04062692,-0.04535602,-0.01710542,0.02033029,0.03503897,0.0102806,-0.13127153,-0.06740461,0.04359274,-0.01960585,0.15596691,0.02354625,0.05354134,-0.02389627,0.05867679,-0.03637219,0.01294602,-0.08368586,0.01230914,0.03094213,-0.00300923,0.00668368,0.01985432,-0.02099174,-0.03332491,0.00671763,0.04199987,-0.03584498,0.05079792,-0.04070524,0.00557096,0.02737499,-0.0064703,0.0341676,-0.05098385,0.04682668,-0.02712348,0.08047403,-0.0125485,0.00632295,-0.04952278,-0.0489524,0.03119483,-0.03794795,-0.03515516,0.00416168,0.02575787,-0.02477345,0.00818973,0.01423388,0.00973017,-0.0495801,-0.04479649,-0.0460091,0.0057786,0.09101552,0.00421785,-0.02557806,-0.0519035,-0.02056074,-0.06284192,0.02425453,0.02480345,0.01098769,0.01233821,0.03839756,0.03447854,0.03081598,-0.00955999,-0.0456026,-0.03633507,-0.03747193,0.04598478,-0.0570149,-0.01031776,-0.00031256,-0.02847813,-0.01209356,0.07030594,-0.03500274,-0.25893238,0.05131499,0.03834984,-0.06746381,-0.05503046,0.04208522,0.03199774,-0.09138967,-0.08541503,0.01584471,-0.01527838,0.03511409,0.01288991,-0.02664212,-0.01665161,-0.02205033,0.12551798,-0.04400701,0.06205136,-0.0620859,0.05005603,0.00421963,0.25816819,-0.03857346,0.07485159,0.03263655,-0.00878165,0.02874319,0.08766733,0.09183308,0.03260703,0.02090385,0.11778712,-0.03658275,-0.01839223,-0.01973484,-0.02127538,-0.0019814,-0.00780534,-0.00206255,-0.02666442,-0.00461565,-0.04319213,-0.02117814,0.06656566,-0.0362092,-0.07833321,-0.07473397,-0.02713105,0.0170849,-0.06790316,0.070662,-0.01487877,-0.00613816,0.02870733,0.00374628,-0.02819415,-0.03863819,-0.01274198,0.01564921,-0.03564759,-0.09055213,0.00279592,0.0347769,0.00833609],"last_embed":{"hash":"1r36uxt","tokens":108}}},"text":null,"length":0,"last_read":{"hash":"1r36uxt","at":1751288784611},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Generics#{4}","lines":[114,120],"size":314,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1r36uxt","at":1751288784611}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Usage": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02426327,-0.02365432,0.02519834,-0.03817591,0.00866069,0.01662117,-0.0237018,-0.03109901,0.03113007,0.02128537,-0.01229269,-0.05180423,-0.01712391,-0.00025046,0.02682295,0.00297209,-0.02071908,0.11596697,-0.04735509,0.03148158,0.04027107,-0.00893712,0.03254661,-0.02924804,0.00366115,0.00285047,0.01255176,0.00813182,0.00446368,-0.18859336,-0.00555147,0.00342976,-0.00319267,0.04719099,0.00506839,-0.05807851,-0.00100706,0.01854425,-0.01633284,0.05233705,0.02208745,0.01089354,0.00551189,-0.0219368,-0.01150158,-0.01524997,0.01826472,0.02341032,-0.00923062,-0.0118874,0.00054179,-0.06108874,0.0396934,-0.01825969,0.00157351,0.11933456,0.06683117,0.0444438,0.02148339,0.07344712,0.01961138,-0.01796039,-0.14465688,0.06704515,0.04770148,0.04774591,-0.05666826,-0.00641068,0.06430328,-0.00690989,-0.00648371,0.02695666,0.02394622,0.11354154,0.02569677,-0.09080231,0.0098418,-0.03760052,0.0265862,-0.04749644,-0.05046144,-0.06133067,0.01114469,0.01493018,0.04291337,0.00952702,0.00552705,-0.04784212,0.04440103,0.02315463,0.01289795,-0.14895593,0.04284367,0.03309991,-0.02081639,-0.03888617,0.06900464,0.02082401,-0.01994569,0.11864191,-0.06939866,0.0125035,0.01182311,0.00749712,0.01366336,-0.0041444,-0.01764014,-0.06293187,-0.02755422,-0.03302776,-0.04414295,-0.0333455,-0.04833151,-0.03770237,-0.05119463,-0.01517881,-0.04227382,-0.00829175,0.03083082,0.0157825,0.03231082,0.07504595,0.04506096,-0.03048408,0.0417283,0.02052342,0.02186153,0.03511912,0.02928095,0.10462077,0.00898478,0.01521013,-0.04364368,0.00195294,0.0036577,0.02051106,0.00700277,0.02290963,0.03128741,0.00532422,-0.01293556,-0.06874885,-0.00611961,-0.0375574,-0.0158975,0.05194252,-0.07454751,0.04450734,-0.00432272,-0.01369302,-0.03996002,0.06432848,-0.05474558,-0.00595193,-0.0077256,0.03085323,0.02186334,-0.0257953,-0.04368069,0.02365938,-0.02496905,0.00354297,-0.06896207,0.05245072,0.00052188,-0.09415162,-0.03143057,0.02807323,-0.01051862,-0.07353618,-0.01582923,0.02076788,-0.0131301,0.01874534,0.1150962,0.00243785,-0.09866874,-0.05042705,0.02614276,-0.00315352,0.04178502,0.00308958,-0.06690269,0.05360344,-0.00528245,-0.04469799,0.02721557,-0.02802079,0.0047884,0.03968684,-0.03115257,-0.04337319,-0.00658684,-0.04498329,0.01256336,0.01039,-0.05690423,-0.05771046,0.03553158,0.01461129,0.1302074,0.01206099,-0.00032572,0.01005877,-0.00214026,0.03460396,0.00855393,-0.01827747,0.03262793,-0.05719187,-0.07932653,0.03531791,0.05628863,0.0559125,0.02504052,-0.04053792,-0.00294708,0.05886645,0.05842585,0.02016125,-0.04829066,-0.00466131,-0.13452975,-0.2121857,0.01391469,0.04825104,-0.05810416,0.00146003,-0.03231492,0.01199109,-0.00583066,-0.04099923,0.07485139,0.09503238,-0.01089872,-0.02806967,-0.04038851,-0.02228827,-0.00996085,0.0148646,-0.1028502,-0.05582435,0.00848457,0.01383671,0.02389812,-0.10399888,-0.08645607,0.0862326,-0.03477976,0.13879532,0.00640225,0.01844537,-0.01562758,0.03753526,-0.002653,-0.01892612,-0.06954498,-0.00238882,0.0410565,0.00084315,-0.00440951,-0.03658823,-0.01633267,0.00986248,0.02522932,0.00256481,-0.00712217,0.05120621,-0.04612411,0.00695655,-0.03887489,-0.01966169,0.06812542,-0.03211087,0.04325743,-0.02320577,0.12553908,-0.01843336,-0.00625685,0.004373,-0.02623092,0.0847156,0.01520259,-0.02093353,-0.02638897,0.03595978,-0.02374946,0.02911969,0.02690809,0.03187392,-0.03525297,0.00019241,-0.01067109,-0.06084793,0.09992655,0.00424778,0.03194854,-0.00529759,0.03194523,-0.0989345,0.09053108,0.04498789,0.03279184,0.01190235,0.03859807,0.01065667,0.02077619,0.02714303,-0.00767803,0.01977318,-0.08571761,0.02558929,-0.07744701,-0.04101527,-0.00648376,-0.03024358,0.04201192,0.07129905,-0.01670227,-0.24837649,0.02382509,0.04091848,-0.01941313,-0.03115354,0.01749731,0.04973435,-0.06943975,-0.05952216,0.00606052,-0.05900903,0.01223729,0.00275816,-0.0338706,0.00681411,-0.0384434,0.05367499,-0.02999804,0.13329758,-0.08290077,0.04577339,0.01204969,0.26720017,-0.02935753,0.07297747,0.063953,0.00392932,0.05756156,0.046423,0.11837942,0.01956708,0.00694568,0.13287143,0.00357059,-0.03501405,0.00180371,-0.043672,-0.00861105,-0.0062621,-0.00642522,-0.00725296,-0.01092339,-0.06296981,-0.00236515,0.05492094,-0.06738854,-0.05229051,-0.05565813,0.02002935,-0.01641841,-0.01773922,0.04855942,0.00574791,-0.0716112,-0.01065763,0.01294607,-0.05732664,0.06347868,-0.03030107,-0.00059635,0.01492066,-0.04382459,-0.01896843,0.05903501,0.0490547],"last_embed":{"hash":"1kw35pl","tokens":461}}},"text":null,"length":0,"last_read":{"hash":"1kw35pl","at":1751288784660},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Usage","lines":[121,184],"size":1510,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1kw35pl","at":1751288784660}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Usage#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02626072,-0.02577654,0.02305116,-0.03722266,0.00697715,0.01609032,-0.02030113,-0.03048252,0.02671971,0.01806313,-0.01225018,-0.04930545,-0.01769131,0.00061125,0.02917534,0.00508285,-0.02259023,0.11420809,-0.04704579,0.0280003,0.03899628,-0.01271846,0.03370578,-0.02962755,0.00543276,0.00139731,0.01005794,0.00759698,0.00628537,-0.18761277,-0.00389482,0.00215538,-0.00529635,0.04905851,0.00444561,-0.05779867,-0.00171241,0.01782623,-0.01538679,0.05189199,0.02021911,0.01090462,0.00682974,-0.02213827,-0.01308767,-0.01680034,0.01601979,0.0241834,-0.00739627,-0.01075611,0.00029454,-0.0614169,0.04160763,-0.0169097,0.0003357,0.11981545,0.06918992,0.04707793,0.02212987,0.07440165,0.02033934,-0.02003156,-0.14528969,0.06801119,0.0461761,0.050451,-0.05612973,-0.0052583,0.06666022,-0.01085399,-0.01100406,0.02530414,0.0206412,0.11389327,0.02367641,-0.09108465,0.00864036,-0.03982964,0.02757998,-0.05108884,-0.04780141,-0.06031635,0.01077752,0.01381727,0.04266603,0.00919411,0.00645797,-0.04579488,0.04245608,0.02051315,0.0129594,-0.14502326,0.04367758,0.03822603,-0.01720102,-0.03618845,0.07021315,0.02337077,-0.02080777,0.11720116,-0.06748782,0.01051019,0.01244818,0.00813605,0.01507893,-0.00478978,-0.01856756,-0.06305064,-0.02870819,-0.03256562,-0.04311894,-0.03347757,-0.04787242,-0.04101431,-0.05299565,-0.01665734,-0.04294866,-0.00910955,0.02481517,0.01687201,0.03094679,0.07458949,0.04634513,-0.02982656,0.03989147,0.02035123,0.02453656,0.0312981,0.02895426,0.10108753,0.00896938,0.01371617,-0.04346308,0.00340772,0.00611164,0.02092482,0.00699295,0.02351604,0.02945839,0.00763473,-0.01075534,-0.06849746,-0.00350058,-0.0355753,-0.01601536,0.05751519,-0.07452681,0.04469461,-0.00263099,-0.0149325,-0.04067796,0.06377927,-0.05537792,-0.00801466,-0.00647403,0.03020752,0.02393522,-0.02406943,-0.04325215,0.02275343,-0.02476178,0.0017508,-0.06836837,0.05246897,-0.00234302,-0.09435052,-0.03081742,0.0300248,-0.01106348,-0.07347261,-0.01848949,0.01945095,-0.01169595,0.01703765,0.11371327,0.00020361,-0.10127707,-0.05537239,0.02831903,-0.00194,0.04346547,0.00095847,-0.06711673,0.0508428,-0.00283863,-0.04459521,0.02786286,-0.02732368,0.00695238,0.04084146,-0.02993738,-0.04033427,-0.00459222,-0.0492915,0.01363731,0.01133666,-0.05790611,-0.05714346,0.03605318,0.01652939,0.13068107,0.01511982,0.00205266,0.00910378,-0.00112802,0.03398994,0.00882974,-0.01883585,0.03191212,-0.0554554,-0.07927562,0.03688958,0.05507238,0.0575669,0.02687322,-0.03944743,0.00036892,0.05833436,0.06032395,0.02153817,-0.04839332,-0.00592555,-0.13649394,-0.21042737,0.01481958,0.04749011,-0.05746612,-0.00130257,-0.03484025,0.01174766,-0.00595928,-0.04117717,0.07524177,0.09566483,-0.01099322,-0.02503932,-0.04145062,-0.01985183,-0.00957887,0.01598776,-0.10156342,-0.05376345,0.00723875,0.01192709,0.02036562,-0.10591241,-0.08716302,0.08443655,-0.03079115,0.13838819,0.00644568,0.01960488,-0.01658748,0.03679874,-0.00550562,-0.01807279,-0.07220607,-0.00436378,0.03857302,0.00072982,-0.00819777,-0.03405637,-0.01753182,0.00693509,0.02592224,0.00386405,-0.00750392,0.05455496,-0.04835816,0.00616435,-0.04217178,-0.02181063,0.07063712,-0.02928383,0.04536727,-0.02285172,0.12265819,-0.02121588,-0.00602263,0.00342475,-0.02277213,0.08482763,0.01539012,-0.02147447,-0.0234079,0.03467163,-0.0234999,0.02708013,0.02674204,0.0297866,-0.03153686,-0.0006463,-0.01090912,-0.06154531,0.10018514,0.00252973,0.03606359,-0.00574725,0.0305385,-0.10076199,0.09012897,0.04366735,0.03040286,0.01005048,0.03751469,0.0089638,0.02401242,0.02687806,-0.00652632,0.02198138,-0.08422584,0.02735175,-0.08047687,-0.04214371,-0.0055296,-0.03004852,0.04423833,0.072519,-0.01609195,-0.24793263,0.02584302,0.0419421,-0.02107618,-0.03068257,0.01674446,0.05280111,-0.07047915,-0.05890286,0.00460376,-0.05776681,0.00801451,0.00528637,-0.02777506,0.00741911,-0.04024406,0.05447049,-0.03048945,0.13171963,-0.08316663,0.04617681,0.01090823,0.26601461,-0.03021127,0.07242564,0.06405313,0.00517126,0.05883012,0.04710049,0.11756262,0.02073017,0.00586547,0.13345204,0.0039864,-0.03153824,0.00070865,-0.0461803,-0.00775508,-0.00712966,-0.00699366,-0.00712657,-0.01120386,-0.06560848,-0.00461622,0.05921602,-0.06641819,-0.05143296,-0.05476826,0.02081575,-0.01463608,-0.01954763,0.04920248,0.00252324,-0.07316679,-0.00789621,0.01282575,-0.05521642,0.06773443,-0.03060635,-0.0005633,0.01627293,-0.0438198,-0.02295121,0.05943125,0.05049433],"last_embed":{"hash":"1ca7ssh","tokens":462}}},"text":null,"length":0,"last_read":{"hash":"1ca7ssh","at":1751288784920},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Usage#{1}","lines":[123,184],"size":1500,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1ca7ssh","at":1751288784920}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#What should I do when I get type errors?": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05346244,-0.04823305,0.0520705,-0.041871,-0.00633726,-0.00386644,-0.06922759,-0.040945,-0.01894342,0.00558746,0.06256272,-0.00809215,-0.02682365,0.04687682,0.03381039,0.0547984,0.03297361,0.04212857,-0.0347676,0.04611835,0.02673906,0.04462761,0.00613498,-0.00205638,-0.01033628,0.01701265,0.03250586,-0.04065472,0.00037393,-0.23139746,-0.00780103,0.04174631,-0.04737672,-0.0078287,0.04356904,-0.08114103,-0.03793214,0.00491973,-0.03572322,0.04172959,0.04313647,0.01389191,-0.00871018,-0.0413161,-0.03384233,-0.01706458,-0.01307757,0.02447149,-0.00178075,-0.0623388,-0.06236545,-0.04174629,0.05587976,-0.00379824,-0.0027487,0.09890005,0.10158756,0.04403994,0.05016457,0.02404372,0.02253107,0.03082657,-0.15319394,0.09518042,0.03287384,0.04452119,-0.03778971,-0.04020852,0.01988139,0.00131409,0.01794956,-0.00097904,-0.03852864,0.08241363,0.04497522,-0.01276607,0.05279259,0.00032698,0.04316816,-0.00493491,-0.07994264,-0.03714217,0.00839558,-0.03159551,0.01549768,-0.01923813,0.0302369,0.0243516,0.05587252,-0.0111624,0.02957306,-0.090941,0.07600918,0.05369406,0.01375666,0.0003481,0.02357333,-0.00506485,-0.0425389,0.13011129,-0.01487926,-0.00219656,0.00681402,0.03853754,0.0431401,0.03134562,-0.02123505,-0.03471655,-0.02088702,-0.03966602,0.00818899,-0.02066133,-0.02512608,-0.06507642,-0.07327173,-0.00965755,-0.01307359,0.02428159,0.04797865,0.00511509,0.01342193,0.081604,0.02295219,-0.02341632,-0.02461945,0.00536779,0.01348649,0.04849396,-0.01077435,0.09307253,0.01481217,0.08138904,-0.0679848,0.00174628,0.0091008,0.00055763,-0.03078564,-0.00016008,0.01574484,-0.03345366,0.01373305,-0.05244656,0.00657845,-0.01924396,-0.03142219,0.06567863,-0.10480975,0.06857052,-0.0124616,-0.01556702,-0.04318294,0.03092813,-0.10815383,0.01144674,0.01238131,0.00218307,0.053217,-0.03389335,-0.04896145,0.01690727,0.00671197,0.017736,-0.04121864,0.02313931,-0.0032945,-0.08302782,-0.05274571,0.01270337,0.01312649,-0.1019251,-0.03325548,-0.00541575,-0.01828279,0.01545468,0.06621026,0.01550217,-0.07205651,-0.05368596,0.02061131,0.03765382,0.04762096,0.03954222,-0.03479329,0.04955488,0.02006911,-0.0779172,0.04890355,-0.06936368,0.05060643,0.02411416,0.01169687,-0.03952079,-0.04133233,-0.02155708,0.00853785,0.02198382,-0.05327952,-0.03345622,0.04438874,-0.06291931,0.09506767,0.02078335,0.00519083,0.00434686,-0.04379556,0.03042602,-0.00787004,-0.03754639,0.03505986,-0.00591268,-0.09545399,0.00805949,0.06573832,0.05430445,0.00292253,-0.017315,0.0379732,0.08169082,0.01875284,0.03151456,-0.04311227,0.0355703,-0.10941584,-0.20513578,-0.02614928,0.05072058,-0.06940106,-0.01479758,-0.01045886,0.01737588,0.04419471,-0.07244139,0.1115054,0.13055646,0.03317184,-0.03194595,-0.05398225,-0.01042837,-0.01552232,-0.01556648,-0.08292092,-0.03694382,-0.01280792,-0.00988258,-0.002517,-0.0870351,-0.07140831,0.06227707,-0.04209569,0.12306575,0.01343086,0.05172558,-0.0355706,0.03638712,0.00014233,0.06391723,-0.13537964,0.07757515,0.07108845,-0.00269265,-0.00944466,0.03623032,-0.0344893,-0.02672732,0.00747841,0.02717406,0.01973513,0.04087636,-0.04422176,-0.01627995,0.03316181,-0.02463671,0.08802945,-0.03630714,0.05005749,-0.01008609,0.13193941,-0.0207292,0.02447675,-0.08161766,-0.02947839,0.06767046,-0.00122679,0.01309951,-0.02195214,0.03856491,-0.04874232,0.05583598,0.00652436,0.01412494,-0.06132517,0.03690331,-0.03122211,-0.00785342,0.12812334,-0.00684878,-0.01235214,-0.03896172,0.03325427,-0.056459,0.02856764,0.04861109,-0.03482377,-0.01037186,-0.01811742,0.05421342,0.04666828,-0.02984909,0.01630109,-0.02599827,-0.04935146,0.0383367,-0.06866524,-0.02023009,0.02794817,-0.0590663,0.00899282,0.12396308,0.02927302,-0.20223683,-0.00191691,0.07964458,-0.03276343,-0.03505671,0.01519997,0.00030753,-0.07452352,-0.04503637,-0.01002801,-0.01889426,-0.01331436,0.00881843,-0.03107954,-0.00758561,-0.03364086,0.03337216,-0.0289629,0.10324813,-0.07178671,0.04339371,0.0206236,0.25411832,-0.0138269,0.01861698,0.04550113,0.00984711,0.01378649,0.05190222,0.05713434,0.02958936,0.01938986,0.10401752,-0.01805831,-0.00884331,0.01010022,0.01806842,-0.03815791,0.01360701,0.00170765,-0.01915398,-0.0088151,-0.02953305,-0.00955086,0.06838804,-0.04429096,-0.06507932,-0.02874506,0.02012409,0.00264579,-0.03469987,0.03124878,-0.05527323,-0.02373154,-0.01501356,-0.02756014,-0.01653341,-0.00758043,-0.02700067,0.03699677,-0.00151956,-0.06404269,-0.00678946,0.05178021,-0.00046979],"last_embed":{"hash":"1c1smfe","tokens":449}}},"text":null,"length":0,"last_read":{"hash":"1c1smfe","at":1751288785158},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#What should I do when I get type errors?","lines":[185,250],"size":2566,"outlinks":[{"title":"type widening","target":"https://blog.mariusschulz.com/2017/02/04/TypeScript-2-1-literal-type-widening","line":10},{"title":"issues","target":"https://github.com/frenic/csstype/issues","line":12}],"class_name":"SmartBlock","last_embed":{"hash":"1c1smfe","at":1751288785158}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#What should I do when I get type errors?#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03830667,-0.04936016,0.06777085,-0.04988258,0.01213644,-0.00776285,-0.0597097,-0.04934873,-0.02575385,0.00614689,0.05115088,-0.00301486,-0.03584561,0.03472449,0.02585,0.03965224,0.03588185,0.05079499,-0.01369075,0.06291234,0.02013444,0.05806421,-0.00201882,0.00647485,-0.005477,0.0142956,0.03521781,-0.03719502,-0.00029414,-0.21658982,0.0060529,0.05699654,-0.04623916,-0.00667697,0.04808067,-0.09432942,-0.02849337,0.00012428,-0.03624059,0.04270811,0.05004963,0.02611253,-0.00530906,-0.04466201,-0.01171373,0.01657699,0.00637142,0.02264649,0.01351151,-0.06397264,-0.05965147,-0.02548972,0.05764762,0.00474493,-0.00102938,0.08970665,0.11136999,0.03714167,0.03226485,0.02184863,0.01319409,0.03673174,-0.15240462,0.06610103,0.03292632,0.04137354,-0.03680668,-0.03667394,0.00618517,0.04379316,0.00466164,-0.00332225,-0.0457082,0.07485716,0.05941571,0.00042444,0.04758686,-0.00541443,0.00902648,0.0186159,-0.09600874,-0.04528712,0.01786753,-0.02786794,-0.00440219,-0.01069298,0.04955379,0.01593812,0.06299849,-0.01455245,0.01659697,-0.07641398,0.0598636,0.06597959,0.02622477,0.0125543,0.03681058,-0.01125749,-0.03022841,0.15916322,-0.02596627,-0.00605763,0.01079081,0.0344013,0.03370233,0.03611937,-0.01928895,-0.02741636,-0.02056283,-0.02786525,0.02503851,-0.00538414,-0.032422,-0.07349972,-0.0689337,-0.01687207,-0.0070957,0.04404277,0.05631875,0.00148035,0.01303709,0.10006555,0.03196871,-0.03254085,-0.01022926,-0.01252614,0.00701279,0.0506188,-0.03902462,0.09157888,0.00492467,0.07081698,-0.0971026,0.00235063,0.01202461,-0.01114738,-0.01357092,-0.00636621,0.01793535,-0.04420375,0.00887555,-0.04058253,0.01986545,-0.04450589,-0.03003287,0.07432728,-0.10252777,0.08703979,-0.01564283,-0.01786374,-0.05245214,0.01971615,-0.08680061,-0.00238003,-0.00142773,-0.01022429,0.04975283,-0.07481278,-0.06230014,0.01312376,0.01181373,0.02379887,-0.04933466,0.04076322,-0.00203144,-0.057346,-0.03685348,0.00950559,0.02667409,-0.08613398,-0.03585533,0.02148855,-0.02509741,0.00079896,0.06474069,0.02825226,-0.0749941,-0.05322042,0.00147014,0.02570337,0.06157074,0.04399294,-0.03019889,0.05839912,0.0130017,-0.08069879,0.04513915,-0.04591872,0.05898505,0.01292426,0.01955481,-0.02981951,-0.03751327,-0.00675966,-0.01048214,0.01957502,-0.05231734,-0.02960506,0.03305377,-0.06447123,0.06036429,0.01997351,-0.0107995,0.00810926,-0.03775973,0.03305757,-0.01293573,-0.03809077,0.02383976,0.00905447,-0.11728044,0.00660439,0.06433246,0.04457537,-0.01665931,-0.01923722,0.03304673,0.08228925,-0.01327024,0.02326026,-0.03093375,0.04205219,-0.09366649,-0.19953927,-0.04226736,0.03024705,-0.06596181,-0.00307342,0.03225103,0.00614927,0.04704046,-0.07235324,0.11338579,0.11877569,0.01708468,-0.0401747,-0.03710322,-0.01888898,-0.00535548,-0.03258186,-0.09335995,-0.05741199,-0.00982548,-0.00327278,-0.00775655,-0.07314517,-0.08704967,0.0704852,-0.02504722,0.11315667,0.00515546,0.06080221,-0.02536854,0.03660638,-0.00748208,0.04570429,-0.1414734,0.08287459,0.07792226,-0.00173069,-0.03964385,0.02391415,-0.03195203,-0.00975371,-0.01015366,0.02175752,0.02091615,0.03931357,-0.03490176,-0.03203441,0.03842334,-0.04228402,0.08665335,-0.05642636,0.03577689,-0.02587465,0.11743547,-0.0283269,0.03362083,-0.08794501,-0.02408183,0.04113577,0.00129735,0.00989579,-0.02145227,0.02053466,-0.04656586,0.074856,0.00702725,0.00919461,-0.0645787,0.03759767,-0.02627872,0.00247932,0.12487198,-0.0115946,-0.05885038,-0.01917958,0.02275562,-0.06977629,0.0276462,0.04715224,-0.03833101,0.00125684,-0.02861128,0.03461751,0.04445326,-0.02013531,-0.00460027,-0.02149014,-0.03918892,0.02023926,-0.08631254,-0.01139591,0.0384833,-0.06855939,0.00003419,0.1321252,0.02500049,-0.19382907,0.02314349,0.05525667,-0.01468934,-0.03285307,-0.00545187,0.01348232,-0.07280263,-0.03149994,0.013332,0.00455082,-0.00154241,-0.00461407,-0.03325105,-0.00510458,-0.02885356,0.04269104,-0.03214728,0.10102206,-0.0692607,0.03867271,0.0256094,0.26032931,-0.02134107,0.03614336,0.0255194,0.01393021,0.02890572,0.03392086,0.07126325,0.04601201,0.00453879,0.08530252,0.00098547,-0.00952153,0.03364245,0.02382366,-0.02458391,0.00728367,0.00787614,-0.01038338,-0.01259809,-0.03733487,-0.01511323,0.080286,-0.05617583,-0.0802009,-0.02867346,0.0137848,0.0193498,-0.03342712,0.03414356,-0.04985429,-0.04001423,-0.01455102,-0.01829121,-0.02663039,0.00203312,-0.04815675,0.04979352,0.02186996,-0.03108557,-0.00709824,0.03956899,-0.00577645],"last_embed":{"hash":"tf6vg4","tokens":116}}},"text":null,"length":0,"last_read":{"hash":"tf6vg4","at":1751288785395},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#What should I do when I get type errors?#{1}","lines":[187,190],"size":401,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"tf6vg4","at":1751288785395}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#What should I do when I get type errors?#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05299926,-0.02172992,0.04570442,-0.03758867,-0.02653459,-0.01147639,-0.0512926,-0.01104285,-0.03826471,0.00082477,0.0394565,-0.01569546,0.00251467,0.03620755,0.03453877,0.0601904,0.00699152,0.01759074,-0.02660475,0.03684823,0.06066633,0.04263055,-0.00323186,-0.01419365,-0.00291238,0.04027173,0.05915143,-0.0656264,0.00741834,-0.20700516,-0.03476818,0.02214711,-0.05111651,-0.01701331,0.04230172,-0.05293269,-0.01154395,-0.03081981,-0.0285357,0.04736751,0.02438732,0.02640737,-0.01470519,-0.04449999,-0.01564925,-0.03682888,-0.04962179,0.0249959,-0.00485888,-0.06337197,-0.06385487,-0.01515763,0.05821495,-0.0104799,-0.01694353,0.0935628,0.08190539,0.0632569,0.03657855,0.01909977,0.02555088,0.00517868,-0.15808967,0.08372317,0.01779221,0.04532637,-0.03210258,-0.04717967,0.00366158,0.01036132,0.04861784,-0.02502792,-0.05095447,0.09378394,0.02932296,-0.0159565,0.03561011,-0.02073042,0.0489715,0.01109168,-0.09256727,-0.06340571,-0.0150535,-0.00915056,0.01711071,-0.04039579,0.00053871,0.04653576,0.04167807,0.0067965,0.04057455,-0.08498217,0.05070755,0.05803598,0.00078278,-0.01033273,0.03430267,0.01374436,-0.05420459,0.13934353,-0.03086942,0.00882278,-0.01688765,0.05481495,0.0441756,0.01458078,-0.01725551,-0.04924206,0.00730575,-0.0354451,-0.01187129,-0.01304746,-0.00530001,-0.05484642,-0.06111302,0.0087593,0.01062898,0.02191653,0.01215142,0.00989089,0.00056361,0.05280343,0.02216473,-0.01055849,-0.04620382,-0.02039379,0.02348865,0.05409731,0.02771454,0.06318843,0.04320483,0.03694148,-0.03655858,-0.01291182,0.02808261,0.00506465,-0.02884821,0.01878848,0.00895118,-0.02214661,0.02483427,-0.08812031,0.01036312,-0.00074926,-0.04228085,0.07444672,-0.09841985,0.05636535,-0.01978455,-0.01036777,-0.03654892,0.01367162,-0.10973007,0.0068684,0.0268454,-0.02595928,0.04547849,-0.00366391,-0.06865051,0.01238013,-0.00360271,0.01353052,-0.06559832,0.00714964,0.00938845,-0.07127893,-0.04951319,0.00854776,0.00430076,-0.09872355,-0.02182659,-0.01435982,-0.04583022,-0.0195098,0.05543301,0.01207839,-0.03629113,-0.06225088,0.00664181,0.05230227,0.00998945,0.03520993,-0.04148976,0.02508379,0.04721499,-0.052316,0.01704879,-0.09358329,0.03007823,0.02462329,-0.01769201,-0.05662356,-0.0651844,-0.0419427,0.0035965,0.00732233,-0.03486244,-0.01357776,0.04086699,-0.06014449,0.09924049,0.03102505,0.02447942,0.03314994,-0.04438927,0.01923296,0.00158766,-0.02898556,0.03733819,0.00612867,-0.07683918,-0.00632584,0.08102132,0.04953203,-0.00013366,-0.00265149,0.06587037,0.08075977,0.02149756,0.02841073,-0.06113626,0.03537979,-0.10837634,-0.22679064,-0.01412098,0.07136083,-0.0727863,-0.01770439,-0.02177471,0.03717192,0.03240693,-0.06962781,0.10375832,0.11905608,0.06845566,-0.03659329,-0.07484339,0.00522851,-0.02424356,-0.01797179,-0.0721928,-0.0104796,0.00828002,-0.00399078,-0.01820125,-0.09371598,-0.05413921,0.04103577,-0.03617948,0.13185173,0.0542541,0.05306542,-0.04357847,0.01676897,0.01157472,0.07943641,-0.13795094,0.06298938,0.04121354,-0.01677286,-0.00360369,0.04801265,-0.02642539,-0.06428146,0.00038163,0.01188824,0.01025737,0.0413393,-0.04240841,0.00309386,0.02921571,0.01617719,0.09191164,-0.01105226,0.06283615,0.0083469,0.148324,0.00810948,0.01196942,-0.10324051,-0.03843455,0.06336661,-0.01634597,0.02459447,-0.00643137,0.03155805,-0.04133932,0.02426928,0.01944735,-0.02357637,-0.04819003,0.02458373,-0.02516298,0.01061794,0.14691086,-0.00100312,-0.00354916,-0.04185882,0.03870927,-0.03756702,-0.00124615,0.06238256,-0.0329423,0.01228703,-0.04075143,0.05809737,0.04118683,-0.02197452,0.0450975,-0.01757961,-0.05201913,0.03627049,-0.07253749,-0.00522796,0.01063857,-0.05866287,-0.02323152,0.09290989,0.02040782,-0.207836,0.00287072,0.11388119,-0.045815,-0.03428752,0.01900986,0.01800243,-0.08826514,-0.0636399,-0.01322366,0.02251579,0.01191744,0.0074459,-0.01971533,-0.02496307,0.01011019,0.05875111,-0.02438475,0.10675655,-0.08248816,0.06675697,0.03452687,0.2236924,-0.00028788,-0.00206866,0.03230923,0.00430673,-0.00891591,0.06904937,0.02871565,0.00808257,0.02445345,0.10498572,-0.05732411,0.01595511,0.00511017,0.03194616,-0.02879464,0.0409734,-0.00678914,-0.00382498,0.02611082,-0.04218445,-0.01278131,0.06676022,-0.01529108,-0.04438256,-0.02416819,0.02710396,-0.01207643,-0.0475837,0.022433,-0.02000134,0.03232929,-0.02043913,-0.01068616,-0.00586654,-0.02280574,-0.03539505,0.0318802,-0.00511023,-0.04381216,0.01945491,0.08303303,0.02096949],"last_embed":{"hash":"17k06qk","tokens":163}}},"text":null,"length":0,"last_read":{"hash":"17k06qk","at":1751288785448},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#What should I do when I get type errors?#{2}","lines":[191,195],"size":485,"outlinks":[{"title":"type widening","target":"https://blog.mariusschulz.com/2017/02/04/TypeScript-2-1-literal-type-widening","line":4}],"class_name":"SmartBlock","last_embed":{"hash":"17k06qk","at":1751288785448}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#What should I do when I get type errors?#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05941321,-0.06557135,0.06117518,-0.05979156,0.00465938,-0.00392634,-0.066996,-0.00859429,-0.02758769,0.01011268,0.05984588,-0.00995476,-0.00593793,0.03160939,0.03326761,0.05527687,0.01124317,0.02000329,0.00956945,0.00147951,0.01764492,0.02281849,0.02101084,-0.01265854,-0.02529349,0.04937203,0.02325324,-0.04411345,0.00206428,-0.20806904,-0.01697521,0.07750534,-0.0320311,-0.00799872,0.06223807,-0.0561383,-0.00255886,0.00478472,-0.02934442,0.03123147,0.03573868,0.04175482,-0.02051033,-0.06442998,0.00964609,0.01740759,-0.0051762,-0.01075681,0.03563983,-0.08679101,-0.02488109,-0.04081241,0.04360817,-0.02068374,-0.01246247,0.07854268,0.09715401,0.02563923,0.04676628,0.02823143,0.01044594,0.03474299,-0.15533437,0.05740483,0.05159446,0.02381228,-0.04094583,-0.02881292,-0.00683693,0.03711431,0.01521361,-0.01549447,-0.04158928,0.10189609,0.05652644,-0.03445463,0.00818643,-0.01040623,-0.00601665,0.02552299,-0.09903313,0.00485604,0.01394787,-0.0066352,-0.00635298,-0.04858001,0.03040353,0.06011687,0.04268887,-0.00027579,0.04198838,-0.04907922,0.08305495,0.10122883,-0.00398141,0.00617645,0.05318974,0.00460178,-0.05821679,0.14571097,-0.02380058,-0.00804194,0.04873645,0.04364725,0.01619025,0.01894949,-0.02684368,-0.03521705,-0.02414102,-0.02642948,0.02316138,-0.00049075,-0.02188137,-0.08544935,-0.03849497,-0.00431721,0.02284521,0.02351204,-0.01210292,0.0431105,-0.00322339,0.07183213,0.04975298,-0.01326749,-0.00363221,-0.02813807,0.01311893,0.05558561,-0.0078696,0.05125501,0.04621393,0.07053547,-0.06570404,-0.03185571,0.02121109,-0.0001558,-0.01612824,-0.03500364,0.00518273,-0.0589657,0.03614598,-0.09933712,0.01060002,-0.05179299,-0.06008665,0.08279612,-0.07907852,0.06962951,-0.01449749,-0.01190701,-0.03491047,0.01328667,-0.08344724,-0.0007669,0.04390002,-0.02008116,0.06003036,-0.03922738,-0.072125,0.02754793,-0.00707479,-0.02176155,-0.05936212,0.05967868,0.02088915,-0.0531644,-0.05414817,0.01449815,0.0057707,-0.07387666,-0.02724191,0.0129421,-0.01110171,-0.00262768,0.07257771,0.01490784,-0.0287312,-0.06657391,0.01019973,0.02769963,0.07141408,0.05719782,-0.01432265,0.05082797,0.00860779,-0.07635007,0.0419632,-0.06563823,0.03522024,0.00670409,-0.03137552,-0.05268615,-0.04062852,0.00019266,-0.05378843,-0.00792218,-0.04201528,-0.04306726,0.03149798,-0.08047161,0.07654531,0.00575226,-0.00595375,0.03155264,-0.05717237,0.00434089,-0.04852309,-0.03390161,0.02707721,0.00673594,-0.09645686,0.02372325,0.08116401,0.03918625,-0.00949825,-0.01703751,0.04021095,0.07179247,0.0211902,0.02559597,-0.0159997,0.01675901,-0.0813788,-0.22057007,-0.03447426,0.04092528,-0.02800152,-0.02318127,0.0283207,0.01891938,0.00535135,-0.06887623,0.13234998,0.12729642,0.05980224,-0.03214932,-0.07009989,-0.00030871,-0.02146228,-0.02932336,-0.09137363,-0.05288578,-0.00911105,-0.00118731,-0.03655098,-0.07014957,-0.05444481,0.05438854,-0.04535361,0.11556212,0.08924172,0.03152972,-0.03652585,0.02308797,0.00852058,0.03779395,-0.13903153,0.04780465,0.04446266,-0.00430643,-0.00127454,0.00516232,-0.03244425,-0.03219729,-0.03337569,0.00483836,0.01932511,0.04651192,-0.01089758,-0.01688731,0.01596311,0.00099779,0.07076176,-0.04488968,0.05510686,-0.01779958,0.12749287,-0.03555863,0.02346481,-0.10935502,-0.04132211,0.04280392,-0.01510273,0.03229312,-0.00843547,0.04043883,-0.06308194,0.06941024,0.04686112,-0.0316511,-0.04312145,0.04088893,-0.02055289,0.01627984,0.11813628,0.0013123,-0.02327664,-0.02291384,-0.00753592,-0.02145956,0.00383501,0.05311331,-0.02323889,0.011917,-0.03570678,0.05761201,0.02685097,-0.01754747,0.03347693,-0.04596201,-0.06985047,0.05479461,-0.11056114,-0.02224388,0.02798049,-0.06241773,-0.00866422,0.09249119,0.01624198,-0.19875424,0.03465519,0.09637903,-0.01508878,-0.04983756,0.00086777,0.00661266,-0.06984124,-0.00762248,-0.00629938,0.02493997,0.01712372,-0.00106459,-0.03701074,-0.0212305,0.0097375,0.04787828,-0.02725379,0.10112795,-0.07539606,0.05088707,0.03548247,0.2440529,-0.03181808,0.02581361,0.01634454,0.00968091,0.03256455,0.05230893,0.07537157,0.01930015,0.00830481,0.09426286,-0.01510433,-0.01853544,0.02291774,0.02666255,-0.00293371,0.01877413,-0.01086376,-0.02635743,0.0288992,-0.04224036,0.00849336,0.05163402,-0.07383671,-0.05427925,-0.02745734,0.01129794,-0.00669285,-0.04158484,0.00974139,-0.01700955,0.01917433,-0.02556258,-0.02486866,-0.0111559,-0.01498895,-0.04445437,0.02888996,0.02817571,-0.03964059,0.04009573,0.05489912,0.01140815],"last_embed":{"hash":"115l8dr","tokens":98}}},"text":null,"length":0,"last_read":{"hash":"115l8dr","at":1751288785528},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#What should I do when I get type errors?#{3}","lines":[196,196],"size":206,"outlinks":[{"title":"issues","target":"https://github.com/frenic/csstype/issues","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"115l8dr","at":1751288785528}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#What should I do when I get type errors?#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03560084,-0.05717637,0.02950351,-0.04625257,-0.01612447,0.01402146,-0.05652229,-0.04421386,-0.01741477,-0.00136027,0.03823109,-0.02615469,-0.02596877,0.0435684,0.03998506,0.06553277,0.00747042,0.09162737,-0.06266321,0.01137546,0.02463197,0.02978366,0.01867327,-0.02259635,-0.00741124,0.01762366,0.00405723,-0.01832624,0.01769082,-0.21652639,-0.00980194,-0.00547544,-0.04484407,-0.00110254,0.02840371,-0.0629898,-0.04675442,0.02875331,-0.05421644,0.03328337,0.04317799,0.01940796,-0.01702905,-0.0190666,-0.06000542,-0.02810204,0.01211237,0.00686805,-0.00743575,-0.06178377,-0.01873027,-0.06319208,0.04036226,-0.03591424,-0.0030685,0.10796594,0.09994829,0.03991012,0.05587147,0.04848094,0.0209416,0.03064836,-0.15142241,0.10452664,0.05646598,0.02999455,-0.03689461,-0.00897668,0.03484786,-0.00755196,0.01201136,0.02054187,-0.0101381,0.10845977,0.01553129,-0.04758592,0.04476171,-0.0174308,0.04766334,-0.03799763,-0.06299769,-0.03623164,0.01531509,-0.02558466,0.03031323,-0.01200488,0.00576987,0.01131163,0.02754772,0.01593437,0.02036165,-0.09716513,0.06822493,0.05068456,0.00257546,-0.00116558,0.02143747,-0.00780904,-0.04018431,0.1167973,-0.02916532,-0.00844927,0.00227452,0.04629568,0.0382731,0.01647475,-0.01361162,-0.04440633,-0.03176812,-0.03740814,0.02594537,-0.03153277,-0.05614009,-0.05822795,-0.04953074,0.02083046,-0.03272921,0.00721682,0.02093696,0.02212063,0.02616195,0.05615038,0.03291541,-0.01713871,-0.01889851,0.01058371,0.02401238,0.02652628,-0.007465,0.10054575,-0.00484546,0.07699799,-0.05030268,0.00800691,-0.00892694,-0.00740276,-0.0216113,-0.02437635,0.00608254,-0.04454766,-0.00124066,-0.03027778,-0.01658048,-0.03453153,-0.01164917,0.04254529,-0.07429565,0.07948297,-0.00207656,-0.02696273,-0.03273097,0.03511369,-0.1015344,0.039263,-0.00976399,0.0176224,0.03516328,-0.01542828,-0.02555117,0.03464841,-0.00776582,0.00604941,-0.03676087,0.03555845,-0.0020541,-0.08949976,-0.06880696,0.01119743,-0.00549294,-0.10021878,-0.03934081,-0.0155216,0.0027554,0.03085034,0.08808875,0.00743121,-0.08426017,-0.0448704,0.03179417,0.04274828,0.06836656,0.01565246,-0.02118909,0.05794121,0.0100362,-0.04688992,0.06481407,-0.05422542,0.02933902,0.01851322,-0.00142358,-0.02055451,-0.00473871,-0.02882864,0.01430048,0.0295328,-0.04918215,-0.06966224,0.04153393,-0.03179663,0.13945949,0.01342275,0.01258677,0.01538132,-0.05500365,0.0468719,-0.02727222,-0.01849692,0.028792,-0.02628979,-0.08905652,0.00388015,0.06975337,0.09042502,0.02239392,-0.03296073,0.04556295,0.0556904,0.05339122,0.04110331,-0.03223491,0.01908584,-0.11885019,-0.21882398,-0.03358089,0.03603064,-0.04129713,-0.0106612,-0.04238679,0.01555697,0.04619363,-0.07777251,0.09982267,0.13363287,0.01351751,-0.00149638,-0.04591592,0.00921886,0.0059876,-0.01364984,-0.07944917,-0.03742418,0.00782717,-0.02003361,0.00522479,-0.102923,-0.07334715,0.07168514,-0.05910026,0.1377636,0.02316679,0.05526529,-0.06326946,0.04749728,-0.00228414,0.03624472,-0.12040108,0.03817136,0.03898177,0.00881081,0.01403562,0.04115513,-0.02885983,-0.02032456,0.02348855,0.02549689,0.02497823,0.04997166,-0.05232158,0.01608742,0.01565571,-0.02148572,0.06595097,-0.01740707,0.05912688,0.00570826,0.11935732,-0.03397137,0.01791921,-0.0640174,-0.03837954,0.05547749,0.01038953,0.00724395,-0.01781008,0.04952428,-0.06200146,0.05577416,0.02606916,0.03065643,-0.0575801,0.06095824,-0.01975702,-0.02618892,0.09896251,0.00331086,0.01609565,-0.03813465,0.03100119,-0.04741202,0.05651776,0.04316871,-0.01270897,-0.00804599,0.00200497,0.04637311,0.03516098,-0.03480044,0.01150529,-0.03951696,-0.06895254,0.06677959,-0.05726954,-0.04846996,0.01962298,-0.04556573,0.0406655,0.09750897,0.02164484,-0.21544723,-0.01184351,0.05940523,-0.0186125,-0.06241369,0.0165721,0.00302544,-0.07443669,-0.04305646,-0.02268458,-0.03860779,-0.01734813,0.02367574,-0.03941411,-0.00799341,-0.04468469,0.03883798,-0.01776888,0.10761181,-0.07571827,0.02931259,0.00680954,0.25315177,-0.02628816,0.02783374,0.04605523,0.00230081,0.02706611,0.0608122,0.07297716,0.02854579,0.03251837,0.12470836,-0.00597329,-0.0034595,-0.01492283,0.02599251,-0.00690358,0.01674125,-0.00555991,-0.04483075,0.00882012,-0.01664707,0.00692019,0.02540072,-0.04800241,-0.07089327,-0.05462859,0.01856761,-0.01801555,-0.03672036,0.02935449,-0.03893714,-0.04155521,-0.00542645,0.00868396,-0.0212673,0.00186465,-0.03500455,0.04025283,-0.01496221,-0.07373466,-0.00129305,0.02392663,-0.01100948],"last_embed":{"hash":"4d0c3f","tokens":421}}},"text":null,"length":0,"last_read":{"hash":"4d0c3f","at":1751288785586},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#What should I do when I get type errors?#{4}","lines":[197,250],"size":1426,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"4d0c3f","at":1751288785586}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#What should I do when I get type errors?#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03723689,-0.05319281,0.02639577,-0.04352355,-0.00719862,0.0095153,-0.05822766,-0.05044145,-0.01252346,-0.00554424,0.04217595,-0.03665291,-0.02410163,0.04295849,0.02687158,0.05773962,0.01727838,0.0804921,-0.06335379,0.03384918,0.02632146,0.03933485,0.02287966,-0.0292591,-0.0182601,0.01443167,0.00194603,-0.01060983,0.01578436,-0.20973219,-0.00195668,-0.01699569,-0.03186884,-0.00584663,0.02892292,-0.07210578,-0.04910994,0.02268827,-0.05442267,0.03746508,0.05653286,0.00962238,-0.00582276,-0.01276079,-0.0540198,-0.02467698,0.01897401,0.01719419,-0.0022282,-0.07628693,-0.02729166,-0.05904007,0.0414609,-0.03876683,-0.00558516,0.10969984,0.08397249,0.03741276,0.05211489,0.04464158,0.01729093,0.02683848,-0.14939384,0.09113905,0.05293416,0.0350496,-0.03669834,-0.00320253,0.03307921,-0.00448353,0.02715194,0.02483324,-0.002564,0.11811526,0.01087115,-0.05067588,0.04089553,-0.01100173,0.03739711,-0.02950247,-0.07248032,-0.04202325,0.02439079,-0.02217743,0.032832,0.00701979,0.0113143,0.0125065,0.03328184,0.02813692,0.02568352,-0.10722309,0.0672387,0.04393505,-0.00864719,0.00457497,0.03157523,-0.01076452,-0.03784332,0.12680291,-0.03069603,-0.01581534,0.00832047,0.04156855,0.02809094,0.01994327,0.0005539,-0.03488977,-0.03762016,-0.04402519,0.03629866,-0.0240281,-0.05022814,-0.06646831,-0.0541196,0.01558341,-0.03564641,0.02343301,0.02850509,0.00868994,0.02395759,0.05768809,0.0330462,-0.02655376,-0.02005143,0.01047122,0.01629175,0.03445741,-0.00417919,0.11675813,-0.01012377,0.07116952,-0.05000559,0.00968113,-0.00391514,-0.0019911,-0.02790378,-0.01561318,0.0056551,-0.04547403,-0.00069617,-0.04713921,-0.01492177,-0.01440251,-0.02258882,0.02104434,-0.07360013,0.07667357,0.00163756,-0.02270669,-0.04151525,0.03874777,-0.09053156,0.03544228,-0.01047014,0.0276262,0.03690087,-0.02643495,-0.02376763,0.03914901,-0.00488479,0.00337734,-0.03528392,0.0480915,-0.00371233,-0.08762677,-0.06431074,0.01944353,-0.00257198,-0.11131783,-0.03517526,-0.0071981,-0.00015831,0.03202355,0.09442587,0.00881947,-0.07646532,-0.04169486,0.01593453,0.02818824,0.06974932,0.0164012,-0.02156289,0.06260248,0.0053606,-0.04915323,0.06327824,-0.04210252,0.03370436,0.01468558,-0.00986159,-0.01442557,-0.01873551,-0.02631917,-0.00248443,0.02468961,-0.03411092,-0.07292239,0.052007,-0.04953285,0.13127458,0.00346999,-0.00590478,0.01390653,-0.04548724,0.03590072,-0.01760039,-0.01288664,0.01992279,-0.02791744,-0.08487317,0.00817849,0.06298073,0.08852778,0.02723894,-0.03113434,0.04201302,0.05747744,0.04377316,0.04522238,-0.03619593,0.0062555,-0.11929078,-0.22239083,-0.04032783,0.03680548,-0.03677248,-0.01338919,-0.01758383,0.01311511,0.04577203,-0.08126,0.09021077,0.12674104,0.0035031,-0.01434962,-0.04289508,0.00129111,0.00236389,-0.02142949,-0.07805087,-0.04535713,0.00408295,-0.0066735,-0.00018618,-0.08229198,-0.06891921,0.07636116,-0.06803275,0.13479362,0.03239301,0.05649969,-0.06538548,0.04662428,0.00389931,0.02755891,-0.12811483,0.05967924,0.04536284,0.00119222,0.01771788,0.02802012,-0.04261342,-0.01541131,0.02371155,0.02151813,0.02331816,0.05542104,-0.05068311,0.01742569,0.01247023,-0.0343301,0.06092868,-0.02502754,0.04414188,0.00891013,0.10896242,-0.03400916,0.01749945,-0.06445158,-0.03619568,0.06753977,0.01222814,0.00510595,-0.02212171,0.05426461,-0.05876783,0.05548122,0.02905476,0.02943804,-0.06010303,0.04962944,-0.03172011,-0.02935863,0.10118675,0.01676186,0.01476799,-0.02751539,0.02700309,-0.05969258,0.03879239,0.04473219,-0.00213477,-0.01082086,0.00455768,0.04669317,0.03818738,-0.03021435,0.01461479,-0.03795746,-0.06707945,0.07309516,-0.06657192,-0.05307414,0.02870044,-0.03855475,0.03968504,0.09106405,0.02012007,-0.21619675,-0.01238142,0.05341072,-0.00642313,-0.05844782,0.0154302,0.00090266,-0.07122661,-0.05153476,-0.02233823,-0.0484155,-0.01239181,0.01456843,-0.04645595,-0.01583669,-0.04951007,0.05712868,-0.02375528,0.11559395,-0.05843861,0.02900469,0.01133241,0.25733325,-0.0265972,0.03209447,0.05229707,0.01146748,0.02737324,0.06542552,0.08406422,0.03181005,0.02615977,0.12135547,0.00251179,-0.00993915,-0.00113455,0.03519903,0.00988285,0.00547565,-0.00888536,-0.04902427,-0.0033565,-0.01981792,0.0198631,0.03767223,-0.05940068,-0.06640881,-0.05601391,0.01553044,-0.01352042,-0.030393,0.02714088,-0.04194178,-0.04561442,-0.00828851,-0.00449777,-0.02222062,0.00992518,-0.03103475,0.03419226,-0.01966878,-0.06358667,0.0011745,0.01679353,-0.0111632],"last_embed":{"hash":"10rkbmc","tokens":379}}},"text":null,"length":0,"last_read":{"hash":"10rkbmc","at":1751288785802},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#What should I do when I get type errors?#{5}","lines":[201,250],"size":1273,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"10rkbmc","at":1751288785802}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Version 3.0": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02549292,-0.01989873,0.0279992,-0.05347203,-0.00148219,0.00063377,-0.03759201,-0.03474259,-0.03205402,0.02912971,-0.00905278,0.00442418,-0.00675667,0.00792391,0.03683994,0.0346035,-0.04511972,0.11161777,-0.04343097,0.01528247,0.11782445,0.00945242,0.04584632,-0.01040885,0.02540512,0.04153353,0.00665446,-0.04408592,0.02783772,-0.2413235,-0.01411308,0.03570502,-0.01368327,0.0030989,0.02361732,-0.07794619,-0.02031119,0.04379092,-0.04769792,0.04016209,0.04264278,0.05015388,-0.00647735,-0.03397847,-0.06161389,-0.00005827,0.00313002,-0.00520976,-0.0077585,-0.08292609,-0.02058692,-0.02248062,0.02948939,0.03165041,-0.01981142,0.04694537,0.07833821,0.01777249,0.03039235,0.04011038,0.01886257,0.03031699,-0.16750236,0.11662561,0.03457161,0.07532213,-0.03070959,-0.04289008,0.05863295,0.0102058,-0.01174072,0.01341963,-0.0228857,0.05155808,0.02984004,-0.02312771,-0.00560949,-0.04973839,0.01495239,-0.02124308,-0.04792933,-0.03622919,0.02023645,-0.00354333,-0.00812817,0.01066073,0.00719359,-0.01524908,0.03993278,0.01420771,-0.02319446,-0.10520913,0.04745204,0.08018938,0.01832131,-0.01527784,-0.00442267,0.04619483,0.01163691,0.12123311,-0.08651707,0.00967274,-0.0571639,0.06558286,0.06661152,0.01740869,0.00736133,-0.02755472,-0.01162726,-0.05951861,0.01161544,-0.00533942,-0.06064701,-0.04338175,-0.05100306,0.01149832,0.00135152,-0.01921417,0.01764731,0.04780208,0.03592111,0.07593165,-0.00336003,0.00854673,0.01054264,0.0096151,0.07655611,0.01338068,0.00168609,0.07378948,0.01870864,0.10105456,-0.0263181,-0.00584391,0.01016353,0.02364625,-0.01862093,0.02193119,0.03111249,-0.00913459,-0.03779783,-0.0286601,-0.00389508,-0.0965751,-0.00333733,0.09175629,-0.10621072,0.0765845,-0.01140654,0.02644232,-0.02200477,0.05305953,-0.08965851,-0.00789847,-0.04986114,0.00714041,-0.0048575,0.00523536,-0.02917619,0.00489638,-0.013765,0.045929,-0.05640362,0.0782673,-0.01328204,-0.1241419,-0.03724248,0.02601774,-0.00965638,-0.07516439,-0.0335705,-0.04093247,0.00697385,0.01684557,0.05232614,0.02596325,-0.05323605,-0.06814013,0.0376833,0.05682321,-0.00451545,0.00419662,-0.02051481,0.03616302,0.0534392,-0.03350589,-0.03469406,0.00332591,0.00630442,0.03680029,-0.03451796,-0.02757718,-0.01604185,0.01883312,-0.06218939,0.05128057,-0.02046681,-0.01849776,0.03648968,-0.03734801,0.08555628,0.05607213,0.02609123,-0.00433704,-0.05475505,0.04641577,-0.04106143,-0.0226403,0.02960144,-0.01427072,-0.05568676,-0.0103481,0.00363307,0.06026023,0.00019114,-0.01313916,0.05401639,0.0344824,0.02719463,0.04431263,-0.03814097,-0.00001601,-0.09506691,-0.20694044,-0.00023177,0.03945698,-0.03474155,-0.01130229,-0.02764916,-0.00369817,0.00016341,-0.02670088,0.03995672,0.08148286,0.06599604,-0.06299835,-0.03728097,-0.00168001,0.00124284,0.00457547,-0.07045943,-0.04207437,-0.00241382,0.02929668,0.01734748,-0.11097257,-0.05984937,0.06158769,-0.03229779,0.1640458,-0.00178028,0.02830144,0.00945546,0.02754179,-0.03445389,-0.01290273,-0.07779679,0.01603976,0.02457969,-0.00034959,0.01742773,0.03302262,-0.02276435,-0.0486315,-0.04179246,0.03549881,-0.04672076,0.07970411,-0.0671402,0.00316145,-0.0208304,-0.06405815,0.05843662,-0.00099143,0.02469351,0.0124203,0.14686586,-0.01798161,0.00534403,-0.02859123,-0.04211413,-0.01408923,0.01693387,0.05578164,0.02110803,0.01451722,-0.04223129,0.02472978,0.04223021,-0.03247369,-0.06576835,0.02598514,-0.02849897,-0.04536642,0.12096141,-0.03058023,-0.02372093,-0.03471358,0.04538958,-0.01694368,0.02662618,0.0068863,-0.00154695,0.05450389,0.02765101,0.04393567,0.0307679,-0.00117562,0.03921426,0.00310837,-0.06470725,0.05295523,-0.06660303,-0.02481587,-0.00014485,-0.04164641,-0.02072876,0.08293278,0.02665611,-0.24751876,-0.02039593,0.02802495,-0.0189041,-0.04111804,0.00754967,0.03464081,-0.10007787,-0.04299559,0.0019418,0.01048258,0.06022395,0.03475147,-0.06987581,-0.04919064,0.01212306,0.08806953,-0.03343011,0.06717221,-0.06975195,0.01613288,0.02681249,0.25460082,-0.01563939,0.06179653,0.01780153,-0.01558153,0.04598043,0.08054236,0.08009385,0.01915694,-0.00855513,0.10416418,-0.02104178,-0.01357812,-0.03515226,0.03882571,-0.01446544,0.0263812,0.00204395,-0.0228411,0.00184275,-0.02765439,-0.03542295,0.00962569,-0.01014853,-0.07777929,-0.02299112,0.03255274,-0.01685322,-0.00960112,0.00854677,-0.00878662,-0.016352,0.03013548,0.00384618,-0.07827111,0.03791473,-0.05183693,0.02171879,-0.00492808,-0.05500427,-0.04895667,0.09459269,-0.00266548],"last_embed":{"hash":"1uvgre8","tokens":292}}},"text":null,"length":0,"last_read":{"hash":"1uvgre8","at":1751288785970},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Version 3.0","lines":[251,267],"size":898,"outlinks":[{"title":"related issue","target":"https://github.com/microsoft/TypeScript/issues/29729","line":12},{"title":"\"Generics\"","target":"#generics","line":14}],"class_name":"SmartBlock","last_embed":{"hash":"1uvgre8","at":1751288785970}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Version 3.0#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04333267,-0.05480785,0.03911382,-0.05345644,-0.00979556,-0.03494297,-0.0526972,-0.01333887,-0.01974354,0.07027766,0.01113668,-0.03123367,0.00994403,0.02383607,0.01531054,0.02882117,-0.03412607,0.09928269,-0.04462144,0.0072235,0.11368069,0.01725613,0.0298485,0.00299002,0.02715934,0.03757144,0.01716966,-0.01220827,0.05496351,-0.19735406,0.00788546,0.04716552,-0.03409819,0.04236527,0.02036993,-0.07726154,-0.00355608,0.00169642,-0.04579713,0.08728547,0.03885391,0.01895948,0.00414912,-0.02855219,-0.04778825,-0.00865486,0.00888607,0.01003382,0.02554568,-0.00199375,-0.0036538,0.00680535,0.02945022,-0.02572521,-0.00580577,0.07565755,0.0702216,0.03786823,0.02153369,0.02850514,0.00600417,0.00695695,-0.16388007,0.07690623,0.03411147,0.04359843,-0.00618043,-0.02775601,0.0170963,0.03605368,0.01877747,0.02908066,-0.03805257,0.07624362,0.04270672,-0.0164238,-0.00802682,-0.06945071,0.01180735,-0.0094,-0.09863967,-0.03981577,0.01085064,0.0028547,-0.01076272,0.01852536,-0.00723582,-0.01535322,0.03834177,-0.00475589,-0.02700844,-0.06921729,0.04398118,0.08593238,-0.03176813,-0.02896765,0.02945038,0.03374985,0.01230413,0.14285572,-0.05917445,-0.01687334,-0.05223148,-0.00059519,0.04253214,0.03465208,-0.03685568,-0.0557467,-0.03580199,-0.04959157,-0.00180117,0.01417599,-0.07761393,-0.05875852,-0.03372802,0.00325492,-0.02979746,-0.02945784,0.0159936,0.0670976,0.01110922,0.0601786,-0.00479291,-0.00389013,0.03854446,0.00677178,0.04546788,0.00973294,0.01555289,0.10347942,0.02935582,0.05490629,-0.0457563,-0.02119383,-0.00519616,0.01026754,-0.00122832,0.03521206,-0.00437973,0.04673232,0.02922823,-0.07448184,-0.02541334,-0.05219677,-0.02083424,0.05571141,-0.06600942,0.04219975,-0.01197683,0.00891097,0.01336688,0.05207234,-0.07867665,-0.05005746,0.01692289,-0.01637213,0.00406515,0.00580236,-0.04562518,0.02293326,-0.01080122,0.01578056,-0.03816192,0.04558739,-0.02330076,-0.07761913,-0.02266092,0.03714668,-0.00725624,-0.09849359,-0.00274178,-0.01370227,-0.04629709,0.0080384,0.07929543,0.04977745,-0.05370583,-0.05306913,0.02720914,0.06265772,-0.0267712,0.01408573,-0.05379556,0.03450768,0.00718779,-0.01924377,0.02962958,-0.04265365,0.02514542,0.04530438,0.00413623,0.02264176,-0.05618796,0.00252824,-0.04598502,0.02033325,-0.03207244,-0.00370821,0.01664628,-0.02075319,0.11998399,0.06650707,0.0044263,0.02183727,-0.03358078,0.0112487,-0.02168255,-0.03846038,-0.01221925,0.00515334,-0.05355092,-0.01305413,0.04300284,0.08601693,-0.01776613,-0.03532985,0.06866393,0.01811498,0.03641211,0.01397086,-0.05981117,-0.02469353,-0.09405632,-0.22593126,0.0061026,0.05988894,-0.03485603,0.03692237,-0.0144271,-0.02401934,0.01262695,-0.04859009,0.08587489,0.10494834,0.0121905,-0.06753832,-0.0236401,-0.00035791,-0.00631034,-0.036628,-0.08624011,-0.04511809,0.02922411,0.02897151,-0.0389246,-0.10252509,-0.07328864,0.06982795,-0.04549317,0.14579085,0.01331896,0.01122863,-0.01766493,-0.00816023,-0.0138636,0.00963547,-0.10667957,0.01463355,0.01483758,-0.01591313,0.02231194,0.09026567,-0.02418053,-0.05191202,-0.00197372,-0.01276575,-0.01308293,0.07114943,-0.06019604,0.0178512,-0.01789515,0.02553064,0.07976818,-0.01004614,0.0737716,0.00153409,0.11910094,-0.05849711,-0.0060012,-0.01493665,-0.02150765,0.02047275,-0.02190804,0.04469683,-0.00840534,0.03652912,-0.02771227,0.0317365,0.01313893,0.02129812,-0.02540188,0.00311572,-0.03003922,-0.03207685,0.07231511,-0.03694661,-0.00825485,-0.04010082,-0.0045942,-0.06865335,-0.00932253,0.02108172,0.02481613,0.08317299,-0.01925913,0.02381287,0.00752299,0.02329478,0.02379948,0.00172005,-0.01193714,0.05325902,-0.05100311,-0.03861661,-0.00916007,-0.01897535,-0.02167605,0.08887986,-0.02803074,-0.24998534,0.01593683,0.05588927,-0.03319446,-0.0093441,-0.00535159,0.03844554,-0.10319099,-0.08498154,0.02480757,0.04768275,0.03228461,0.01793443,-0.06090554,-0.0079368,0.01725643,0.11130665,-0.02531138,0.10178874,-0.06017794,0.04849023,0.01551491,0.26127622,-0.01403993,0.07834678,0.03256811,-0.01082945,0.07293686,0.06347685,0.07711256,-0.02278935,-0.00009177,0.13876788,-0.03861019,-0.03691086,-0.02895675,0.01975116,-0.04055107,0.00793947,0.01584271,-0.04254817,0.03523237,-0.04107281,-0.050789,0.0385536,-0.01851949,-0.06608162,-0.08287018,0.03412205,-0.0255122,-0.04172723,0.04959737,-0.00207037,-0.00881295,0.00556935,0.02691395,-0.05843215,-0.00690538,-0.05960915,0.00257589,-0.02966609,-0.0261634,-0.04195046,0.07061937,0.00439788],"last_embed":{"hash":"1ycuj77","tokens":111}}},"text":null,"length":0,"last_read":{"hash":"1ycuj77","at":1751288786074},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Version 3.0#{8}","lines":[262,262],"size":249,"outlinks":[{"title":"related issue","target":"https://github.com/microsoft/TypeScript/issues/29729","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1ycuj77","at":1751288786074}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Contributing": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02710746,-0.02828854,0.06898201,-0.00975288,0.03289342,-0.01702625,-0.06808855,-0.03101085,-0.0215369,-0.02659395,0.02623102,-0.02837002,0.03434637,0.01201208,0.03574929,0.00950038,-0.05459044,0.05566286,-0.00846592,-0.02172966,0.01276774,0.01390489,0.03733188,-0.03272858,-0.02922389,0.06299473,0.00337578,-0.0107547,-0.01538029,-0.21742417,0.01549968,0.01952078,-0.08209181,0.02967766,0.05230547,-0.01969384,-0.0196972,0.02394577,-0.06072561,0.03549114,0.04139739,0.06772204,-0.03895338,-0.026394,-0.01041778,-0.01505673,0.00393711,0.01305478,-0.01868421,-0.02555741,-0.00499502,-0.07181296,0.02698287,-0.01925096,-0.00061194,0.07389754,0.07825785,0.06232942,0.06494753,0.00132016,0.00648485,0.05589782,-0.16565172,0.03135126,0.0516636,0.00902958,-0.0418313,-0.01266017,0.04729715,0.04531739,0.00824382,0.06881929,-0.03929872,0.06474858,-0.00003463,-0.00363269,-0.02509634,-0.00532665,0.0033653,0.06988861,-0.08955757,0.00112013,-0.02559925,0.03082886,0.03039721,0.00314437,0.03970633,0.02233812,0.07019519,0.02416402,-0.00491189,-0.10609364,0.04135307,0.0204123,0.01380767,0.02577128,0.04028906,-0.01167869,-0.02577672,0.12149908,-0.06857092,0.03535868,0.0055381,0.05117645,0.01419156,0.03066813,-0.01107748,-0.02575805,0.00588991,-0.04765917,0.04866417,-0.00329986,0.00390965,-0.08026899,-0.06079033,0.01881695,-0.02899945,0.03830993,-0.04145217,0.0757385,0.01130717,0.08401027,0.04352387,0.00026872,0.0575326,-0.01570537,0.00480035,0.05902333,-0.00289976,0.02883466,0.03320321,0.09217189,-0.02459369,-0.01022688,-0.01055828,-0.01884194,-0.05974413,-0.01955268,0.00146096,-0.05231127,-0.01017609,-0.00470848,0.01915804,-0.07057013,-0.00101866,0.06264886,-0.03433961,0.09145582,-0.03093234,-0.0517571,-0.02175301,-0.03421872,-0.04644188,-0.0667206,0.01298473,0.03899135,0.01946814,0.03474748,-0.04027376,0.05040977,-0.05464906,0.02912725,-0.04927243,0.04753254,0.04780955,-0.08857208,-0.02844076,-0.00647776,0.0342857,-0.08500747,-0.05270299,-0.02673052,-0.02025679,-0.05379177,0.03842341,-0.01454626,-0.01666688,0.00387852,0.05896878,0.05069581,0.06189623,0.00408875,-0.00742844,0.03160576,0.00755465,-0.04806878,-0.00093682,-0.0710888,-0.01241476,0.01522881,-0.05811389,-0.01072701,-0.0038742,0.00742335,-0.00029294,-0.04998495,-0.03924559,-0.00020467,0.01635527,0.00678396,0.06701755,0.01173326,0.00656402,0.00807922,-0.0872509,0.02066986,-0.04445192,-0.02564988,0.0707432,0.03809755,-0.03079861,0.01480796,0.02161069,0.08048574,-0.01457326,-0.01003499,0.01790431,0.08434162,0.01599976,0.08665241,-0.03737283,0.0478857,-0.06106315,-0.2146479,-0.0894137,0.02591375,-0.00808457,0.07054002,-0.00932608,0.00662296,0.01116038,-0.08047533,0.03541236,0.0967614,0.06133227,-0.04424731,-0.00838703,-0.01048254,-0.01099707,-0.0464045,-0.02079233,-0.04541237,-0.00040677,0.04003982,-0.02749169,-0.06245076,-0.07189437,0.03794583,-0.03298575,0.1365276,0.0319672,0.04299154,0.00652103,0.0050231,0.03014574,0.03398577,-0.18402424,-0.00171574,0.05826066,-0.03296384,0.02029639,0.01221603,0.00395378,-0.04234296,-0.01618776,0.00712547,-0.10294574,0.06883133,-0.05521556,-0.04403679,-0.02491049,-0.00752378,0.01843229,-0.06266899,-0.01325286,0.00841209,0.05226755,-0.02576797,-0.0004545,-0.04917069,0.01106196,0.04243965,-0.02831562,-0.00212626,0.04328692,0.04553075,-0.06464427,0.11193489,-0.00250556,-0.02667798,-0.00140462,0.03608521,-0.01429729,0.02558989,0.06871098,0.00768888,-0.02599756,-0.02203024,0.05263683,-0.07512771,-0.0076497,-0.01194307,0.01559866,0.00919881,-0.04237526,0.03764913,0.02598125,-0.03037283,0.00558121,0.01858968,-0.05865815,0.04462094,-0.02934087,-0.00240738,0.02892785,-0.03455492,-0.00586439,0.06270704,0.03657521,-0.23883703,0.0458574,-0.00231269,-0.04628284,-0.03232091,0.01233656,0.04679107,-0.02915443,-0.01303641,0.02429405,0.05553414,0.07591586,0.03054211,-0.05417943,0.00021647,0.02950947,0.09698433,-0.05512549,0.09463035,-0.07692618,0.02278308,0.04047732,0.24491464,0.0125507,0.03577115,0.05165011,-0.03122017,0.05088883,0.07043961,0.0764713,-0.01512476,0.0126828,0.10255859,-0.04672205,0.00454833,0.03197981,0.07051323,0.02022151,0.00208892,-0.02282851,-0.02911703,-0.01640637,-0.01453273,-0.02521706,0.02966147,-0.12756322,-0.09502184,-0.05853122,-0.00645231,-0.08338363,-0.05210155,-0.02295178,0.02008704,-0.05438066,0.03027724,0.00694825,-0.06121277,0.04427859,-0.03481948,0.04092595,-0.01431268,-0.08796031,0.04450718,0.04549177,-0.05554807],"last_embed":{"hash":"x5ffve","tokens":164}}},"text":null,"length":0,"last_read":{"hash":"x5ffve","at":1751288786121},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Contributing","lines":[268,278],"size":548,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"x5ffve","at":1751288786121}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Contributing#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0095128,-0.05970295,0.0668748,-0.02213823,0.01372863,-0.03669112,-0.06514576,-0.02570169,-0.00272217,-0.01558254,0.03504876,-0.02890595,0.04030092,0.01810697,0.04378152,0.02329797,-0.03884073,0.07264841,-0.03775472,-0.02689078,0.01854431,0.00211387,0.05167051,-0.04619113,-0.00309621,0.0564682,-0.01608427,-0.02118874,-0.01418662,-0.2165191,0.02832005,0.01624423,-0.10150196,0.01977381,0.03780499,-0.01044999,-0.00949737,0.03179301,-0.04866805,0.03632836,0.04776555,0.07563289,-0.05098301,-0.02776427,-0.01535117,-0.0133008,0.01279411,0.03534654,-0.01005089,-0.00718997,0.02308457,-0.0577007,0.01183963,0.00985488,-0.00913759,0.08823414,0.06145941,0.05704363,0.06736217,0.01128382,0.01140921,0.0663616,-0.15121846,0.03908118,0.04309769,0.01438681,-0.03398744,-0.01180263,0.03756936,0.0435137,0.01301804,0.06806271,-0.03439612,0.06349839,0.02298671,-0.0111686,-0.00569832,0.01513042,-0.00223461,0.06015651,-0.06570033,0.0016247,-0.02149083,0.01310819,0.00373907,0.00275586,0.04849529,0.00262716,0.05609466,0.01231611,-0.01640026,-0.09987567,0.07459058,-0.00465505,-0.00480762,0.01858107,0.0294433,-0.02622028,-0.01880908,0.12898506,-0.07708644,0.05359328,0.00054896,0.06815335,0.00738754,0.03602659,0.00448897,-0.01023127,0.0166861,-0.02652709,0.03212994,-0.02218508,0.00379982,-0.07092697,-0.05383536,0.0226863,-0.03598539,0.02722316,-0.01613536,0.06391058,-0.00150709,0.10140758,0.03285649,0.04397738,0.04750013,-0.01358908,-0.01317146,0.04673395,-0.00416663,0.03990092,0.04891261,0.09583282,-0.02808932,-0.00902803,-0.0348145,-0.02582294,-0.04258829,-0.03953885,-0.00045991,-0.0409112,-0.00092631,-0.00075243,0.0030245,-0.07717051,0.00387239,0.0874833,-0.04251142,0.08147996,-0.02912248,-0.05103875,-0.02748611,-0.04669201,-0.05846861,-0.03729695,-0.00363871,0.06944673,0.03087581,0.00606379,-0.02405955,0.05483907,-0.05474128,0.02472702,-0.06730576,0.05737691,0.06324947,-0.0838108,-0.03373878,-0.02742454,0.03143207,-0.05328803,-0.04714542,-0.02970353,-0.00148525,-0.03252445,0.0577516,-0.01549975,-0.01933306,-0.00351532,0.05664634,0.06646807,0.07071047,0.02551666,-0.01442282,0.02740094,0.01115174,-0.0589237,-0.01274107,-0.04991686,-0.01196458,0.04861098,-0.08301266,0.01629733,-0.01102086,0.00396034,-0.00534867,-0.06236228,-0.04754153,-0.00959199,0.00350658,0.0014671,0.06693574,0.03391831,-0.00577644,0.00646191,-0.08769971,0.03469909,-0.05217613,-0.03330356,0.03307127,0.02929608,-0.04271266,0.01472748,0.01467568,0.07974476,-0.01431517,0.00263338,0.00206175,0.07652131,0.02165206,0.05593789,-0.04236453,0.04929331,-0.04161167,-0.22635262,-0.08641078,0.01910451,-0.01616707,0.06662344,-0.0215563,0.00187622,0.01142181,-0.09148996,0.04541795,0.084668,0.06051832,-0.04608741,-0.01256981,-0.00173461,-0.01867124,-0.04081918,-0.01686419,-0.03627603,-0.03096085,0.04249468,-0.01239385,-0.05586823,-0.05685445,0.04078192,0.00236372,0.1448514,0.0408359,0.02840906,0.01444021,0.0087081,0.01989812,0.03550442,-0.18596934,-0.01400473,0.053662,-0.02116568,-0.00754119,0.00637633,-0.02011935,-0.03905701,-0.02234834,-0.00784139,-0.11495344,0.07108916,-0.03996805,-0.04335398,-0.02793738,0.00519061,0.02998594,-0.07138627,-0.00733118,-0.01016352,0.06333684,-0.00801904,0.01490497,-0.03009072,0.04235294,0.06362653,-0.04132282,-0.00835352,0.02889935,0.02470922,-0.07593581,0.09966145,0.00710292,-0.03848821,-0.0193253,0.03115345,-0.00663024,0.02386625,0.07590907,0.01210636,-0.01242483,-0.02588979,0.05341098,-0.05851511,0.00040018,-0.00724862,-0.01849892,0.01165883,-0.07585834,0.05566762,0.02243599,-0.04362846,0.02836395,0.01444773,-0.06208806,0.0234565,-0.01621749,-0.00277349,0.0000508,-0.05545513,0.00245338,0.0499456,0.02920999,-0.23115537,0.04974804,0.01514669,-0.03980009,-0.02989076,0.05238981,0.05558776,-0.04540912,-0.01881314,0.01389366,0.0506051,0.07368385,0.04651151,-0.03892292,-0.00174824,0.03702598,0.09064333,-0.05419821,0.0807877,-0.05493476,0.00782837,0.04287047,0.24385938,0.01046905,0.03424556,0.0220691,-0.02946165,0.04418927,0.05569016,0.08147106,-0.02901389,0.01485035,0.10115042,-0.05640355,0.01100659,0.01779424,0.06837372,0.01680174,-0.00844552,-0.02067009,-0.05074874,-0.02489509,-0.00071597,-0.03767958,0.03929028,-0.12472506,-0.07636968,-0.05872462,-0.00889605,-0.08286326,-0.03141066,-0.00856068,0.01057755,-0.04644958,0.00524135,-0.00259838,-0.07434775,0.04452703,-0.02879881,0.04549201,0.0038992,-0.08982033,0.01979803,0.03097742,-0.07283907],"last_embed":{"hash":"nodc0a","tokens":108}}},"text":null,"length":0,"last_read":{"hash":"nodc0a","at":1751288786187},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Contributing#{1}","lines":[270,271],"size":326,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"nodc0a","at":1751288786187}},
"smart_blocks:Projects/Piecework/node_modules/csstype/README.md#CSSType#Contributing#Commands": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06431419,0.02054756,0.04005004,-0.03395689,0.04433517,0.02423752,-0.06210821,-0.02945965,-0.04041154,-0.00230918,0.00787087,-0.03215818,0.00016114,0.00601596,0.02183393,-0.00100275,-0.05303304,0.05443907,0.02327246,-0.00146,0.02550086,0.02489761,0.01789779,-0.02386361,-0.04799517,0.04281646,0.04589421,-0.01419433,0.02160439,-0.18293707,0.01426644,0.02652572,-0.00758595,0.02480681,0.05573188,-0.05246774,-0.03031955,-0.0118546,-0.08096042,0.04958881,0.02764628,0.03999923,-0.01771021,-0.01585417,-0.00059359,-0.02858914,0.00880326,-0.0153808,0.00590196,-0.07812201,-0.03177252,-0.05221553,0.04855566,-0.05226033,0.00946622,0.02448149,0.09622173,0.04728168,0.06652638,0.00651274,-0.02737628,0.03234482,-0.17665976,0.00902786,0.05601724,0.04553856,-0.04294033,-0.00143857,0.01000164,0.02120957,0.00112639,0.02810872,-0.03019262,0.05723096,0.02019982,-0.0090538,-0.02181606,-0.04298662,-0.00967218,0.00838145,-0.09183458,-0.0293268,-0.0121316,0.0494252,0.05630643,0.00815697,0.02150375,0.05569765,0.08332805,0.05446691,-0.00668973,-0.10553273,0.01333346,0.08761697,0.01427145,-0.01566356,0.0637189,0.03056392,-0.05157278,0.14650543,-0.04898558,0.01030121,0.01553946,0.02744287,0.03263569,-0.00299766,-0.02338313,-0.05340098,-0.01931613,-0.04025831,0.03998701,0.02324711,-0.02056727,-0.08336027,-0.04951198,-0.01769597,-0.01666078,0.04713177,-0.04123737,0.05496654,0.03819651,0.0546937,0.05835596,-0.04838226,0.05593126,-0.00467372,0.04246565,0.06216159,0.01391807,0.0620605,0.02285995,0.08537344,-0.06398903,-0.02328046,0.02187924,-0.01474247,-0.07087914,-0.04729486,0.01500818,-0.04338284,0.00972699,-0.00416708,0.04416894,-0.06883262,-0.02036688,0.03986467,-0.03919636,0.07925068,-0.02511674,-0.02997302,-0.03278625,0.02635444,-0.03161794,-0.06409367,0.01458449,-0.02469407,0.02055177,0.01917524,-0.0471057,0.04373238,-0.03377959,0.00989454,-0.01706672,0.0453427,0.0124439,-0.08093669,-0.04854567,0.03115597,0.00538684,-0.10274044,-0.02148174,-0.00914663,-0.08024698,-0.03768135,0.03587443,-0.0081065,-0.04862924,-0.01298078,0.03531747,0.02752179,0.05602862,-0.01879263,-0.02748194,0.05818861,0.00256737,-0.00027258,0.04979375,-0.07729412,0.02320865,-0.01756459,-0.00091627,-0.03770495,-0.00134145,-0.01426289,-0.00208226,-0.00423211,-0.02222355,-0.0051213,0.02688695,-0.02537093,0.06118624,-0.01486339,-0.00614696,0.02912312,-0.07667129,-0.0171338,-0.04291809,-0.0042399,0.11529217,0.05570559,-0.06161701,-0.00893132,0.07091832,0.06126554,-0.04828433,-0.01050286,0.03384594,0.05776945,0.03405807,0.06778315,-0.02547015,-0.00145321,-0.09849261,-0.20828994,-0.04438198,0.04402724,-0.01712367,0.02299362,0.01998077,0.00530592,-0.01074729,-0.04946041,0.03740903,0.12527514,0.02713336,-0.01181517,-0.02139177,-0.01012826,0.00799639,-0.04834349,-0.08028619,-0.03971934,0.03849355,0.02588861,-0.05488389,-0.01964431,-0.07163499,0.04423913,-0.06706682,0.12585035,0.04470326,0.07068597,-0.01983432,0.03275694,0.03555329,0.01148757,-0.18595767,-0.00121601,0.06343401,-0.01454802,0.04622447,0.03272886,0.0046084,-0.04559277,-0.00836354,0.02038852,-0.04454366,0.04185184,-0.06827715,-0.016807,-0.0233417,-0.02369356,0.05125593,-0.03522,0.01257878,0.01670161,0.07098003,-0.04156865,-0.024418,-0.09479464,-0.04708461,-0.03544497,0.01914445,0.00244478,0.02730294,0.04421921,-0.05101842,0.07073995,-0.01235644,-0.02563077,-0.00358421,0.02344037,-0.0263913,0.00667694,0.05020623,-0.0104468,-0.02520188,-0.0214124,-0.01298051,-0.08451358,-0.01027081,-0.00106741,0.01625223,0.02549,0.03160475,-0.00216859,0.04217497,0.01137128,0.00364777,0.00112858,-0.03737973,0.08132868,-0.05483908,-0.02409959,0.04217184,-0.00933046,-0.01411936,0.11933428,0.04374525,-0.24729237,0.0269858,0.01144579,-0.02960031,-0.0372353,-0.05393477,0.04657378,-0.03819609,-0.01307226,0.048673,0.03890622,0.0514299,-0.011157,-0.04504925,-0.00709717,-0.0068254,0.08930635,-0.04792497,0.11143878,-0.11823487,0.02798875,0.04645672,0.24218664,0.00297227,0.04053311,0.05292959,-0.00462234,0.06246805,0.0785465,0.08154258,0.04032277,0.01101713,0.11237222,-0.03281976,-0.02642912,0.05920413,0.04294263,0.0367035,0.02982982,-0.00890229,-0.00189177,-0.00393861,-0.04950411,0.00317578,0.04593078,-0.07478935,-0.06403419,-0.06175248,-0.00738381,-0.02906933,-0.06738191,-0.02400558,0.00354595,-0.0188711,0.04725343,0.01465296,-0.03598416,0.00003885,-0.02946947,0.01502143,-0.02185221,-0.05073022,0.07140081,0.07022469,-0.0082371],"last_embed":{"hash":"1vk6r6n","tokens":78}}},"text":null,"length":0,"last_read":{"hash":"1vk6r6n","at":1751288786235},"key":"Projects/Piecework/node_modules/csstype/README.md#CSSType#Contributing#Commands","lines":[272,278],"size":204,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1vk6r6n","at":1751288786235}},
