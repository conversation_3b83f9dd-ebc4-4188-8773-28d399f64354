
"smart_sources:Projects/Piecework/node_modules/source-map-js/README.md": {"path":"Projects/Piecework/node_modules/source-map-js/README.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0888932,-0.04589514,0.00706021,-0.02982329,0.03068667,-0.04157923,-0.15052086,-0.00582648,-0.00945115,0.0033287,0.05108023,-0.03356148,0.00609501,0.02528936,0.01798318,-0.01084651,-0.01389972,-0.00127784,-0.01930483,0.03869392,0.00017528,-0.00973443,0.01938158,-0.06147704,0.02423814,0.06976233,-0.03218008,0.03567488,0.00826807,-0.23457374,-0.01128879,-0.01014947,-0.01409831,-0.01655316,-0.02771011,-0.01347274,0.02983893,-0.01213003,-0.02947927,0.03689494,0.00521588,0.05234284,-0.04557075,-0.01940883,-0.0189101,-0.0061266,0.00083847,-0.00647424,-0.01212044,-0.01005124,-0.05786989,-0.01133461,0.02951301,0.01084809,0.00605398,0.06854222,0.06200046,0.12677673,0.01943308,0.04541603,0.00043735,0.01548752,-0.18599126,0.0446769,0.0848865,0.07081606,-0.0055445,-0.03819637,-0.00729435,0.00775605,-0.02220341,0.06965621,0.00844749,0.04166964,-0.00145094,-0.04135638,-0.0015423,0.07902464,0.01776801,-0.00726469,-0.06020944,-0.00601419,-0.02589241,-0.01073742,-0.01346346,-0.04120036,0.00175623,0.00025612,0.08271898,-0.01595739,-0.01438051,-0.05318383,0.04274121,0.07567326,-0.00981636,0.04262768,0.04066657,0.08364365,0.00840852,0.10437985,-0.04970568,0.04676484,-0.02783895,0.0039018,0.00098513,-0.02313069,-0.02450008,-0.03029117,-0.00408454,0.00518746,-0.01069008,-0.00992683,-0.03381503,-0.0510423,-0.01324065,-0.0505622,0.01576043,0.0148869,-0.04389102,0.08668833,-0.00009289,0.06060953,0.06964491,-0.01618199,0.07404329,0.02066433,-0.01896583,0.00498396,0.02702863,0.0523136,0.02605622,0.05800404,-0.04903482,-0.01519428,0.03939195,-0.02512461,0.00180978,-0.03850947,0.00619273,-0.00886911,0.01402385,-0.05093,0.02806056,-0.04241845,-0.0276205,0.09702802,-0.01151954,0.07682685,-0.05303814,-0.02671721,-0.0281031,0.01922893,-0.07917992,-0.00841047,-0.02767922,-0.008951,-0.02899445,0.01068926,-0.07304876,0.02166576,0.02533798,-0.03231434,0.05018418,0.0446659,0.00333213,-0.07862366,-0.03705713,0.02825542,-0.00334072,-0.0826317,-0.04710095,0.04150949,0.00900533,-0.04908639,0.03583185,-0.00040932,-0.07253148,-0.08968678,0.01518562,-0.00640357,0.00271495,-0.03358829,-0.02349719,0.00683375,-0.00147092,-0.0253107,0.0129708,-0.01622759,-0.04154079,0.02674818,0.02075332,0.01381886,0.07343512,0.01319277,-0.01337166,-0.01349419,-0.03514443,0.01282938,0.05927974,0.00390917,0.07269518,0.05090747,-0.00769159,0.02065005,-0.10603794,0.01807469,-0.0011608,-0.02622204,0.05861718,0.05967601,-0.13828336,-0.04607642,0.01752139,0.085978,-0.04892547,-0.05316206,0.06014301,0.04544552,0.01882429,0.13308519,-0.06988347,-0.00702495,-0.10977942,-0.20363294,0.01096654,0.00813085,-0.00729287,0.00144261,-0.04790635,-0.00070461,-0.0366622,-0.03619378,0.08256139,0.11333769,-0.01296799,-0.01499718,-0.01562274,-0.02657303,0.0007962,-0.0117734,0.00222425,-0.04795982,-0.02600498,-0.02847631,-0.0104195,-0.05027682,-0.07726503,0.02448986,-0.05056204,0.12250641,0.00184279,0.03400654,-0.02331105,0.01513931,-0.01273921,0.05468384,-0.11628916,-0.02416387,0.03557882,0.01634141,0.00719528,0.03381813,0.03846643,-0.00388414,-0.01210643,0.00903725,-0.09479517,-0.0223026,0.00041472,-0.04420652,-0.01751845,0.01700759,0.03250877,-0.04955865,0.0152443,0.08574789,0.01900394,-0.0342019,0.0250546,-0.03080087,-0.04544487,0.00900192,0.05569256,0.04211102,0.01337474,-0.0257708,-0.07432979,0.06203946,0.05115599,-0.00204732,0.01006732,0.0397563,-0.00829385,-0.04596508,0.10526472,-0.0041817,0.00823725,0.01451804,0.02323509,-0.01079979,0.02870804,0.01829911,0.00300305,-0.01314957,-0.04180632,0.03031348,0.02175835,-0.03812523,0.00164166,-0.01606875,-0.0201979,-0.02878675,0.00611281,-0.02328827,0.02221944,-0.05058699,-0.06162198,0.09410477,0.01958126,-0.23516083,0.02842433,0.0450244,-0.01456306,-0.0456395,0.00651198,0.08686732,-0.01695727,-0.00486312,0.01088088,0.01962492,0.08447846,-0.02818936,-0.05308876,0.04120474,0.03876564,0.08872963,-0.01612143,0.06795781,-0.08387875,0.02331027,0.05571811,0.2571103,-0.02242846,-0.00300568,0.10154379,-0.03412899,0.07264318,0.08011492,0.02572863,-0.03391356,0.03745733,0.08749139,0.00847526,-0.03038117,0.00838044,-0.03161896,-0.00186402,0.00545448,-0.00985977,-0.00403881,0.0095884,-0.03776743,0.05198918,0.10860166,-0.08826873,-0.03728031,-0.10444523,0.00644366,0.01778789,-0.06791056,0.04551782,0.04478002,0.01520192,0.05004645,0.03743422,-0.03056206,-0.00088899,-0.06093707,-0.00271114,0.03430385,-0.06240555,0.05038028,0.03776494,-0.00262513],"last_embed":{"hash":"wfvm6c","tokens":483}}},"last_read":{"hash":"wfvm6c","at":1751288821723},"class_name":"SmartSource","last_import":{"mtime":1751244535527,"size":26040,"at":1751288765735,"hash":"wfvm6c"},"blocks":{"#Source Map JS":[1,766],"#Source Map JS#{1}":[3,27],"#Source Map JS#Use with Node":[28,41],"#Source Map JS#Use with Node#{1}":[30,41],"#Source Map JS#Table of Contents":[42,80],"#Source Map JS#Table of Contents#{1}":[44,48],"#Source Map JS#Table of Contents#{2}":[49,78],"#Source Map JS#Table of Contents#{3}":[79,80],"#Source Map JS#Examples":[81,183],"#Source Map JS#Examples#Consuming a source map":[83,121],"#Source Map JS#Examples#Consuming a source map#{1}":[85,121],"#Source Map JS#Examples#Generating a source map":[122,183],"#Source Map JS#Examples#Generating a source map#{1}":[124,126],"#Source Map JS#Examples#Generating a source map#With SourceNode (high level API)":[127,159],"#Source Map JS#Examples#Generating a source map#With SourceNode (high level API)#{1}":[129,159],"#Source Map JS#Examples#Generating a source map#With SourceMapGenerator (low level API)":[160,183],"#Source Map JS#Examples#Generating a source map#With SourceMapGenerator (low level API)#{1}":[162,183],"#Source Map JS#API":[184,766],"#Source Map JS#API#{1}":[186,198],"#Source Map JS#API#SourceMapConsumer":[199,449],"#Source Map JS#API#SourceMapConsumer#{1}":[201,204],"#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)":[205,229],"#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)#{1}":[207,210],"#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)#{2}":[211,212],"#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)#{3}":[213,214],"#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)#{4}":[215,215],"#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)#{5}":[216,217],"#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)#{6}":[218,219],"#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)#{7}":[220,221],"#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)#{8}":[222,223],"#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)#{9}":[224,229],"#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)#{10}":[226,229],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.computeColumnSpans()":[230,260],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.computeColumnSpans()#{1}":[232,260],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)":[261,307],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{1}":[263,266],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{2}":[267,267],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{3}":[268,271],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{4}":[272,272],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{5}":[273,274],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{6}":[275,275],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{7}":[276,282],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{8}":[283,283],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{9}":[284,285],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{10}":[286,286],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{11}":[287,288],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{12}":[289,289],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{13}":[290,291],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{14}":[292,307],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{15}":[294,307],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)":[308,335],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)#{1}":[310,313],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)#{2}":[314,315],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)#{3}":[316,316],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)#{4}":[317,318],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)#{5}":[319,319],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)#{6}":[320,323],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)#{7}":[324,324],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)#{8}":[325,326],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)#{9}":[327,327],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)#{10}":[328,335],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)":[336,372],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{1}":[338,346],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{2}":[347,348],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{3}":[349,349],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{4}":[350,351],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{5}":[352,352],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{6}":[353,356],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{7}":[357,357],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{8}":[358,359],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{9}":[360,360],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{10}":[361,372],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.hasContentsOfAllSources()":[373,391],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.hasContentsOfAllSources()#{1}":[375,391],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])":[392,414],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])#{1}":[394,414],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.eachMapping(callback, context, order)":[415,449],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.eachMapping(callback, context, order)#{1}":[417,419],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.eachMapping(callback, context, order)#{2}":[420,420],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.eachMapping(callback, context, order)#{3}":[421,423],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.eachMapping(callback, context, order)#{4}":[424,424],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.eachMapping(callback, context, order)#{5}":[425,426],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.eachMapping(callback, context, order)#{6}":[427,427],"#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.eachMapping(callback, context, order)#{7}":[428,449],"#Source Map JS#API#SourceMapGenerator":[450,560],"#Source Map JS#API#SourceMapGenerator#{1}":[452,454],"#Source Map JS#API#SourceMapGenerator#new SourceMapGenerator([startOfSourceMap])":[455,478],"#Source Map JS#API#SourceMapGenerator#new SourceMapGenerator([startOfSourceMap])#{1}":[457,458],"#Source Map JS#API#SourceMapGenerator#new SourceMapGenerator([startOfSourceMap])#{2}":[459,459],"#Source Map JS#API#SourceMapGenerator#new SourceMapGenerator([startOfSourceMap])#{3}":[460,461],"#Source Map JS#API#SourceMapGenerator#new SourceMapGenerator([startOfSourceMap])#{4}":[462,463],"#Source Map JS#API#SourceMapGenerator#new SourceMapGenerator([startOfSourceMap])#{5}":[464,464],"#Source Map JS#API#SourceMapGenerator#new SourceMapGenerator([startOfSourceMap])#{6}":[465,468],"#Source Map JS#API#SourceMapGenerator#new SourceMapGenerator([startOfSourceMap])#{7}":[469,469],"#Source Map JS#API#SourceMapGenerator#new SourceMapGenerator([startOfSourceMap])#{8}":[470,478],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.fromSourceMap(sourceMapConsumer, sourceMapGeneratorOptions)":[479,492],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.fromSourceMap(sourceMapConsumer, sourceMapGeneratorOptions)#{1}":[481,482],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.fromSourceMap(sourceMapConsumer, sourceMapGeneratorOptions)#{2}":[483,484],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.fromSourceMap(sourceMapConsumer, sourceMapGeneratorOptions)#{3}":[485,492],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.fromSourceMap(sourceMapConsumer, sourceMapGeneratorOptions)#{4}":[487,492],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.addMapping(mapping)":[493,514],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.addMapping(mapping)#{1}":[495,498],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.addMapping(mapping)#{2}":[499,500],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.addMapping(mapping)#{3}":[501,502],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.addMapping(mapping)#{4}":[503,504],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.addMapping(mapping)#{5}":[505,514],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.addMapping(mapping)#{6}":[507,514],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)":[515,527],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)#{1}":[517,518],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)#{2}":[519,520],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)#{3}":[521,527],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)#{4}":[523,527],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])":[528,551],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])#{1}":[530,534],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])#{2}":[535,536],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])#{3}":[537,537],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])#{4}":[538,540],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])#{5}":[541,541],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])#{6}":[542,551],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.toString()":[552,560],"#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.toString()#{1}":[554,560],"#Source Map JS#API#SourceNode":[561,766],"#Source Map JS#API#SourceNode#{1}":[563,568],"#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])":[569,592],"#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{1}":[571,571],"#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{2}":[572,573],"#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{3}":[574,574],"#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{4}":[575,577],"#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{5}":[578,579],"#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{6}":[580,580],"#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{7}":[581,582],"#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{8}":[583,592],"#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{9}":[585,592],"#Source Map JS#API#SourceNode#SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])":[593,609],"#Source Map JS#API#SourceNode#SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])#{1}":[595,596],"#Source Map JS#API#SourceNode#SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])#{2}":[597,598],"#Source Map JS#API#SourceNode#SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])#{3}":[599,600],"#Source Map JS#API#SourceNode#SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])#{4}":[601,601],"#Source Map JS#API#SourceNode#SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])#{5}":[602,609],"#Source Map JS#API#SourceNode#SourceNode.prototype.add(chunk)":[610,622],"#Source Map JS#API#SourceNode#SourceNode.prototype.add(chunk)#{1}":[612,613],"#Source Map JS#API#SourceNode#SourceNode.prototype.add(chunk)#{2}":[614,614],"#Source Map JS#API#SourceNode#SourceNode.prototype.add(chunk)#{3}":[615,622],"#Source Map JS#API#SourceNode#SourceNode.prototype.prepend(chunk)":[623,633],"#Source Map JS#API#SourceNode#SourceNode.prototype.prepend(chunk)#{1}":[625,626],"#Source Map JS#API#SourceNode#SourceNode.prototype.prepend(chunk)#{2}":[627,627],"#Source Map JS#API#SourceNode#SourceNode.prototype.prepend(chunk)#{3}":[628,633],"#Source Map JS#API#SourceNode#SourceNode.prototype.setSourceContent(sourceFile, sourceContent)":[634,647],"#Source Map JS#API#SourceNode#SourceNode.prototype.setSourceContent(sourceFile, sourceContent)#{1}":[636,638],"#Source Map JS#API#SourceNode#SourceNode.prototype.setSourceContent(sourceFile, sourceContent)#{2}":[639,640],"#Source Map JS#API#SourceNode#SourceNode.prototype.setSourceContent(sourceFile, sourceContent)#{3}":[641,647],"#Source Map JS#API#SourceNode#SourceNode.prototype.setSourceContent(sourceFile, sourceContent)#{4}":[643,647],"#Source Map JS#API#SourceNode#SourceNode.prototype.walk(fn)":[648,672],"#Source Map JS#API#SourceNode#SourceNode.prototype.walk(fn)#{1}":[650,653],"#Source Map JS#API#SourceNode#SourceNode.prototype.walk(fn)#{2}":[654,672],"#Source Map JS#API#SourceNode#SourceNode.prototype.walk(fn)#{3}":[656,672],"#Source Map JS#API#SourceNode#SourceNode.prototype.walkSourceContents(fn)":[673,694],"#Source Map JS#API#SourceNode#SourceNode.prototype.walkSourceContents(fn)#{1}":[675,677],"#Source Map JS#API#SourceNode#SourceNode.prototype.walkSourceContents(fn)#{2}":[678,694],"#Source Map JS#API#SourceNode#SourceNode.prototype.walkSourceContents(fn)#{3}":[680,694],"#Source Map JS#API#SourceNode#SourceNode.prototype.join(sep)":[695,710],"#Source Map JS#API#SourceNode#SourceNode.prototype.join(sep)#{1}":[697,699],"#Source Map JS#API#SourceNode#SourceNode.prototype.join(sep)#{2}":[700,710],"#Source Map JS#API#SourceNode#SourceNode.prototype.join(sep)#{3}":[702,710],"#Source Map JS#API#SourceNode#SourceNode.prototype.replaceRight(pattern, replacement)":[711,724],"#Source Map JS#API#SourceNode#SourceNode.prototype.replaceRight(pattern, replacement)#{1}":[713,715],"#Source Map JS#API#SourceNode#SourceNode.prototype.replaceRight(pattern, replacement)#{2}":[716,717],"#Source Map JS#API#SourceNode#SourceNode.prototype.replaceRight(pattern, replacement)#{3}":[718,724],"#Source Map JS#API#SourceNode#SourceNode.prototype.replaceRight(pattern, replacement)#{4}":[720,724],"#Source Map JS#API#SourceNode#SourceNode.prototype.toString()":[725,743],"#Source Map JS#API#SourceNode#SourceNode.prototype.toString()#{1}":[727,743],"#Source Map JS#API#SourceNode#SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])":[744,766],"#Source Map JS#API#SourceNode#SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])#{1}":[746,766]},"outlinks":[{"title":"![NPM","target":"https://nodei.co/npm/source-map-js.png?downloads=true&downloadRank=true","line":3},{"title":"source-map","target":"https://github.com/mozilla/source-map","line":5},{"title":"source-map@0.7.0","target":"https://github.com/mozilla/source-map/blob/master/CHANGELOG.md#070","line":9},{"title":"Downloads count","target":"media/downloads.png","line":13},{"title":"Ben Rothman (@benthemonkey)","target":"https://github.com/benthemonkey","line":15},{"title":"community asked to create branch for 0.6 version","target":"https://github.com/mozilla/source-map/issues/324","line":15},{"title":"the issue","target":"https://github.com/mozilla/source-map/issues/370","line":15},{"title":"Roman Dvornov (@lahmatiy)","target":"https://github.com/lahmatiy","line":17},{"title":"Vyacheslav Egorov (@mraleph)","target":"https://github.com/mraleph","line":17},{"title":"«Maybe you don't need Rust and WASM to speed up your JS»","target":"https://mrale.ph/blog/2018/02/03/maybe-you-dont-need-rust-to-speed-up-your-js.html","line":17},{"title":"serveral posts","target":"https://t.me/gorshochekvarit/76","line":17},{"title":"Examples","target":"#examples","line":44},{"title":"Consuming a source map","target":"#consuming-a-source-map","line":45},{"title":"Generating a source map","target":"#generating-a-source-map","line":46},{"title":"With SourceNode (high level API)","target":"#with-sourcenode-high-level-api","line":47},{"title":"With SourceMapGenerator (low level API)","target":"#with-sourcemapgenerator-low-level-api","line":48},{"title":"API","target":"#api","line":49},{"title":"SourceMapConsumer","target":"#sourcemapconsumer","line":50},{"title":"new SourceMapConsumer(rawSourceMap)","target":"#new-sourcemapconsumerrawsourcemap","line":51},{"title":"SourceMapConsumer.prototype.computeColumnSpans()","target":"#sourcemapconsumerprototypecomputecolumnspans","line":52},{"title":"SourceMapConsumer.prototype.originalPositionFor(generatedPosition)","target":"#sourcemapconsumerprototypeoriginalpositionforgeneratedposition","line":53},{"title":"SourceMapConsumer.prototype.generatedPositionFor(originalPosition)","target":"#sourcemapconsumerprototypegeneratedpositionfororiginalposition","line":54},{"title":"SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)","target":"#sourcemapconsumerprototypeallgeneratedpositionsfororiginalposition","line":55},{"title":"SourceMapConsumer.prototype.hasContentsOfAllSources()","target":"#sourcemapconsumerprototypehascontentsofallsources","line":56},{"title":"SourceMapConsumer.prototype.eachMapping(callback, context, order)","target":"#sourcemapconsumerprototypeeachmappingcallback-context-order","line":58},{"title":"SourceMapGenerator","target":"#sourcemapgenerator","line":59},{"title":"SourceMapGenerator.fromSourceMap(sourceMapConsumer)","target":"#sourcemapgeneratorfromsourcemapsourcemapconsumer","line":61},{"title":"SourceMapGenerator.prototype.addMapping(mapping)","target":"#sourcemapgeneratorprototypeaddmappingmapping","line":62},{"title":"SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)","target":"#sourcemapgeneratorprototypesetsourcecontentsourcefile-sourcecontent","line":63},{"title":"SourceMapGenerator.prototype.toString()","target":"#sourcemapgeneratorprototypetostring","line":65},{"title":"SourceNode","target":"#sourcenode","line":66},{"title":"SourceNode.prototype.add(chunk)","target":"#sourcenodeprototypeaddchunk","line":69},{"title":"SourceNode.prototype.prepend(chunk)","target":"#sourcenodeprototypeprependchunk","line":70},{"title":"SourceNode.prototype.setSourceContent(sourceFile, sourceContent)","target":"#sourcenodeprototypesetsourcecontentsourcefile-sourcecontent","line":71},{"title":"SourceNode.prototype.walk(fn)","target":"#sourcenodeprototypewalkfn","line":72},{"title":"SourceNode.prototype.walkSourceContents(fn)","target":"#sourcenodeprototypewalksourcecontentsfn","line":73},{"title":"SourceNode.prototype.join(sep)","target":"#sourcenodeprototypejoinsep","line":74},{"title":"SourceNode.prototype.replaceRight(pattern, replacement)","target":"#sourcenodeprototypereplacerightpattern-replacement","line":75},{"title":"SourceNode.prototype.toString()","target":"#sourcenodeprototypetostring","line":76},{"title":"**Compiling to JavaScript, and Debugging with Source Maps**","target":"https://hacks.mozilla.org/2013/05/compiling-to-javascript-and-debugging-with-source-maps/","line":125}],"last_embed":{"hash":"wfvm6c","at":1751288811642}},"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08918875,-0.04577359,0.00763148,-0.02791266,0.03176722,-0.04220137,-0.14788669,-0.0019365,-0.00746439,0.00290414,0.05343153,-0.03255146,0.00537907,0.0260886,0.01720903,-0.01419924,-0.01280159,-0.00270551,-0.01870058,0.03860095,0.00098152,-0.00832984,0.019583,-0.06096891,0.02724278,0.07071611,-0.03265643,0.03521215,0.00748059,-0.23389843,-0.0135362,-0.01029466,-0.01408183,-0.01678067,-0.0303346,-0.01165407,0.03070898,-0.01261948,-0.02694225,0.03566667,0.00466789,0.05224596,-0.04429577,-0.01741211,-0.01901658,-0.0078874,0.00027922,-0.00857219,-0.01371722,-0.00902021,-0.06204072,-0.00951231,0.03042082,0.01162654,0.00802403,0.06637174,0.0632307,0.12600558,0.0230451,0.04389981,-0.00079159,0.01367707,-0.18741249,0.04704839,0.08659255,0.070607,-0.0048763,-0.03871737,-0.00817788,0.00525086,-0.02036584,0.07090438,0.00621134,0.04207682,-0.00167947,-0.04401755,-0.00297646,0.07710457,0.01537435,-0.00612851,-0.06044552,-0.00540408,-0.02573077,-0.01202446,-0.01467478,-0.03873821,0.00027634,-0.00105735,0.08483644,-0.01683017,-0.0142538,-0.05236283,0.04585731,0.07892915,-0.00944427,0.04243588,0.04158187,0.08788888,0.0119073,0.10205567,-0.04970929,0.04835762,-0.02991309,0.00567006,0.00370576,-0.02600159,-0.02503107,-0.03234138,-0.00674699,0.00556768,-0.00822422,-0.01365392,-0.03215312,-0.05067407,-0.01209015,-0.0509992,0.02232051,0.01806153,-0.04616702,0.08538124,-0.00166654,0.06280669,0.07036794,-0.01610621,0.07209039,0.02165824,-0.01585533,0.00539096,0.02572654,0.05538706,0.02749018,0.05809536,-0.05245823,-0.01703631,0.03828641,-0.02106001,-0.00210599,-0.0380046,0.00617873,-0.0088499,0.01409539,-0.05256806,0.0258383,-0.04127503,-0.02838977,0.09683385,-0.01499288,0.07411849,-0.05166266,-0.02644418,-0.02648034,0.0195931,-0.0782159,-0.01112219,-0.03063071,-0.01017606,-0.02718294,0.00636797,-0.07488936,0.0217375,0.02318973,-0.03109965,0.04921215,0.04476039,0.00323639,-0.07637127,-0.03624611,0.02820714,-0.00367529,-0.08365974,-0.04511605,0.03833732,0.00780586,-0.04799677,0.03474675,-0.00364305,-0.06952735,-0.0910383,0.01276318,-0.00684665,0.00212909,-0.03423201,-0.02291137,0.00774429,0.00151987,-0.02430404,0.01121455,-0.01691895,-0.0354859,0.02640983,0.02094759,0.01280387,0.07329012,0.01295118,-0.0137158,-0.01208834,-0.03839302,0.014594,0.05878028,0.00358137,0.07230158,0.05148577,-0.00889895,0.0219933,-0.10586368,0.01601092,-0.00096458,-0.02573075,0.05811733,0.06122839,-0.13496481,-0.04389665,0.01361569,0.08580441,-0.04753556,-0.0525308,0.05826258,0.04579284,0.01686058,0.13331771,-0.07090764,-0.01231618,-0.10934389,-0.2050797,0.01427616,0.0053521,-0.0079471,-0.00077994,-0.04892422,-0.00176094,-0.03525598,-0.03190997,0.08383558,0.11324998,-0.00874212,-0.01575238,-0.01508862,-0.02374159,0.00269976,-0.01242402,0.0013639,-0.05174195,-0.02580208,-0.02654187,-0.00888088,-0.05070951,-0.07822876,0.02443775,-0.05084082,0.12117565,0.00118458,0.03501806,-0.02374458,0.01432606,-0.01118681,0.05704135,-0.11906426,-0.02572461,0.0375715,0.01918609,0.01124232,0.0300187,0.03650359,-0.00225896,-0.0111974,0.00852081,-0.09495984,-0.02165201,-0.00158405,-0.04228111,-0.01745265,0.01720098,0.03267275,-0.05228647,0.01464672,0.08439355,0.0203735,-0.03129309,0.0258781,-0.03442034,-0.04510481,0.00991052,0.05513965,0.04124558,0.01450386,-0.02581331,-0.07290613,0.06164043,0.05214693,-0.00471961,0.01473966,0.03952686,-0.00491365,-0.04950227,0.10692981,-0.0036223,0.01063268,0.01163986,0.02444185,-0.01010711,0.02933694,0.02028277,0.00443262,-0.01225491,-0.03899017,0.02954368,0.02116542,-0.0358104,-0.00009319,-0.0162388,-0.02196086,-0.03004424,0.00929451,-0.02447015,0.02363378,-0.05239137,-0.06292173,0.09459179,0.02106658,-0.23685841,0.02807547,0.04475578,-0.01701901,-0.04152231,0.00737543,0.08492092,-0.01860267,-0.00395249,0.01177269,0.01616499,0.08407323,-0.03127476,-0.05464613,0.04074022,0.04091388,0.08815983,-0.01362855,0.06673363,-0.08195849,0.02698228,0.05288015,0.25625914,-0.02164617,-0.00184168,0.09714724,-0.03507799,0.07058406,0.08040344,0.02696493,-0.0335697,0.03781573,0.0849961,0.00904191,-0.03365919,0.01308446,-0.03185885,-0.00103968,0.00719563,-0.00823889,-0.00276514,0.01110201,-0.03966165,0.05205286,0.10903478,-0.08795085,-0.03993931,-0.10323474,0.00619607,0.01799569,-0.06615973,0.04551338,0.0444935,0.01430542,0.05194461,0.03517609,-0.03094241,-0.00492501,-0.05833131,-0.00502845,0.03391792,-0.06463717,0.05021205,0.03928424,-0.00500058],"last_embed":{"hash":"wfvm6c","tokens":496}}},"text":null,"length":0,"last_read":{"hash":"wfvm6c","at":1751288811846},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS","lines":[1,766],"size":26036,"outlinks":[{"title":"![NPM","target":"https://nodei.co/npm/source-map-js.png?downloads=true&downloadRank=true","line":3},{"title":"source-map","target":"https://github.com/mozilla/source-map","line":5},{"title":"source-map@0.7.0","target":"https://github.com/mozilla/source-map/blob/master/CHANGELOG.md#070","line":9},{"title":"Downloads count","target":"media/downloads.png","line":13},{"title":"Ben Rothman (@benthemonkey)","target":"https://github.com/benthemonkey","line":15},{"title":"community asked to create branch for 0.6 version","target":"https://github.com/mozilla/source-map/issues/324","line":15},{"title":"the issue","target":"https://github.com/mozilla/source-map/issues/370","line":15},{"title":"Roman Dvornov (@lahmatiy)","target":"https://github.com/lahmatiy","line":17},{"title":"Vyacheslav Egorov (@mraleph)","target":"https://github.com/mraleph","line":17},{"title":"«Maybe you don't need Rust and WASM to speed up your JS»","target":"https://mrale.ph/blog/2018/02/03/maybe-you-dont-need-rust-to-speed-up-your-js.html","line":17},{"title":"serveral posts","target":"https://t.me/gorshochekvarit/76","line":17},{"title":"Examples","target":"#examples","line":44},{"title":"Consuming a source map","target":"#consuming-a-source-map","line":45},{"title":"Generating a source map","target":"#generating-a-source-map","line":46},{"title":"With SourceNode (high level API)","target":"#with-sourcenode-high-level-api","line":47},{"title":"With SourceMapGenerator (low level API)","target":"#with-sourcemapgenerator-low-level-api","line":48},{"title":"API","target":"#api","line":49},{"title":"SourceMapConsumer","target":"#sourcemapconsumer","line":50},{"title":"new SourceMapConsumer(rawSourceMap)","target":"#new-sourcemapconsumerrawsourcemap","line":51},{"title":"SourceMapConsumer.prototype.computeColumnSpans()","target":"#sourcemapconsumerprototypecomputecolumnspans","line":52},{"title":"SourceMapConsumer.prototype.originalPositionFor(generatedPosition)","target":"#sourcemapconsumerprototypeoriginalpositionforgeneratedposition","line":53},{"title":"SourceMapConsumer.prototype.generatedPositionFor(originalPosition)","target":"#sourcemapconsumerprototypegeneratedpositionfororiginalposition","line":54},{"title":"SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)","target":"#sourcemapconsumerprototypeallgeneratedpositionsfororiginalposition","line":55},{"title":"SourceMapConsumer.prototype.hasContentsOfAllSources()","target":"#sourcemapconsumerprototypehascontentsofallsources","line":56},{"title":"SourceMapConsumer.prototype.eachMapping(callback, context, order)","target":"#sourcemapconsumerprototypeeachmappingcallback-context-order","line":58},{"title":"SourceMapGenerator","target":"#sourcemapgenerator","line":59},{"title":"SourceMapGenerator.fromSourceMap(sourceMapConsumer)","target":"#sourcemapgeneratorfromsourcemapsourcemapconsumer","line":61},{"title":"SourceMapGenerator.prototype.addMapping(mapping)","target":"#sourcemapgeneratorprototypeaddmappingmapping","line":62},{"title":"SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)","target":"#sourcemapgeneratorprototypesetsourcecontentsourcefile-sourcecontent","line":63},{"title":"SourceMapGenerator.prototype.toString()","target":"#sourcemapgeneratorprototypetostring","line":65},{"title":"SourceNode","target":"#sourcenode","line":66},{"title":"SourceNode.prototype.add(chunk)","target":"#sourcenodeprototypeaddchunk","line":69},{"title":"SourceNode.prototype.prepend(chunk)","target":"#sourcenodeprototypeprependchunk","line":70},{"title":"SourceNode.prototype.setSourceContent(sourceFile, sourceContent)","target":"#sourcenodeprototypesetsourcecontentsourcefile-sourcecontent","line":71},{"title":"SourceNode.prototype.walk(fn)","target":"#sourcenodeprototypewalkfn","line":72},{"title":"SourceNode.prototype.walkSourceContents(fn)","target":"#sourcenodeprototypewalksourcecontentsfn","line":73},{"title":"SourceNode.prototype.join(sep)","target":"#sourcenodeprototypejoinsep","line":74},{"title":"SourceNode.prototype.replaceRight(pattern, replacement)","target":"#sourcenodeprototypereplacerightpattern-replacement","line":75},{"title":"SourceNode.prototype.toString()","target":"#sourcenodeprototypetostring","line":76},{"title":"**Compiling to JavaScript, and Debugging with Source Maps**","target":"https://hacks.mozilla.org/2013/05/compiling-to-javascript-and-debugging-with-source-maps/","line":125}],"class_name":"SmartBlock","last_embed":{"hash":"wfvm6c","at":1751288811846}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09275112,-0.04540959,0.0050006,-0.02885473,0.03178897,-0.03880067,-0.14950719,-0.00331142,-0.0066483,0.00577008,0.05226576,-0.031147,0.00744694,0.0224273,0.01625711,-0.01069091,-0.01726359,-0.00184469,-0.02107484,0.03863807,0.00047823,-0.01216033,0.01443273,-0.06919773,0.03043192,0.07127006,-0.03589793,0.03767771,0.01020628,-0.23312901,-0.01191909,-0.01051662,-0.01033987,-0.02420909,-0.03245221,-0.01365566,0.02918735,-0.00775743,-0.02583789,0.03496081,0.00363136,0.05138687,-0.04313404,-0.02262107,-0.01883608,-0.00704547,-0.00165353,-0.00628218,-0.01169969,-0.00374468,-0.05590692,-0.01298951,0.02664237,0.01401463,0.00765106,0.0637097,0.06626211,0.12924984,0.02378339,0.04985981,-0.00178072,0.00876134,-0.18675011,0.0466344,0.08751632,0.07061714,-0.00303709,-0.03769552,-0.00823606,0.01150497,-0.02888187,0.07125528,0.01134902,0.04158563,-0.00367518,-0.04203099,-0.00271482,0.0807425,0.02003155,-0.00583561,-0.05787664,-0.01083508,-0.03024096,-0.00994017,-0.01149325,-0.04166375,-0.00379574,-0.00204621,0.08161512,-0.01990854,-0.01252537,-0.05232274,0.04284428,0.07261005,-0.01178221,0.0455771,0.03711459,0.08673798,0.0071418,0.1047392,-0.0481062,0.0463456,-0.02570604,0.00480618,0.00279353,-0.02690541,-0.02651427,-0.03032598,-0.00385971,0.00794197,-0.00847913,-0.00951643,-0.03108701,-0.05381144,-0.01339653,-0.05520022,0.01634242,0.01536189,-0.04615682,0.08170537,-0.00353083,0.05920337,0.07462913,-0.01717955,0.07414982,0.02264252,-0.0243792,0.00117647,0.02541318,0.05163616,0.02702183,0.05525763,-0.04717229,-0.01388758,0.0394729,-0.0231907,0.00081263,-0.0326713,0.00706217,-0.00035289,0.01700374,-0.05283239,0.0233056,-0.04045098,-0.02687126,0.09801743,-0.01370728,0.07750066,-0.04868038,-0.02855605,-0.03417072,0.01984934,-0.08205944,-0.01207588,-0.02760575,-0.00745955,-0.03524301,0.0096509,-0.07338596,0.01932492,0.0230238,-0.03104252,0.05426687,0.04891565,0.00020868,-0.0772528,-0.03030892,0.02546656,-0.00305737,-0.07965802,-0.05188848,0.03865823,0.00665983,-0.05012811,0.03636659,-0.00697179,-0.06931607,-0.08818439,0.0146238,-0.00361505,0.00473471,-0.02878013,-0.02206429,0.00620943,0.00468395,-0.02796981,0.00929301,-0.01025471,-0.04036171,0.02904128,0.01485129,0.0134918,0.07501332,0.00727254,-0.01241098,-0.01477088,-0.03194257,0.01191424,0.06059746,0.01043946,0.0721456,0.05081761,-0.00387989,0.0193707,-0.10438751,0.01544805,0.00029071,-0.02562199,0.05683242,0.06531051,-0.13957801,-0.04504823,0.01776376,0.0837932,-0.04860923,-0.05039701,0.06035184,0.04420939,0.01618416,0.13845938,-0.07192098,-0.00826359,-0.10558096,-0.20242216,0.0132465,0.00660582,-0.0064727,0.00452174,-0.05274327,0.00038215,-0.03777254,-0.03710027,0.08047555,0.11081471,-0.01270656,-0.01388695,-0.01247787,-0.02676627,-0.00153445,-0.00374337,-0.0001077,-0.04421071,-0.02720124,-0.02919649,-0.00910423,-0.04513004,-0.07980748,0.02260785,-0.05163988,0.12206794,-0.00191413,0.03353856,-0.02316598,0.00958421,-0.01024211,0.04950143,-0.12014683,-0.02360177,0.03356772,0.01707748,0.00354027,0.03110081,0.03790012,-0.00569584,-0.00803853,0.0087299,-0.09225629,-0.02364068,-0.00101089,-0.04296761,-0.01719175,0.01675075,0.02977906,-0.05202368,0.01563833,0.08564502,0.0154976,-0.03156727,0.02254962,-0.03224703,-0.04366846,0.00990491,0.05840359,0.04101707,0.00853637,-0.02719401,-0.07143527,0.05985651,0.0520622,-0.00155232,0.01353916,0.03928584,-0.00591771,-0.04777783,0.10857047,-0.00422019,0.00959787,0.01306037,0.02334092,-0.01148401,0.03158218,0.02294569,-0.00154907,-0.01614781,-0.0434632,0.03191492,0.02029891,-0.03523611,-0.00117313,-0.00878578,-0.02091703,-0.02933302,0.00977883,-0.02318067,0.02290223,-0.05193196,-0.06159787,0.09275352,0.02298767,-0.23813215,0.02628475,0.04686107,-0.01646108,-0.04396602,0.01058646,0.08908781,-0.01318492,-0.00296191,0.01373403,0.01413674,0.08563442,-0.02273317,-0.04992595,0.04237067,0.04075554,0.09113561,-0.01678411,0.06920245,-0.08100046,0.02429181,0.0551063,0.25527462,-0.02167173,0.00093562,0.09649316,-0.03378347,0.07144248,0.07936994,0.0182035,-0.03619139,0.03673693,0.08440223,0.01050516,-0.03028792,0.01312713,-0.03582443,-0.00496513,-0.00008929,-0.00727722,-0.00635459,0.0046954,-0.03620755,0.0543784,0.11055221,-0.08621854,-0.03396453,-0.09973952,0.00542641,0.01534934,-0.06992278,0.04695969,0.04150356,0.01181383,0.05318463,0.04095828,-0.03534148,0.00067205,-0.06157791,-0.00373934,0.0356521,-0.06040407,0.05033921,0.03842571,-0.00162165],"last_embed":{"hash":"180hga9","tokens":478}}},"text":null,"length":0,"last_read":{"hash":"180hga9","at":1751288812083},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#{1}","lines":[3,27],"size":2074,"outlinks":[{"title":"![NPM","target":"https://nodei.co/npm/source-map-js.png?downloads=true&downloadRank=true","line":1},{"title":"source-map","target":"https://github.com/mozilla/source-map","line":3},{"title":"source-map@0.7.0","target":"https://github.com/mozilla/source-map/blob/master/CHANGELOG.md#070","line":7},{"title":"Downloads count","target":"media/downloads.png","line":11},{"title":"Ben Rothman (@benthemonkey)","target":"https://github.com/benthemonkey","line":13},{"title":"community asked to create branch for 0.6 version","target":"https://github.com/mozilla/source-map/issues/324","line":13},{"title":"the issue","target":"https://github.com/mozilla/source-map/issues/370","line":13},{"title":"Roman Dvornov (@lahmatiy)","target":"https://github.com/lahmatiy","line":15},{"title":"Vyacheslav Egorov (@mraleph)","target":"https://github.com/mraleph","line":15},{"title":"«Maybe you don't need Rust and WASM to speed up your JS»","target":"https://mrale.ph/blog/2018/02/03/maybe-you-dont-need-rust-to-speed-up-your-js.html","line":15},{"title":"serveral posts","target":"https://t.me/gorshochekvarit/76","line":15}],"class_name":"SmartBlock","last_embed":{"hash":"180hga9","at":1751288812083}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Use with Node": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06500094,-0.0361995,0.06700791,-0.0632055,0.00361732,-0.00053151,-0.13040255,-0.00795908,-0.06695307,0.00751943,0.03674245,-0.06137558,0.00983946,0.01958766,0.06855067,-0.01812175,-0.07060304,0.06954006,0.00169103,-0.01116185,0.0323389,0.01181323,0.01210384,0.01375154,0.00152031,0.09357321,0.02444215,-0.03716401,-0.01912246,-0.20063983,-0.02232829,-0.00505278,-0.04067757,0.01166863,0.01822588,0.00101954,-0.00239038,0.00506042,-0.00098341,0.04584621,0.02359249,0.01239662,-0.05077425,-0.03855748,0.0063871,-0.02640883,0.0016426,0.01851668,0.03380544,-0.0326872,-0.04285975,-0.00075477,0.03983441,-0.00582813,0.02284433,0.08665001,0.05789472,0.10295852,0.04611058,0.01886252,0.01201087,0.05067386,-0.20242675,0.06995987,0.05780099,0.05810666,-0.02937499,-0.00406953,0.02418227,0.00229683,-0.01461836,0.06248648,0.03111308,0.04426985,0.02759193,-0.07046029,-0.01928927,0.02459783,0.03176972,-0.04013498,-0.08182102,0.00988523,0.00668673,-0.00213074,0.02083796,-0.04551626,0.02691182,0.00290749,0.03851601,0.01565691,-0.0064103,-0.06113201,0.06534655,0.10646014,-0.03731016,0.0108639,0.01391467,0.04508122,-0.02963201,0.12427302,-0.05786418,0.0357279,-0.01837724,0.0088074,-0.01411762,-0.01107532,-0.01642708,-0.0299224,-0.01312451,0.03306986,-0.02895818,0.00317149,-0.07049564,-0.0560192,-0.01317172,-0.01406803,0.02148347,-0.00738026,-0.05538308,0.03420942,0.03509483,0.05936675,0.03813702,0.03064423,0.05268976,-0.01365065,0.04935442,0.02863305,0.01637337,0.062812,-0.00776292,0.08085167,-0.08992782,0.01863816,0.041696,-0.00128552,0.01829063,-0.03108719,-0.01334691,-0.00667991,0.02578166,-0.03585116,-0.01093341,-0.04169563,-0.01659236,0.05849537,-0.02067249,0.07897317,-0.01521609,-0.04431579,-0.01558341,0.05384437,-0.10856011,0.00379939,0.00029578,-0.01545048,0.01134538,0.00709572,-0.05406614,0.02142676,-0.00052263,-0.02993722,0.01302597,0.06479944,0.00936032,-0.06546334,-0.06780132,0.04419098,0.03213103,-0.05562908,-0.04684814,0.03317042,0.00481451,-0.03851286,0.03651999,0.02633209,-0.01152609,-0.05544449,0.05268457,0.01779954,0.00241646,-0.07793166,-0.0472755,0.0338737,-0.01864886,-0.04216513,0.00554685,-0.0348039,-0.02159668,0.02789059,0.01137336,0.0495665,0.06256511,-0.01755124,0.01384174,-0.01266834,-0.06486861,-0.0054376,0.03272051,-0.04056893,0.09789152,0.0380033,-0.02285586,-0.01776401,-0.0661798,0.0147053,-0.0239345,-0.00472572,0.06239524,0.00412212,-0.11947276,-0.0152625,0.05560745,0.07028072,-0.07010879,-0.0330583,0.02127984,0.04528564,0.02847127,0.08943719,-0.06021666,0.02399829,-0.0757197,-0.21012968,0.0220791,0.01405513,-0.00924147,-0.04128296,-0.02517783,-0.01778445,-0.03852521,-0.05354225,0.06502436,0.14474374,-0.02473894,0.01789799,0.00293817,-0.02262785,-0.00882419,0.02557574,-0.05776696,-0.03562308,-0.02297757,0.01333836,-0.02534607,-0.07855946,-0.11496382,0.04617612,-0.0159984,0.12113619,0.04594516,0.00259627,-0.00991257,0.05319799,-0.03189328,0.01523955,-0.12801121,-0.05756753,0.03184288,0.0236722,0.02812876,0.04444208,0.04757956,-0.04210336,-0.00323185,-0.02644324,-0.07556451,0.0145286,-0.00706416,-0.05167498,0.01466445,0.00717398,0.02592073,-0.01851632,0.05146575,0.05700883,0.02174321,-0.06959789,-0.00240045,0.00816531,-0.00709006,-0.00822585,0.04595237,0.02031408,-0.0031258,-0.01037408,-0.09133383,0.06641941,0.05312472,0.0410682,-0.02646739,0.07904682,-0.0060262,-0.03239176,0.05411632,0.03081925,0.02859808,0.05029407,-0.0173674,-0.05036727,0.01503363,0.00473574,-0.03604952,-0.00861265,-0.0453883,-0.00481013,0.01750387,-0.03952665,0.02536425,-0.01791245,-0.02517952,-0.01497077,-0.02519131,-0.02701422,-0.02121111,-0.04249846,-0.02418907,0.11943444,-0.00398218,-0.24909981,0.03719103,0.0369226,-0.01498436,-0.03486326,-0.02027813,0.06102644,-0.00548318,-0.02768651,-0.04711482,0.00246239,0.07615212,-0.04593481,-0.06215853,0.03292564,0.02899613,0.08187249,0.00729454,0.08452232,-0.07309411,0.05337904,0.06967763,0.23853053,-0.00298421,0.0243173,0.0705816,-0.0707617,0.06048647,0.0893313,0.06775449,-0.0454924,0.04647923,0.07697117,0.00456297,-0.02738058,-0.01929372,-0.00394011,-0.01971654,0.01887827,-0.00918718,-0.03860794,0.01957347,-0.01942457,0.04382885,0.08929368,-0.08994625,-0.01128836,-0.09645817,-0.0236892,0.02808059,-0.06234225,0.02221538,0.00699849,-0.00064704,-0.00645047,0.03540682,0.00191183,0.00722157,-0.03804267,-0.00922611,0.01014012,-0.06348935,0.07053155,0.02478724,-0.02336209],"last_embed":{"hash":"23ceo7","tokens":251}}},"text":null,"length":0,"last_read":{"hash":"23ceo7","at":1751288812261},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Use with Node","lines":[28,41],"size":486,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"23ceo7","at":1751288812261}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Use with Node#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06753443,-0.0331059,0.06763708,-0.0601497,0.00142711,0.00183842,-0.13143864,-0.00480814,-0.06773993,0.00703128,0.04115193,-0.05966682,0.01370085,0.01962341,0.06677838,-0.01684049,-0.07339381,0.07352074,0.00208866,-0.01556197,0.0309803,0.01258752,0.01399009,0.01323929,0.00624835,0.09058032,0.02247014,-0.03517834,-0.01944834,-0.20264186,-0.0170782,-0.00510721,-0.04210209,0.0115687,0.0190988,-0.00088705,-0.00294132,0.00771033,-0.00060251,0.0450925,0.02234159,0.01311802,-0.05044222,-0.04076521,0.00780647,-0.0265631,-0.00014005,0.01594424,0.03519155,-0.03279094,-0.04112695,-0.00701432,0.03869642,-0.00767298,0.02136673,0.08575056,0.05794,0.10160228,0.04775676,0.02198284,0.0117961,0.04822443,-0.1999152,0.07469042,0.05414999,0.05939631,-0.03200065,-0.00524892,0.02453795,0.00641284,-0.01730415,0.06430276,0.03227599,0.04678972,0.03157438,-0.07225125,-0.02225748,0.02308874,0.03463946,-0.0398124,-0.08384018,0.00665642,0.00230412,-0.00958389,0.01983078,-0.04682389,0.02742595,0.00191693,0.03400359,0.01640365,-0.00750345,-0.06205641,0.06356419,0.10522375,-0.03688402,0.01109678,0.0124419,0.04519214,-0.03691554,0.12388395,-0.05755683,0.0343093,-0.02308042,0.00775399,-0.01384023,-0.00805467,-0.01710613,-0.02760689,-0.01083486,0.03290155,-0.02726288,0.00424525,-0.07177634,-0.05814613,-0.01394308,-0.01478214,0.01605997,-0.00558225,-0.05851345,0.03242305,0.0382137,0.05739748,0.0394153,0.03056763,0.05155671,-0.01421601,0.04853607,0.02797399,0.01479744,0.06443594,-0.00981992,0.07976446,-0.08970428,0.01898961,0.04382584,-0.00023227,0.01398873,-0.03050118,-0.01689199,-0.00619389,0.02518126,-0.03359056,-0.01118443,-0.04356795,-0.01652876,0.05720714,-0.02480183,0.07837985,-0.01396075,-0.04822442,-0.01545225,0.05515311,-0.1114394,-0.0007343,-0.00104252,-0.01702685,0.01081897,0.00677513,-0.05237536,0.02216754,-0.00391124,-0.02672963,0.01453664,0.06364846,0.00723417,-0.06330405,-0.06732338,0.04529698,0.03236288,-0.0488812,-0.04371085,0.03181988,0.00394471,-0.04045071,0.03861269,0.02502601,-0.01071406,-0.05586249,0.05120289,0.01824632,0.00265458,-0.07704435,-0.04243403,0.03137303,-0.01838719,-0.04110717,0.00684801,-0.03461064,-0.02488359,0.02138284,0.01110747,0.05227863,0.06383514,-0.02241323,0.01492983,-0.01049759,-0.06609023,-0.00692361,0.03127144,-0.03886724,0.09973737,0.04336549,-0.02012915,-0.01966235,-0.06424691,0.01534489,-0.02597084,-0.00897378,0.06497305,0.00588121,-0.11788628,-0.01435261,0.05738824,0.06928162,-0.07191785,-0.02807082,0.02154374,0.04432973,0.02747263,0.09016504,-0.05517943,0.02920744,-0.07022884,-0.21068907,0.02579973,0.01613979,-0.00724384,-0.04067275,-0.02723232,-0.01430698,-0.03793242,-0.05430742,0.06585608,0.14023237,-0.02468841,0.02219048,0.00682813,-0.02385387,-0.00832598,0.02700397,-0.05449073,-0.03098473,-0.02429032,0.01303348,-0.02599801,-0.07750174,-0.11278343,0.04629907,-0.01574504,0.12442137,0.0446373,0.00014773,-0.00663112,0.05075959,-0.02919673,0.01130213,-0.13251355,-0.06012348,0.03352088,0.02283948,0.02757768,0.04610408,0.042937,-0.04312348,0.0005495,-0.02665579,-0.0766658,0.01464804,-0.00565007,-0.05332145,0.01602461,0.00561363,0.02479548,-0.02145791,0.05502369,0.05308718,0.02145772,-0.07133635,-0.00877458,0.00770389,-0.00492151,-0.00801095,0.0441705,0.01733851,-0.00734956,-0.00719404,-0.08870561,0.06593142,0.05241732,0.04441271,-0.02516068,0.08161441,-0.00462626,-0.03123845,0.05385688,0.02947908,0.03125689,0.05226069,-0.01285655,-0.05152889,0.02047719,0.00343908,-0.03850783,-0.00537032,-0.04428441,-0.0047253,0.0158323,-0.03926941,0.02489923,-0.01693149,-0.02495965,-0.01828026,-0.02137692,-0.02948402,-0.01930273,-0.04032768,-0.01734927,0.11399425,-0.0103561,-0.25188571,0.03709774,0.03789219,-0.01359311,-0.03423695,-0.01952098,0.05814779,0.00035347,-0.03130941,-0.04781811,-0.00054319,0.07186007,-0.04354828,-0.06247228,0.03061311,0.02739665,0.0828418,0.00783733,0.08336993,-0.07845666,0.05242228,0.06931821,0.23964337,-0.00455114,0.0250075,0.07091502,-0.06813932,0.06468415,0.09079637,0.06950717,-0.04580279,0.04703759,0.07608758,0.00204669,-0.02484969,-0.01764234,-0.00604862,-0.02092386,0.02114028,-0.00746088,-0.0377866,0.01818569,-0.02022731,0.04386665,0.08983472,-0.09164386,-0.0103304,-0.09183222,-0.02238051,0.02653395,-0.06276438,0.02004896,0.00452477,-0.0015592,-0.0023348,0.03652272,0.00380678,0.01209306,-0.0390365,-0.0070631,0.00917588,-0.06209875,0.06984501,0.02210381,-0.02158241],"last_embed":{"hash":"8cjpem","tokens":250}}},"text":null,"length":0,"last_read":{"hash":"8cjpem","at":1751288812339},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Use with Node#{1}","lines":[30,41],"size":468,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"8cjpem","at":1751288812339}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Table of Contents": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06083686,-0.03696281,0.03241648,-0.08511722,-0.00112683,-0.02058298,-0.09163846,-0.02127512,-0.04152823,0.02246214,0.00646884,-0.06944274,0.00703314,0.03823325,0.01904851,-0.04055542,-0.03698399,0.05216721,-0.01496808,-0.00323325,0.07710017,-0.00628702,0.01409914,-0.03585221,0.01103591,0.08660192,0.00956001,-0.04222728,0.02986487,-0.21440749,-0.03384951,0.0031388,-0.00717156,0.02060095,0.00709891,-0.02517616,-0.02626524,0.00479322,-0.02088167,0.02757578,0.03692811,0.03938451,-0.07568222,-0.01636971,0.00605264,-0.02597197,-0.01650022,0.0064023,0.02025155,-0.03434787,-0.05470015,-0.00044686,0.02778404,-0.00526634,0.02252414,0.06742588,0.07506873,0.09881019,0.00795205,0.04392603,0.0102337,0.05163381,-0.18309882,0.01235754,0.08690584,0.07684352,-0.01988654,-0.0014775,0.02385193,0.00035953,0.00346121,0.03371213,0.05115359,0.04169046,0.02883443,-0.05434076,-0.00570186,0.02753538,0.01027397,-0.03205578,-0.07572016,0.03686983,-0.00679571,0.05310082,0.01694045,-0.01224717,0.02629823,0.00499401,0.06026613,0.01495165,-0.02376195,-0.05366664,0.04738866,0.11974708,-0.01641656,-0.00265199,0.03768179,0.01760527,0.02083749,0.1225391,-0.09459786,0.02489337,-0.01610136,0.00655059,-0.01095432,-0.03988842,-0.01843005,-0.03054736,-0.03938358,0.00717716,-0.03903525,0.00619071,-0.07206585,-0.0559979,-0.04496453,-0.0437139,0.06271216,0.01551676,-0.06918348,0.05519072,-0.00939546,0.08549821,0.00576221,0.01878548,0.06479562,-0.01044966,0.04271129,0.03170909,0.00547187,0.07456426,0.01692831,0.04089041,-0.06820411,-0.00795857,0.01802776,-0.03670926,0.0228381,-0.04305726,0.00979494,0.01619979,0.02598108,-0.04028153,-0.00909504,-0.04684658,-0.02976158,0.1199174,-0.01790076,0.1012018,0.00409769,-0.00546284,-0.01352708,0.01404252,-0.05537224,-0.00141113,0.00005538,-0.00097914,0.00170641,0.02114542,-0.02412997,0.02457888,0.01571984,-0.02813841,-0.01464089,0.09887441,0.0135842,-0.09287134,-0.04576025,0.06387766,0.0286706,-0.05279369,-0.02421526,0.03300073,-0.01459894,-0.00468024,0.05187952,0.02294852,-0.06647772,-0.05289855,0.04641809,0.0334705,0.0147672,-0.06650976,-0.07114945,0.01126223,0.00922634,-0.04506733,0.03240516,-0.02453107,-0.01613448,0.04426435,-0.001718,0.05592999,0.03046338,0.02401345,-0.02137952,-0.06006104,-0.04524974,0.01708749,0.06256094,-0.05774949,0.08069442,0.03850484,-0.03722875,0.04311885,-0.04580294,0.01184215,-0.00520789,-0.02368254,0.0477398,0.03886215,-0.09041274,-0.03327063,0.03490255,0.08551192,-0.08570269,-0.04447358,0.04848567,0.03391764,0.01842812,0.0754087,-0.0612647,-0.02728727,-0.09705339,-0.20261499,0.01583613,0.00288916,-0.0345501,-0.04566483,-0.01844369,-0.04302793,-0.05861847,-0.05608837,0.02685716,0.14142431,-0.02822904,-0.00895347,-0.01557685,-0.0316247,-0.01130831,-0.00523365,-0.05384155,-0.03412927,0.01651718,0.03110349,-0.00042077,-0.05556489,-0.08725946,0.02591309,-0.01128017,0.13701791,0.01476908,0.00585393,0.00329612,0.03859886,-0.04151823,0.01738471,-0.07234219,-0.03708747,0.00167026,-0.00314208,0.02553162,0.0493692,0.00363616,-0.04703995,0.01378271,-0.01585261,-0.08000515,0.04623939,0.00593984,-0.06509928,0.02020595,0.03096289,0.05595692,-0.00953676,0.00887808,0.08748758,0.04700145,-0.04523652,0.01601931,-0.03761402,-0.01469905,-0.01043772,0.04704782,0.00598806,0.0039534,-0.02301509,-0.05594487,0.0680241,0.04280249,0.00937817,-0.01346257,0.02817877,-0.02442276,-0.01463692,0.07954915,0.03014749,0.00209239,0.04492029,-0.02379158,-0.0617649,-0.03407385,0.02423952,0.00047364,-0.0009011,-0.04984859,0.00670126,0.05159257,-0.02980716,0.02100497,-0.00137224,-0.0017582,0.0361119,-0.05601574,-0.03140853,0.00710777,-0.05502097,-0.02398119,0.10270573,0.03258542,-0.25821036,0.03720995,0.01905816,-0.01414716,-0.02991321,-0.03375445,0.05762096,-0.03916064,-0.01138091,-0.00727766,0.00729477,0.04299945,-0.00907156,-0.06720483,0.0104778,0.06021137,0.07256465,-0.01936341,0.08095939,-0.06578377,0.02607393,0.03848594,0.25152841,-0.01688283,0.02033334,0.05310303,-0.06259295,0.02865595,0.04765753,0.05336794,-0.02278485,0.01542472,0.14008801,0.01165058,-0.0528889,0.02018501,0.02330853,-0.03962779,0.0123192,-0.01666983,-0.02904795,0.01441087,-0.03791246,0.02137961,0.11862274,-0.04198337,-0.03838842,-0.1292792,-0.00788908,0.03212213,-0.07577965,0.0495417,0.04755055,-0.00291489,-0.01850533,0.01838551,-0.02221481,0.01345095,-0.04805973,-0.01534085,0.02561126,-0.01284866,0.06218101,0.06510197,-0.0406651],"last_embed":{"hash":"1f89xk9","tokens":437}}},"text":null,"length":0,"last_read":{"hash":"1f89xk9","at":1751288812420},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Table of Contents","lines":[42,80],"size":3375,"outlinks":[{"title":"Examples","target":"#examples","line":3},{"title":"Consuming a source map","target":"#consuming-a-source-map","line":4},{"title":"Generating a source map","target":"#generating-a-source-map","line":5},{"title":"With SourceNode (high level API)","target":"#with-sourcenode-high-level-api","line":6},{"title":"With SourceMapGenerator (low level API)","target":"#with-sourcemapgenerator-low-level-api","line":7},{"title":"API","target":"#api","line":8},{"title":"SourceMapConsumer","target":"#sourcemapconsumer","line":9},{"title":"new SourceMapConsumer(rawSourceMap)","target":"#new-sourcemapconsumerrawsourcemap","line":10},{"title":"SourceMapConsumer.prototype.computeColumnSpans()","target":"#sourcemapconsumerprototypecomputecolumnspans","line":11},{"title":"SourceMapConsumer.prototype.originalPositionFor(generatedPosition)","target":"#sourcemapconsumerprototypeoriginalpositionforgeneratedposition","line":12},{"title":"SourceMapConsumer.prototype.generatedPositionFor(originalPosition)","target":"#sourcemapconsumerprototypegeneratedpositionfororiginalposition","line":13},{"title":"SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)","target":"#sourcemapconsumerprototypeallgeneratedpositionsfororiginalposition","line":14},{"title":"SourceMapConsumer.prototype.hasContentsOfAllSources()","target":"#sourcemapconsumerprototypehascontentsofallsources","line":15},{"title":"SourceMapConsumer.prototype.eachMapping(callback, context, order)","target":"#sourcemapconsumerprototypeeachmappingcallback-context-order","line":17},{"title":"SourceMapGenerator","target":"#sourcemapgenerator","line":18},{"title":"SourceMapGenerator.fromSourceMap(sourceMapConsumer)","target":"#sourcemapgeneratorfromsourcemapsourcemapconsumer","line":20},{"title":"SourceMapGenerator.prototype.addMapping(mapping)","target":"#sourcemapgeneratorprototypeaddmappingmapping","line":21},{"title":"SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)","target":"#sourcemapgeneratorprototypesetsourcecontentsourcefile-sourcecontent","line":22},{"title":"SourceMapGenerator.prototype.toString()","target":"#sourcemapgeneratorprototypetostring","line":24},{"title":"SourceNode","target":"#sourcenode","line":25},{"title":"SourceNode.prototype.add(chunk)","target":"#sourcenodeprototypeaddchunk","line":28},{"title":"SourceNode.prototype.prepend(chunk)","target":"#sourcenodeprototypeprependchunk","line":29},{"title":"SourceNode.prototype.setSourceContent(sourceFile, sourceContent)","target":"#sourcenodeprototypesetsourcecontentsourcefile-sourcecontent","line":30},{"title":"SourceNode.prototype.walk(fn)","target":"#sourcenodeprototypewalkfn","line":31},{"title":"SourceNode.prototype.walkSourceContents(fn)","target":"#sourcenodeprototypewalksourcecontentsfn","line":32},{"title":"SourceNode.prototype.join(sep)","target":"#sourcenodeprototypejoinsep","line":33},{"title":"SourceNode.prototype.replaceRight(pattern, replacement)","target":"#sourcenodeprototypereplacerightpattern-replacement","line":34},{"title":"SourceNode.prototype.toString()","target":"#sourcenodeprototypetostring","line":35}],"class_name":"SmartBlock","last_embed":{"hash":"1f89xk9","at":1751288812420}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Table of Contents#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05674392,-0.02149956,0.02536556,-0.10062797,-0.00443801,-0.04466585,-0.10220042,-0.02644078,-0.04589787,0.03508759,0.01041056,-0.06513581,0.00917889,0.03464584,0.03184187,-0.02324434,-0.01908372,0.03889557,-0.02098888,-0.00233909,0.06735516,0.01880953,0.01496135,-0.03377271,0.0087042,0.07177568,0.016296,-0.03890603,0.02241802,-0.19246773,-0.03107989,0.0014595,-0.01197613,0.01793457,0.00975874,-0.02703655,-0.01696826,-0.01364749,-0.02638299,0.02295802,0.05502255,0.03483499,-0.084264,-0.00119605,-0.00433967,-0.03243107,-0.02617163,-0.00375063,0.02406958,-0.03878024,-0.04537362,0.01635594,0.01453163,-0.0080483,0.02445532,0.06085936,0.06999297,0.11312081,0.0076274,0.0425703,0.01636984,0.05513849,-0.16952579,0.00342896,0.05794114,0.07957587,-0.04423565,0.02628821,0.013473,-0.00228415,0.00376102,0.01792333,0.03084835,0.0497797,0.03139292,-0.06539152,-0.01690527,0.02085445,0.00896436,-0.02387987,-0.07048324,0.02104903,-0.01165343,0.05585882,0.0098963,-0.02111733,0.02024985,0.02324305,0.07717025,0.0294698,-0.02823598,-0.04326459,0.0358814,0.12050402,-0.01632028,-0.01125774,0.02666665,0.01643608,0.00730829,0.12957443,-0.09332088,0.02370042,0.00140733,0.00951232,-0.02785877,-0.03746758,-0.00811538,-0.02834596,-0.04606287,0.01620436,-0.03114407,0.00721453,-0.07309488,-0.04288782,-0.03062357,-0.03689096,0.03504332,0.01625644,-0.08374079,0.03386475,0.00377585,0.0724446,0.02441448,0.02590591,0.07220823,-0.00066781,0.05789286,0.04254714,0.00620206,0.09652112,0.00387651,0.04040622,-0.07466102,0.0043223,0.01603677,-0.04525065,0.0375715,-0.04475431,0.00141291,0.02986155,0.02047826,-0.02909945,0.01549222,-0.07990965,-0.02363923,0.09785171,0.01431896,0.09210659,-0.017014,-0.03368739,-0.0143325,0.01843101,-0.05048404,-0.00397831,0.00238625,-0.00073007,0.02289775,0.0086353,-0.03583658,0.03556199,0.0061663,-0.02643519,-0.0120937,0.10880112,0.01169155,-0.06095957,-0.05780719,0.05898887,0.03016664,-0.06296279,-0.0229705,0.04476755,-0.02718788,-0.00037979,0.06394261,0.0210814,-0.07455534,-0.05069364,0.02619173,0.01840082,0.03783902,-0.04680943,-0.05415139,0.00862644,-0.00035306,-0.0567078,0.03936275,-0.01541036,-0.00731191,0.03967248,-0.03061036,0.04842709,0.02088204,0.00144917,-0.02525159,-0.04856768,-0.05202515,0.01263846,0.06102835,-0.05474984,0.06619319,0.02435957,-0.04510355,0.03833859,-0.05143752,0.00998205,-0.01171083,-0.02794407,0.04909874,0.03308519,-0.09188107,-0.01510322,0.05135181,0.08402752,-0.06847562,-0.05505163,0.03196414,0.02391527,0.00489415,0.05624778,-0.05042707,-0.04801754,-0.09317843,-0.21242021,0.03099349,-0.00139709,-0.05022914,-0.04025584,-0.03667006,-0.0363153,-0.05603262,-0.09415424,0.06306819,0.14678495,-0.05851349,-0.00114897,-0.00879371,-0.01586343,-0.00885439,-0.02572985,-0.05708605,-0.03592785,0.02597998,0.01179261,-0.01992667,-0.02443504,-0.09537753,0.01885301,0.00220547,0.13404304,0.01004717,0.01970482,0.00056464,0.05466813,-0.04989082,0.00988464,-0.08517903,-0.0583914,0.01351067,0.02715807,0.02993488,0.04137908,-0.00042552,-0.04485225,0.01922395,-0.02476418,-0.06902955,0.0369608,-0.01285532,-0.06060737,-0.0184861,0.02808574,0.05246462,0.00397831,0.03251519,0.07806338,0.04066282,-0.0467335,0.00539421,-0.0429506,0.00469042,-0.02914799,0.05574499,-0.00660638,-0.00028435,-0.02471467,-0.06560293,0.06981178,0.05288727,0.00781015,-0.02630582,0.02574367,-0.01488987,-0.02386862,0.08800562,0.02456503,-0.01170065,0.06686319,-0.00681848,-0.07115365,-0.02755156,0.03183686,0.00994061,-0.001686,-0.02583368,0.00271523,0.05124545,-0.02445759,0.04472515,-0.02458844,-0.01562854,0.02793475,-0.05593461,-0.02097736,0.00105764,-0.04878027,-0.01936024,0.10732275,0.02567147,-0.2533977,0.04371706,0.0451753,-0.00546881,-0.03640003,-0.02707029,0.04342381,-0.03196186,-0.01084945,0.00638663,0.03161325,0.03399482,-0.00570136,-0.05543535,0.03389428,0.07143478,0.07728075,-0.00899009,0.08457222,-0.05897553,0.04122416,0.04521725,0.25508854,-0.01568274,0.00642746,0.04643446,-0.06489241,0.03892608,0.02554075,0.05981316,-0.02437779,0.0396358,0.14961262,0.0051675,-0.0304963,0.03191822,0.01914099,-0.03435684,0.01447096,-0.00898139,-0.01404038,0.0220074,-0.06450327,0.0174499,0.08996348,-0.0502655,-0.03640715,-0.14756472,-0.02104487,0.02628002,-0.05333601,0.03486608,0.03763071,0.0085966,-0.00758002,0.03018597,-0.01258227,0.00536362,-0.02066658,-0.00061473,0.01400586,-0.02508498,0.06780516,0.04601326,-0.03022424],"last_embed":{"hash":"1gb64jx","tokens":127}}},"text":null,"length":0,"last_read":{"hash":"1gb64jx","at":1751288812620},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Table of Contents#{1}","lines":[44,48],"size":295,"outlinks":[{"title":"Examples","target":"#examples","line":1},{"title":"Consuming a source map","target":"#consuming-a-source-map","line":2},{"title":"Generating a source map","target":"#generating-a-source-map","line":3},{"title":"With SourceNode (high level API)","target":"#with-sourcenode-high-level-api","line":4},{"title":"With SourceMapGenerator (low level API)","target":"#with-sourcemapgenerator-low-level-api","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"1gb64jx","at":1751288812620}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Table of Contents#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0680792,-0.04614532,0.05079353,-0.08067286,0.00572659,-0.01059757,-0.08034161,-0.03294869,-0.02759424,0.02352269,0.00925245,-0.07100764,0.01403506,0.02908114,0.00898928,-0.05243101,-0.04166995,0.06991595,-0.01378846,-0.01107409,0.08125438,-0.02053117,0.01493937,-0.03030484,-0.0017091,0.10898995,-0.00673418,-0.05606584,0.02712535,-0.20590566,-0.03594392,-0.01198089,-0.00951372,0.00679447,0.02135553,-0.02209325,-0.02204257,0.01241411,-0.03060237,0.032734,0.03938534,0.03193627,-0.05094283,-0.01634271,0.01664034,-0.03183239,-0.01219267,0.02923659,0.02694481,-0.04183666,-0.04726275,-0.01661354,0.03128722,-0.00583702,0.03313909,0.0715847,0.06888124,0.09032146,0.01758841,0.0328262,0.00574033,0.0517936,-0.18852615,0.02756937,0.09671006,0.06054704,-0.00418867,-0.01922007,0.04364528,-0.00374743,0.0009328,0.04581887,0.05768568,0.03685329,0.03419161,-0.05099747,0.00621449,0.02339638,-0.00241025,-0.02118426,-0.08047944,0.0394391,-0.01080977,0.04097653,0.02802648,-0.0004186,0.00561751,0.00185742,0.04520406,-0.00449049,-0.01480867,-0.06090204,0.05776018,0.11840634,-0.03868225,-0.00533276,0.03928983,0.02355512,0.03626923,0.12193629,-0.08002843,0.01737979,-0.0180843,0.013554,-0.00163723,-0.03678197,-0.02678683,-0.04629026,-0.04501727,0.01170074,-0.03727398,0.01149925,-0.06524511,-0.06999663,-0.030632,-0.03318299,0.05459776,0.00616293,-0.07160883,0.06436245,-0.00586856,0.08149474,-0.00126676,0.03272632,0.0565783,-0.01507876,0.03905401,0.02255224,0.00791215,0.0665188,0.03374698,0.04581424,-0.06315915,-0.01691858,0.02186024,-0.01048774,0.03072709,-0.0494965,0.02010332,0.00589597,0.02962591,-0.05522624,-0.01346293,-0.0316991,-0.03356021,0.1318635,-0.0249683,0.09512265,0.00673368,0.00397774,-0.02577068,0.01977192,-0.04875113,0.00489862,0.00671203,-0.00871992,-0.01509992,0.02404458,-0.02474341,0.00555995,0.02560007,-0.03386293,-0.010166,0.12193399,-0.00425877,-0.10520075,-0.02115278,0.06166993,0.01711726,-0.05995053,-0.0192256,0.02571425,-0.00650754,-0.01554879,0.03582682,0.03361799,-0.04495598,-0.06141347,0.05093239,0.02991422,-0.00092218,-0.07251751,-0.05942217,0.01985505,0.01207796,-0.02933277,0.03311546,-0.0356004,-0.02175573,0.03503483,0.00921501,0.05184535,0.02481662,0.02893359,-0.01969238,-0.07516032,-0.03468589,0.01533691,0.0449342,-0.04280084,0.08934887,0.03692455,-0.02770345,0.05360788,-0.04180017,-0.00429365,-0.01611011,-0.01192821,0.0371119,0.05429783,-0.10204215,-0.03836819,0.0263296,0.054628,-0.08676628,-0.04813043,0.0640192,0.02709197,0.02293957,0.08317453,-0.06974726,-0.00528375,-0.09516016,-0.19460656,0.00619158,-0.00364791,-0.02859788,-0.05524685,-0.02084529,-0.04538671,-0.04553124,-0.0311805,0.01983601,0.14593072,-0.00461981,-0.01370385,-0.0198729,-0.04749548,-0.01744387,0.02656099,-0.0630855,-0.04793991,0.00667286,0.0424617,0.01620759,-0.07472901,-0.08355342,0.03067282,-0.02334247,0.13914223,0.02185267,0.00743504,0.0017235,0.03841792,-0.02002852,0.00423747,-0.07061915,-0.01216543,0.00013036,-0.02348208,0.0173457,0.04115337,0.02449484,-0.03741225,0.0161832,-0.00700157,-0.08158412,0.0506159,-0.0050596,-0.067835,0.01767709,0.02339674,0.05573412,-0.01784814,0.00370976,0.07047012,0.04445406,-0.04037562,0.03269028,-0.00820055,-0.01816615,0.01181534,0.03942947,0.00877438,0.01199371,-0.02929169,-0.05342478,0.06993235,0.02720214,0.01472308,-0.01522708,0.03678392,-0.03580384,-0.00672251,0.05494596,0.03345274,0.01178999,0.03038473,-0.03666605,-0.05312107,-0.0316195,0.02493503,-0.00675741,-0.00089366,-0.057072,0.00509904,0.03516239,-0.01310168,0.01374226,0.0140453,0.00505397,0.03261008,-0.05000972,-0.01640097,-0.0004741,-0.05821737,-0.03069099,0.1019188,0.04084763,-0.26189566,0.04302222,0.01314706,-0.02713558,-0.0217487,-0.02315467,0.06179195,-0.04367168,-0.02179607,-0.03047568,-0.00804662,0.07397309,-0.02029404,-0.06000366,0.01033602,0.05079392,0.09071072,-0.01688058,0.06723437,-0.06247491,0.01755367,0.04548033,0.24974406,-0.0081987,0.03492563,0.04984972,-0.04972406,0.0163307,0.06300992,0.05827435,-0.01319709,-0.00345843,0.12188739,0.02560371,-0.06251676,0.01762641,0.03150124,-0.03822719,0.00469008,-0.02874907,-0.03867981,-0.00325252,-0.02315579,0.02741443,0.12008712,-0.02784108,-0.0611059,-0.1129444,-0.00052815,0.02759996,-0.08851738,0.03589528,0.02615008,-0.02265238,-0.02903329,0.01433394,-0.01442829,0.00831642,-0.0638845,-0.01779478,0.02813577,-0.01590595,0.05808361,0.06391515,-0.02841102],"last_embed":{"hash":"t72k4z","tokens":437}}},"text":null,"length":0,"last_read":{"hash":"t72k4z","at":1751288812666},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Table of Contents#{2}","lines":[49,78],"size":2976,"outlinks":[{"title":"API","target":"#api","line":1},{"title":"SourceMapConsumer","target":"#sourcemapconsumer","line":2},{"title":"new SourceMapConsumer(rawSourceMap)","target":"#new-sourcemapconsumerrawsourcemap","line":3},{"title":"SourceMapConsumer.prototype.computeColumnSpans()","target":"#sourcemapconsumerprototypecomputecolumnspans","line":4},{"title":"SourceMapConsumer.prototype.originalPositionFor(generatedPosition)","target":"#sourcemapconsumerprototypeoriginalpositionforgeneratedposition","line":5},{"title":"SourceMapConsumer.prototype.generatedPositionFor(originalPosition)","target":"#sourcemapconsumerprototypegeneratedpositionfororiginalposition","line":6},{"title":"SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)","target":"#sourcemapconsumerprototypeallgeneratedpositionsfororiginalposition","line":7},{"title":"SourceMapConsumer.prototype.hasContentsOfAllSources()","target":"#sourcemapconsumerprototypehascontentsofallsources","line":8},{"title":"SourceMapConsumer.prototype.eachMapping(callback, context, order)","target":"#sourcemapconsumerprototypeeachmappingcallback-context-order","line":10},{"title":"SourceMapGenerator","target":"#sourcemapgenerator","line":11},{"title":"SourceMapGenerator.fromSourceMap(sourceMapConsumer)","target":"#sourcemapgeneratorfromsourcemapsourcemapconsumer","line":13},{"title":"SourceMapGenerator.prototype.addMapping(mapping)","target":"#sourcemapgeneratorprototypeaddmappingmapping","line":14},{"title":"SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)","target":"#sourcemapgeneratorprototypesetsourcecontentsourcefile-sourcecontent","line":15},{"title":"SourceMapGenerator.prototype.toString()","target":"#sourcemapgeneratorprototypetostring","line":17},{"title":"SourceNode","target":"#sourcenode","line":18},{"title":"SourceNode.prototype.add(chunk)","target":"#sourcenodeprototypeaddchunk","line":21},{"title":"SourceNode.prototype.prepend(chunk)","target":"#sourcenodeprototypeprependchunk","line":22},{"title":"SourceNode.prototype.setSourceContent(sourceFile, sourceContent)","target":"#sourcenodeprototypesetsourcecontentsourcefile-sourcecontent","line":23},{"title":"SourceNode.prototype.walk(fn)","target":"#sourcenodeprototypewalkfn","line":24},{"title":"SourceNode.prototype.walkSourceContents(fn)","target":"#sourcenodeprototypewalksourcecontentsfn","line":25},{"title":"SourceNode.prototype.join(sep)","target":"#sourcenodeprototypejoinsep","line":26},{"title":"SourceNode.prototype.replaceRight(pattern, replacement)","target":"#sourcenodeprototypereplacerightpattern-replacement","line":27},{"title":"SourceNode.prototype.toString()","target":"#sourcenodeprototypetostring","line":28}],"class_name":"SmartBlock","last_embed":{"hash":"t72k4z","at":1751288812666}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04558332,-0.05609035,0.01473841,-0.08344301,0.00856199,-0.03383876,-0.10846519,-0.01657265,-0.03537397,-0.00900201,-0.00949038,-0.06712323,-0.0174153,0.03718538,0.0404432,-0.01834663,-0.05056509,0.02942549,-0.02542756,0.01243525,0.04665653,0.02892222,0.01874462,-0.02220544,-0.02354502,0.09432998,0.03351264,-0.04399197,0.00709121,-0.18860319,-0.03983788,-0.03413721,-0.01643429,-0.00511468,-0.00734404,-0.03574006,-0.00488556,0.00509904,-0.0206816,0.01264595,0.04886909,0.02974436,-0.06543192,-0.02485207,0.00297148,-0.04416306,-0.02956367,0.01340899,0.038043,-0.01629966,-0.03498043,-0.00382297,0.02359743,-0.03113168,0.05039153,0.07588587,0.04470463,0.11606827,0.01287724,0.05068194,0.01210592,0.03558922,-0.18742144,0.031903,0.0761834,0.07521416,-0.0282376,0.00631043,0.01417026,-0.02460519,-0.00875972,0.016577,0.03906916,0.0620777,0.00448428,-0.0611023,-0.00365888,0.06139715,-0.00424147,-0.01567589,-0.07096078,0.03772018,0.03182416,0.06453631,0.05052179,-0.00668157,0.01584544,-0.00946396,0.08305489,0.00928633,-0.00330635,-0.05318356,0.06697098,0.11067854,-0.03191322,-0.00692555,0.04003903,0.04758666,0.00745201,0.10967562,-0.08366868,0.04059676,0.02660421,0.00720952,-0.02171019,-0.03972305,-0.01736594,-0.04447349,-0.03035634,0.01420558,-0.03657503,-0.01796055,-0.07398588,-0.05033149,-0.01660709,-0.03417284,0.04341214,-0.00946192,-0.05780215,0.0494886,0.01249085,0.06586293,0.02328975,0.02701906,0.06496011,0.00647941,0.0477491,0.03256976,0.01239629,0.06570803,0.00884127,0.05125516,-0.08703739,0.02764201,-0.00133232,-0.03280269,0.02932443,-0.02000242,0.01516278,0.02284321,0.03295211,-0.04281731,0.00034801,-0.0396862,-0.048906,0.07170366,-0.00052537,0.0771785,-0.02315195,-0.03091998,0.01765944,0.05041593,-0.0640969,0.037311,-0.00567734,0.01548999,0.01719703,0.02999616,-0.04643059,0.03185732,-0.00783423,-0.03239968,0.0032433,0.06614459,0.0326623,-0.07680781,-0.02955816,0.0701779,0.02049671,-0.07738408,-0.04263785,0.00253055,-0.02634893,-0.01714847,0.03278802,0.01033705,-0.0567533,-0.03737118,0.03279959,0.01836078,-0.00505136,-0.03629214,-0.054604,0.02800192,-0.0025965,-0.04998178,0.02452418,-0.04113568,-0.01192128,0.02922302,-0.00028623,0.03435292,0.03186911,-0.0085827,-0.03581716,-0.01045111,-0.04286923,-0.01783203,0.04135901,-0.04829747,0.12696649,0.03348006,-0.04814231,0.0077163,-0.06228204,0.02082855,-0.00024923,-0.01370149,0.06286383,0.01305698,-0.102943,-0.00602824,0.02691901,0.06830534,-0.0521594,-0.05052527,0.02632343,0.02621888,0.03513983,0.10002346,-0.06682412,-0.03817805,-0.09785545,-0.21337916,0.01670837,0.01574279,-0.03411134,-0.03045873,-0.03217749,-0.04300006,-0.04831928,-0.07226948,0.05616347,0.12939763,-0.0291082,0.00289699,-0.01394974,-0.02336252,0.00372036,-0.01214874,-0.04414343,-0.04413447,0.00686588,-0.01063505,-0.0083221,-0.05493322,-0.06862548,0.01764003,-0.02366667,0.14226606,0.01553241,-0.00098414,-0.01975241,0.0602774,-0.04735249,0.0597811,-0.09140252,-0.05226016,-0.00008713,0.02312575,0.02325699,0.03458744,0.03205081,-0.04477748,-0.0018535,-0.02186228,-0.0627088,0.02370614,-0.00119021,-0.05761782,-0.01632257,0.04125796,0.04967191,0.0048372,0.02736794,0.07648937,0.02854773,-0.04400275,0.04192843,-0.01932704,-0.01055955,-0.02254938,0.03394834,0.01509758,-0.00609068,0.00626819,-0.08583686,0.09421912,0.08658084,-0.02593206,-0.04698363,0.0279723,-0.00712044,-0.04995829,0.07342342,0.02994724,-0.02116024,0.01966653,-0.02613636,-0.04259136,-0.03550929,0.02261483,0.00973527,0.00081167,-0.03066925,0.02214488,0.02225475,-0.03221167,0.0306107,0.00694229,-0.01204822,0.02646362,-0.06524661,-0.03126138,-0.02191795,-0.03690191,-0.02328409,0.10605613,0.02565413,-0.25112909,0.03371621,0.02700276,-0.00556185,-0.02277857,-0.01943718,0.0524087,-0.03562642,0.00490526,-0.00942135,0.02591344,0.059754,-0.02001774,-0.05266073,0.04909736,0.06832471,0.05503329,-0.01589064,0.11230194,-0.07088655,0.05592163,0.04866721,0.23636192,-0.01040709,0.03402188,0.08085302,-0.07788813,0.04839252,0.06311895,0.04189432,-0.00821134,0.02940144,0.13866104,0.01612568,-0.04483066,0.01768247,-0.00887803,-0.02798941,-0.00503906,-0.00935835,-0.01792873,0.01183498,-0.03744515,0.03028557,0.10758901,-0.06691378,-0.03041212,-0.14483859,-0.02931522,0.01600186,-0.05895968,0.02395346,0.05577573,0.0052282,-0.01158766,0.05586501,-0.01543,0.00320943,-0.04553416,-0.02242442,0.0201839,-0.04400681,0.06921489,0.01198591,-0.03364804],"last_embed":{"hash":"10ou0gk","tokens":488}}},"text":null,"length":0,"last_read":{"hash":"10ou0gk","at":1751288813117},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples","lines":[81,183],"size":2114,"outlinks":[{"title":"**Compiling to JavaScript, and Debugging with Source Maps**","target":"https://hacks.mozilla.org/2013/05/compiling-to-javascript-and-debugging-with-source-maps/","line":45}],"class_name":"SmartBlock","last_embed":{"hash":"10ou0gk","at":1751288813117}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Consuming a source map": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0346574,-0.06109256,0.01645988,-0.08695389,0.01015924,-0.03743353,-0.10285258,-0.02014262,-0.03413985,-0.00526611,-0.0027669,-0.07579342,-0.02270766,0.03146389,0.05103183,-0.02138226,-0.0444722,0.03400571,-0.02810241,0.02150355,0.04839492,0.01995821,0.02660698,-0.0127896,-0.00948882,0.07467508,0.02461206,-0.05150528,0.02320407,-0.19176748,-0.03220465,-0.02898759,-0.01670895,-0.00006456,0.01197614,-0.03032551,-0.01028685,0.00284367,-0.01969166,0.00990091,0.05055911,0.03721731,-0.07261462,-0.01691902,0.00307681,-0.04443645,-0.01894568,0.01182778,0.05190357,-0.01825899,-0.03853247,-0.00954667,0.02561292,-0.02856484,0.05347322,0.07715999,0.04766399,0.10297301,0.0153979,0.04521424,0.00740605,0.03548318,-0.19396736,0.03626022,0.08017473,0.07450367,-0.03009045,0.00342525,0.01711046,-0.01331288,-0.00905023,0.01743508,0.03127704,0.05718978,-0.00596442,-0.067312,-0.01055579,0.05599536,-0.02086019,-0.01099775,-0.07810148,0.05388086,0.04099343,0.05377918,0.03811055,-0.01489271,0.01554351,-0.01256284,0.07507785,0.01705458,-0.01009839,-0.04777521,0.06633335,0.12725981,-0.02979847,-0.01204749,0.03845007,0.05023845,0.01227979,0.11053377,-0.07642875,0.04289261,0.02834993,0.00223774,-0.01793848,-0.02907894,-0.01497893,-0.03043564,-0.03683085,0.01865388,-0.03187009,-0.02166187,-0.08096951,-0.05807662,-0.01534862,-0.0389709,0.04581434,-0.00332154,-0.0522281,0.05541424,-0.00045505,0.06703142,0.02311931,0.02167608,0.0557171,-0.00206726,0.06464116,0.03089583,0.00805354,0.06610151,0.0097689,0.04319595,-0.08154345,0.02529921,0.00217511,-0.02076302,0.02899605,-0.02002574,0.01233907,0.01796651,0.03304563,-0.05215855,-0.00323998,-0.0503847,-0.05424208,0.08871613,0.0075972,0.07237537,-0.02614211,-0.02151669,0.01469402,0.04738555,-0.05878294,0.03629974,-0.01171094,-0.00370587,0.02100849,0.03180591,-0.04654462,0.03362745,-0.00149994,-0.03911595,0.00258627,0.07428046,0.03768397,-0.08034765,-0.01562069,0.07361504,0.00816356,-0.07592321,-0.03251778,0.00253959,-0.03601474,-0.01123686,0.0412931,0.02366925,-0.05905082,-0.05167212,0.03601359,0.01304259,-0.00533482,-0.03339138,-0.04325239,0.03016926,0.00178886,-0.04278833,0.02492432,-0.04061331,-0.00826515,0.02397652,0.00473753,0.02939856,0.02465818,0.00536903,-0.04783074,-0.01453072,-0.03579208,-0.02117374,0.02574231,-0.05949508,0.12670095,0.03587449,-0.06054922,0.01047489,-0.0591462,0.02421705,-0.00840792,-0.01203286,0.05111864,0.01170266,-0.09990394,-0.00432413,0.02430505,0.06776755,-0.049291,-0.05182033,0.01932326,0.02479947,0.04257803,0.10532407,-0.060151,-0.04106178,-0.09462664,-0.21519734,0.03405373,0.01541444,-0.03451866,-0.00251561,-0.02564652,-0.04387363,-0.05056679,-0.06486229,0.05763387,0.12888256,-0.03052113,0.00771432,-0.0125115,-0.01759971,-0.00010004,-0.00797841,-0.0471695,-0.03112337,-0.00073589,-0.00956772,-0.01619634,-0.0604564,-0.06430852,0.01931904,-0.02507326,0.1571013,0.01500957,-0.01154582,-0.01494242,0.05452259,-0.03931571,0.05101173,-0.0966026,-0.04930739,-0.01080886,0.02195534,0.0172453,0.03265045,0.02377762,-0.04729075,-0.00059794,-0.01745939,-0.0572154,0.03056383,0.02026723,-0.06207545,0.00193272,0.03064252,0.04955818,-0.00140031,0.04267165,0.06370084,0.02995874,-0.03810417,0.03411684,-0.02696334,-0.00913862,-0.02893484,0.03403398,0.00488803,-0.00105115,0.00444007,-0.09049294,0.0954223,0.09246908,-0.02772371,-0.03952564,0.01655919,-0.01201547,-0.03828301,0.05894705,0.02946983,-0.02473229,0.02175318,-0.02332456,-0.0429318,-0.03073677,0.02002285,0.0177083,0.00006278,-0.04996929,0.01693326,0.02435363,-0.02311547,0.03579916,0.01373515,-0.01247078,0.03399437,-0.06126053,-0.03140768,-0.03054225,-0.04345769,-0.01743716,0.09955939,0.02357262,-0.2589331,0.0498206,0.02803184,-0.00404735,-0.01281021,-0.0167832,0.02812674,-0.04095414,0.0091948,-0.01642538,0.0242875,0.05065456,-0.0084569,-0.03452738,0.03512257,0.06445208,0.06507494,-0.0098995,0.11400095,-0.07298689,0.06709821,0.05744789,0.2340287,-0.00957573,0.03557485,0.06241051,-0.08805051,0.04409261,0.05281909,0.04921914,-0.00666232,0.0116047,0.13314615,0.00877299,-0.03853624,0.01848989,-0.00739064,-0.03109582,0.00263692,-0.0092723,-0.03448308,0.00946171,-0.05651999,0.02828279,0.10909962,-0.06221627,-0.0348479,-0.1372612,-0.03859957,0.0117752,-0.04622326,0.02032543,0.04909124,0.00690001,-0.00030744,0.05894742,-0.02843508,0.00047811,-0.04248233,-0.01976411,0.01704214,-0.03951506,0.06815129,-0.00223725,-0.03500607],"last_embed":{"hash":"16o1uis","tokens":389}}},"text":null,"length":0,"last_read":{"hash":"16o1uis","at":1751288813442},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Consuming a source map","lines":[83,121],"size":797,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"16o1uis","at":1751288813442}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Consuming a source map#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03160767,-0.05989824,0.0127798,-0.0892292,0.0152825,-0.0372005,-0.09927049,-0.01944767,-0.03468603,-0.00336018,-0.00104104,-0.07504898,-0.02614311,0.03185087,0.05265872,-0.02065522,-0.04694094,0.03264158,-0.0323915,0.02164268,0.05089305,0.0173439,0.02526898,-0.01664474,-0.01100432,0.07569281,0.02192703,-0.05063984,0.01799887,-0.19069059,-0.03110031,-0.03260041,-0.01481582,0.00040354,0.00707066,-0.03686957,-0.01124943,0.00483639,-0.01475519,0.00711424,0.04890107,0.03456384,-0.07766864,-0.02309291,0.0003249,-0.04288537,-0.02099574,0.01538447,0.05663957,-0.0165254,-0.03919007,-0.01044027,0.02557332,-0.02908474,0.05560669,0.07958255,0.0459629,0.09916701,0.01825545,0.04583878,0.00775467,0.03853431,-0.19054972,0.03631164,0.0720781,0.07256598,-0.03268074,0.00578517,0.01651455,-0.01582276,-0.00918325,0.01863649,0.02959139,0.05240174,-0.00752016,-0.07375336,-0.01103797,0.05093345,-0.02214444,-0.00961394,-0.07730106,0.0539913,0.04020299,0.05106836,0.0386689,-0.01508659,0.01316132,-0.01415278,0.07570723,0.01869094,-0.00884375,-0.04594899,0.06766661,0.12660272,-0.02990774,-0.01365635,0.03889934,0.05451516,0.01217943,0.1132982,-0.07663432,0.04690013,0.03042628,0.0008894,-0.01726784,-0.03272491,-0.01788403,-0.0245164,-0.03827242,0.01857033,-0.02894117,-0.01854843,-0.08352796,-0.05382704,-0.01706871,-0.03113403,0.04876017,-0.0040354,-0.05143776,0.05831132,0.00285357,0.06947595,0.02395988,0.02451394,0.05769745,-0.00145585,0.06775752,0.02876763,0.00624337,0.06319991,0.00610535,0.04017742,-0.08619394,0.02854674,0.00418916,-0.0180665,0.02998745,-0.02208898,0.01488988,0.01982853,0.02856279,-0.05128732,-0.00553478,-0.05137091,-0.05038612,0.09060975,0.00592053,0.07303407,-0.02552553,-0.02531257,0.01315749,0.05067398,-0.06199495,0.03224619,-0.01344014,-0.00934938,0.01956289,0.02614487,-0.04427703,0.03118638,-0.00224781,-0.03439505,0.00543869,0.07194762,0.03500084,-0.07394589,-0.01845817,0.07600017,0.00848875,-0.07531346,-0.03403262,-0.00107442,-0.04244447,-0.01282783,0.03753385,0.02063563,-0.06051329,-0.048838,0.03601049,0.00784885,-0.00585494,-0.03305434,-0.04149942,0.02931441,0.00340893,-0.04186556,0.02467175,-0.04023659,-0.00646088,0.02803558,0.00247337,0.02457118,0.02208815,0.00148943,-0.04448134,-0.01329354,-0.03726999,-0.01430871,0.02296533,-0.05603303,0.13146205,0.03388431,-0.06196684,0.00951824,-0.05758747,0.02612855,-0.00918027,-0.01343167,0.05359274,0.01435301,-0.1024035,0.00003402,0.0260386,0.06641363,-0.04200893,-0.04792049,0.02312027,0.02396285,0.04321242,0.10464731,-0.05813422,-0.04354779,-0.09499479,-0.21258856,0.03479255,0.01882841,-0.03146723,-0.00105505,-0.02736553,-0.04612103,-0.05139587,-0.06453611,0.05727031,0.1260954,-0.02704689,0.00739939,-0.0092557,-0.01432837,0.00454891,-0.00306056,-0.04974664,-0.03259551,0.0020147,-0.01602978,-0.02083045,-0.06158392,-0.05875136,0.01896746,-0.02453722,0.16011737,0.01722243,-0.01609919,-0.01867765,0.05584611,-0.03851956,0.04966401,-0.09749728,-0.05174226,-0.0129034,0.02004864,0.0129021,0.03295708,0.01913282,-0.04989291,-0.00726774,-0.02249353,-0.05994064,0.03254105,0.02003516,-0.06198923,-0.00095026,0.03476238,0.04882956,0.00274484,0.04482267,0.0642523,0.02973387,-0.03844043,0.03219606,-0.02623419,-0.00646583,-0.0343052,0.03493552,0.0027794,-0.00403864,0.00093395,-0.09289859,0.09181988,0.09151144,-0.02606685,-0.04048973,0.01913577,-0.01259561,-0.03695318,0.05700239,0.02629293,-0.02033273,0.02181894,-0.02456339,-0.0462406,-0.03246173,0.02263877,0.02014717,0.00755423,-0.048273,0.01793133,0.02739737,-0.02474731,0.02852979,0.01071192,-0.01302972,0.03316473,-0.06512645,-0.03391643,-0.03374461,-0.04892521,-0.02153589,0.09722587,0.02580642,-0.25637999,0.04859198,0.02608971,-0.00048508,-0.00328584,-0.01974384,0.03143194,-0.03565733,0.01040161,-0.01254084,0.02629418,0.05408897,-0.00882938,-0.02859068,0.03930276,0.06297322,0.06983589,-0.0092968,0.11311656,-0.06997696,0.07027398,0.05952201,0.23624542,-0.00653766,0.03427916,0.06079147,-0.08497913,0.05190426,0.05461088,0.04978136,-0.00814637,0.01132501,0.13381939,0.00775977,-0.03689536,0.01563798,-0.01048778,-0.02974744,0.00015154,-0.01113539,-0.03197781,0.00615885,-0.0565909,0.02442216,0.10891751,-0.0629447,-0.03547662,-0.13969991,-0.03799486,0.01512034,-0.04611126,0.02522237,0.048678,0.00899251,0.00129474,0.06086723,-0.0300246,0.00025468,-0.03853781,-0.01928658,0.01840923,-0.03871079,0.06471042,-0.00073555,-0.0366178],"last_embed":{"hash":"c9v22y","tokens":387}}},"text":null,"length":0,"last_read":{"hash":"c9v22y","at":1751288813837},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Consuming a source map#{1}","lines":[85,121],"size":769,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"c9v22y","at":1751288813837}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Generating a source map": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08198214,-0.02794576,0.03709361,-0.05803644,-0.00412815,-0.02420062,-0.08819473,0.00301599,-0.03824293,0.01107731,0.00686961,-0.05243521,-0.00914115,0.01751828,0.02330108,-0.00017047,-0.05230213,0.03005334,-0.00808946,0.00077117,0.05715313,0.02145173,0.02107841,-0.06652105,-0.00789848,0.07963104,0.05010973,-0.04283623,-0.01377349,-0.18517174,-0.05124469,-0.02443052,-0.03983116,-0.01015084,-0.00417081,-0.04215732,-0.00430397,0.00186,-0.02751223,0.02788519,0.03394915,0.04080755,-0.03937793,-0.03361581,-0.00251395,-0.02473602,-0.03079927,-0.0187533,0.00910239,-0.03157296,-0.02911988,-0.00002165,0.01388603,-0.02844968,0.02610436,0.05599949,0.0449833,0.12118586,0.01808005,0.04758054,0.02078057,0.02463892,-0.17608461,0.02513842,0.07376213,0.06112837,-0.04063633,-0.00223438,-0.00723869,-0.03666923,-0.01772416,0.00992776,0.0583609,0.07873387,0.01955293,-0.04480296,-0.01448831,0.04902311,0.00674305,-0.02228007,-0.05963293,0.00448009,0.01735963,0.08855018,0.04412987,-0.01833918,0.04354195,0.00119995,0.08010796,0.00782995,-0.00294429,-0.05830237,0.03587422,0.07656723,-0.03645202,0.01608253,0.03762383,0.03020823,-0.00648605,0.12792444,-0.07084712,0.02572429,-0.00602816,0.01973892,-0.04272878,-0.04761611,-0.01761522,-0.07065805,-0.01218088,-0.00599393,-0.05305664,0.0284004,-0.02840236,-0.07107931,-0.02378884,-0.00915478,0.01260404,-0.02409698,-0.09437782,0.03407587,0.03676833,0.03994592,-0.0030609,0.03122946,0.06433929,0.02418629,-0.0072158,0.04287531,0.02668,0.07235785,0.02280412,0.0722139,-0.07069476,0.02603263,0.0135058,-0.03812557,0.00539568,-0.00159123,0.0160341,0.02882484,0.05044547,-0.05194486,-0.01129946,-0.00364065,-0.01119828,0.06521823,-0.04796667,0.08172502,-0.0097494,-0.03382682,-0.00653495,0.03170045,-0.07766999,0.02767524,0.00526258,0.03135059,0.00876666,0.011787,-0.04904585,0.02061409,-0.01790294,-0.0012905,0.01192545,0.0721797,0.03030357,-0.05826888,-0.05867164,0.03343716,0.06022134,-0.07224526,-0.05527903,0.00817843,-0.00400705,-0.02879338,0.04653362,-0.02281076,-0.03934838,-0.02249017,0.03392425,0.03663183,0.01434061,-0.03619972,-0.05795957,0.02405714,-0.0165639,-0.05419457,0.02816295,-0.03404721,-0.01335765,0.05348652,-0.0073847,0.04883874,0.02940439,-0.0219294,-0.01576205,-0.02052496,-0.03661815,-0.02079567,0.08828847,-0.02410109,0.13565005,0.01562533,-0.0218407,0.01674308,-0.06692839,0.01813518,-0.00047321,-0.0473912,0.03046739,0.02609191,-0.08909333,-0.00988805,0.06639165,0.0738966,-0.04427168,-0.04422315,0.01556326,0.04261732,0.02428554,0.08143592,-0.08421357,-0.02595264,-0.08515742,-0.22052287,-0.01145315,0.0247503,-0.02769658,-0.05161793,-0.02050925,-0.03776954,-0.03412725,-0.0886997,0.04116796,0.12647474,-0.01655586,0.01023033,-0.0069505,-0.03268486,-0.02679028,-0.01849392,-0.04049914,-0.032556,0.0179703,-0.01095259,0.00251394,-0.02975419,-0.09726024,0.01087058,-0.03270989,0.11563288,0.02689393,0.0643485,-0.04407746,0.05057228,-0.04503706,0.05275254,-0.09163035,-0.00184407,0.0004734,0.02349897,0.03767172,0.02515166,0.02144171,-0.03695039,-0.01159321,-0.04360876,-0.05371355,0.03074888,-0.04376071,-0.04099832,-0.01033033,0.06007862,0.07684414,-0.01427428,0.01296999,0.0879856,0.02687026,-0.04989847,0.01660874,-0.02752183,-0.03290023,0.01610376,0.04335645,0.03557348,-0.02765463,-0.01575433,-0.07644499,0.06606232,0.0715234,0.0041011,-0.05503982,0.04938895,-0.01254553,-0.03463831,0.07810929,0.02475085,-0.02734155,0.03606286,-0.00086166,-0.04349969,-0.03641726,0.02160978,-0.00642064,-0.00240389,-0.02040446,0.0277236,0.01365565,-0.04176241,0.04101165,0.01256901,-0.01179419,0.03703808,-0.06868351,-0.05997626,0.01804263,-0.03728276,-0.01937623,0.09365789,0.0209173,-0.2685411,0.00281357,0.03028161,-0.0229107,-0.03803604,-0.00055711,0.07101204,-0.02375844,-0.01234185,-0.00864492,-0.01504682,0.04704875,-0.00391364,-0.0650766,0.03838538,0.05745093,0.03564243,-0.00393273,0.1170792,-0.05149702,0.05171772,0.04672064,0.24098909,-0.01609568,0.03639353,0.10357347,-0.03355786,0.05032801,0.05466053,0.02985933,-0.02032665,0.05501821,0.15257408,0.0208328,-0.033316,0.00349865,0.01181518,-0.00128083,0.00372157,-0.03240475,-0.00094934,0.0085437,-0.00997006,0.02897474,0.10986604,-0.05268311,-0.02363526,-0.1298143,0.00494437,0.02395767,-0.09670674,0.05072919,0.05301362,-0.0111671,-0.02041241,0.01926785,0.00623027,-0.0151043,-0.06672495,-0.01292035,0.03060604,-0.06336524,0.05945042,0.05530486,-0.00400662],"last_embed":{"hash":"13wyla7","tokens":506}}},"text":null,"length":0,"last_read":{"hash":"13wyla7","at":1751288814091},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Generating a source map","lines":[122,183],"size":1303,"outlinks":[{"title":"**Compiling to JavaScript, and Debugging with Source Maps**","target":"https://hacks.mozilla.org/2013/05/compiling-to-javascript-and-debugging-with-source-maps/","line":4}],"class_name":"SmartBlock","last_embed":{"hash":"13wyla7","at":1751288814091}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Generating a source map#With SourceNode (high level API)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08006651,-0.02834092,0.04004911,-0.05508758,-0.02477423,-0.03139123,-0.05284917,0.00390646,-0.03508796,0.03184824,0.01594041,-0.06033275,-0.00565209,0.00772021,0.01707269,0.0101992,-0.03771975,0.02956579,-0.01766447,0.01109325,0.07486068,0.01823533,0.03004813,-0.07529762,-0.00331459,0.07002135,0.03550302,-0.04878869,-0.02419842,-0.17896673,-0.04413182,-0.02461522,-0.04996086,0.00131467,-0.00649115,-0.02770699,-0.01424769,-0.02720217,-0.03022361,0.02453814,0.02665802,0.04371423,-0.03427553,-0.04116118,0.00072058,-0.01838007,-0.02465006,-0.0215428,0.02490494,-0.03299795,-0.03504708,0.01768295,0.00686892,-0.02952008,0.02552023,0.05753997,0.04928696,0.12071434,0.00611703,0.03346564,0.02982683,0.03410223,-0.17251857,0.03329787,0.06708994,0.04791239,-0.04619974,-0.01439638,-0.00988499,-0.0383272,-0.01101705,0.00922406,0.05547163,0.09158312,0.03712786,-0.04437585,-0.0330252,0.03952258,0.01592115,-0.01393286,-0.05367216,0.00617939,0.01206298,0.0783343,0.02793911,-0.03446457,0.04124492,-0.0095604,0.08364888,0.01919641,-0.02215201,-0.03510738,0.03352698,0.08583102,-0.02950263,0.01665845,0.03343716,0.03997294,-0.00104248,0.12401287,-0.05980315,0.03252336,-0.03269184,0.01426602,-0.05112524,-0.03556909,-0.02176149,-0.05664502,-0.02274768,-0.01803632,-0.05440405,0.04564933,-0.04215438,-0.09219772,-0.03529337,-0.00092105,0.02584106,-0.02651927,-0.101752,0.03948357,0.02699668,0.04746853,-0.02245441,0.03262449,0.04725542,0.0274142,0.02133051,0.03278251,0.0100473,0.07492338,0.03382511,0.06144901,-0.07106708,0.0135078,0.01633502,-0.02896025,0.00458097,-0.02402065,0.00594661,0.02091522,0.06626921,-0.07472468,-0.01495168,0.00190192,0.00848748,0.07756648,-0.04414594,0.07729616,-0.01002204,-0.01633828,-0.02574554,0.02475174,-0.07288423,0.02064946,0.00387927,0.02110799,-0.00217384,-0.00715482,-0.03594002,0.00466934,-0.01051598,-0.00368145,0.00533836,0.0749121,0.02877404,-0.06642786,-0.05724075,0.02325926,0.06803113,-0.07536677,-0.03844557,0.01323736,-0.01015716,-0.02397338,0.06316978,-0.02130775,-0.04365319,-0.04396291,0.02513546,0.03790427,0.01577669,-0.02907453,-0.04149929,0.01662074,-0.01701811,-0.04593519,0.04081079,-0.04188706,-0.00819887,0.05370491,-0.0034708,0.06270377,-0.00190902,-0.02056508,-0.0045627,-0.02590977,-0.03527959,-0.01830462,0.09178564,-0.01942451,0.13053504,0.0016229,-0.02879563,0.01133488,-0.05403364,0.01580011,0.00254299,-0.04985413,0.00258546,0.03600385,-0.07366206,-0.02196565,0.06635122,0.10524328,-0.0670332,-0.04874023,0.01478122,0.04872255,0.0421916,0.07232414,-0.08133702,-0.01185443,-0.06438053,-0.21570139,0.00510832,0.03134901,-0.03950257,-0.02813761,-0.01566169,-0.05396473,-0.02664397,-0.08822845,0.03126188,0.14303176,-0.0115706,0.00216312,0.01370848,-0.0357222,-0.03085492,-0.03550562,-0.03109563,-0.0190116,0.009677,-0.00405658,0.00152306,-0.03918491,-0.1003189,0.01876176,-0.02553135,0.11511312,0.02579527,0.08992285,-0.03624197,0.05831116,-0.04595022,0.04080073,-0.09184501,0.02122414,-0.00794274,0.01926548,0.04343957,0.01355583,0.0112551,-0.01867146,-0.02089977,-0.04710119,-0.05855696,0.05694655,-0.04533353,-0.04709254,-0.00768419,0.06434359,0.07903688,-0.02466688,0.01116142,0.08030409,0.03532892,-0.02883988,0.00306334,-0.02800343,-0.02701226,0.02027132,0.04044265,0.02975289,0.00055866,-0.01806477,-0.06298583,0.04979576,0.06844374,-0.00358081,-0.0387693,0.04906092,-0.02230165,-0.02406779,0.05530195,0.0242607,-0.03254939,0.01430863,0.00496826,-0.04925316,-0.02627699,0.01851007,0.00275812,0.00443157,-0.02956296,0.02549897,0.00851712,-0.0254756,0.03892456,0.01676353,-0.00036093,0.07121072,-0.06554642,-0.05835987,0.01176802,-0.04779722,-0.00478649,0.08669364,0.00107395,-0.27559105,0.01908479,0.02838147,-0.01493729,-0.04328075,0.00742763,0.0757243,-0.0291577,-0.02522753,-0.00753736,-0.02849657,0.04685947,0.00951724,-0.03460027,0.02933164,0.04430043,0.05644927,0.00182412,0.11545578,-0.04862334,0.04233065,0.06174038,0.24779616,-0.02551146,0.05171414,0.09237511,-0.03942015,0.04269004,0.04057791,0.03231896,-0.01648196,0.03558939,0.14902613,0.01790777,-0.01357672,-0.01384218,0.02247544,0.01982277,-0.00832505,-0.03958347,-0.01363067,0.01485221,-0.02543648,0.02291791,0.10904054,-0.05125541,-0.01969977,-0.13399678,0.007819,0.01781158,-0.09805976,0.06014885,0.04565948,-0.0195189,-0.02155393,0.00203925,-0.00799979,-0.02323957,-0.06559642,-0.01760628,0.04823349,-0.05985624,0.04940531,0.04988861,-0.00078074],"last_embed":{"hash":"1jgiyr1","tokens":269}}},"text":null,"length":0,"last_read":{"hash":"1jgiyr1","at":1751288814401},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Generating a source map#With SourceNode (high level API)","lines":[127,159],"size":671,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1jgiyr1","at":1751288814401}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Generating a source map#With SourceNode (high level API)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08171821,-0.02464459,0.04023139,-0.05617382,-0.02693487,-0.03594024,-0.05090911,0.00443887,-0.03458073,0.03378519,0.01371917,-0.05772468,-0.00438427,0.01116468,0.01941433,0.00936111,-0.03366582,0.03084076,-0.01760218,0.01360034,0.07368597,0.01899446,0.02931988,-0.07817017,-0.001847,0.06866199,0.03711511,-0.0484593,-0.02441192,-0.17874347,-0.04221293,-0.02697162,-0.05231528,0.00379794,-0.00725046,-0.02541821,-0.01402393,-0.02766332,-0.02777073,0.02411424,0.02897112,0.04483168,-0.03395198,-0.03872127,-0.00243454,-0.0163818,-0.02625044,-0.02041669,0.02509294,-0.03153571,-0.0302381,0.01757399,0.00845681,-0.02958952,0.02707845,0.05627834,0.0487399,0.12220957,0.00756685,0.03541279,0.02905495,0.03790523,-0.17099756,0.03325517,0.06647845,0.04732514,-0.04488058,-0.01556312,-0.0101867,-0.03561641,-0.01229139,0.00567474,0.05522411,0.09347515,0.03821852,-0.04557854,-0.03296409,0.0365469,0.01515966,-0.01351113,-0.05392401,0.00654247,0.01095251,0.07917908,0.02696669,-0.03223563,0.04272283,-0.01081324,0.08358042,0.01970118,-0.02263361,-0.03727358,0.02989073,0.08078901,-0.03290362,0.01592761,0.03449323,0.04134432,-0.00402267,0.12216085,-0.05867877,0.02945568,-0.03600893,0.01148861,-0.05009334,-0.03161188,-0.02135262,-0.05883088,-0.02445496,-0.01641185,-0.05270975,0.0482727,-0.04524759,-0.08948007,-0.03559168,0.00431757,0.02591846,-0.02959759,-0.10336546,0.03542992,0.02965597,0.04645151,-0.0209399,0.03211435,0.04450231,0.02749981,0.02223598,0.03109536,0.00805385,0.07585599,0.03391039,0.05992231,-0.07083844,0.01338626,0.01716428,-0.03063803,0.00650403,-0.02306092,0.00316892,0.02217318,0.06754792,-0.07483659,-0.01327192,-0.0004847,0.01113229,0.07626107,-0.04513537,0.07769399,-0.00931633,-0.01741955,-0.02662782,0.0231778,-0.0732102,0.01850801,0.00262213,0.02096958,-0.00315793,-0.00952068,-0.03415694,0.00442726,-0.01295001,-0.00478868,0.00645231,0.075692,0.02889865,-0.06628301,-0.05527435,0.02555131,0.072877,-0.07505199,-0.03677209,0.01471844,-0.0091249,-0.02494032,0.06263964,-0.0231385,-0.04303662,-0.04072823,0.02388993,0.03884214,0.0161963,-0.02919254,-0.04124502,0.01704286,-0.01688084,-0.04872004,0.03985101,-0.04255576,-0.0074186,0.05344823,-0.00509432,0.06647394,-0.00310085,-0.02209657,-0.00437809,-0.02557649,-0.03828073,-0.01903136,0.09061436,-0.01891389,0.13054539,-0.00048182,-0.02792402,0.00817549,-0.05192076,0.01502926,0.00435341,-0.04946028,0.00132842,0.03601513,-0.07301077,-0.02465711,0.0709126,0.1059896,-0.06491118,-0.04785177,0.01378968,0.04861905,0.04228726,0.07175519,-0.08007196,-0.0121298,-0.0617976,-0.21536158,0.00530064,0.03088233,-0.04527065,-0.02562408,-0.01664143,-0.05182295,-0.02880518,-0.09039106,0.02858877,0.14159545,-0.0087451,0.00275527,0.01345819,-0.03357632,-0.03139507,-0.03601796,-0.03075503,-0.01899808,0.01023803,-0.00654257,-0.00013423,-0.03714599,-0.09980701,0.01644019,-0.0225717,0.11620123,0.02261963,0.08971993,-0.03746114,0.0574006,-0.05033566,0.03666463,-0.09053258,0.02241527,-0.00560315,0.02078789,0.04375878,0.01210748,0.00902953,-0.01884182,-0.02203102,-0.04730162,-0.05962674,0.05749597,-0.0451869,-0.04625628,-0.01150283,0.06520904,0.07919819,-0.02369703,0.01379679,0.08030187,0.03346422,-0.02829064,-0.00183808,-0.02607898,-0.02905477,0.01899652,0.04294978,0.02736909,-0.00331298,-0.01950188,-0.0628644,0.04997111,0.06752674,-0.00070911,-0.03804496,0.05033498,-0.0219378,-0.02516415,0.05506067,0.02106492,-0.0320004,0.0162253,0.00461085,-0.05024324,-0.02584843,0.02028792,0.00463649,0.00840854,-0.02966884,0.0234684,0.00939531,-0.02376957,0.04136481,0.01701701,0.00079599,0.07224132,-0.0645985,-0.06096157,0.00923005,-0.04961802,-0.00176745,0.08305199,-0.0017214,-0.27700248,0.02022991,0.02643758,-0.01366087,-0.0470012,0.00606735,0.0757709,-0.02761832,-0.02546006,-0.00573408,-0.02877544,0.04983129,0.011871,-0.03435602,0.02875324,0.0472768,0.05578049,0.00447809,0.11376658,-0.05034108,0.04176761,0.06238662,0.25014138,-0.02836907,0.05148694,0.09035628,-0.03834783,0.04525892,0.04045213,0.03154282,-0.01696494,0.03899033,0.14930752,0.01538365,-0.00969199,-0.0114235,0.02478429,0.01829353,-0.00878742,-0.0388536,-0.01287309,0.01457086,-0.02707403,0.02200746,0.10912882,-0.05244444,-0.02055019,-0.13176213,0.00808346,0.0193878,-0.09749972,0.06119605,0.04675508,-0.0182434,-0.02271353,-0.00068372,-0.00683371,-0.02492191,-0.06475456,-0.01796809,0.04641756,-0.05781627,0.04948939,0.04966766,-0.00308254],"last_embed":{"hash":"a7p0xa","tokens":266}}},"text":null,"length":0,"last_read":{"hash":"a7p0xa","at":1751288814550},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Generating a source map#With SourceNode (high level API)#{1}","lines":[129,159],"size":632,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"a7p0xa","at":1751288814550}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Generating a source map#With SourceMapGenerator (low level API)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07175663,-0.04352051,0.03487962,-0.06626193,-0.01184445,-0.02811375,-0.12399922,-0.01671785,-0.02349889,0.04082362,0.01238283,-0.07761016,-0.0125829,0.02103179,0.04215836,-0.02077984,-0.06067429,0.05368941,-0.05083231,-0.02018406,0.06262213,0.02139489,0.02763144,-0.04355891,-0.01593244,0.06401423,0.01895417,-0.03298197,0.01314617,-0.19023338,-0.04787334,-0.02526803,-0.03088748,0.01544522,-0.00850066,-0.04637456,-0.02993046,0.02238677,-0.02994125,0.01639487,0.06715936,0.03265069,-0.05919164,-0.00530496,-0.01451527,-0.05348959,-0.0291791,-0.00615764,0.01335828,-0.04820503,-0.03943374,-0.01768906,0.01270011,-0.00853549,0.02097877,0.05647904,0.05504074,0.1128867,0.03072078,0.01583153,0.02346234,0.04187098,-0.19415933,0.02447442,0.06216287,0.05073262,-0.03752881,-0.0004294,0.02524914,0.00022022,-0.00372319,0.0370574,0.05474453,0.05433727,0.0508315,-0.0661992,-0.04239721,0.04938066,0.01041811,0.00818839,-0.07957721,0.00800276,-0.0083888,0.06734291,0.02123494,-0.02331189,0.04206842,-0.00468318,0.07601969,0.04111073,0.0018854,-0.07321417,0.0172498,0.09236083,-0.06173188,-0.01529336,0.03989458,0.03658805,-0.02773651,0.1315784,-0.06149793,0.01103741,0.02960778,0.01702515,-0.02451812,-0.03856602,-0.02851706,-0.03988047,-0.0425243,0.01484337,-0.02124851,0.02237579,-0.07625687,-0.05793616,-0.03068562,-0.04275626,0.01678219,0.02918641,-0.09028462,0.03350174,0.03253054,0.04765234,0.01750776,0.03510085,0.06914268,0.01559487,0.03054508,0.04528439,0.03381215,0.07318427,0.02770067,0.05974682,-0.07882003,0.01249728,0.03085206,-0.03606923,0.0072253,-0.00512516,0.01645263,0.0366545,0.04167965,-0.03545997,-0.04109656,-0.04604939,-0.01026263,0.08712587,-0.00896418,0.08528554,-0.02271862,-0.00796383,-0.01434989,0.0242353,-0.07122347,0.00132176,0.0143787,0.00722862,0.02868197,0.01302364,-0.01915163,0.04528527,0.01076742,-0.01582444,0.00288193,0.10293087,0.01713854,-0.08202697,-0.01611464,0.06126255,0.02217735,-0.06716357,-0.04657779,0.04479628,-0.01640986,-0.02758639,0.05652551,0.01485965,-0.04586411,-0.04581258,0.03600824,0.00065564,0.0228805,-0.03649233,-0.0474232,0.02744019,-0.01742962,-0.04001796,0.02541767,-0.01170288,-0.02313855,0.03469392,0.02228653,0.05224309,0.0425837,0.00249804,-0.01988489,-0.0415423,-0.03354757,-0.01787144,0.07806399,-0.05560766,0.10754303,0.0264448,-0.02370802,0.03086051,-0.06726153,0.02025815,-0.01438034,-0.01740048,0.03891476,0.02721519,-0.08156818,-0.03867891,0.0607235,0.0759244,-0.065924,-0.01459329,0.01484558,0.02930669,-0.00558211,0.07459264,-0.07215253,0.01273978,-0.10278371,-0.22422779,0.02035314,0.01226116,-0.04506586,-0.03904698,-0.03049901,-0.04323308,-0.03658742,-0.0959546,0.01536598,0.13149171,-0.03990268,0.01064164,-0.00017522,-0.00665112,-0.01041117,-0.02103287,-0.06212182,-0.02149303,0.01884282,0.01979587,0.0168718,-0.01991175,-0.09540822,0.03289955,-0.05698306,0.12565209,0.04183607,0.03756217,-0.03369874,0.04387895,-0.04388519,0.0061012,-0.07705691,-0.0152824,0.01604589,0.0084832,0.04042422,0.04553906,0.02350657,-0.03915067,0.00728898,-0.0373104,-0.05922252,0.05559232,-0.03258218,-0.03442519,-0.00192712,0.03012067,0.05592981,-0.01824733,0.00627305,0.08280982,0.02323331,-0.03944802,0.00179776,-0.04340981,0.00245637,0.01150967,0.04900272,0.02032465,-0.02723935,-0.00793152,-0.07367591,0.05679822,0.05816768,0.01335346,-0.03108984,0.03014732,-0.0201516,-0.03574882,0.08416004,0.02259055,0.02598521,0.0488489,-0.01931595,-0.05831273,-0.03516309,0.00649827,-0.00138572,-0.03009104,-0.01863611,-0.00179704,0.03145778,-0.03250051,0.04538407,0.00574112,0.03061515,0.01385881,-0.06014156,-0.03335006,0.00748404,-0.03415754,-0.03391894,0.07415319,0.01526689,-0.27048323,0.03384858,0.03328405,-0.00745015,-0.03951678,-0.00232978,0.0538445,-0.01296331,-0.00438915,-0.00182016,-0.01577268,0.0453704,-0.0068312,-0.0450877,0.0097193,0.07218926,0.05100245,-0.04860652,0.10079139,-0.0739329,0.04814542,0.04588923,0.25480026,0.00449565,0.03780051,0.05846822,-0.0259493,0.0377207,0.05589414,0.03358589,-0.04043541,0.02693288,0.14392033,0.0210258,-0.01775546,0.00822458,0.02727941,-0.03721649,-0.00359533,-0.0148849,-0.02443789,0.02856587,-0.03073785,0.04156825,0.08362059,-0.05601683,-0.02371724,-0.11671817,-0.02338266,0.0213591,-0.06422275,0.04004759,0.01954882,-0.00484528,0.00800385,0.04478364,-0.00369147,-0.01060167,-0.03841139,0.00592329,0.00343091,-0.06460004,0.07589999,0.01916671,-0.00451513],"last_embed":{"hash":"vzeb4n","tokens":207}}},"text":null,"length":0,"last_read":{"hash":"vzeb4n","at":1751288814778},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Generating a source map#With SourceMapGenerator (low level API)","lines":[160,183],"size":432,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"vzeb4n","at":1751288814778}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Generating a source map#With SourceMapGenerator (low level API)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07086644,-0.04001703,0.03294873,-0.06737961,-0.01408595,-0.02920118,-0.12195846,-0.01362076,-0.02200232,0.04064443,0.01327461,-0.07510812,-0.01167199,0.0215539,0.04172461,-0.02394333,-0.06306302,0.05703329,-0.04649611,-0.01815536,0.05901342,0.01694918,0.02220273,-0.04240146,-0.01447986,0.06442232,0.01972443,-0.03485686,0.01083234,-0.18951637,-0.04727092,-0.03057378,-0.02961029,0.01461029,-0.0066999,-0.04939206,-0.03191976,0.02310213,-0.02991976,0.01338593,0.06872088,0.03176691,-0.05854601,-0.00643232,-0.01628772,-0.05711906,-0.03115335,-0.00431488,0.01340325,-0.04733786,-0.03918306,-0.01438671,0.01205519,-0.00661173,0.01972943,0.05742547,0.0564704,0.11245748,0.03271472,0.01600083,0.01850576,0.04144781,-0.19283985,0.02399533,0.056432,0.04935485,-0.03911732,-0.00216765,0.02638768,0.00048794,-0.00428721,0.03669816,0.05579475,0.05196514,0.05024742,-0.06894912,-0.04704808,0.04823839,0.01000677,0.01061073,-0.08069521,0.01088361,-0.00758572,0.0669028,0.01609449,-0.02180058,0.03921585,-0.00807247,0.07544062,0.04025288,0.00123555,-0.07359495,0.01766077,0.09267236,-0.06239405,-0.01399661,0.04152603,0.03608633,-0.02885405,0.13193777,-0.0586928,0.00892903,0.02856877,0.01799975,-0.02288232,-0.04108855,-0.03074378,-0.04244052,-0.0433376,0.0173356,-0.02253296,0.02260389,-0.07689938,-0.05629523,-0.03086073,-0.04124771,0.01604084,0.02752539,-0.09195293,0.03482499,0.03388258,0.04486357,0.0173263,0.03724639,0.07295443,0.01815985,0.02716911,0.04476663,0.03276689,0.07277052,0.02789192,0.05580583,-0.07859234,0.00906651,0.0338624,-0.03622389,0.01437373,-0.00333864,0.01829084,0.03939383,0.04124808,-0.03701464,-0.03645399,-0.04487101,-0.00799971,0.08737543,-0.01045132,0.08529962,-0.02569087,-0.01035136,-0.01314071,0.02347419,-0.07092845,-0.00208704,0.0157959,0.00468895,0.0274686,0.01086277,-0.02345637,0.04460923,0.00873054,-0.01637447,0.00580221,0.10049549,0.01524169,-0.07924626,-0.01423051,0.06191815,0.0177716,-0.06581411,-0.04788392,0.04737944,-0.01842293,-0.02957049,0.05753932,0.01601598,-0.04740737,-0.04398398,0.03417213,0.00112703,0.02553831,-0.03603248,-0.04343973,0.02796549,-0.01811164,-0.0376787,0.02344766,-0.01273009,-0.02156251,0.03089799,0.02213235,0.0530192,0.04503747,0.00508558,-0.02125643,-0.04032481,-0.03580371,-0.01752938,0.0808031,-0.05403474,0.11084644,0.02515074,-0.02312727,0.03084404,-0.06410339,0.02157489,-0.01348058,-0.01993987,0.03793147,0.02725757,-0.0836747,-0.03768548,0.06261835,0.0743572,-0.06456475,-0.01436663,0.01406505,0.02825037,-0.00825454,0.07344148,-0.06723977,0.01372243,-0.10232141,-0.2255087,0.02178195,0.01280227,-0.04690043,-0.04181922,-0.03134428,-0.04633081,-0.03575284,-0.09436183,0.01480179,0.13241853,-0.03682996,0.01230186,0.00132125,-0.00953022,-0.00728333,-0.02151663,-0.05802583,-0.02303201,0.01846441,0.01823521,0.01495105,-0.01669874,-0.09442817,0.03702055,-0.05341737,0.12371261,0.03999138,0.03806911,-0.03597499,0.04150706,-0.04911582,0.00681845,-0.07604869,-0.013442,0.01551699,0.00959032,0.03692753,0.04711909,0.02131529,-0.04260787,0.00758463,-0.03548577,-0.06467776,0.05459109,-0.03172369,-0.03041534,-0.00250976,0.0310003,0.05641409,-0.01913991,0.00699384,0.08551041,0.02730455,-0.037088,-0.00114193,-0.04428268,-0.00016643,0.01247692,0.05042412,0.02091344,-0.02891159,-0.01021957,-0.07584952,0.05519255,0.05790208,0.01611396,-0.02986418,0.0341538,-0.01575238,-0.03991041,0.0842835,0.02128251,0.02893841,0.0534863,-0.01690334,-0.05992506,-0.03399616,0.00388171,0.00066304,-0.03062045,-0.01666136,-0.00014338,0.03404909,-0.03339701,0.04462568,0.00754335,0.03176939,0.01128377,-0.0631273,-0.03579608,0.00936138,-0.03627729,-0.03252856,0.07305961,0.01571498,-0.27191198,0.03622713,0.03423794,-0.00669629,-0.03921124,-0.0047688,0.05381281,-0.01375334,-0.00310652,-0.00112014,-0.01429209,0.04559724,-0.00499902,-0.04193808,0.01148701,0.07419764,0.04878175,-0.0510385,0.10088591,-0.07221497,0.04943648,0.04664423,0.25506407,0.00422281,0.03737803,0.05700287,-0.02302842,0.03823385,0.05367199,0.03444327,-0.04230693,0.02780398,0.14390422,0.02219654,-0.01420609,0.01373704,0.02682061,-0.03904424,-0.00556098,-0.0142274,-0.0203304,0.02990045,-0.02977915,0.03838161,0.08304919,-0.05293425,-0.0242196,-0.11577103,-0.02199819,0.0227477,-0.06393424,0.04484617,0.02125961,-0.00665012,0.00768669,0.04276791,0.00071276,-0.0129556,-0.03647986,0.0036987,0.00065394,-0.06783665,0.07612117,0.01575887,-0.00430951],"last_embed":{"hash":"132i0li","tokens":204}}},"text":null,"length":0,"last_read":{"hash":"132i0li","at":1751288814887},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#Examples#Generating a source map#With SourceMapGenerator (low level API)#{1}","lines":[162,183],"size":386,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"132i0li","at":1751288814887}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05518046,-0.04157703,0.02719907,-0.07719509,0.02978692,0.00364641,-0.0510949,-0.01374234,-0.0439942,0.00283578,0.00178664,-0.08459571,0.00927654,0.01122735,-0.00169378,-0.03869157,-0.03019249,0.04166566,-0.03435031,-0.00899963,0.0563692,-0.03350892,0.00362239,-0.03177,-0.00759055,0.10264952,0.02137185,-0.06024803,-0.01642566,-0.18100967,-0.03725855,-0.04382461,-0.01419538,-0.00235871,-0.0175979,-0.03790979,-0.01994623,0.01116076,-0.00210483,0.02855425,0.05600255,0.02675562,-0.050266,0.01046965,-0.0167953,-0.04119287,-0.02745322,0.02373536,0.02240653,-0.02985965,-0.04510657,-0.00387381,0.01534154,-0.00050815,0.03860296,0.08040825,0.04884497,0.09035131,0.01592265,0.04925184,0.01404449,0.03584291,-0.1913611,0.03709148,0.07823854,0.03895978,-0.0196413,-0.01529075,0.01514577,-0.0438511,-0.02346528,0.03923324,0.0336073,0.04027832,0.03294793,-0.07636095,0.00279846,0.06254674,0.00762338,0.00499648,-0.07777441,0.05816828,0.02927105,0.06473641,0.0299269,0.00533562,0.03683627,-0.0124545,0.09727506,0.01594643,-0.02043187,-0.07045361,0.04411591,0.12087212,-0.02003757,-0.01372066,0.04999006,0.07311559,0.04461831,0.11680082,-0.10596288,0.01686295,0.01275971,0.03718041,0.00177956,-0.03861955,-0.03418903,-0.01249987,-0.03282928,0.02974775,-0.05523029,0.00388534,-0.07192545,-0.04946171,-0.0046788,-0.0394402,0.02971287,-0.02048516,-0.06377944,0.05219834,0.00598766,0.0680417,0.00896549,0.03237595,0.06424433,0.00495445,0.02362139,0.02155643,0.02751802,0.06670495,0.02283455,0.04290856,-0.08444335,0.00330831,0.01339634,0.00104089,0.03835589,-0.05090593,0.01427643,0.02686558,0.03295472,-0.07448606,0.00399043,0.00531436,-0.03378242,0.1243552,-0.03533403,0.07567284,-0.00871843,-0.01619063,-0.01650037,0.04783431,-0.06608906,0.00932831,0.00540757,0.00571327,0.02042221,-0.00496454,-0.04752355,-0.02127551,0.03544274,-0.04457708,0.01975619,0.08532511,-0.00068168,-0.07708065,-0.0514676,0.04949836,0.00958137,-0.05540258,-0.0148493,0.01158702,-0.00044064,-0.05037969,0.02810382,0.01023592,-0.06012043,-0.02333752,0.04662215,0.01754207,-0.04122701,-0.05533693,-0.04883372,0.03299324,-0.00608072,-0.03869765,-0.00482003,-0.05997748,-0.02190373,0.05521533,0.01124822,0.03279077,0.05764659,0.00350689,-0.00778699,-0.0148148,-0.06128452,0.02161751,0.05417818,-0.04733937,0.12128276,0.01933259,-0.02156325,0.04072876,-0.08532511,0.02812408,-0.01709369,0.0247347,0.00694628,0.02540825,-0.12527238,-0.02837181,0.03881894,0.05508126,-0.06442582,-0.05303685,0.04909353,0.00700149,0.01824752,0.11924008,-0.06226353,-0.04237861,-0.08574256,-0.21969046,0.00156894,-0.00931001,-0.03256107,-0.03840141,-0.02996786,-0.02566994,-0.01965638,-0.06266345,0.04551069,0.13623878,-0.02841588,0.0106871,0.01131006,-0.04586961,-0.0073199,0.01197675,-0.0572463,-0.06026943,0.00922768,0.01766912,-0.01099523,-0.08067038,-0.0804189,0.02044377,-0.01397797,0.12279166,0.03516942,0.01710177,-0.0418205,0.05026564,-0.03824894,-0.00399367,-0.08622081,-0.02474762,-0.01473153,0.00040178,0.01140703,0.04965488,0.04447988,-0.01504766,-0.01836985,-0.01271037,-0.05679989,0.04240734,-0.02593567,-0.04883756,-0.01048142,0.03475174,0.0665497,-0.00632936,0.01411856,0.11071452,0.02755579,-0.02812795,0.02648778,-0.0138779,-0.02342439,0.00089849,0.02793728,0.01832385,-0.01444654,-0.02061094,-0.07593227,0.07808938,0.0370814,0.01633555,-0.00967262,0.04443849,-0.0078866,-0.02340198,0.04703486,0.04748443,0.03645118,0.03328332,-0.01844328,-0.02374951,-0.03741473,0.02492489,-0.01936399,0.00599628,-0.04152686,0.00870557,0.03033586,-0.03468599,0.0294161,0.0409824,0.02366089,0.02691521,-0.04829991,-0.01619576,0.00041592,-0.06610358,-0.06505889,0.0911279,0.04673683,-0.26254654,0.02459527,0.01449053,-0.02880632,-0.01105774,-0.03315307,0.0795104,-0.01539005,0.01352571,-0.01037523,-0.0027521,0.0712189,-0.02981388,-0.07638756,0.01688808,0.06015448,0.0742984,-0.00721296,0.08054586,-0.03484479,0.03582359,0.04436526,0.24216732,-0.01277002,0.04158909,0.05601227,-0.04722555,0.00338127,0.07881367,0.04671295,-0.01077091,0.00496715,0.11488403,0.0184812,-0.03538825,0.02238211,-0.00046961,-0.00037184,-0.00454221,-0.0226022,0.00363982,-0.00726305,-0.02447686,0.02704016,0.08642642,-0.05260225,-0.0485411,-0.11433087,-0.02498398,0.00597113,-0.07533479,0.04403874,0.01273813,-0.01116466,-0.02518081,0.01247795,0.0128135,-0.0228191,-0.02547463,-0.01850968,0.01220251,-0.0566113,0.07678752,0.0542555,0.00281018],"last_embed":{"hash":"qkcpgu","tokens":434}}},"text":null,"length":0,"last_read":{"hash":"qkcpgu","at":1751288814985},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API","lines":[184,766],"size":17966,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"qkcpgu","at":1751288814985}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05109271,-0.02782598,0.04204927,-0.06126999,0.01961115,-0.01305349,-0.05113881,0.01263643,-0.03624075,-0.02263271,-0.00766684,-0.09862681,-0.01619666,0.02102645,0.02205852,-0.02609796,-0.0455871,0.01979049,-0.02334307,-0.00007614,0.05811476,-0.00665667,0.00637532,-0.01278713,-0.01185562,0.09783215,0.03277565,-0.0499487,0.00301524,-0.13387759,-0.03413326,-0.03065842,-0.03801687,-0.01381316,-0.00696775,-0.03113535,-0.02947306,-0.01001037,-0.01143211,0.02066552,0.07438541,0.04280985,-0.04428732,0.0044701,-0.0059326,-0.03851246,-0.02361744,0.03858653,0.0055658,-0.04734091,-0.01340941,-0.01313136,0.03440414,-0.00585022,0.03396402,0.11056591,0.03215949,0.10251232,0.0268372,0.04699627,-0.01259891,0.02506115,-0.17734939,0.04664497,0.0623941,0.03415038,0.00069685,-0.00237259,0.02552994,-0.03630132,-0.01680483,0.04257935,0.02874121,0.05772714,0.03995309,-0.06909252,0.00178083,0.02097146,0.02572493,-0.02737055,-0.0742255,0.02517009,0.01434446,0.04503562,0.01970783,-0.00180331,0.04755285,0.00397662,0.1216488,0.02246834,-0.03273894,-0.09536158,0.02769573,0.09628598,-0.02229122,-0.00183621,0.05043465,0.07070004,0.01388451,0.1437563,-0.10658778,0.02532354,0.00128557,0.01515714,-0.01853768,-0.02919056,-0.03510437,-0.02392832,-0.05330805,0.02078357,-0.0321022,0.00039907,-0.0484941,-0.05194854,-0.01344528,-0.04110228,-0.00846372,-0.01553607,-0.02336827,0.02102479,0.04929446,0.05228165,0.03365235,0.00971437,0.05164883,0.00975933,0.02547692,0.01051618,0.02063256,0.06269917,0.00715923,0.04587808,-0.07237633,-0.02430686,0.01940222,0.00944239,0.02196937,-0.04380811,-0.01180353,0.01858001,0.04415116,-0.05709945,0.02632456,-0.01725747,-0.0062018,0.07947329,-0.01874021,0.06243525,-0.03057833,-0.03115283,-0.00881107,0.05993433,-0.07129758,0.02295555,0.00542141,-0.00936939,0.0229308,0.00759392,-0.02665156,-0.01108991,0.0413879,-0.0221563,0.00538573,0.06623037,-0.00032708,-0.06974493,-0.05700622,0.02968742,0.00119289,-0.08024094,-0.03896677,0.05447111,-0.01835595,-0.04351616,0.03773338,0.02434303,-0.05788475,-0.00007989,0.02763677,0.00514145,-0.02281051,-0.07506316,-0.04604294,0.03584391,0.00846451,-0.04379229,0.00887197,-0.09188092,-0.02827797,0.03948091,-0.00264434,0.05617581,0.05264008,-0.00493014,0.00400248,0.00773447,-0.07177993,0.00238717,0.07078283,-0.0207512,0.11921211,0.0138791,-0.02131539,0.05183487,-0.0943622,0.03386809,-0.02771739,0.03679533,0.01466628,0.02889399,-0.14813919,-0.03218688,0.07009137,0.06316501,-0.05849405,-0.03191255,0.03044404,0.0231503,-0.00661249,0.09868624,-0.0713984,-0.01881018,-0.08917968,-0.20452347,0.01452342,-0.01970727,-0.03000279,-0.05302596,-0.01884041,0.02370637,-0.02632232,-0.10106867,0.08730478,0.14577362,-0.02081623,0.02839643,0.01153245,-0.04309314,-0.01252958,-0.00382044,-0.08117896,-0.0566708,-0.00103426,-0.00253318,-0.03424793,-0.07565352,-0.08512264,0.01609692,0.00656271,0.10581634,0.03523042,0.0217607,-0.07954484,0.06526368,-0.02068829,0.00442351,-0.11839922,-0.04374969,0.02871642,0.02795809,0.00855336,0.06933529,0.03167726,-0.01357535,-0.02019299,-0.00603537,-0.04794614,0.0394989,-0.05397595,-0.04323333,-0.02765176,0.00247028,0.05879366,-0.01111941,0.04089291,0.08115296,0.02407922,-0.05161744,0.02141872,-0.01083574,-0.01459345,0.00011886,0.02624909,0.01724789,0.01862637,-0.03847193,-0.0909334,0.10324701,0.03648956,0.02992564,-0.04354721,0.0609572,-0.00415739,-0.0273171,0.03854683,0.03605296,0.02761456,0.02969099,-0.0163116,-0.01059971,-0.01054268,0.04344814,-0.02290014,-0.00265283,-0.02134724,-0.01217764,0.02318661,-0.02659958,0.04152952,0.00414038,-0.00129041,0.03094424,-0.0524323,-0.00359283,0.02050146,-0.04048304,-0.06715137,0.09706143,0.03151567,-0.25228941,0.01312743,0.06280076,-0.01048008,-0.04812286,-0.01907647,0.07732131,-0.03581493,0.00765276,0.00462717,0.01470082,0.04001095,-0.03463997,-0.03751651,0.04017922,0.04740286,0.07097918,0.00256727,0.06963032,-0.03803433,0.03156976,0.04151761,0.25093085,-0.01992141,0.03188583,0.0722717,-0.06505897,0.02137648,0.08527795,0.06432526,-0.00096358,0.03140847,0.11408093,0.00115288,-0.0291118,-0.00845376,0.00605951,0.00619195,0.00600288,-0.00154996,0.00403016,0.00399175,-0.02872207,0.0297757,0.05588458,-0.09163069,-0.05879677,-0.11905387,-0.05029076,0.01481083,-0.07126026,0.00948462,0.05132681,-0.01512478,-0.03464575,0.01927785,0.03967848,-0.04186563,-0.0304881,-0.00116234,-0.01064115,-0.04968435,0.05836207,0.03548635,0.00682815],"last_embed":{"hash":"1nxrcqu","tokens":112}}},"text":null,"length":0,"last_read":{"hash":"1nxrcqu","at":1751288815254},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#{1}","lines":[186,198],"size":235,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1nxrcqu","at":1751288815254}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05127957,-0.04596674,0.0182649,-0.07723794,0.01998049,-0.0209817,-0.05257048,-0.03708236,-0.05766987,0.0363456,0.01994397,-0.07660647,0.02437785,0.00182105,0.00511374,-0.05090142,-0.00646275,0.0511485,-0.03434411,-0.0091259,0.07915228,-0.02799508,-0.00641935,-0.04425783,0.00424652,0.08641482,0.01055821,-0.04852567,-0.02315891,-0.19836037,-0.022418,-0.05091909,-0.0004644,0.01605631,-0.01627392,-0.02012641,-0.01076385,0.03012443,-0.00035107,0.03959199,0.04886466,0.01035418,-0.05778736,-0.015204,-0.02205103,-0.03973465,-0.01763371,0.03101447,0.02739589,-0.02945564,-0.05491436,0.00330578,0.02054999,-0.01373703,0.04279931,0.06116857,0.05849252,0.0706599,0.02748883,0.02992453,0.00348665,0.03000883,-0.20347905,0.03857541,0.07288387,0.03881458,-0.03470632,-0.02550723,0.00878139,-0.00872077,-0.01565761,0.03342247,0.02890648,0.02016743,0.02606753,-0.08427944,0.01732405,0.07424045,0.00411874,0.02562081,-0.08146564,0.06197061,0.02271976,0.06565006,0.02431051,0.01245728,0.03165552,0.00094029,0.06563766,-0.00698739,-0.0115878,-0.06023973,0.05663227,0.10729118,-0.03159357,-0.01334913,0.04403981,0.06930187,0.06348133,0.11215737,-0.09114031,0.00483237,0.00636501,0.03378261,0.01374671,-0.04359079,-0.02695729,-0.00656837,-0.02030161,0.03221602,-0.04632314,0.02989401,-0.08877933,-0.05106623,-0.00291362,-0.0279758,0.04695373,0.00281143,-0.07482623,0.06458548,-0.01256967,0.07482235,0.00154865,0.03316828,0.04809161,-0.00065391,0.03604882,0.00447484,0.03403438,0.05641443,0.02451022,0.03194171,-0.08061896,0.02275049,0.01568651,-0.00945929,0.03393387,-0.04133295,0.03524535,0.03099421,0.00995491,-0.07464174,-0.02898551,-0.00975721,-0.03069648,0.14576392,-0.03229678,0.08800778,0.00147048,-0.00293721,-0.01897793,0.04167232,-0.05228985,0.01580166,0.00248204,0.01206906,0.01711086,-0.011727,-0.04783304,-0.00636163,0.02473098,-0.05360762,0.02884459,0.10255421,0.00241684,-0.08952676,-0.04030895,0.05071612,0.00764143,-0.03741828,0.00521587,0.00733877,0.00056827,-0.03935805,0.01928563,0.01705985,-0.06575911,-0.03226219,0.03442257,0.01649516,-0.04286936,-0.03533185,-0.03512287,0.00120394,-0.00479255,-0.03159437,-0.00855152,-0.03292382,-0.01578513,0.04285817,0.01225955,0.03356132,0.04768357,-0.00545264,-0.01210922,-0.03267496,-0.03071941,0.02728946,0.04038128,-0.05157524,0.10982786,0.02727454,-0.02296022,0.03546095,-0.0774208,0.00729835,-0.0116227,0.01067843,0.00694952,0.01544047,-0.09296224,-0.01472538,0.01189933,0.03425793,-0.06857368,-0.06245223,0.03697662,0.00497171,0.03060291,0.1096535,-0.06441626,-0.04714577,-0.0792015,-0.23276602,0.00637128,0.00010384,-0.03095737,-0.03562284,-0.03017903,-0.07345301,-0.02454341,-0.01681389,0.02588254,0.13555552,-0.027994,0.0013262,0.00734843,-0.02543327,-0.00064347,0.02480786,-0.05286479,-0.05894372,0.03174658,0.04014477,0.00180018,-0.0791369,-0.07164292,0.02564297,-0.03165457,0.14848292,0.03880401,0.02293354,-0.01663882,0.04248617,-0.03321492,-0.01310889,-0.08566036,-0.01783668,-0.01694933,-0.02190109,-0.00560055,0.02874293,0.03999579,-0.02075764,0.0030726,-0.01433274,-0.06809261,0.04825951,-0.01458063,-0.05052298,0.03102526,0.02891739,0.0782215,-0.00077137,-0.00802671,0.11145044,0.03069567,-0.03074892,0.01154757,-0.00237675,-0.04546911,0.00018961,0.01814859,0.01321356,-0.02625481,0.00449426,-0.05215865,0.05194888,0.0353495,-0.00242201,0.00222486,0.0514401,-0.00811014,-0.02274752,0.05368493,0.0324459,0.02379774,0.04223161,-0.02315355,-0.03975369,-0.04416272,0.01257991,-0.01714559,0.02245288,-0.04669691,0.0299778,0.03526196,-0.03506048,0.02402966,0.04052396,0.0279373,0.02939099,-0.0438061,-0.02557259,-0.01637659,-0.08668707,-0.04086276,0.08040773,0.04718858,-0.27765742,0.03886725,-0.00202454,-0.02368811,0.00102265,-0.03620749,0.071725,-0.01935103,0.00386704,-0.0290918,-0.00947604,0.08870715,-0.02392301,-0.07021998,0.00074753,0.05805853,0.07250005,-0.00945058,0.08797494,-0.04344396,0.03334146,0.05607319,0.24577118,-0.00151354,0.03965581,0.04842035,-0.03940073,0.004043,0.0702401,0.03694524,-0.01166091,0.00138834,0.12686573,0.02160364,-0.01661732,0.03100136,0.02155511,-0.01459221,-0.00921991,-0.02430155,-0.00730754,-0.0159254,-0.03417926,0.01474342,0.10365906,-0.03068881,-0.04563685,-0.10447343,-0.00966943,0.00410622,-0.06075476,0.044969,-0.00100549,-0.00351988,-0.02377556,0.00285264,-0.02256004,-0.02563361,-0.0360609,-0.03115226,0.03026297,-0.0512866,0.08572135,0.05990406,-0.01014533],"last_embed":{"hash":"kokxq1","tokens":490}}},"text":null,"length":0,"last_read":{"hash":"kokxq1","at":1751288815312},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer","lines":[199,449],"size":7735,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"kokxq1","at":1751288815312}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05939575,-0.05295341,0.0213702,-0.08287631,0.02925704,-0.0265319,-0.05909071,-0.02129231,-0.05450629,0.03088537,0.03391367,-0.0666884,0.02298611,0.00218567,0.02699476,-0.03939832,-0.0037307,0.06454212,-0.05047622,0.01626492,0.0977753,0.02829433,0.01249411,-0.0379757,0.00350015,0.08242989,0.025041,-0.05642424,-0.02367057,-0.18337092,-0.03554901,-0.04629728,-0.01391603,0.01108955,-0.02187315,-0.01871697,-0.01455272,0.01721101,-0.00488463,0.04491035,0.05562361,0.00299226,-0.05549684,-0.01129101,-0.0400915,-0.04574057,-0.01907196,0.04175286,0.04468023,-0.03620347,-0.05465322,0.01939505,0.00314031,-0.01349712,0.02737038,0.06928992,0.06247478,0.07313044,0.0289942,0.03441468,0.01202055,0.04720153,-0.18053392,0.0266222,0.06614356,0.05577358,-0.01795346,-0.01516465,-0.00232657,-0.03631611,-0.00629605,0.05047179,0.03555334,0.01076612,0.0312364,-0.08627228,-0.0104263,0.07117485,0.01721131,0.03297761,-0.06548577,0.04153844,0.03653903,0.05311611,0.04706879,0.01334269,0.02436609,-0.00931696,0.07547455,-0.01484176,-0.01902988,-0.06989019,0.06402584,0.10728447,-0.02798019,-0.00297321,0.03243129,0.0532813,0.04141754,0.11653086,-0.10806361,0.00571553,0.010904,0.01999884,0.0148109,-0.05030039,-0.01685191,-0.01513662,-0.01730623,0.04063715,-0.02313416,0.00954207,-0.08910704,-0.04829526,-0.00997932,-0.02544481,0.0162123,-0.009206,-0.09072577,0.05411677,-0.00289049,0.04930651,0.02126984,0.0243656,0.04202747,-0.00105801,0.04407203,0.00441284,0.04936265,0.07481601,0.02159658,0.02425531,-0.1029686,0.02244331,0.01073585,-0.00772771,0.06160462,-0.04089873,0.0100469,0.03672966,0.00640175,-0.06688643,-0.04963085,-0.00811946,-0.03866299,0.1258423,-0.03070578,0.08523498,-0.00947447,0.00570745,-0.02999486,0.03405173,-0.06096399,0.0152059,0.00470046,0.00591336,0.0121665,-0.01700065,-0.04753986,-0.00255883,0.03765811,-0.06151923,0.02563502,0.10764881,-0.03139341,-0.09226469,-0.03871428,0.0389988,0.02043081,-0.05339247,0.00844148,0.00160674,0.00029432,-0.02657014,0.00729474,0.01360142,-0.06498137,-0.03482657,0.01817578,0.00728593,-0.05508668,-0.02708406,-0.04365369,0.0186346,0.00443626,-0.034979,-0.011639,-0.03312939,-0.00258457,0.05144836,0.01191902,0.00915298,0.0242177,-0.02570833,-0.00262848,-0.00265118,-0.0379283,0.03452282,0.05060254,-0.05858692,0.10220587,0.04212658,-0.00289806,0.02724192,-0.07917714,0.00782384,-0.02615612,-0.00724006,-0.003939,0.03957835,-0.09805369,-0.000299,0.01184779,0.04791305,-0.06778112,-0.04213211,0.05603366,0.00291658,0.02846148,0.10731861,-0.09083614,-0.04229756,-0.07849944,-0.22763771,0.00802632,0.01034229,-0.0541922,0.01368884,-0.03045093,-0.06792773,-0.01977883,-0.04590185,0.02935094,0.14293873,-0.01124866,-0.00111244,-0.00054772,-0.03291893,0.02595965,0.00768723,-0.06290447,-0.05836366,0.03108712,-0.01264207,-0.00086141,-0.06902034,-0.06066725,0.01412291,-0.03227491,0.14303151,0.01559419,0.02904011,-0.00782169,0.04625661,-0.02560429,0.0023261,-0.09279648,-0.03203262,-0.01263335,-0.00825919,0.00115576,0.02430874,0.03242671,-0.0159299,-0.00411378,-0.02232904,-0.07878184,0.05325377,-0.02608661,-0.06046891,0.02694059,0.04499064,0.06149227,-0.02429086,0.02764728,0.09552913,0.03553639,-0.03314324,0.03806994,-0.01388411,-0.02872424,0.00334888,-0.00000136,0.01328779,-0.02460648,-0.02018962,-0.0576207,0.04969771,0.03942555,0.01643871,-0.02268983,0.05256669,-0.02374274,-0.02721294,0.05139471,0.04217757,0.02483647,0.03468894,-0.01795078,-0.05023629,-0.05772628,0.03534513,-0.01388868,0.01894964,-0.04852589,0.01573447,0.03388362,-0.02694391,0.04165285,0.02761199,0.00167128,0.02751758,-0.04076498,-0.01808946,-0.05145685,-0.06331418,-0.03970904,0.08963738,0.02613471,-0.26159087,0.04141372,0.03134403,-0.04229571,-0.01323449,-0.00499533,0.08945383,-0.03924395,-0.01422155,-0.01568536,0.02292595,0.08482699,-0.03799061,-0.05908965,0.02748594,0.07235163,0.09301681,-0.01354018,0.0866845,-0.02016215,0.0338541,0.05032738,0.24491356,-0.00236698,0.0263958,0.05062975,-0.03933572,0.03178772,0.06969897,0.02317864,-0.02100328,0.00689379,0.11958999,0.01474818,-0.01901712,0.02916507,0.00336513,-0.03679211,-0.01360447,-0.01495151,-0.01271329,-0.04038148,-0.04256896,0.04216667,0.0859793,-0.04186418,-0.04069802,-0.11359632,-0.01358121,0.01603103,-0.04145916,0.03885747,-0.00976414,-0.00365816,0.00555118,0.02939365,-0.02314802,-0.01997231,-0.02352608,-0.01460909,0.02928248,-0.03740726,0.06696778,0.06213967,-0.00559611],"last_embed":{"hash":"5u122m","tokens":263}}},"text":null,"length":0,"last_read":{"hash":"5u122m","at":1751288815652},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#new SourceMapConsumer(rawSourceMap)","lines":[205,229],"size":828,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"5u122m","at":1751288815652}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.computeColumnSpans()": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04386833,-0.02944287,0.04241859,-0.06928158,-0.01120561,0.02561281,-0.07191692,-0.01148144,-0.01601912,0.03020512,-0.01092314,-0.07843296,0.01196692,0.03867959,-0.02098023,-0.03278608,-0.04564743,0.08777976,-0.03889693,-0.06434167,0.06229271,-0.07124838,-0.0078566,-0.04558671,0.02377993,0.09049951,0.01261704,-0.08728635,0.00587786,-0.21486779,-0.03128399,-0.02811643,-0.02458034,0.00610445,0.02260832,-0.04698262,-0.02351782,0.01821662,-0.04072396,0.04580281,0.01676646,0.04787116,-0.04490721,-0.02570033,-0.00570017,-0.05260227,-0.0035113,0.00010518,0.03657398,-0.0222796,-0.05213716,-0.04089158,0.00866628,-0.01853618,0.02993589,0.06483511,0.03520061,0.07362355,0.01023944,0.02255476,0.03645317,0.01874556,-0.23141651,0.04863482,0.07510031,0.01829561,-0.03898934,-0.05616168,0.05694175,0.06063803,0.00095943,0.01472468,0.05995278,0.02247275,0.03760533,-0.00758512,0.0050983,0.01879543,0.002945,0.01050683,-0.09852608,0.05707253,-0.01387053,0.04316922,-0.01490904,0.0166858,-0.02368614,-0.02539252,0.05354614,0.01834715,-0.00528183,-0.06395318,0.05096534,0.11840709,-0.04162247,-0.05331636,0.05226623,0.01900553,0.06274042,0.14745714,-0.05051362,0.00629078,-0.01589917,0.04450531,-0.00123461,-0.03562838,-0.03635658,-0.02564212,-0.02225558,0.0081108,-0.10847955,0.00875725,-0.04776917,-0.05949295,-0.0139733,-0.04395301,0.07843368,0.02853845,-0.05094033,0.07941743,-0.02821216,0.0841231,-0.03190469,0.04389633,0.04840491,0.01853139,-0.01631709,0.0432731,-0.02897411,0.04966654,0.031654,-0.00012167,-0.04475644,0.00284874,0.02502019,-0.02299709,0.0331571,-0.03095401,0.05614097,0.04591602,-0.01202897,-0.03029983,0.01131761,-0.02709423,-0.01644351,0.14247026,-0.02917595,0.0856289,0.00384805,0.01046758,0.01395989,0.05641285,-0.02466816,0.00220387,-0.0124049,0.01398651,0.02402389,0.00139523,-0.04950155,0.00381107,-0.02571545,-0.03567388,-0.01658262,0.12085181,0.01814276,-0.05581839,-0.03233663,0.06787912,-0.00639505,-0.03805423,-0.00684167,0.01295472,-0.00731744,-0.04670573,0.05797325,0.03535036,-0.07414862,-0.02094614,0.06914257,0.01493844,0.01224743,-0.03970675,-0.04521049,0.00868378,-0.01357419,-0.03090169,-0.00475993,-0.03273829,-0.03236683,-0.0014233,0.03214496,0.05742856,0.04348816,0.05856664,-0.03983215,-0.06044769,-0.0260652,-0.00256891,0.03120085,-0.04329274,0.12365573,0.02232818,-0.02081615,0.03352136,-0.03266453,0.02024076,-0.0022016,0.00264622,0.04046692,0.01340267,-0.10262629,-0.03931941,0.04329623,0.07330997,-0.06403923,-0.02720383,0.03434711,0.06237637,0.00488075,0.07596841,0.01036794,-0.01992985,-0.09922051,-0.23046312,0.00106625,0.042201,-0.03275113,-0.05981207,-0.00971663,-0.02095328,-0.03010559,-0.02497954,0.0166977,0.08634897,-0.00086691,0.00455444,0.00633786,-0.0608788,-0.01304711,0.02254096,-0.03471494,-0.05940959,0.02286014,0.05640363,0.00678038,-0.05540002,-0.11248016,0.01225851,-0.02805137,0.16372029,0.01889701,0.0193303,-0.04986936,0.03408596,-0.04670428,-0.03193226,-0.01460232,0.02589197,-0.02678771,-0.02983379,-0.01868339,0.0281478,0.0315906,-0.02579946,0.01957977,0.00745388,-0.10236209,0.06471419,0.01761502,-0.03368722,-0.01375871,0.02497683,0.07025909,0.02580328,-0.03760346,0.09117295,0.00828475,-0.02399812,0.02640737,-0.00192093,-0.017452,-0.02185606,0.05995195,0.01811741,0.01700173,-0.01567462,-0.04958682,0.04999825,0.02698266,0.00173303,-0.01185393,0.04273982,-0.0186759,-0.00474008,0.038559,0.0227258,0.00539384,0.04396007,-0.0272397,-0.04305857,-0.00456848,-0.0173466,0.01919416,0.03698995,-0.10102316,0.03771066,0.02830503,0.00815815,0.01983533,0.02078949,0.05796549,0.01004629,-0.04469697,-0.03844288,0.03847447,-0.07859426,-0.01941869,0.0749445,0.0227915,-0.25232324,0.06117605,-0.02647345,0.00025083,-0.00006259,-0.06050662,0.02516897,-0.00028452,-0.0038434,-0.01920114,-0.02678591,0.08034241,0.02475429,-0.05024979,-0.00369003,0.04433768,0.05609589,-0.02002261,0.07578999,-0.07197914,0.02934222,0.0187272,0.20324868,0.02067177,0.04129296,0.04896526,-0.0227446,-0.0038293,0.04945854,0.04651808,-0.0178409,0.0147661,0.12265532,0.00765671,-0.02549015,0.0160616,0.02719401,0.00367812,-0.00173669,-0.03597248,-0.01394953,0.00545287,0.00404871,0.0039068,0.09784801,-0.04400237,-0.06953862,-0.07932811,-0.02190783,0.0129912,-0.08194606,0.03697657,-0.02622804,0.00988859,-0.0320304,-0.00572718,0.00623506,-0.03100542,-0.03720537,-0.00218461,-0.0027504,-0.01532084,0.06719699,0.04278063,-0.02589718],"last_embed":{"hash":"ghrhvr","tokens":256}}},"text":null,"length":0,"last_read":{"hash":"ghrhvr","at":1751288816015},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.computeColumnSpans()","lines":[230,260],"size":637,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"ghrhvr","at":1751288816015}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.computeColumnSpans()#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04040682,-0.02825903,0.0439379,-0.06736839,-0.01005712,0.02708821,-0.07323214,-0.00984915,-0.01478366,0.02958504,-0.00936682,-0.0779373,0.01154538,0.03810868,-0.02143152,-0.03092295,-0.04351696,0.08355392,-0.03867087,-0.06071217,0.06323688,-0.07204977,-0.00601816,-0.04424663,0.02511751,0.08952589,0.01199911,-0.08775644,0.00711482,-0.21368475,-0.03142203,-0.02677726,-0.02591692,0.00492056,0.02082683,-0.04789107,-0.02513735,0.01866234,-0.04108292,0.0464186,0.01955707,0.04864513,-0.04419998,-0.02552181,-0.00585225,-0.05310735,-0.00539861,-0.00195017,0.03779761,-0.02234306,-0.0495523,-0.04070968,0.009039,-0.02051327,0.0295024,0.06412946,0.03512763,0.07494598,0.01095364,0.02285361,0.03292342,0.01701192,-0.23447664,0.04583738,0.07532985,0.0177168,-0.04097413,-0.05658078,0.05401007,0.06322537,-0.00205693,0.01642529,0.06110701,0.01987098,0.03763668,-0.00796742,0.00761212,0.01887779,0.00392701,0.00969329,-0.10007134,0.0568145,-0.01294378,0.03963231,-0.01516697,0.01286226,-0.02371273,-0.02523876,0.05335455,0.01935008,-0.00601714,-0.06546346,0.05147036,0.11822032,-0.04399572,-0.05180626,0.05208385,0.02065638,0.06743534,0.146954,-0.05190664,0.00836065,-0.0141518,0.04342462,-0.00228791,-0.03406639,-0.03535837,-0.02228137,-0.02012322,0.01246849,-0.10678355,0.00909085,-0.04623795,-0.06092374,-0.01225212,-0.04502234,0.07709586,0.02876416,-0.05211306,0.07974092,-0.02767139,0.08261489,-0.02970361,0.04070463,0.04756908,0.0186621,-0.01579011,0.04308166,-0.02934218,0.04600238,0.03159169,-0.00063379,-0.04317206,0.00603883,0.02730805,-0.02449184,0.03336189,-0.02687331,0.0571796,0.04695237,-0.00998475,-0.02991072,0.00912268,-0.02778544,-0.01585893,0.14193514,-0.02867807,0.09087017,0.00352037,0.0109354,0.01419905,0.05843653,-0.02401762,0.0044025,-0.01039038,0.01593012,0.02281984,0.00080508,-0.04716429,0.00625344,-0.02219276,-0.03487418,-0.01576364,0.12100898,0.01635182,-0.05384647,-0.0324661,0.066179,-0.00638187,-0.03768704,-0.00867329,0.01154819,-0.00871443,-0.04764776,0.05790527,0.03550846,-0.07577417,-0.02138319,0.06741392,0.01273793,0.00841954,-0.04112177,-0.04322807,0.00877396,-0.01208588,-0.0307965,-0.00172974,-0.03161704,-0.0348385,-0.00047939,0.02847463,0.05703988,0.0432832,0.06003241,-0.03626728,-0.06138866,-0.02817591,-0.00056294,0.03127548,-0.04611088,0.12330347,0.02153168,-0.01955699,0.0307497,-0.03127977,0.0211358,-0.00390079,0.00265971,0.04189428,0.0140804,-0.10499991,-0.04133251,0.04576038,0.07384752,-0.06391566,-0.02761927,0.0347471,0.06187105,0.00552683,0.07394131,0.00886702,-0.01780221,-0.10143474,-0.23073225,0.00307838,0.04188058,-0.0346604,-0.05933153,-0.01070168,-0.02200172,-0.03028249,-0.02275129,0.01606638,0.08614653,-0.00253629,0.00644815,0.00632713,-0.05921126,-0.01331063,0.02679859,-0.03610312,-0.06009211,0.02514788,0.05488766,0.00664792,-0.05448855,-0.11176737,0.01241008,-0.02817515,0.16265862,0.01809574,0.01700223,-0.05148923,0.03235312,-0.04413878,-0.03461226,-0.01583724,0.02722958,-0.02926526,-0.02921933,-0.02083657,0.02850977,0.03087546,-0.02715001,0.0181907,0.00449231,-0.10048357,0.06737028,0.01672225,-0.03489654,-0.01178904,0.02500315,0.06886598,0.02202284,-0.03537275,0.09022488,0.00735517,-0.02386692,0.0250739,-0.00321987,-0.01823208,-0.02348494,0.0618655,0.01820856,0.01841088,-0.01815018,-0.04948414,0.05187558,0.02869526,0.00136291,-0.01038474,0.04597932,-0.01983872,-0.00428395,0.03900131,0.02301647,0.00397812,0.04413974,-0.02792728,-0.04285982,-0.00582504,-0.01761621,0.01940355,0.03735726,-0.10365646,0.03939672,0.03191365,0.00627712,0.0216866,0.02062119,0.05800167,0.00841128,-0.04395219,-0.04053469,0.03677324,-0.07871991,-0.02309018,0.07418812,0.02429105,-0.25246266,0.05823247,-0.02488876,-0.00192524,-0.00045209,-0.06232129,0.02435518,-0.00172671,-0.00641897,-0.01815511,-0.02703062,0.08072312,0.02646788,-0.0497297,-0.0025813,0.0448368,0.05552423,-0.01797369,0.07754169,-0.07362623,0.02958526,0.01853036,0.20418231,0.01891592,0.0375614,0.04854605,-0.01999385,-0.00216547,0.04937569,0.04577269,-0.01805353,0.01509198,0.1246075,0.00620847,-0.0260262,0.01645159,0.02494959,0.0009318,-0.00281107,-0.0345009,-0.01358996,0.0035121,0.00366046,0.00257231,0.09909985,-0.0450976,-0.06879028,-0.07796711,-0.02264567,0.01127617,-0.08136715,0.03787595,-0.02552758,0.01000508,-0.02854954,-0.00664444,0.00675352,-0.03178839,-0.03726468,-0.00355625,-0.00547143,-0.01334439,0.06673073,0.04409082,-0.02440162],"last_embed":{"hash":"5lphaa","tokens":253}}},"text":null,"length":0,"last_read":{"hash":"5lphaa","at":1751288816123},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.computeColumnSpans()#{1}","lines":[232,260],"size":582,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"5lphaa","at":1751288816123}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06571704,-0.03698849,0.03472877,-0.07419667,0.00657171,0.02902821,-0.04717716,-0.01822009,-0.02059837,0.04974497,-0.00201832,-0.05927884,0.0181646,0.00551769,-0.03841328,-0.06683304,-0.02304137,0.06379357,-0.04470671,-0.00202687,0.0702064,-0.06368177,0.00833853,-0.06843604,0.05140142,0.07723101,-0.00268825,-0.04567852,0.02756334,-0.22469306,-0.0546818,-0.01249766,0.01802725,-0.00305402,-0.00235856,-0.02767897,-0.02322749,0.00417646,-0.01971925,0.02680209,0.00840623,0.03043486,-0.02100728,-0.00115445,-0.00400235,-0.00836886,-0.03043414,0.02786396,0.00644371,-0.01279765,-0.06219541,0.04441648,0.00961115,0.02233575,0.0472361,0.05054503,0.05589703,0.0823629,0.02652816,0.03879884,0.01261404,0.01408406,-0.21077694,0.0223982,0.07142453,0.00540527,-0.03784746,-0.03619144,0.00117016,-0.00716367,-0.01215845,0.0184997,0.01848014,0.05223477,0.04581364,-0.03718788,-0.02478693,0.0302394,0.02115385,0.00208448,-0.08859047,0.02174551,0.00409546,0.04320443,-0.00303523,-0.01025472,0.05393186,0.01186855,0.06713384,0.01245418,-0.00790393,-0.03920654,0.03048488,0.11039439,-0.0548538,-0.01489207,0.04119718,0.06387592,0.00357148,0.12075127,-0.0470801,0.0264628,-0.01673839,0.05434491,0.03099875,-0.01448142,-0.02754287,-0.02817076,-0.03734735,0.00845387,-0.03882531,-0.00333221,-0.05618669,-0.07895674,0.01190407,-0.02045237,0.05577937,-0.02233113,-0.04968753,0.0597968,-0.01438498,0.06243736,-0.02845856,0.03934651,0.04841413,0.01353448,0.02115401,0.02742915,0.00547303,0.06480645,0.05350968,0.00684944,-0.0854971,-0.00132978,0.00312396,-0.03205723,0.01900187,0.01509239,0.04452796,-0.00169986,0.0145712,-0.08218595,-0.0127254,0.02273782,-0.04527046,0.13249876,-0.050601,0.09302969,0.01961584,0.00201666,-0.0269503,0.01994573,-0.05711664,-0.00381215,-0.00126544,0.0120825,0.01203688,0.0065148,-0.06484982,0.00757109,0.02190689,-0.02608654,-0.01776261,0.10543904,0.01868488,-0.06565806,-0.07545272,0.05429323,-0.01285083,-0.04007376,-0.00522161,0.02142287,0.00639471,-0.03215681,0.03706323,0.01450536,-0.0724655,-0.03424302,0.05218317,0.04902795,-0.02006239,-0.03183448,-0.05194697,-0.01255842,-0.00738474,-0.02661554,-0.01024169,-0.01849129,-0.03015005,0.03700272,0.00578921,0.04519327,0.03151005,0.03311987,-0.02480784,-0.04197396,-0.03369116,-0.00480404,0.0635614,-0.03289653,0.12019846,0.00933028,-0.04791114,0.03594493,-0.04475049,0.0116233,0.0329381,0.00031831,0.0465751,0.02555773,-0.09877269,-0.03888036,0.01085516,0.03642632,-0.08344502,-0.03837034,0.03560693,-0.01280698,0.03078426,0.07269521,-0.04238158,-0.02427468,-0.08008462,-0.2362255,0.0123047,0.00502628,-0.02408172,-0.06255037,-0.01461298,-0.03442654,-0.03632712,-0.02134264,0.0061544,0.1230086,-0.03217845,-0.00879776,0.00553062,-0.07108565,-0.00316047,-0.01660515,-0.06135102,-0.02143643,0.00771832,0.01992665,0.00702817,-0.09777454,-0.08712731,0.04136332,-0.03159041,0.13432246,0.02856928,0.00261229,-0.00784992,0.01981414,-0.06204425,-0.003665,-0.02543294,0.0067741,-0.02137232,-0.06187952,0.00258061,0.0159534,0.0501257,-0.0089701,0.00318754,-0.00329585,-0.07269015,0.03744094,0.00745667,-0.04872407,0.01643433,0.00676638,0.07830403,-0.00494884,0.00079985,0.10136885,0.03029328,0.0066128,0.02572364,-0.0296333,-0.01626527,0.00711429,0.05119276,0.02951326,0.00727954,0.01306879,-0.05156225,0.02723594,0.05895922,0.03074703,0.02895112,0.04174547,-0.00340316,0.0216886,0.06915548,0.05621553,0.03665188,0.0253752,-0.03211276,0.00345118,-0.05728961,0.00186453,-0.0134108,0.00542657,-0.06650478,0.05011059,0.00564382,-0.02469671,0.01156962,0.07433536,0.01226203,0.04420915,-0.04117054,-0.01958087,0.01744048,-0.07294593,-0.04649267,0.08748279,0.01445315,-0.27168205,0.01948235,-0.05164028,-0.00753663,-0.01110713,-0.05295534,0.04051376,-0.01019803,-0.02366045,-0.02976844,-0.03590433,0.04460696,0.02309318,-0.06732422,-0.02362251,0.0517988,0.06512493,-0.03312326,0.04835512,-0.04333507,0.05048689,0.04581201,0.24557513,0.03191382,0.02885319,0.02719003,-0.00054461,0.00596313,0.06025554,0.05463726,-0.02386768,-0.02497297,0.14571181,0.03830396,-0.01707023,0.05752427,-0.00049139,0.01947729,-0.02223801,-0.06216317,-0.04790954,0.022594,-0.01123373,0.04041811,0.13556547,-0.03146801,-0.00896337,-0.06937078,0.01997199,0.02658593,-0.08867674,0.08317634,0.02134811,0.01052574,-0.00947672,-0.02846142,-0.01573958,-0.02211699,-0.06218036,-0.02898081,0.01997173,-0.04941433,0.08207046,0.04401441,-0.01056672],"last_embed":{"hash":"1hcjw03","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"1hcjw03","at":1751288816253},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)","lines":[261,307],"size":1695,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1hcjw03","at":1751288816253}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03825233,-0.01410434,0.03418227,-0.08447227,0.01657399,0.00976346,-0.04508893,0.00105015,-0.00592643,0.03843978,-0.00009423,-0.0532327,0.01647525,0.03602996,-0.01733036,-0.04005626,-0.01722802,0.11786371,-0.04655885,-0.02336155,0.09327336,-0.02257075,-0.02193121,-0.07061841,0.08257893,0.06208919,-0.00633795,-0.07060093,0.04437999,-0.23397401,-0.07612909,-0.04169383,-0.00688541,-0.00396497,-0.01516505,0.02829598,-0.04711361,0.00039491,-0.03492516,0.03249017,0.00715353,0.08801351,-0.00704501,-0.03709513,0.01446114,-0.00847011,0.01660152,0.01823792,-0.01335342,-0.03628201,-0.04753628,0.00171335,-0.02428248,0.02416743,0.05067877,0.04867843,0.02846076,0.07261711,0.01626807,0.03646906,-0.00787588,0.02273049,-0.14482099,0.00208805,0.10189547,-0.00405709,-0.01711982,-0.06253154,-0.03139362,0.0323506,0.05275932,0.03676043,0.00891461,0.0776983,0.02599617,-0.04553768,0.00623414,-0.00249285,0.02959898,-0.00692277,-0.11017392,0.01750078,-0.00983968,0.02491375,-0.00125949,-0.04203935,-0.00335332,0.00647149,0.05923365,0.00585643,-0.02333573,-0.07898244,0.02890558,0.08147644,-0.05671354,0.01164935,0.06656328,0.06541229,-0.01655465,0.13619941,-0.03832151,0.06693228,-0.03562023,0.02100623,0.05602024,-0.00124091,-0.04580097,-0.03855388,-0.0360246,-0.00279661,-0.02491694,-0.04409992,-0.04518909,-0.07928349,-0.01294887,-0.03986647,0.03946878,0.03868968,-0.03437793,0.04133952,-0.00783462,0.06330479,-0.00697828,0.01846753,0.07078725,-0.00068013,-0.01500316,-0.0042994,-0.00443375,0.07006421,0.0117889,-0.02953703,-0.04878694,-0.01880554,0.05815222,-0.02365528,0.0095191,0.00407391,0.04617234,-0.00645417,0.03835833,-0.06232023,-0.02170365,-0.03034112,-0.03791557,0.11342119,-0.05381408,0.08489593,0.01197128,-0.04335621,-0.03064258,-0.00798164,-0.08669782,0.01505998,0.01177765,0.01547509,0.00330513,-0.00848681,0.00989754,0.03065566,0.00276517,0.00319836,0.01307219,0.1051373,-0.0024215,-0.06563261,-0.0461801,0.02968367,0.00500475,-0.01554764,0.01327675,0.07337897,0.02948828,-0.06474388,0.05114379,0.00993693,-0.06918387,-0.03020712,-0.01836829,0.05961119,-0.02146332,-0.03475909,-0.07728501,-0.01595108,-0.00078514,0.01000008,0.01268916,-0.00070987,-0.02172242,0.00804886,0.00452051,0.03974015,0.03861374,-0.00225468,-0.02574575,-0.06082098,-0.02397739,-0.00638277,0.0513975,-0.00900251,0.12608343,0.03885233,-0.03163826,0.0599106,-0.05353728,0.021349,0.04297581,-0.02491765,0.05212856,0.04529207,-0.09616212,-0.1026293,0.02788145,0.00297997,-0.0863534,-0.02004,0.03829757,0.03812349,-0.02482799,0.04684737,-0.01171931,-0.02727905,-0.08332053,-0.2282967,0.02364746,-0.04137595,-0.00272415,-0.05425211,-0.04573401,-0.02096239,-0.01265161,-0.04129265,-0.03801519,0.06783233,-0.03949505,-0.02356272,0.01594555,-0.09678996,0.00654313,-0.00959608,-0.04456323,-0.01091622,0.01903577,0.00195725,-0.0040599,-0.10338444,-0.08281269,0.0442722,-0.01528298,0.13405116,0.01475155,0.00978897,-0.04528773,0.0013449,-0.00903309,-0.04166897,0.00173678,-0.04822768,0.04481314,-0.04571636,-0.01486704,0.04986181,0.06184059,-0.01005579,0.03919651,0.0072474,-0.09771179,0.02347543,0.00550847,-0.05787853,-0.01343997,0.00343454,0.08001495,0.0002692,-0.01459186,0.05306389,0.00944885,-0.01574962,0.03073903,-0.03882306,-0.01833657,0.04449194,0.09596842,0.00670494,-0.02699949,0.0359391,-0.05575481,0.03068022,0.03389893,0.05529935,-0.00529263,0.04080455,-0.01529668,0.018543,0.06483415,0.02245189,0.04544513,0.0090104,-0.01051932,0.00171352,0.00008866,0.03990648,-0.02557724,0.03133756,-0.01895855,0.04556365,-0.04641711,-0.00767627,0.00860593,0.03454652,0.03889078,0.02836148,-0.02092038,-0.00764944,0.03225329,-0.07287583,-0.01944072,0.07266232,-0.02474502,-0.25619525,0.02741694,0.03252805,-0.00151028,-0.05156077,0.01452382,0.08187743,-0.03262706,-0.03288729,-0.02476639,0.01032524,0.06280726,0.01079017,-0.02756642,-0.02617758,0.06480362,0.05320339,-0.01277169,0.03044974,-0.06986152,0.02543799,0.0649346,0.26948658,-0.00889726,0.02576427,0.0677105,-0.03583914,0.01088193,0.03073149,0.03741631,-0.02767789,0.00190972,0.11629185,0.00765971,-0.02700809,0.06352442,-0.01344122,0.00806445,0.00964405,-0.01981393,-0.02062034,-0.03091029,-0.0358578,0.02995818,0.12051742,-0.0298358,-0.04091697,-0.02933309,-0.01053876,0.02452019,-0.09956608,0.06126691,0.04142258,-0.01837206,0.02988061,0.02764288,-0.00074444,-0.03308224,-0.0742726,-0.02817619,0.05149283,-0.0054342,0.059823,0.03508396,0.03940899],"last_embed":{"hash":"1746liz","tokens":125}}},"text":null,"length":0,"last_read":{"hash":"1746liz","at":1751288816534},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{7}","lines":[276,282],"size":327,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1746liz","at":1751288816534}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{14}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04261977,-0.02136053,0.0338282,-0.04820829,0.01161784,-0.00120978,-0.0342202,-0.01083877,-0.02691197,0.03291317,-0.00952543,-0.07520398,0.00721044,0.02809784,0.01096257,-0.04347111,0.01145566,0.06910374,-0.0203147,-0.01837007,0.07781169,-0.04797895,0.01720099,-0.02899387,0.04725603,0.09599556,0.00749937,-0.00054299,0.04998779,-0.17746077,-0.03365926,-0.04221147,-0.01044663,0.01881595,0.01918489,-0.02426949,-0.02008626,0.02241918,-0.02105754,0.03285607,0.02529765,0.01386857,-0.06670403,-0.02274192,0.00562947,-0.0212103,-0.02637903,0.02983546,0.02622811,-0.01129882,-0.06139411,-0.01896616,0.04488894,-0.02435496,0.03401668,0.08995656,0.0567905,0.07727595,0.06050754,0.02520473,0.02075859,0.03861779,-0.20680414,0.02240898,0.07452943,0.03891248,-0.03432892,-0.0408526,0.02257356,0.02818121,0.01673243,0.04605639,0.02815254,0.0499128,0.05134324,-0.04340152,0.00225545,0.03432745,0.01880961,-0.01623413,-0.09668972,0.02348218,-0.0192305,0.02355058,-0.01488385,0.00363493,0.0448915,0.0080819,0.05926409,0.00936368,-0.01436349,-0.06237981,0.05868598,0.10341945,-0.06174251,0.00326508,0.03094827,0.05293189,-0.00036431,0.13729802,-0.07896867,0.04223396,-0.01068241,0.0085588,0.01578844,-0.03807642,-0.0571512,-0.03293038,-0.01433411,0.01279588,-0.04122074,0.02567802,-0.06820915,-0.05849815,0.00241597,-0.01328659,0.06591956,0.00560606,-0.03660003,0.0516057,0.01876148,0.07442669,0.00698133,0.02634127,0.02469572,0.00886469,0.0196468,0.02760305,-0.00306572,0.06266961,0.03152772,0.01337315,-0.06604441,0.02080228,0.02070025,-0.01721479,0.00772296,0.00980956,0.04402743,0.00125144,0.01052111,-0.06606439,-0.00684884,-0.02357502,-0.02054799,0.09017831,-0.04584163,0.09490791,0.02577129,0.00263113,-0.0258134,0.01787154,-0.06926634,0.00877678,-0.03001852,0.00779778,0.02952096,0.02115508,-0.05268946,0.02328712,0.03709545,-0.04146467,0.01310676,0.10695088,0.01800248,-0.07480624,-0.0763736,0.05257383,-0.00132076,-0.04541264,-0.02549285,0.02316043,0.0056427,-0.04547537,0.02663174,0.03533724,-0.06243367,-0.04698049,0.0337504,0.00640307,-0.0239559,-0.06747162,-0.05728693,-0.017075,-0.0030504,-0.06126203,-0.02023865,-0.04067519,-0.02306085,0.01886212,0.00377838,0.0401632,0.03291174,0.03126139,-0.01231286,-0.02388127,-0.03922204,0.00186669,0.06450486,-0.04447806,0.10983085,0.02120618,-0.0150464,0.03726451,-0.05157254,0.01348025,0.01377698,-0.013827,0.0510214,-0.00886343,-0.11928333,-0.03139011,0.01817336,0.0772222,-0.06583408,-0.0676171,0.00056893,0.03284736,0.04158821,0.06080223,-0.05544891,-0.0109904,-0.07613103,-0.24751841,0.03350332,0.00063847,-0.01950363,-0.09692547,-0.02358241,-0.04060398,-0.04069206,-0.01679395,-0.00588455,0.11704493,-0.00903527,-0.01021225,0.02876727,-0.04905247,0.00537962,0.0197255,-0.06348911,-0.02673716,0.01071227,0.01416671,-0.01177102,-0.05859515,-0.09557638,0.02144557,-0.01842424,0.14799526,0.06632509,0.01828326,-0.01800461,0.01393559,-0.01046887,0.01201738,-0.07346405,-0.01439404,0.00423777,-0.03321802,-0.01442618,0.03663353,0.03521428,-0.05236859,0.00715754,0.00632698,-0.08809593,0.05720856,0.01779617,-0.09499506,0.03692922,0.01059905,0.09643638,0.00476993,0.02687216,0.06969662,0.04222552,-0.02785083,0.01669529,0.00196229,-0.02748156,-0.0035198,0.04326852,0.01344024,0.01358222,-0.00084808,-0.06530663,0.05438204,0.05111177,0.0095446,-0.01785649,0.05337867,-0.00971225,-0.02002099,0.06543054,0.06420181,-0.01415294,0.00410434,-0.02302174,-0.0264731,-0.05659208,0.00923924,-0.01778856,-0.01782226,-0.04822844,0.0317906,0.03253885,-0.02155598,0.03953117,0.04195447,-0.02400237,0.03578883,-0.05766226,-0.04726265,0.02216506,-0.06669834,-0.03531227,0.07503425,0.02413886,-0.2559326,0.02149657,-0.00680064,-0.01224607,0.00409733,-0.04870776,0.02753048,-0.02767343,-0.0248966,-0.03811962,-0.02559937,0.02681617,0.01126963,-0.05334451,-0.01304106,0.06438074,0.06149318,-0.01975188,0.05548206,-0.08802345,0.04930971,0.06984758,0.26899874,0.03321806,-0.002671,0.04042933,-0.01598312,0.01557632,0.04299138,0.06316338,-0.04031182,-0.00947991,0.14347282,0.02237222,-0.01766089,-0.00553904,0.02749951,-0.01220527,-0.00607583,-0.03803915,-0.08261853,0.00025047,-0.01467742,0.02901614,0.11806235,-0.0502095,-0.03060016,-0.07340465,-0.03297967,0.03304943,-0.07280151,0.04437216,0.02887803,0.03251161,-0.0152261,-0.03209566,-0.0140632,-0.03139838,-0.05537795,-0.0291474,0.01035196,-0.00773487,0.08896391,0.03100626,-0.02030976],"last_embed":{"hash":"13pw2dp","tokens":191}}},"text":null,"length":0,"last_read":{"hash":"13pw2dp","at":1751288816624},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{14}","lines":[292,307],"size":378,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"13pw2dp","at":1751288816624}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{15}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04558348,-0.02428564,0.0437047,-0.0593005,0.00880129,-0.00809769,-0.05220665,-0.00227592,-0.02316074,0.02977713,-0.01586942,-0.0824051,0.00749657,0.03672537,0.01446985,-0.05067356,0.00263017,0.07182178,-0.01679054,-0.01427541,0.07652698,-0.05292797,0.01447084,-0.03754984,0.03830454,0.09635617,-0.00276027,-0.00922741,0.05097394,-0.17435758,-0.04210104,-0.033838,-0.00538299,0.02234294,0.0200743,-0.02580357,-0.01965803,-0.00737429,-0.0326931,0.03306406,0.02769482,0.02774386,-0.07149919,-0.0317128,0.00672666,-0.02548355,-0.03368557,0.03095162,0.03155112,-0.0324091,-0.0631562,-0.02054843,0.03415451,-0.0292407,0.04605752,0.08451426,0.06153473,0.0809189,0.05833493,0.0219452,0.01744327,0.01937785,-0.21538197,0.01556783,0.07586849,0.03074196,-0.04225684,-0.03215623,0.01842609,0.04130248,0.01402003,0.03851138,0.03568718,0.04991552,0.05922188,-0.04540642,0.00282132,0.01806899,0.009321,-0.01669325,-0.09787115,0.03110793,-0.01883153,0.03142537,-0.01080214,0.00032663,0.03629778,0.0120346,0.05384336,0.0041376,-0.01263404,-0.06193642,0.04542295,0.10473493,-0.06211431,-0.00972812,0.03925359,0.04028212,0.00497747,0.13693012,-0.06764663,0.03904256,-0.00673066,0.00715474,0.00138587,-0.02838696,-0.03991307,-0.03081227,-0.0152115,0.01185927,-0.04905946,0.02593982,-0.07026087,-0.0653248,-0.00701611,-0.02324861,0.05667539,0.00745267,-0.03945388,0.06244016,0.01101863,0.08459847,-0.00311165,0.02242114,0.02764565,0.00854561,0.02029489,0.02672348,-0.00040145,0.05999444,0.01962846,0.01481043,-0.05876721,0.01702095,0.01653447,-0.0266776,0.00283063,0.00811851,0.05265246,0.00896131,0.00585316,-0.05949302,-0.00246245,-0.0397398,-0.0169585,0.08493532,-0.03994777,0.09115515,0.02240691,0.00854493,-0.01651715,0.02357242,-0.04825675,0.00985982,-0.02420562,0.00289532,0.03040184,0.03095751,-0.05045815,0.02318797,0.02610005,-0.04095075,0.00829591,0.09366862,0.01806965,-0.07652564,-0.06813943,0.04313295,-0.01592407,-0.03865673,-0.01731305,0.02379511,-0.00173836,-0.043548,0.02991403,0.04164569,-0.06353353,-0.04389688,0.03959132,0.00642225,-0.0118225,-0.05482775,-0.05651748,-0.01014633,0.0040341,-0.05450274,-0.00896144,-0.04360989,-0.03068616,0.00578371,0.01078233,0.03861809,0.02554309,0.03547116,-0.01150127,-0.02971679,-0.03167436,0.00189907,0.05623388,-0.02118937,0.11192963,0.01101261,-0.0249355,0.04090535,-0.04291726,0.0178565,0.01304731,-0.00493152,0.04358947,0.00078876,-0.12401082,-0.02536444,0.0271156,0.08318032,-0.06554884,-0.06275563,0.00322228,0.04509037,0.04285796,0.06355386,-0.05777599,-0.01751342,-0.08808209,-0.24918106,0.03747851,0.00427244,-0.02383269,-0.09180094,-0.00593159,-0.04745419,-0.04336214,-0.01924617,-0.00222213,0.12638684,-0.01747298,0.00455558,0.01828128,-0.04891231,0.00050401,0.00186753,-0.06170619,-0.03100029,0.010802,0.02088311,-0.02047149,-0.05566648,-0.10749191,0.03746921,-0.01570372,0.14846417,0.0678881,0.01023365,-0.01778719,0.03531628,-0.01873431,0.01291503,-0.06876542,-0.02265118,0.01952708,-0.01959713,-0.01069556,0.04091229,0.03518132,-0.05854511,0.0217147,0.00825617,-0.08817729,0.0640237,0.01489006,-0.08293124,0.04233092,0.00301062,0.09867026,-0.00115485,0.00317755,0.07183216,0.03717018,-0.03582549,0.00886614,-0.00074028,-0.02898782,-0.00571782,0.04542933,0.0180546,0.01401312,0.00744597,-0.06265716,0.05613012,0.05556485,0.00911292,-0.01519545,0.03713363,-0.00505324,-0.0132459,0.05980221,0.05485003,-0.00653427,0.01066874,-0.01716758,-0.0353207,-0.04558577,0.01000338,-0.01844977,-0.00394566,-0.04917772,0.01887893,0.03268348,-0.02933099,0.03689209,0.04110277,-0.01929909,0.04528856,-0.05770777,-0.04338611,0.01868447,-0.06936327,-0.01964449,0.08049496,0.02888815,-0.26002195,0.0158611,-0.00527777,-0.00827424,-0.00189923,-0.04510694,0.02574382,-0.02364293,-0.02386558,-0.03410932,-0.02915179,0.02885333,0.01952811,-0.04697702,-0.00183253,0.05404557,0.06076916,-0.01624128,0.07020984,-0.09513916,0.0420825,0.06737491,0.26764685,0.0313288,0.00156979,0.04264615,-0.01993294,0.00551891,0.04512135,0.07355648,-0.0368448,-0.0008957,0.13985094,0.02944578,-0.02516449,-0.0002002,0.02900214,-0.006911,-0.00316215,-0.03902287,-0.07097559,0.01530044,-0.00601087,0.01975124,0.11528011,-0.05636778,-0.03636982,-0.08437141,-0.02434901,0.03116664,-0.07618488,0.0444044,0.04051237,0.02594825,-0.01557789,-0.01815923,-0.01389319,-0.02883156,-0.05643461,-0.02548291,0.01443052,-0.00838441,0.08838459,0.02882322,-0.01872078],"last_embed":{"hash":"sauuih","tokens":171}}},"text":null,"length":0,"last_read":{"hash":"sauuih","at":1751288816805},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.originalPositionFor(generatedPosition)#{15}","lines":[294,307],"size":296,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"sauuih","at":1751288816805}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06934679,-0.03361074,0.05094993,-0.08130504,0.00019571,0.02488742,-0.02471401,-0.01767674,-0.01495924,0.05649419,-0.00284439,-0.04959692,0.01125341,0.00969669,-0.02503617,-0.07399182,-0.02624764,0.06059251,-0.04416267,-0.00838481,0.07363455,-0.05914982,0.01585883,-0.0642691,0.04123727,0.08940485,-0.0046399,-0.03161454,0.03371394,-0.21111679,-0.02061572,-0.01331467,-0.00525372,0.00383328,0.00384062,-0.03548736,-0.01827317,0.01585581,-0.02788658,0.01583622,0.01360715,0.02985262,-0.03324235,-0.00402886,-0.00987306,-0.00578092,-0.01879568,0.03644202,0.02079801,-0.01175395,-0.05122419,0.0316332,0.0270578,-0.00540306,0.04584802,0.05370105,0.05688327,0.0812616,0.02802569,0.04583235,0.01993709,0.0230403,-0.21048246,0.0430747,0.06190433,0.01523488,-0.03464147,-0.03900384,0.00674716,-0.00640543,-0.02221016,0.0144941,0.00996304,0.03419438,0.05096642,-0.03915019,-0.03079209,0.03207849,0.01418753,8.6e-7,-0.09855899,0.02210279,0.00263834,0.05212516,-0.01970525,-0.00507508,0.05183563,-0.00415743,0.07756192,0.02209677,-0.000351,-0.03113386,0.01656844,0.10844845,-0.04037828,-0.01851242,0.03461499,0.05935515,0.02028059,0.13290039,-0.0600265,0.01710463,-0.00897094,0.04758665,0.00269088,-0.02222647,-0.04240888,-0.02894899,-0.02809666,-0.0031919,-0.03319035,0.02209725,-0.09286868,-0.08083934,0.00958113,-0.02858902,0.04561953,-0.01133931,-0.05374875,0.05627044,0.00882024,0.05654484,-0.02184278,0.04591845,0.03004157,0.0043597,0.04480213,0.03983743,-0.00371521,0.06691278,0.05531618,0.01665305,-0.10157972,-0.01134425,-0.00062789,-0.01661742,0.01808268,-0.01675236,0.0312939,0.01192744,0.01932496,-0.07936752,-0.0043778,0.02721658,-0.02936945,0.1268755,-0.04751818,0.09896915,0.01972621,0.01154686,-0.03712892,0.02357676,-0.04353653,-0.0107568,-0.01195085,0.00509252,-0.00324258,-0.00738015,-0.05771327,0.00643709,0.00872485,-0.01351122,-0.01879628,0.09933048,0.02330155,-0.07410778,-0.06224542,0.05568845,0.01508661,-0.04785983,-0.0214912,0.01599085,-0.0188518,-0.02883854,0.04059342,0.01370869,-0.07587671,-0.04984115,0.06105707,0.04039736,0.00870916,-0.05450632,-0.04008919,-0.01326677,-0.00901474,-0.03414821,-0.02363371,-0.01013436,-0.03117051,0.05658524,0.01456482,0.05006327,0.02348553,0.02890296,-0.011892,-0.03384455,-0.03978655,0.000752,0.06257831,-0.04368111,0.10910855,-0.00165284,-0.0345016,0.04323559,-0.0567539,0.01974958,0.04095716,0.0080796,0.04835027,0.01790871,-0.10966317,-0.03905914,0.00868367,0.04546754,-0.08045498,-0.05012706,0.02269862,-0.00905887,0.04967184,0.07855772,-0.05175202,-0.03492495,-0.05624758,-0.22518979,0.00991536,0.01382291,-0.01589425,-0.05998937,-0.00248654,-0.03338095,-0.02604421,-0.02004296,0.00713667,0.13971926,-0.04052319,-0.00059858,-0.00823917,-0.05527176,-0.01355387,-0.02464594,-0.05322617,-0.0326573,0.00123172,0.02603461,-0.00115769,-0.08487367,-0.0860708,0.04373699,-0.04518036,0.12811579,0.0501107,0.02385953,-0.00123956,0.03208644,-0.07637051,0.00636843,-0.03488104,0.01288971,-0.0300164,-0.04926781,0.00067925,-0.00076167,0.03743241,-0.01435333,-0.00079313,-0.00261194,-0.0698088,0.03931201,0.00703721,-0.04489976,0.0205783,0.01529173,0.08564322,-0.02026857,-0.00244837,0.10059292,0.0446911,0.02554715,0.02293801,-0.03154013,-0.02346186,0.00749635,0.03803582,0.03861493,0.01536822,-0.00567669,-0.04601999,0.02423934,0.0553834,0.02224864,0.02768362,0.04850466,-0.00897396,0.02127943,0.062746,0.05569103,0.02214064,0.03325544,-0.04452647,0.00778667,-0.05414493,-0.00342057,-0.01603241,0.00476766,-0.07694491,0.04551915,0.02176345,-0.00693699,-0.006472,0.0694345,0.01540953,0.03642351,-0.05760882,-0.02643256,0.02490321,-0.0780744,-0.0320557,0.06774486,0.01862784,-0.27353865,0.03626482,-0.04005722,0.0249018,-0.00531392,-0.04893807,0.03640195,0.00696475,-0.01087886,-0.02067521,-0.04750485,0.0183449,0.0135928,-0.07534701,-0.03211323,0.04897542,0.05598135,-0.04041114,0.07189498,-0.05045046,0.06687444,0.04251441,0.24152885,0.02817802,0.04446101,0.01929552,-0.01684621,0.01053475,0.04943695,0.04219367,0.0003937,-0.02828049,0.12399323,0.03568708,-0.01306002,0.03660214,0.01924265,0.03139576,-0.01571831,-0.06972022,-0.05304952,0.00030214,-0.01632419,0.03503237,0.13385682,-0.01760922,-0.00515386,-0.08592395,0.00706553,0.02215497,-0.09920254,0.07070184,0.01620135,0.00359596,-0.01328787,-0.04066311,-0.02057521,-0.01849266,-0.03999176,-0.02175416,0.02903109,-0.05313383,0.10083465,0.0422047,-0.02586621],"last_embed":{"hash":"vpjwce","tokens":243}}},"text":null,"length":0,"last_read":{"hash":"vpjwce","at":1751288816890},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.generatedPositionFor(originalPosition)","lines":[308,335],"size":835,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"vpjwce","at":1751288816890}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05342219,-0.03000703,0.02724988,-0.07802819,-0.00375356,0.00876792,-0.05166693,-0.0160315,-0.0294882,0.04061916,0.01524653,-0.06575411,0.00906278,0.01515977,-0.03268372,-0.0551862,-0.01663234,0.07101973,-0.03608488,-0.04790274,0.06493828,-0.06160996,0.01330908,-0.04963316,0.04129158,0.08454827,0.00135129,-0.03487162,0.01135618,-0.22355819,-0.02458306,-0.03186094,0.00144409,0.00284217,0.01232407,-0.02466282,-0.01182464,0.02934677,-0.00437102,0.02060128,0.01473893,0.03277588,-0.04835064,-0.00762299,-0.00537132,-0.01604145,-0.01854905,0.03485588,0.0341187,-0.0114919,-0.06737415,0.02139769,0.02004291,0.00845453,0.03972305,0.06297893,0.0323013,0.09861809,0.02559971,0.02674028,0.01463731,0.03210103,-0.20657165,0.04515337,0.05782782,0.01646765,-0.02826715,-0.05524645,0.02753758,0.01361452,-0.00103555,0.02243679,0.04133699,0.0436944,0.05142161,-0.03645714,-0.02757238,0.0357067,0.01095022,0.0046818,-0.11301578,0.02567434,0.00438421,0.05119941,-0.00231396,-0.01937793,0.0285702,-0.00729277,0.07398365,0.03036559,0.00414819,-0.05568077,0.02414161,0.10213421,-0.04468941,-0.02506753,0.03593091,0.05732375,0.04559782,0.12381227,-0.05027379,0.02486924,0.0081906,0.0627754,-0.00320547,-0.02974712,-0.03551672,-0.02419561,-0.04163343,0.00521054,-0.04333751,-0.0040222,-0.06885828,-0.06639856,-0.00151174,-0.03913023,0.04934027,0.00573487,-0.04502543,0.05120556,-0.01070623,0.06801195,-0.02179078,0.03846873,0.02640942,0.01307748,0.0178312,0.03732341,0.00559496,0.05926584,0.03753882,0.01598216,-0.08697813,0.00094344,0.00340044,-0.03064353,0.02944233,-0.02465179,0.03725376,0.02025827,-0.00299946,-0.09303808,-0.02048891,0.00710744,-0.0387411,0.1379739,-0.04292892,0.10007948,0.00380877,0.00908515,-0.02741892,0.02162904,-0.03136958,-0.00396731,-0.03117195,0.01659706,0.03579244,-0.01877748,-0.06564422,0.00940268,-0.00220806,-0.01066523,-0.00311117,0.10726671,0.00517609,-0.07165797,-0.0697129,0.04669155,0.00485185,-0.05485729,-0.02252636,-0.00202755,-0.01453723,-0.04369966,0.04366199,0.02234562,-0.09809359,-0.06202921,0.06166758,0.04611218,-0.00673294,-0.04526101,-0.03666172,-0.00530341,-0.0020129,-0.03364965,-0.03272125,-0.01776671,-0.04446948,0.0357957,0.01948728,0.02955725,0.03631648,0.03186965,-0.00767181,-0.04638519,-0.04178242,-0.00851021,0.07344707,-0.03139072,0.13766417,0.00454174,-0.01737639,0.03402605,-0.04843443,0.02938596,0.02209181,0.00581394,0.03864819,0.02992673,-0.10380533,-0.04389083,0.01190531,0.05186855,-0.07591084,-0.05542403,0.02264774,0.00343523,0.02142946,0.09993944,-0.04194981,-0.03909323,-0.07914983,-0.21271443,-0.00371752,0.00604941,-0.00491451,-0.05675756,-0.00968887,-0.02718272,-0.03554668,-0.02873952,0.01945734,0.11316635,-0.01424422,0.01668258,0.02789856,-0.07249981,0.00756708,0.00700385,-0.02200802,-0.04043806,0.0141832,0.03848848,0.00511802,-0.06601882,-0.09091558,0.04354607,-0.04440759,0.14578076,0.05706432,0.02982852,-0.02206792,0.03064135,-0.06198256,0.00289159,-0.01876094,0.00985347,-0.02637017,-0.03570786,-0.00672215,0.01354303,0.04781142,-0.00326399,0.00064035,0.00160386,-0.08323383,0.03809718,0.01102586,-0.04687398,0.00325495,0.019183,0.08646587,-0.01007895,-0.02471785,0.10897229,0.03667992,0.01030601,0.05064322,-0.01879196,-0.03709819,0.01113071,0.05076451,0.0403502,0.02856459,-0.02060305,-0.06073785,0.01748055,0.04809138,0.01863563,0.00673488,0.05090388,-0.02556108,0.01667422,0.05391758,0.04803451,0.02153157,0.02467752,-0.0328012,0.01620878,-0.03916123,0.00335277,0.00668086,0.02216111,-0.09836052,0.04888512,0.01614749,-0.01079617,-0.00177757,0.06227754,0.01304391,0.02652872,-0.03757345,-0.01343054,0.01603223,-0.08395719,-0.03648224,0.07848699,0.01066934,-0.25619984,0.01927518,-0.04681782,0.01807126,-0.00851303,-0.04441673,0.03090549,-0.00189149,0.00735076,-0.02156027,-0.04071932,0.04780621,0.01221566,-0.07685862,-0.02481598,0.05303463,0.07977296,-0.03497826,0.08230734,-0.0617405,0.05529262,0.05036644,0.23776534,0.02621961,0.02614138,0.05375117,-0.02251315,-0.01067378,0.03976316,0.03583878,-0.0111303,-0.03097052,0.11461442,0.02671613,-0.00132374,0.05366267,0.02417936,0.01585768,-0.0262093,-0.08110034,-0.04085762,-0.00017788,-0.02890282,0.02245171,0.12799978,-0.0213525,-0.02795887,-0.08606652,-0.02665085,0.03599695,-0.06813306,0.05384919,0.02248958,-0.00142847,-0.01450058,-0.0258187,-0.03666389,-0.03063124,-0.06642868,-0.01504053,0.01378168,-0.03771425,0.08032794,0.04568465,-0.02644763],"last_embed":{"hash":"18pse2j","tokens":340}}},"text":null,"length":0,"last_read":{"hash":"18pse2j","at":1751288817006},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)","lines":[336,372],"size":1234,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"18pse2j","at":1751288817006}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0596134,-0.02528559,0.03883691,-0.08189309,-0.00963538,0.00683651,-0.04135336,-0.0147612,-0.03497457,0.04480546,0.02492913,-0.07950208,0.01233738,0.01717026,-0.03330385,-0.05118204,-0.02327984,0.0910343,-0.03151879,-0.05864069,0.06354973,-0.04496142,0.00396808,-0.03690953,0.04110243,0.09674012,-0.00445184,-0.04729293,0.01347409,-0.21522745,-0.02645658,-0.04790335,-0.03020164,0.00039279,0.01950617,-0.01385004,-0.01730984,0.03184649,0.00271867,0.01749629,0.00621568,0.03831815,-0.04978156,-0.0032567,-0.00031638,-0.01925047,-0.00476588,0.0357669,0.033465,-0.02435479,-0.0574402,0.02462464,0.01991837,0.01265636,0.04066259,0.06542388,0.01662306,0.09973978,0.02093742,0.02521506,0.01602717,0.04105766,-0.19057208,0.03668435,0.06427544,0.0274923,-0.0202329,-0.07559221,0.03171245,0.01666738,0.01489344,0.00785069,0.04284276,0.05150677,0.06219976,-0.03827549,-0.02594192,0.02217219,0.00521988,0.0026532,-0.11162621,0.02844655,0.01217665,0.05304085,-0.00451605,-0.02621489,0.00323608,-0.01128717,0.07992838,0.02152931,0.0131951,-0.06834088,0.01870234,0.10915697,-0.04726953,-0.02080201,0.03344603,0.05169113,0.0554542,0.12564383,-0.05483482,0.0279429,-0.00023076,0.07120616,-0.0008963,-0.02278455,-0.03983321,-0.02471928,-0.04821585,-0.00126981,-0.04720185,-0.01041485,-0.06764455,-0.06798706,-0.00804771,-0.05742611,0.04323239,0.01620119,-0.05775866,0.05246268,-0.01692158,0.06847125,-0.01760001,0.04781947,0.01815196,-0.00843,0.01802696,0.03250551,-0.00397812,0.03886673,0.03887993,-0.00092895,-0.07478069,0.00036877,0.0097785,-0.02960774,0.04723683,-0.0519187,0.04207763,0.02039282,-0.0066806,-0.08523437,-0.01281577,0.01182892,-0.0392262,0.13633929,-0.05016249,0.09932833,-0.00407496,0.01658336,-0.05292572,0.01105267,-0.02922987,0.00616589,-0.02412063,0.01160431,0.03514552,-0.02739652,-0.05923296,-0.00093366,-0.01212718,0.0012731,-0.00927098,0.12645626,-0.01598833,-0.07854059,-0.06852238,0.04653023,0.01092283,-0.04579467,-0.02450015,0.00511942,-0.02113413,-0.05189581,0.03419625,0.03063122,-0.11497672,-0.06372666,0.05103769,0.04726914,-0.00825439,-0.0488222,-0.02941643,-0.00012014,-0.00264097,-0.03202954,-0.03801646,-0.01054169,-0.03512462,0.02260686,0.016537,0.01819954,0.02634442,0.02680701,-0.01096874,-0.05480269,-0.03300921,-0.0040152,0.07480463,-0.03303195,0.1312485,0.00450883,-0.00869421,0.04518076,-0.05700865,0.01937595,0.02985611,0.00479991,0.02824657,0.03321292,-0.1051171,-0.06012073,0.00842007,0.04842064,-0.08867931,-0.0482848,0.02440718,0.01184236,0.0162995,0.09533221,-0.03629323,-0.02675162,-0.07048168,-0.20343791,-0.01629365,-0.00511315,0.0116364,-0.05821768,-0.01801211,-0.02827581,-0.00651108,-0.02716129,0.01286505,0.10410814,-0.00493558,0.01556513,0.02443843,-0.07609236,0.01913348,0.00550802,-0.00846282,-0.04824892,0.01196845,0.03504295,0.00646496,-0.07345364,-0.08760089,0.05605592,-0.03776592,0.14420635,0.0562319,0.03673033,-0.01471336,0.03439299,-0.07626748,-0.00063702,-0.03077511,0.00247645,-0.00263545,-0.03437242,-0.00607941,0.02777334,0.03852807,-0.00272346,0.00979686,-0.01204094,-0.10029604,0.04496685,0.01319984,-0.03704184,0.00141396,0.03056733,0.09292071,-0.02682282,-0.02350221,0.09264254,0.03593199,0.01242002,0.06624591,-0.02414688,-0.0398915,0.02078338,0.05293878,0.04565341,0.03030868,-0.02331557,-0.05774697,0.0230702,0.03887514,0.0262848,0.01272697,0.05106905,-0.0299602,0.01664406,0.04170218,0.04494145,0.0234889,0.03140785,-0.03178405,0.02946056,-0.03332556,0.0090372,0.00496176,0.02973811,-0.08562271,0.03734732,0.0055452,-0.00053198,-0.00727132,0.04866851,0.01672085,0.01691494,-0.03383429,-0.00055187,0.019708,-0.09528733,-0.02379024,0.08252103,-0.00067866,-0.24463901,0.02422292,-0.01528518,0.01518658,-0.02013774,-0.03236719,0.03191921,-0.01239082,0.00547411,-0.0048168,-0.03015115,0.0479957,-0.00040954,-0.07825607,-0.03177026,0.06452301,0.09890931,-0.04249588,0.08225626,-0.06946774,0.04531019,0.04444392,0.23398985,0.02718689,0.02891491,0.05204186,-0.03678885,-0.02052192,0.02620593,0.0308424,0.00251072,-0.03054999,0.07846494,0.02965748,0.00432943,0.07796342,0.03729105,0.03718244,-0.01297121,-0.0727896,-0.03678204,-0.00300808,-0.03110047,0.0195664,0.12640081,0.00653653,-0.04122252,-0.08417756,-0.03126962,0.03667451,-0.0691997,0.0465513,0.01055032,-0.00975327,-0.00671219,-0.01400861,-0.0295608,-0.03066838,-0.05986245,-0.00905601,0.02385626,-0.02297925,0.07321185,0.0414843,-0.0165437],"last_embed":{"hash":"13jmxx8","tokens":144}}},"text":null,"length":0,"last_read":{"hash":"13jmxx8","at":1751288817155},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{1}","lines":[338,346],"size":472,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"13jmxx8","at":1751288817155}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04963464,-0.0465639,0.04496134,-0.08050617,-0.02673501,0.01526357,-0.06468726,-0.01842078,-0.03520936,0.04686423,0.0222893,-0.06980228,-0.00117101,0.04067732,-0.02674211,-0.0469586,-0.03563667,0.07700144,-0.02089306,-0.04134619,0.08976926,-0.07242231,0.01355249,-0.0309268,0.03315302,0.10416014,0.00844463,-0.04472535,0.03270039,-0.20178962,-0.00540468,-0.01547161,0.00165238,0.01516285,0.00247619,-0.03396957,-0.02331386,0.01623008,-0.02557701,0.03350399,0.01025083,0.04021659,-0.05482429,-0.02762708,-0.00635911,-0.03016567,-0.0185784,0.0565277,0.03036525,-0.03497693,-0.04694461,-0.01188568,0.02347416,-0.01826035,0.03035364,0.08293776,0.04083313,0.07569758,0.03701461,0.02706889,0.01258328,0.02573097,-0.2163347,0.04540339,0.06782018,0.02105995,-0.03150785,-0.05645338,0.02174827,0.04213617,0.00559269,0.0359133,0.03146039,0.02862007,0.04363149,-0.03925166,-0.01599692,0.0240542,0.0205231,0.00003209,-0.09786171,0.02643815,-0.00177315,0.02324021,-0.01822476,-0.02477635,0.01216978,-0.00747209,0.05097542,0.01056745,-0.01446556,-0.04234526,0.05360644,0.09737819,-0.0230702,-0.01982309,0.04147558,0.05007372,0.02438029,0.13312367,-0.05846648,0.01493946,-0.02696626,0.04533833,-0.00004865,-0.02477035,-0.03180469,-0.03564875,-0.03162737,-0.02273785,-0.05158433,0.01143666,-0.06085152,-0.06538859,0.00241338,-0.02646001,0.04907147,0.02763961,-0.0270998,0.06507553,0.00674309,0.06312565,-0.01996364,0.04314968,0.04694304,0.02105053,0.01850418,0.0352082,-0.01791016,0.06873703,0.03149592,0.02376937,-0.04325987,-0.01432089,0.0001006,-0.0138671,0.01803735,-0.03160239,0.0622489,0.04108256,-0.00489181,-0.07369424,-0.01270209,-0.03810845,-0.01116785,0.14102605,-0.03710248,0.10135619,0.00830491,0.02250226,-0.00662437,0.03211235,-0.01493153,-0.01012372,-0.04003283,0.00334609,0.0148368,-0.00002683,-0.06361115,-0.00080526,0.01141268,-0.01527643,0.01154691,0.09785493,0.00782331,-0.0774342,-0.04681007,0.04299119,-0.00935758,-0.03094339,-0.02604258,0.0202769,-0.02080474,-0.02146297,0.05678145,0.04967926,-0.07177188,-0.04012662,0.04861382,0.01655722,0.02068915,-0.04039498,-0.04103372,-0.00793538,-0.01047571,-0.033252,-0.0071859,-0.03554153,-0.03567082,0.00941783,0.01892851,0.05763727,0.03698868,0.03393315,0.00565052,-0.04169814,-0.03256071,0.01871989,0.05885161,-0.02439663,0.12510473,0.01302758,-0.02829392,0.03426011,-0.04376258,0.03701001,0.01812448,-0.00536194,0.02400585,0.00625794,-0.10828509,-0.0558227,0.02579061,0.06888026,-0.06173035,-0.04351155,-0.00324861,0.02472958,0.03237183,0.08152069,-0.01803827,-0.01985714,-0.10104845,-0.22929312,0.01396279,0.02497051,-0.00371335,-0.08315934,-0.0022731,-0.03082483,-0.03265607,-0.01949198,0.02989308,0.09913857,-0.0489617,0.01593486,0.00914829,-0.06142784,-0.01234345,0.00276714,-0.04098776,-0.03771181,0.00186545,0.04884652,0.00569092,-0.0678263,-0.07550783,0.05484109,-0.03211459,0.14160103,0.06003188,0.00470933,-0.02097146,0.03376627,-0.03982776,0.00134927,-0.05016875,-0.01078071,-0.01272993,-0.039294,-0.03318455,0.03196494,0.058231,-0.03673948,0.02985449,0.00801838,-0.11111967,0.06769523,0.01950388,-0.06198436,0.02853641,0.00002716,0.06567851,-0.00260448,-0.02401436,0.09242668,0.05795656,-0.01999213,0.02460383,-0.0062354,-0.03631008,-0.0001925,0.03731101,0.00893117,0.01507455,0.00224579,-0.05072616,0.03148714,0.03604367,0.01126642,-0.01211843,0.04484629,-0.01364967,0.01924505,0.05040374,0.06964584,0.01288642,0.02442466,-0.02343983,-0.03440964,-0.03117573,-0.0074839,-0.00428142,0.03772341,-0.08919878,0.01823151,0.02437921,-0.01872509,-0.00465484,0.03621631,0.03657007,0.05570806,-0.05175889,-0.02828033,0.0310658,-0.0791397,-0.01363841,0.07427245,0.01713769,-0.27129465,0.03329597,-0.03044972,0.0127044,-0.01441572,-0.04013669,0.01574071,-0.0241981,-0.01375354,-0.02585191,-0.05532411,0.0445465,0.02282181,-0.03834799,-0.02218989,0.04819559,0.04902425,-0.03889409,0.08047712,-0.08123296,0.05313938,0.05935429,0.24653339,0.04313019,0.01574882,0.0706336,-0.0442323,0.00563949,0.00669806,0.05623081,-0.01085353,-0.01531973,0.12078777,0.02147011,-0.01414253,0.03216855,0.04494985,-0.01126714,-0.00395814,-0.0448429,-0.04983913,0.02346051,-0.02116006,0.01309186,0.12152564,-0.04523041,-0.05448283,-0.08169746,-0.03499338,0.02551815,-0.0742046,0.02617016,0.02800189,0.00690257,-0.02203476,-0.01150737,-0.01967472,-0.03117049,-0.0714137,-0.03342796,-0.00545823,-0.02658393,0.09126361,0.05143345,-0.0337331],"last_embed":{"hash":"92vgxj","tokens":135}}},"text":null,"length":0,"last_read":{"hash":"92vgxj","at":1751288817224},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)#{10}","lines":[361,372],"size":219,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"92vgxj","at":1751288817224}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.hasContentsOfAllSources()": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04927779,-0.03316976,0.02373141,-0.04219006,-0.00092635,-0.05602378,-0.07271998,-0.00258038,-0.04210382,0.03072231,0.00458742,-0.05651351,-0.00569391,-0.00040741,0.01364092,-0.01872654,-0.00552548,0.07702154,-0.00924679,-0.02174147,0.11261882,-0.02258632,0.01779372,-0.03032314,0.02399571,0.09364383,0.01670045,-0.05117884,-0.00199706,-0.19623649,-0.04073754,-0.08992603,-0.01213773,0.00306353,0.01553524,-0.02280845,-0.01971902,-0.0115289,-0.05408958,0.06691194,0.017227,0.03885229,-0.05504571,-0.02054429,-0.01580303,-0.02024826,-0.00246132,0.03451382,0.00818093,-0.05176554,-0.049222,0.02290367,0.01198236,-0.00862811,-0.00696467,0.08105481,0.03246529,0.12432913,0.04343189,0.04036965,-0.00269611,0.03111101,-0.18249057,0.0420041,0.0792684,0.05283257,-0.03611786,-0.01340828,0.00746624,0.01306498,0.02935509,0.04395786,0.01540693,0.03577615,0.04067671,-0.04452141,0.04035292,0.03016252,-0.00046466,0.01539239,-0.08618596,0.01800508,-0.01863,0.00936149,0.00603915,-0.00391212,0.00338701,0.02211075,0.08798271,0.01319606,-0.00784535,-0.02357869,0.0233148,0.08841822,-0.00571557,-0.01981433,0.04403991,0.04927497,0.06256202,0.11834572,-0.06682073,0.01389483,0.00055514,0.00701569,0.00716164,-0.02194314,-0.02512904,0.00132906,-0.02719371,0.01602697,-0.05244446,-0.00011483,-0.03438282,-0.0765465,-0.01231054,-0.0234617,0.06603548,-0.01090387,-0.07219794,0.02979754,-0.00379395,0.09086302,0.02863159,0.01386944,0.04110437,-0.01434921,0.08233602,-0.01080049,0.00847205,0.05732272,0.01553264,-0.02487834,-0.10136309,-0.01002171,0.02129844,0.00013127,0.02086216,-0.00432901,0.01005183,0.04026086,0.02238205,-0.04795061,-0.00008516,-0.03337559,-0.03415568,0.1080419,0.0222907,0.09333406,-0.03430682,-0.02192306,-0.03961021,0.01537082,-0.07225809,-0.01878363,0.00791402,0.00439638,-0.01776266,-0.01209472,-0.04633931,0.02245819,-0.00679085,-0.02127798,-0.01923216,0.11034437,-0.03772349,-0.05051914,-0.05191445,0.03721428,0.04044363,-0.08892051,-0.05147841,0.01250736,-0.02108771,-0.04412797,0.02629249,0.01053299,-0.09779329,-0.04053221,0.02170408,0.02749952,-0.016834,-0.03009786,-0.0454557,0.03706012,0.02994487,-0.04500181,0.00707867,-0.03230626,-0.00694593,0.04098582,-0.00064002,-0.00704414,0.01356493,-0.00834068,0.00669571,-0.04824472,-0.05338342,0.0222499,0.06085621,0.00985165,0.08285479,0.00401015,-0.01462884,0.03386196,-0.0543262,0.00990609,-0.03534859,-0.01490215,0.04942074,0.01812608,-0.10327788,-0.02447055,0.01749995,0.06558374,-0.04839213,-0.06464601,0.05828925,0.07021915,0.03463862,0.0490869,-0.07342515,0.01134633,-0.07892869,-0.22244075,0.02712245,0.01510428,-0.02199047,-0.0132706,-0.04330376,-0.02156041,-0.02326567,-0.03562377,0.01724732,0.12031873,0.03146692,0.00995747,0.01829111,-0.0242554,0.00915115,-0.00034173,-0.0614489,-0.07064147,0.01158549,-0.03060853,-0.00219181,-0.07210373,-0.05486334,0.04027567,-0.03468269,0.12178963,0.0455425,0.01694601,-0.03140629,0.02336008,0.00407202,-0.01020305,-0.07851298,-0.02976562,-0.00934967,-0.00041619,-0.02351482,0.06418671,0.04719435,-0.02407356,0.02423627,-0.03315822,-0.09384535,0.01376799,-0.00295027,-0.0394409,0.03124405,-0.02295407,0.08797567,-0.03104662,0.01192603,0.07990958,0.04789675,-0.0437905,0.03690201,0.00731033,-0.03950083,-0.00070382,0.06271354,-0.00568466,0.03058689,-0.03557098,-0.0286618,0.07525439,0.04319867,0.03737367,-0.0401602,0.05069163,-0.03558521,-0.00876105,0.08711445,0.02006886,-0.02746038,-0.00198483,0.00935078,0.00695303,-0.05839042,0.02060888,0.00408974,0.0638933,-0.05793218,0.03060776,0.03987659,-0.0143656,0.01660373,-0.01165935,0.02311471,0.01244541,-0.05974624,-0.00455202,0.03039875,-0.05651727,-0.0246008,0.07419227,0.05871772,-0.24432124,-0.00515136,0.00957855,-0.01353713,-0.04430458,-0.0475335,0.07353492,-0.03229643,-0.02887973,0.00856354,0.03870595,0.05845164,-0.03429224,-0.03086962,0.0097649,0.05855893,0.08250955,0.01137111,0.09035452,-0.09467444,0.01506077,0.03358258,0.27508757,0.00919069,0.00425519,0.09404221,-0.03979364,0.03447368,0.05407,0.05587647,0.01037602,-0.00920751,0.10559271,0.00467575,-0.04068417,-0.03728872,0.01986412,-0.01702986,0.02213935,-0.02855414,0.00989581,-0.01702715,-0.04540821,-0.00335863,0.10273291,-0.03345926,-0.0431056,-0.11089884,-0.05291285,0.05679004,-0.09528871,-0.00521863,0.03368272,-0.03827734,-0.02312482,0.03295553,-0.05078888,-0.00570557,-0.0567129,-0.00058798,0.01083535,0.00381272,0.07193788,0.09735169,-0.00183817],"last_embed":{"hash":"1qj2s4d","tokens":183}}},"text":null,"length":0,"last_read":{"hash":"1qj2s4d","at":1751288817298},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.hasContentsOfAllSources()","lines":[373,391],"size":474,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1qj2s4d","at":1751288817298}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.hasContentsOfAllSources()#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04767216,-0.03143663,0.0240354,-0.04495399,-0.00216972,-0.05595409,-0.0737459,-0.00192976,-0.0410364,0.03177898,0.00353049,-0.05512743,-0.00681888,0.0007943,0.01558231,-0.01729172,-0.00109117,0.07610546,-0.00812528,-0.01902309,0.10884378,-0.02450635,0.01840279,-0.02798436,0.02873181,0.09082221,0.01613885,-0.05137504,-0.0042777,-0.19713415,-0.04096853,-0.09085634,-0.01024483,0.00470984,0.01589944,-0.02475634,-0.01932284,-0.00982617,-0.05149455,0.06750996,0.0175581,0.0440561,-0.05775626,-0.01919812,-0.01471907,-0.02151148,-0.00094911,0.03481535,0.00885826,-0.05235814,-0.04916586,0.02543355,0.01166626,-0.00898785,-0.00965401,0.07873697,0.03127958,0.12612131,0.04227522,0.04027231,-0.00471389,0.03157076,-0.18230951,0.04130423,0.08046848,0.05258343,-0.03682138,-0.01455149,0.00583759,0.01263385,0.02951765,0.04518205,0.01655147,0.03545065,0.0411096,-0.0430367,0.03880152,0.02629028,-0.00188458,0.01606366,-0.0846481,0.01550268,-0.01934236,0.00602487,0.00835674,-0.00483639,0.00191127,0.02324902,0.09170198,0.01601527,-0.00723321,-0.02225616,0.02213525,0.08682396,-0.00800364,-0.02280808,0.04505625,0.04855714,0.06388307,0.11621567,-0.06634386,0.01740969,0.00164407,0.00620226,0.00692928,-0.0173273,-0.02535653,0.00166671,-0.02684698,0.01824672,-0.05324341,0.00129,-0.03160108,-0.07641424,-0.01256282,-0.02365454,0.06604512,-0.01328998,-0.07537306,0.03067756,-0.00564964,0.08840118,0.02964889,0.0152743,0.04026249,-0.01250413,0.08480645,-0.01005866,0.00641184,0.05686484,0.01557894,-0.02751388,-0.09854791,-0.00964857,0.0235497,-0.00070722,0.02063702,-0.00464825,0.01265203,0.0416204,0.01935174,-0.04731051,-0.00125201,-0.03546683,-0.03147746,0.11033942,0.02489103,0.09278828,-0.03381902,-0.02495942,-0.04067816,0.01173779,-0.0712133,-0.02175602,0.00637234,0.00380626,-0.02174875,-0.01266636,-0.04652824,0.02194246,-0.00740382,-0.02380254,-0.02042367,0.11381841,-0.03859486,-0.0490277,-0.05245147,0.03645268,0.03647843,-0.08900025,-0.05146899,0.01001398,-0.02037205,-0.04607725,0.02484433,0.01112017,-0.09634399,-0.04221064,0.02177726,0.02723582,-0.01838768,-0.02960255,-0.04592467,0.03701213,0.03110999,-0.04537321,0.00644382,-0.03179052,-0.00621659,0.03859806,-0.0045085,-0.00738899,0.0147007,-0.00927865,0.00775074,-0.04949974,-0.05512347,0.020669,0.06197535,0.0085327,0.0856002,0.00418983,-0.01290926,0.03350459,-0.05238261,0.00978591,-0.03427684,-0.01482599,0.05016072,0.02048237,-0.10294528,-0.02452251,0.01686979,0.06527654,-0.04897271,-0.06496966,0.05790446,0.07181948,0.03694136,0.04764693,-0.07074035,0.01459673,-0.0775639,-0.22284661,0.02819169,0.01409413,-0.02282359,-0.01089004,-0.04518246,-0.02087929,-0.02352433,-0.03455846,0.0158186,0.12149036,0.0325355,0.01038963,0.01861919,-0.02296328,0.00850146,-0.00076692,-0.05740038,-0.06566888,0.01096593,-0.03262316,0.00146598,-0.06950631,-0.05559019,0.0394096,-0.03280582,0.12303462,0.04452968,0.01574985,-0.03156099,0.023099,0.00500717,-0.0123347,-0.07667079,-0.03023896,-0.01007817,-0.0010933,-0.02443225,0.06550182,0.04470184,-0.02499543,0.02573068,-0.03462836,-0.09613416,0.01531224,-0.00302946,-0.03908705,0.03505938,-0.02366105,0.08840565,-0.03095325,0.0128311,0.0781153,0.0474593,-0.04157756,0.03618316,0.00512143,-0.03846015,0.00067127,0.06378282,-0.00599191,0.03195332,-0.03384796,-0.02753832,0.07685989,0.04522815,0.03618753,-0.03892715,0.04891973,-0.03656795,-0.00972259,0.08418957,0.01857245,-0.02844406,-0.00201631,0.01213198,0.00659523,-0.05781307,0.01730848,0.00466023,0.0633699,-0.05584909,0.03080871,0.04201753,-0.0142002,0.01489193,-0.01160019,0.02387019,0.01259244,-0.05865079,-0.00329728,0.02889809,-0.05891139,-0.02143878,0.07262767,0.05851293,-0.2468662,-0.00653672,0.00460653,-0.01405196,-0.04482861,-0.04862001,0.07142504,-0.03330012,-0.03122424,0.01101076,0.03887653,0.05850966,-0.03659355,-0.03032045,0.00767907,0.05806726,0.08305985,0.01443863,0.08947099,-0.09352547,0.01607762,0.0325307,0.27718309,0.01124015,0.00123328,0.09182433,-0.040128,0.03299836,0.05491024,0.05404158,0.01213918,-0.01000341,0.10502765,0.00533406,-0.03838111,-0.03777245,0.02000139,-0.01896183,0.02205027,-0.02750701,0.01205471,-0.01796186,-0.0437505,-0.00574423,0.10316785,-0.0294969,-0.04406587,-0.10796732,-0.0531544,0.05400553,-0.0945899,-0.00805802,0.03173983,-0.03961561,-0.02223993,0.03129609,-0.05377799,-0.00561743,-0.05795648,-0.00376108,0.01186611,0.00707812,0.06977118,0.09863424,-0.00039529],"last_embed":{"hash":"996bpk","tokens":180}}},"text":null,"length":0,"last_read":{"hash":"996bpk","at":1751288817387},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.hasContentsOfAllSources()#{1}","lines":[375,391],"size":414,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"996bpk","at":1751288817387}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05577772,-0.03339221,0.01858105,-0.02803377,0.02389823,-0.02867333,-0.07010374,-0.01579176,-0.05259688,-0.00003711,0.0087548,-0.09605622,0.01022005,0.01590701,0.02483993,-0.01677012,0.02565669,0.05390336,0.00303082,0.01155922,0.07003231,-0.03879022,0.05716808,-0.0500694,0.021266,0.0526913,0.00845218,-0.03230504,-0.00071205,-0.19058754,-0.07189472,-0.08341685,-0.06675371,0.01545563,0.00559122,-0.02325256,-0.00482568,-0.01179207,-0.00644165,0.0446287,0.03537189,0.02983803,-0.02663785,-0.00299691,0.00336534,-0.01317034,-0.02789325,0.02983736,0.044869,-0.05315385,-0.03925914,0.04026511,0.02517543,-0.00649107,0.00892852,0.08833325,0.05983258,0.10319651,0.05122773,0.04586315,-0.00587019,0.04372785,-0.18777117,0.01428318,0.06420843,0.03794979,-0.02920646,-0.01286886,-0.01947249,-0.01562254,0.02751387,0.04906489,0.03895331,0.04220707,0.06104372,-0.05347475,0.00970985,0.0504107,0.0222917,0.00585811,-0.06897737,0.022399,-0.01681852,0.02878266,0.0450314,-0.02200638,0.00955782,-0.00038542,0.08791644,0.00170687,0.00521025,-0.04929041,0.06143475,0.11971436,-0.05458073,-0.00048609,0.0429773,0.08861311,0.03322762,0.10868526,-0.05913452,0.02607006,0.0011019,0.03040403,0.02120165,-0.00801163,-0.01677283,0.01410363,0.00013959,0.04767232,-0.03411895,-0.00627878,-0.02325542,-0.05546446,-0.00863472,-0.00544571,0.04551684,-0.01353934,-0.05923652,0.04516209,0.01039269,0.03127636,0.02563936,0.03639988,0.00579544,-0.01278159,0.02219898,0.00843894,0.0038612,0.03456201,0.01180105,0.00705559,-0.08443364,-0.00237963,0.02277773,-0.0176558,0.03328235,0.01343599,0.00567058,0.01340398,0.0104766,-0.08860382,-0.02900562,-0.00225584,-0.05268098,0.07196944,-0.02411868,0.08658534,-0.04553203,-0.00255241,-0.03365523,-0.00292606,-0.09199686,0.01271755,0.02350565,-0.00246927,0.01979855,0.00177391,-0.04450286,0.03694558,0.04135689,-0.03387734,0.00356602,0.10307612,-0.03597575,-0.0533634,-0.03470111,0.0290427,0.01953664,-0.06324854,-0.04316295,0.00997966,-0.00029138,-0.06393761,0.03168651,0.0194157,-0.08136818,-0.0654947,0.02262603,0.0256206,-0.02747671,-0.06160839,-0.05462876,0.03431524,0.02547584,-0.0533645,0.01058383,-0.05520833,-0.03878191,0.05564164,-0.03942505,0.00538684,0.01776024,-0.0138484,0.00365092,-0.05189315,-0.05296409,0.00547416,0.06418553,0.00544465,0.12009652,0.0308846,0.00149649,0.03567751,-0.0801843,0.01544875,-0.01485327,-0.01894574,0.05140636,0.0338663,-0.10243105,-0.03499075,0.00341877,0.06443316,-0.04714754,-0.03400794,0.07528512,0.03096408,0.0509418,0.04735935,-0.09502631,0.00246038,-0.09372872,-0.22679043,0.01045614,-0.0052566,-0.05083361,-0.00799438,-0.01762513,-0.03450945,-0.01228255,-0.03968525,0.02571357,0.13870503,0.01341044,-0.01537468,0.02629412,-0.05068852,0.01353197,-0.00560433,-0.05143092,-0.05184463,0.01697301,-0.0199849,-0.04477645,-0.05573506,-0.1099434,0.03004516,-0.04005087,0.11441706,0.06923926,0.03875899,-0.01940529,0.04263846,0.01616758,0.01617943,-0.08298296,-0.02362299,-0.00580062,-0.02498324,-0.01615326,0.02854318,0.03532303,-0.0135117,-0.00349183,-0.02763966,-0.07965415,0.04181258,0.00436452,-0.07085449,0.02971361,-0.01200817,0.08334766,-0.02299442,0.01994431,0.08519318,0.04204493,-0.04586319,0.05286047,-0.00303702,-0.00613598,0.03479416,0.08284309,0.02121625,0.0305001,0.00325039,-0.05937141,0.04905522,0.04577435,0.04368356,-0.01290726,0.05878123,-0.01849302,0.00242902,0.08369256,0.03188305,-0.00371355,0.003919,-0.00081209,0.01835061,-0.07197597,0.02934419,-0.00984849,-0.00589497,-0.02641956,0.029181,0.03792618,-0.02282794,0.03246276,-0.0048794,-0.01568955,0.03813686,-0.0407063,-0.03588313,0.01395979,-0.07396809,-0.05463918,0.10083584,0.03385809,-0.24727961,0.00303904,0.0070424,-0.04500636,-0.02510989,-0.04241601,0.08665482,-0.02679851,-0.04243861,-0.02595485,0.03753759,0.02938663,-0.05123082,-0.04846353,0.02129772,0.04112215,0.06169407,0.02972898,0.07620713,-0.05812321,0.01607717,0.06463873,0.24909939,-0.00682175,-0.03165683,0.06348859,-0.02881888,0.02601659,0.09485259,0.01703997,-0.04024831,-0.00891014,0.14897273,0.00947525,-0.05256273,-0.03388139,0.00904852,-0.01935746,-0.00844287,-0.04668729,-0.02928461,-0.01181045,-0.01625513,0.01826352,0.09445155,-0.04978044,-0.01159655,-0.0773039,-0.0351573,0.0435806,-0.06615373,0.04610636,0.02918334,0.01201017,-0.0214192,0.00257935,-0.03806961,-0.04588341,-0.05978907,-0.05025125,0.03235759,-0.009101,0.0878413,0.03579586,0.01374695],"last_embed":{"hash":"ab4g29","tokens":239}}},"text":null,"length":0,"last_read":{"hash":"ab4g29","at":1751288817465},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])","lines":[392,414],"size":672,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"ab4g29","at":1751288817465}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05374908,-0.03187235,0.01866464,-0.02813744,0.02625495,-0.02816411,-0.07332569,-0.01348054,-0.05436169,-0.00172384,0.00817172,-0.09730978,0.01121386,0.01504823,0.02699482,-0.01793643,0.02778369,0.0518225,0.00307975,0.01432198,0.07030538,-0.04127799,0.05953994,-0.0489304,0.02277135,0.0522813,0.00778471,-0.03356286,-0.0024762,-0.19003932,-0.07273208,-0.08615396,-0.06771402,0.01563997,0.00666746,-0.02430422,-0.00569954,-0.01314181,-0.0060285,0.04702559,0.03417422,0.03034697,-0.02574976,-0.00071227,0.00330164,-0.01379692,-0.02751183,0.02725187,0.04514652,-0.05595524,-0.03889769,0.03898317,0.02285003,-0.00854402,0.01035716,0.08712496,0.05608147,0.10257828,0.05345586,0.04761714,-0.00599314,0.04338805,-0.18783018,0.01336771,0.06116872,0.03606015,-0.03218751,-0.01496582,-0.01941298,-0.01597778,0.02490891,0.04957461,0.03673666,0.04184464,0.06195606,-0.05384182,0.00971144,0.04776964,0.01975403,0.00356007,-0.0675787,0.02196118,-0.01871994,0.02643118,0.0458051,-0.02448372,0.00570753,-0.00283391,0.08649064,0.00443117,0.00410101,-0.04967691,0.05978113,0.1182157,-0.0574824,-0.00094303,0.04300783,0.09310707,0.03519223,0.10759448,-0.05641517,0.02566669,0.00124392,0.03078283,0.02295442,-0.00640527,-0.01691608,0.01786795,0.00234446,0.05185413,-0.03339985,-0.00767487,-0.02236176,-0.05594974,-0.00939852,-0.00485526,0.04482199,-0.01202126,-0.05783993,0.04685627,0.01257436,0.02833701,0.02597823,0.03706634,0.00625578,-0.01047996,0.02381565,0.00779078,0.00277604,0.0321681,0.01090345,0.00615959,-0.08138213,-0.00263048,0.02545102,-0.01829721,0.0337774,0.01508064,0.00311984,0.01352107,0.01207876,-0.08846191,-0.02945815,-0.00252057,-0.05139939,0.07169876,-0.02410953,0.08767752,-0.04672222,-0.00176798,-0.03265969,-0.00482011,-0.09301478,0.01247749,0.0258845,-0.00199714,0.01649607,0.00008238,-0.04453224,0.03721541,0.04118311,-0.03312033,0.00485001,0.10173549,-0.03874629,-0.05336342,-0.03446505,0.02875142,0.01938059,-0.06127479,-0.04285795,0.00921597,-0.00007542,-0.06735159,0.03380783,0.01853764,-0.08162735,-0.06873863,0.0202619,0.025402,-0.02735753,-0.06147708,-0.05254915,0.03543555,0.02666694,-0.05581792,0.00991205,-0.05732685,-0.04268805,0.05463491,-0.04209422,0.00487606,0.0173888,-0.01472503,0.00495477,-0.05283455,-0.05460966,0.0069206,0.06260976,0.00526789,0.122485,0.03128404,0.00462711,0.03584581,-0.08221557,0.01488232,-0.01389781,-0.0195789,0.05217754,0.03476708,-0.10286187,-0.0345658,0.00689414,0.05997884,-0.04692622,-0.0307127,0.07767198,0.03317761,0.04945922,0.04617997,-0.09370772,0.00506446,-0.094252,-0.22638088,0.01154228,-0.00631515,-0.05248217,-0.00598612,-0.01687786,-0.0345301,-0.00968947,-0.03802332,0.02178159,0.13635571,0.01384074,-0.01565768,0.02714463,-0.0491904,0.01512162,-0.00154948,-0.05059005,-0.04864338,0.01651585,-0.02192241,-0.04518311,-0.05653156,-0.11080268,0.03041718,-0.04032265,0.11304743,0.06945048,0.03934303,-0.02184226,0.04172988,0.01795543,0.01395596,-0.08332863,-0.02415481,-0.00977556,-0.02491347,-0.01813283,0.03004516,0.03422747,-0.01223713,-0.00360302,-0.02888279,-0.07877966,0.045723,0.00415613,-0.07091432,0.028944,-0.01220675,0.08529723,-0.02277383,0.02184407,0.08304958,0.041702,-0.04579055,0.05196192,-0.00269433,-0.0069732,0.03663165,0.08391272,0.02044237,0.03093765,0.00429052,-0.0606407,0.05000629,0.04686829,0.04453244,-0.01281348,0.06068968,-0.02073667,0.00307329,0.08169312,0.02934524,-0.00161232,0.00488754,0.0030163,0.01947802,-0.0671386,0.02857466,-0.00881425,-0.00273718,-0.02431944,0.03119077,0.03647807,-0.02277897,0.03017106,-0.00576947,-0.01426494,0.03743207,-0.04009633,-0.03753951,0.01307368,-0.07227115,-0.05496173,0.09879898,0.03158663,-0.24795306,0.00437096,0.0077528,-0.04613085,-0.02558174,-0.03978833,0.08586311,-0.02616859,-0.0420265,-0.02255223,0.03967801,0.0285342,-0.04977061,-0.04908582,0.02122713,0.04164515,0.05974219,0.03032574,0.07874057,-0.0598144,0.01687764,0.06648165,0.24947487,-0.00687112,-0.0355431,0.06388446,-0.02624876,0.02665671,0.09617255,0.01520948,-0.03993016,-0.00662328,0.14784035,0.00699409,-0.05223809,-0.0330169,0.00984308,-0.01599501,-0.00707396,-0.04688884,-0.02737417,-0.01315285,-0.01581051,0.01591101,0.09255883,-0.04972532,-0.0109302,-0.07390635,-0.03426818,0.04229125,-0.06693288,0.04548304,0.02878646,0.01189119,-0.02012092,0.00144828,-0.03860011,-0.04739231,-0.05879135,-0.05184754,0.03308372,-0.00755458,0.08740266,0.03461649,0.01542905],"last_embed":{"hash":"1ra3p76","tokens":236}}},"text":null,"length":0,"last_read":{"hash":"1ra3p76","at":1751288817582},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])#{1}","lines":[394,414],"size":590,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1ra3p76","at":1751288817582}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.eachMapping(callback, context, order)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0599775,-0.04114914,0.01561909,-0.06370111,-0.02322507,-0.00638664,-0.04181516,-0.02834915,-0.02892097,0.00133636,-0.0272481,-0.04311963,0.00334521,0.02655288,-0.00864635,-0.0405467,-0.05646805,0.09629424,-0.04323708,-0.03589775,0.06968252,-0.02709997,-0.01641611,-0.04889838,0.03313811,0.08420767,0.02298099,-0.0468376,0.00112014,-0.21090788,0.00429495,-0.00669932,0.00106045,0.00981563,-0.00362309,-0.02705035,-0.03769312,0.03925562,-0.01849449,0.01601086,0.0262763,0.02140339,-0.04708456,-0.04603035,0.00027681,-0.0389351,-0.04081213,0.02087441,0.02524914,-0.01607904,-0.03650628,-0.02195305,-0.00548444,-0.0118417,0.02460291,0.0643042,0.06199678,0.10143089,0.03852247,0.03981594,0.01833839,0.00431842,-0.1969123,0.03724043,0.04350384,0.0189278,-0.03108042,-0.0107803,0.02130618,0.02424556,-0.01296541,0.03079785,0.03436078,0.05225514,0.07514901,-0.09170606,-0.01836309,0.05400632,0.00156408,-0.00916817,-0.09997994,0.00183743,-0.00318776,0.04484883,0.02333228,-0.01389728,0.03017526,-0.01389279,0.0763539,0.01329,-0.00918519,-0.05829037,0.04297195,0.12466352,-0.05213254,-0.02686562,0.03398638,0.05677416,0.01927164,0.12412651,-0.05513812,0.01419295,0.01261017,0.03638611,-0.03168771,-0.03185709,-0.02659003,-0.06149321,-0.03958831,0.02443657,-0.04484162,-0.02355962,-0.06427342,-0.06608976,-0.02732744,-0.02708268,0.03611321,-0.00043576,-0.03984494,0.051362,-0.00092332,0.08874392,0.01324042,0.00503838,0.05896576,0.0155431,0.04742685,0.03623106,0.02845782,0.05046443,0.01784616,0.03865381,-0.08305857,0.0133419,0.02746237,-0.02482732,0.00541892,-0.02916794,0.03802389,0.01176292,0.01159721,-0.06245664,-0.02766806,-0.0419357,-0.01651695,0.12919101,-0.02640681,0.07561596,-0.01264508,0.00710628,-0.01757815,0.03327122,-0.04195836,0.00048955,-0.0017932,0.01600999,-0.00344741,0.00388597,-0.03763784,0.03392429,-0.01475387,-0.00791892,0.00739439,0.1238211,0.00069237,-0.08105937,-0.02019485,0.08017486,-0.00141122,-0.05231871,-0.04709256,0.03116833,-0.00947899,-0.03743583,0.05366744,0.0042789,-0.0802706,-0.09145454,0.05862295,0.00894113,0.00077749,-0.02893684,-0.02003207,0.01498856,-0.00878221,-0.02628785,-0.01059287,-0.00314519,-0.00960654,0.01059042,0.02567901,0.03523683,0.06353156,-0.00027136,-0.02404173,-0.06091665,-0.06995203,-0.01701728,0.06495833,-0.0299464,0.11210373,0.01213631,-0.0084644,0.03855295,-0.04979563,0.01356231,-0.02598113,0.01539123,0.0112848,0.00873984,-0.1295557,-0.00760819,0.02588068,0.06649002,-0.0568478,-0.06081305,0.01869037,0.0218091,0.01152604,0.08476385,-0.03010556,-0.012993,-0.07840572,-0.20854171,0.00418156,0.04138801,-0.02908136,-0.02693378,-0.03833082,-0.02295063,-0.02908492,-0.06642642,0.0059885,0.1458203,0.01540137,-0.00071227,0.00535462,-0.07403236,0.02503693,0.00495592,-0.07270483,-0.04800141,-0.00230915,0.00439837,0.02105444,-0.03195483,-0.11969409,0.0360688,-0.03782302,0.14726476,0.03125311,0.03137511,0.01839232,0.04784724,-0.04749043,0.01414601,-0.03386404,-0.00707836,-0.0283722,-0.01481995,0.02644623,0.01718648,0.02969927,-0.01820039,0.0014745,-0.04165861,-0.07885977,0.03351413,-0.00644256,-0.0431708,0.02404624,0.04506574,0.03915812,-0.00894468,-0.01241232,0.06330197,0.00072543,-0.02019521,0.04876999,0.01360114,-0.02846447,-0.00159267,0.05432767,0.04892854,0.02984909,-0.02766728,-0.0441252,0.05010362,0.01862346,0.03294868,0.00537811,0.04965147,-0.0416679,-0.02614887,0.0505737,0.03955159,0.01345319,0.03778023,-0.01740515,-0.01169255,-0.05327924,0.01897308,0.01584085,0.00568356,-0.0589279,0.01213378,0.04139344,0.00144602,0.01305657,0.02032085,0.05002062,0.02407935,-0.04808948,-0.02465188,0.04476257,-0.06281981,-0.01017704,0.07949038,0.02122625,-0.26546675,0.03695456,-0.03964341,0.01591529,0.014325,-0.01161834,0.03923197,0.01172642,-0.00029113,-0.02321937,-0.0104289,0.08741295,-0.02397181,-0.05819941,-0.0011728,0.04815434,0.07751406,-0.03059473,0.09374136,-0.05860143,0.01628541,0.02822827,0.26513585,0.01634443,0.05065514,0.04587279,-0.0529605,0.0301875,0.06092154,0.05084711,-0.0015654,-0.01504489,0.13034442,0.01161476,-0.04186935,-0.00061131,0.01471161,0.02546646,0.00305312,-0.04794461,-0.0515626,-0.00388805,-0.01658578,-0.01690179,0.09246096,-0.05336506,-0.0607325,-0.07981121,-0.03016905,0.06186539,-0.07607669,0.03608453,-0.02452499,-0.02578399,-0.02352279,0.03254138,0.03053538,0.00179078,-0.06682623,0.01817985,0.02530606,-0.05653955,0.07549237,0.02743905,0.01032498],"last_embed":{"hash":"9wsdh2","tokens":379}}},"text":null,"length":0,"last_read":{"hash":"9wsdh2","at":1751288817760},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.eachMapping(callback, context, order)","lines":[415,449],"size":1146,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"9wsdh2","at":1751288817760}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.eachMapping(callback, context, order)#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08503796,-0.06367347,0.02384266,-0.07756466,0.00165493,-0.00860953,-0.0525101,-0.0446043,-0.03241954,0.03096483,-0.00200339,-0.05246205,0.00289731,0.03591549,-0.01811325,-0.04966377,-0.01819145,0.08929148,-0.04597172,-0.01544007,0.06515697,-0.04096613,-0.01782222,-0.06149044,0.02930938,0.08216849,-0.0000827,-0.04457842,0.00782206,-0.2212754,-0.00836252,-0.02580994,-0.00152614,0.0031141,-0.00703679,-0.06146742,-0.03877822,0.05696202,-0.03323471,0.00581584,0.01109439,0.01659239,-0.06962705,-0.02732518,-0.00067817,-0.05408377,-0.03406763,0.03486327,0.03103707,-0.01847912,-0.05239353,-0.02349314,-0.00815274,-0.01187326,0.02810131,0.05121665,0.0555871,0.09431404,0.0488981,0.02288396,0.01389256,0.03006443,-0.18959533,0.04252297,0.05276193,0.03328044,-0.05074605,-0.01250175,0.01137534,0.04226143,-0.02123992,0.0342162,0.03896748,0.05950357,0.07332759,-0.06812448,-0.00406631,0.03470356,-0.00480441,0.00580365,-0.10542823,-0.00818625,-0.00528334,0.04114768,0.0169112,0.00976425,0.02075521,-0.01115249,0.08406806,0.01365038,0.00773035,-0.05929967,0.03547617,0.11078206,-0.04607897,-0.01630824,0.01544779,0.04220952,0.03417305,0.13251708,-0.04400092,0.01653879,-0.00269528,0.04547389,-0.04342347,-0.03218442,-0.01932663,-0.05974972,-0.02822707,0.00301702,-0.03866811,-0.01537295,-0.04253864,-0.06701509,-0.0000458,-0.01948506,0.03177917,0.02048869,-0.03206603,0.05165058,0.00014424,0.07523118,0.00345792,0.00652793,0.04990872,0.00663177,0.01654284,0.01319452,0.01909055,0.05162483,0.00186439,0.03977351,-0.07923707,0.00678314,0.02017427,-0.00918046,0.00785214,-0.02367523,0.02837003,0.00873636,0.01021616,-0.06500839,-0.02973089,-0.0404424,-0.0109512,0.13559629,-0.04971257,0.06140123,-0.01593475,0.00794187,-0.03248719,0.03166701,-0.05395836,-0.00871386,-0.00584008,0.0066776,0.01401467,-0.00024452,-0.05427435,0.02682406,-0.00994221,-0.00081065,0.02005834,0.12774962,-0.00843781,-0.06763656,-0.01884045,0.07398102,0.0037844,-0.04659941,-0.04324474,0.02714229,-0.0010357,-0.02375655,0.04668044,0.00977963,-0.05028502,-0.09280448,0.0320846,0.01223541,-0.0054668,-0.04022924,-0.01237873,0.02362366,0.00538636,-0.0251069,-0.0087776,0.00130493,-0.01213739,0.00971331,0.01455858,0.04295437,0.05439744,0.00522451,-0.00949527,-0.04241855,-0.07157395,-0.00897491,0.08226083,-0.04636238,0.1188397,0.03655239,-0.00692272,0.05140384,-0.05423712,0.01698496,-0.00758757,0.01182101,0.02888657,0.00077039,-0.13076161,-0.03162695,0.02669743,0.06578384,-0.05863036,-0.04980132,0.03287111,0.04664728,0.03021656,0.09432449,-0.02761081,0.00393193,-0.07360802,-0.21178955,0.00026839,0.02860412,-0.0345017,-0.03182783,-0.04125157,-0.03017744,-0.03713181,-0.05899641,0.00708366,0.13365273,0.01275098,-0.0063073,-0.00193241,-0.08504562,0.0135062,-0.01563308,-0.06835886,-0.03206025,0.00292762,0.01672544,0.02323093,-0.0412807,-0.11390765,0.02857736,-0.04309454,0.15014349,0.03931088,0.0390896,0.02632035,0.04076932,-0.02853655,0.03708254,-0.05140039,0.0055997,-0.00846216,-0.03476688,0.01423549,0.03346953,0.03513525,-0.00807864,-0.00607399,-0.04080321,-0.08074883,0.04068611,-0.01162784,-0.04527878,0.02902421,0.04146471,0.04421667,-0.01183838,-0.01179895,0.05912076,0.00880437,-0.0367556,0.05041597,-0.00634865,-0.03265255,0.00464609,0.04283042,0.06793413,0.03597743,-0.0377955,-0.05732024,0.04589893,0.01781532,0.02239292,0.00533044,0.05013695,-0.02328673,-0.01594054,0.04059554,0.02158746,0.02170802,0.02685512,0.00360728,-0.02759277,-0.02330445,0.01307494,0.0037969,0.01242903,-0.03801075,0.02554134,0.02853181,-0.00955967,0.03301555,0.02906273,0.04416349,0.01534919,-0.02845235,-0.0165472,0.05212977,-0.07752168,-0.0033305,0.08308725,0.0195122,-0.25970379,0.0412416,-0.04035988,0.00780338,0.01045094,-0.01381814,0.04349146,0.01383049,0.00215871,-0.01566612,-0.02518802,0.07141475,-0.00796074,-0.0638522,-0.01710902,0.0368902,0.06470674,-0.01210976,0.10523992,-0.08286727,0.02636744,0.0347461,0.27391165,0.03131175,0.0115072,0.03482168,-0.05448132,0.03442443,0.05199573,0.05702391,0.00255378,-0.0040059,0.12780255,0.0224053,-0.02456385,0.02853006,0.0273112,0.01786959,-0.00708505,-0.02607859,-0.06140074,-0.00239458,-0.01969109,-0.02032868,0.07991522,-0.0769479,-0.07136527,-0.07268387,-0.04519318,0.05493665,-0.06464857,0.02647712,-0.01875197,-0.010676,-0.00695834,0.03656662,0.03090047,-0.00657464,-0.05982801,0.010226,0.03122066,-0.05200305,0.0847213,0.02741435,-0.00045472],"last_embed":{"hash":"1k9bcnq","tokens":260}}},"text":null,"length":0,"last_read":{"hash":"1k9bcnq","at":1751288817958},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapConsumer#SourceMapConsumer.prototype.eachMapping(callback, context, order)#{7}","lines":[428,449],"size":615,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1k9bcnq","at":1751288817958}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06821747,-0.0490999,0.04213726,-0.06689006,0.00440564,-0.04881403,-0.05224964,-0.03953061,-0.04206801,0.03971786,0.01303894,-0.078771,0.00701437,-0.00152693,0.01518909,-0.04616769,-0.0503023,0.07119799,-0.00884211,-0.02924353,0.07848062,-0.00478779,0.01812642,-0.03840547,0.00349148,0.11059532,0.03248208,-0.02092961,-0.00327274,-0.20589642,-0.05040768,-0.04745899,-0.04638457,0.01942839,-0.04351401,-0.03645876,-0.03187105,0.0371491,-0.02424443,0.01679411,0.06021458,0.04895996,-0.04124617,-0.01442615,-0.01314155,-0.04019202,-0.02792722,0.02118037,0.00233925,-0.03199417,-0.02609176,0.00088759,0.02115077,-0.00559452,0.02175123,0.04547073,0.07756515,0.09599349,0.02760456,-0.00341269,0.01090799,0.04447263,-0.16586681,0.02854088,0.06258823,0.03814972,-0.03410669,-0.0202271,-0.00597108,-0.00046172,0.01698528,0.05039224,0.01879355,0.01812169,0.05660855,-0.06000848,-0.01262783,0.05595204,0.01831701,0.01534059,-0.05263863,0.01051064,0.00457474,0.03583148,0.02239538,-0.02912457,0.04459597,-0.00927308,0.08958378,0.03020158,-0.00449692,-0.07084356,0.0509363,0.11826381,-0.012054,-0.00312416,0.03129623,0.04904683,-0.03725244,0.11903037,-0.06924592,0.00848761,0.00092857,0.02319854,-0.00125091,-0.05057733,-0.03965061,-0.02150097,-0.02826887,0.03891036,-0.01491224,0.00358639,-0.07807257,-0.03625004,-0.02529023,-0.0176362,0.02474206,0.00226743,-0.07140204,0.03693228,-0.01510114,0.06167329,0.04005787,0.06352463,0.07379254,-0.01313683,0.05912487,0.04214047,0.02179695,0.03778435,-0.00215249,0.00995218,-0.08049302,0.01496007,0.03912944,-0.00231781,0.00986427,-0.00949222,0.0202452,0.03861509,0.01742335,-0.06683747,-0.03090201,-0.0339448,-0.03733212,0.09825757,-0.02666299,0.10181918,-0.01039,-0.01473067,-0.02402791,0.02805601,-0.10296556,-0.0156579,0.03136046,0.01259166,0.00403625,0.00983508,-0.03472044,0.00462066,0.00259569,0.00823088,0.00509532,0.0692684,-0.03345127,-0.07751903,-0.01556788,0.05447498,0.0222282,-0.08280123,-0.04501034,0.01146432,-0.00112065,-0.06019494,0.0273234,0.01139186,-0.04216778,-0.03473376,0.00160874,-0.01993358,-0.00128993,-0.03935522,-0.05509704,0.0281221,-0.00785124,-0.05122245,0.02453188,-0.03816809,-0.03818437,0.06901065,-0.00036615,0.04167791,0.01389991,-0.01990988,-0.00821263,-0.03254957,-0.06322557,0.00225605,0.06879283,-0.05086412,0.10296012,0.04466875,0.01699558,0.0250578,-0.05398608,0.03257834,-0.01122702,0.0043006,0.0080062,0.02948028,-0.09450282,-0.02382504,0.0394675,0.06460575,-0.05773541,-0.05208109,0.01602903,0.03483661,0.02778592,0.06897664,-0.06703032,0.02848839,-0.09060925,-0.2074929,-0.00526887,0.00834216,-0.01975742,-0.02724893,-0.06167868,-0.0375053,-0.02764878,-0.0715132,-0.01209489,0.13358168,-0.04338684,-0.00970767,0.03986576,-0.02089854,0.00620481,-0.00018486,-0.06299952,-0.05364692,0.00640498,0.00254127,0.03537187,-0.00215481,-0.09717409,0.03558232,-0.08057,0.12535286,0.04735058,0.04773866,-0.00475416,0.06322397,-0.00547187,-0.00103111,-0.09650018,-0.00626487,-0.00551646,0.0005015,0.01517486,0.05884612,0.04373982,0.00145397,0.00110928,-0.03276375,-0.08670804,0.03230777,-0.03132913,-0.03707274,0.03787211,0.01766016,0.05528348,0.01334437,-0.04205126,0.12447733,0.036608,0.00946462,0.00187008,-0.02183191,0.00840433,0.01787453,0.05400766,0.02828365,-0.0111981,-0.01317863,-0.05142555,0.06258572,0.0277453,0.02089153,-0.01227296,0.05253801,-0.02620246,-0.01916229,0.11466375,0.01948005,0.00028005,0.05575154,-0.02367176,-0.02823508,-0.04516537,0.02503552,0.02130963,-0.04646005,-0.01943951,-0.01586348,0.03931849,-0.03217943,0.0200072,-0.00925371,0.03961589,0.02049549,-0.05712648,-0.01686228,0.01357078,-0.06661838,-0.05188563,0.08856737,0.0108706,-0.2587972,0.00744854,0.01750432,-0.02284181,-0.04452951,-0.03220836,0.05893191,-0.00931089,0.0006362,-0.03486801,-0.03003388,0.07281396,-0.01695559,-0.04005531,0.02220255,0.07348058,0.08382355,-0.01593832,0.10044265,-0.0688333,0.03937113,0.03506373,0.25540125,0.01228105,0.04263871,0.06371216,-0.03990272,0.02899014,0.04516051,0.00193516,-0.06302509,0.01938443,0.13938548,0.00225208,-0.00652838,0.01270717,0.04544091,-0.03352049,-0.01025084,-0.03972096,-0.00753978,0.0135025,-0.02384677,0.02511704,0.08442809,-0.05429495,-0.02582113,-0.09808981,-0.01835773,0.02971871,-0.03690222,0.06427335,0.02024915,-0.01098755,-0.03568099,0.03631549,0.0050835,-0.01877702,-0.03510474,-0.04584673,0.02900494,-0.05871334,0.09359247,0.0465943,0.00750601],"last_embed":{"hash":"ssr8o","tokens":458}}},"text":null,"length":0,"last_read":{"hash":"ssr8o","at":1751288818071},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator","lines":[450,560],"size":3751,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"ssr8o","at":1751288818071}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#new SourceMapGenerator([startOfSourceMap])": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06328934,-0.0440488,0.04643974,-0.0792319,0.03377112,-0.03274401,-0.04908712,-0.02638113,-0.04007508,0.02830377,0.00416086,-0.06956452,0.02844896,0.01171668,0.00694426,-0.03104711,-0.03536467,0.07698206,-0.00570111,0.00324516,0.08943617,0.01593163,0.04177038,-0.03597916,0.01067915,0.10463339,0.03858724,-0.02521429,-0.02509345,-0.21439387,-0.05378771,-0.04624072,-0.06834594,0.01848987,-0.02760802,-0.02804722,-0.02868143,0.02297635,-0.01737983,0.02606146,0.05995836,0.04077335,-0.02112476,-0.02419019,-0.00799749,-0.03697734,-0.03253349,0.01807546,0.00212793,-0.0249386,-0.03769017,-0.01553244,0.01614629,-0.01746882,0.02201786,0.05039366,0.05826165,0.08197534,0.02216711,0.01628905,0.02610547,0.05183028,-0.16862381,0.02926023,0.06476402,0.04811659,-0.02835879,-0.01971839,-0.02407266,-0.01035806,0.0116726,0.05975422,0.00015169,0.04506057,0.05918733,-0.06050287,-0.00543313,0.04459415,0.03519444,-0.00890325,-0.04520956,0.00635709,0.01159244,0.02131319,0.02221672,-0.02896488,0.01875052,0.00674126,0.07816517,0.04115909,0.01274933,-0.09835328,0.05631367,0.10709088,-0.01487051,0.00681531,0.02154834,0.04501703,-0.03696336,0.12388048,-0.08626654,0.00628817,-0.01698134,0.02840345,-0.0147832,-0.05271745,-0.03348874,-0.01776853,-0.00236845,0.05028406,-0.00545646,-0.02287436,-0.07773232,-0.02391993,-0.02907679,-0.00449395,0.03898825,0.0161378,-0.09993698,0.02417291,-0.00248237,0.04346128,0.04707128,0.04897836,0.05835553,-0.02603349,0.06550319,0.05530472,0.03156266,0.05381473,-0.01317326,0.00383293,-0.09400074,-0.00623649,0.02110694,0.0030342,-0.00255267,-0.00111612,0.01646545,0.04463167,0.01707206,-0.0859087,-0.02568233,-0.01402199,-0.03438743,0.08686005,-0.04436578,0.08974977,0.00828279,-0.03258429,-0.02576158,0.03857449,-0.11497864,0.00297739,0.0035955,0.00839364,-0.00954923,0.01837485,-0.04101409,0.00592916,-0.01199931,0.00963304,-0.00663644,0.07660994,-0.03886424,-0.07019672,-0.01971032,0.03580365,0.03267455,-0.06809391,-0.03344949,-0.01137979,0.0118637,-0.05750322,0.02164925,-0.01198241,-0.03266991,-0.03047072,-0.03685752,-0.01696439,-0.01812185,-0.03573584,-0.05729487,0.03512885,-0.00180386,-0.04959914,0.02167103,-0.03091684,-0.0277553,0.04972456,-0.0123378,-0.00021111,-0.01227118,-0.05588734,-0.01290964,-0.01485008,-0.05806424,-0.00430606,0.0533071,-0.04940024,0.11887927,0.0493206,0.03007405,0.02803353,-0.05087782,0.06180177,-0.00935123,0.02004751,0.00061425,0.04348481,-0.1055579,-0.04437027,0.03343303,0.05236274,-0.0496001,-0.0459805,0.01620578,0.04500047,0.0117302,0.07408296,-0.06498405,0.03318682,-0.08056934,-0.22492176,-0.00169745,-0.00053562,-0.00659139,-0.00028412,-0.0684998,-0.03215621,-0.00240405,-0.07229642,0.01646783,0.11802641,-0.0359085,-0.02455662,0.03955377,-0.01866748,-0.00522228,-0.02309795,-0.06990992,-0.04950676,0.00995627,-0.01654438,0.00071642,-0.0031766,-0.0893259,0.02994787,-0.07097487,0.12928037,0.05060163,0.03597255,-0.02237109,0.0641736,-0.00711543,0.02639544,-0.09983367,0.01484959,-0.00514338,-0.00724215,0.00769458,0.04982094,0.03228699,0.01147637,-0.0156578,-0.02286777,-0.08258794,0.03482662,-0.03641509,-0.03522496,0.04476242,0.02600184,0.05888459,0.01868419,-0.02375897,0.11577658,0.05232917,0.01313818,0.00788666,-0.0238354,-0.00422934,0.01468741,0.04897684,0.03628264,-0.00138544,-0.00798273,-0.0450415,0.05961126,0.02505382,0.0071946,-0.01621882,0.06560589,0.00195363,-0.01191837,0.1173693,0.01537675,-0.00795916,0.03263277,-0.01211586,-0.03428997,-0.03932647,0.03268098,0.01049275,-0.01598186,-0.00889454,0.00603807,0.02584443,-0.01374067,0.02416744,-0.00217305,0.02249824,0.03633772,-0.06176566,-0.02476829,0.00248948,-0.06039833,-0.03860136,0.11345249,-0.00441764,-0.24628679,-0.00941268,0.02625251,-0.00583104,-0.03222326,-0.03164922,0.06831512,-0.02946413,-0.02608413,-0.01705433,-0.00144771,0.0726711,-0.01067035,-0.04875322,0.01113027,0.08028566,0.0633184,0.00564236,0.12773316,-0.06914005,0.05344315,0.03910156,0.25641143,0.0138589,0.02256746,0.07667985,-0.04527717,0.04375274,0.03903496,0.00742589,-0.05895902,0.02799367,0.13727202,-0.00664893,0.00294754,-0.00634514,0.03476517,-0.02671522,0.00945524,-0.0510762,-0.00722596,-0.00497013,-0.02446708,0.02432485,0.06440597,-0.07046948,-0.0134508,-0.08683048,-0.00322368,0.0227991,-0.02475371,0.05214025,0.00264399,0.00164125,-0.02323466,0.04210511,0.01259201,-0.02656724,-0.04218917,-0.05281389,0.03191016,-0.05020587,0.09219151,0.04723576,-0.00974053],"last_embed":{"hash":"wena3c","tokens":253}}},"text":null,"length":0,"last_read":{"hash":"wena3c","at":1751288818311},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#new SourceMapGenerator([startOfSourceMap])","lines":[455,478],"size":791,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"wena3c","at":1751288818311}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.fromSourceMap(sourceMapConsumer, sourceMapGeneratorOptions)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08260953,-0.0452297,0.04730913,-0.06939455,-0.01300873,-0.0479966,-0.05324748,-0.0183471,-0.0293817,0.03536386,0.02838172,-0.06545781,-0.0105831,0.01229334,0.040918,-0.04195992,-0.05430562,0.06036544,-0.01285657,-0.02068731,0.0833049,-0.01234782,-0.00977965,-0.04538478,0.0014043,0.10362317,0.0247612,-0.00955215,0.03508314,-0.17959839,-0.02800076,-0.04583863,-0.02927354,0.00872708,-0.04294839,-0.03216836,-0.02593626,0.01572183,-0.04959583,0.03592546,0.04328507,0.04487249,-0.05421706,-0.02971514,-0.00307541,-0.04893248,-0.01180391,0.02459976,-0.00262222,-0.0572035,-0.01709343,0.00771216,0.01697964,-0.02042388,0.03330424,0.04266867,0.08041971,0.07474867,0.03446489,0.00846354,-0.01901064,0.04905242,-0.17873621,0.02199604,0.05681543,0.03973685,-0.02605196,-0.01028086,0.02683858,0.01333373,0.01662832,0.04191499,0.02605179,0.00781519,0.0269111,-0.03524163,-0.02449547,0.04609621,0.01118798,0.01169755,-0.05893884,0.01934751,-0.00853349,0.03923322,0.00848299,-0.01856309,0.03529079,0.00209857,0.07418028,0.00753272,-0.0274921,-0.05325986,0.0373685,0.11205741,-0.02127693,0.01637566,0.0285487,0.04300117,-0.02139709,0.1292875,-0.06261538,0.00580239,0.00086022,0.00780718,0.00784063,-0.03980155,-0.05580226,-0.02585493,-0.05086618,0.01425102,-0.01502046,0.01183089,-0.07320167,-0.05954874,-0.03238851,-0.02485632,0.00070814,-0.00597833,-0.05428725,0.05849889,-0.01393151,0.07161706,0.01057507,0.06057672,0.07683062,-0.0069305,0.03765345,0.00931326,0.00552459,0.03990965,0.01561773,0.00976158,-0.0688633,0.02587865,0.08549093,-0.02796098,0.03350262,-0.01701981,0.0277805,0.01674991,0.02982584,-0.0353613,-0.00984495,-0.0679432,-0.01614381,0.09452246,-0.00817915,0.09995048,-0.02286151,-0.00140987,-0.02606753,0.01840058,-0.08225051,-0.02776767,0.05274013,0.01393511,-0.01041049,0.02853196,-0.03047436,0.01239977,0.01880592,0.00139272,0.03083952,0.09155195,-0.02010192,-0.08694933,-0.0177738,0.04725143,0.03089877,-0.08066142,-0.0598672,0.04733291,-0.00875122,-0.0382615,0.04782548,0.04726604,-0.05215827,-0.05269057,0.02809264,-0.02221032,0.01342799,-0.04139176,-0.03397296,0.01805941,-0.02056651,-0.05335755,0.02140452,-0.05742569,-0.03832417,0.0701206,0.00828017,0.05984719,0.02556295,0.00283288,0.0029512,-0.05159497,-0.07306156,0.02203875,0.06617535,-0.03594241,0.07845656,0.04695987,-0.00759157,0.0595338,-0.06638914,0.01256326,-0.01106422,-0.02024937,0.03161599,0.03318492,-0.07727229,-0.00755628,0.06359808,0.08008669,-0.07251735,-0.04326286,0.01061398,0.04336669,0.03072157,0.04710009,-0.06920077,0.02442757,-0.09673516,-0.20316014,0.02105883,0.00372144,-0.03209172,-0.04746095,-0.0293062,-0.02928678,-0.04968085,-0.04376422,-0.00275389,0.16585803,-0.03505314,0.01937247,0.01383127,-0.00708946,0.01826838,-0.01010563,-0.06242824,-0.04589259,0.00405872,0.01927922,0.02924366,-0.01865829,-0.09377033,0.0285307,-0.05669222,0.12960209,0.03968821,0.04407145,-0.00491473,0.07124617,-0.0233891,-0.01461058,-0.08404926,-0.0159518,0.00587971,0.01361471,0.00339263,0.05141936,0.03261376,-0.01793445,0.02598067,-0.02922786,-0.0848702,0.04504173,-0.03941017,-0.04324563,0.04291813,0.00361846,0.04441918,-0.00189936,-0.02508233,0.10668558,0.00409769,-0.00526835,-0.00950434,-0.01687742,0.00350748,-0.00480552,0.06395703,0.01615535,0.00308136,-0.02575648,-0.05304218,0.06179245,0.02175912,0.04230655,-0.01317885,0.02648521,-0.04641542,-0.02636431,0.07924744,0.0311553,0.01540362,0.06658029,-0.02481134,-0.0347832,-0.0619848,0.0192566,0.01131747,-0.03337861,-0.04284202,-0.02193913,0.04560313,-0.02258017,0.00199238,-0.0315673,0.03114562,0.01914927,-0.04649455,-0.02668255,0.00611638,-0.07604021,-0.03594502,0.07134785,0.02961435,-0.26813158,0.02427909,0.0231528,-0.03814045,-0.04722424,-0.04337819,0.05929421,-0.0087586,-0.00109186,-0.01960986,-0.03245242,0.05386701,-0.04385389,-0.00097056,0.04614045,0.07316925,0.10510149,-0.03355699,0.05710839,-0.06922515,0.01870165,0.03775173,0.25936446,0.02455336,0.04035896,0.05708267,-0.02266673,0.03809167,0.04550233,0.01805761,-0.03140236,0.00639768,0.13837747,-0.01621355,-0.02298571,0.02896826,0.04317508,-0.02788311,-0.01351435,-0.01395847,-0.03056573,0.01796871,-0.02361943,0.02169897,0.10278025,-0.05686174,-0.06501435,-0.11962491,-0.02527868,0.04945556,-0.07176825,0.05422689,0.03982086,-0.02475619,-0.02410881,0.04694473,-0.01567381,-0.01809686,-0.03204651,-0.03760681,0.0202674,-0.05086865,0.07982753,0.0444971,0.0056928],"last_embed":{"hash":"1xk9tds","tokens":175}}},"text":null,"length":0,"last_read":{"hash":"1xk9tds","at":1751288818430},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.fromSourceMap(sourceMapConsumer, sourceMapGeneratorOptions)","lines":[479,492],"size":445,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1xk9tds","at":1751288818430}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.fromSourceMap(sourceMapConsumer, sourceMapGeneratorOptions)#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05801809,-0.03609521,0.05009257,-0.0719545,-0.00399474,-0.05048652,-0.03865334,-0.01683083,-0.03012319,0.02388882,0.01228915,-0.06351247,-0.01039347,0.01312491,0.03101447,-0.03034556,-0.05151956,0.079192,-0.0148446,-0.0289492,0.0917693,-0.00046121,0.0026385,-0.0534065,-0.00080112,0.08250278,0.02599786,-0.02617867,0.00833467,-0.18190289,-0.02665226,-0.06113487,-0.03899514,0.0110431,-0.05319944,-0.01758672,-0.03003485,0.00161097,-0.04850455,0.03605396,0.05259913,0.05174194,-0.04524086,-0.03585514,-0.01335149,-0.04891021,-0.01038076,0.01958855,-0.00601361,-0.06003901,-0.01540898,0.02780393,0.02314989,-0.02244331,0.02459064,0.06071591,0.08094276,0.06882708,0.04326824,0.02542824,-0.03867014,0.05752971,-0.19100617,0.02596525,0.06342085,0.05110558,-0.02058229,-0.0130549,0.00840108,0.01418824,0.02731369,0.04113498,0.01565349,0.0015327,0.03755491,-0.02907681,-0.02039153,0.02799234,0.0129843,-0.00770084,-0.05542143,0.01927099,-0.00951481,0.03357949,0.02001845,-0.01192418,0.03428873,0.00677385,0.06762494,0.0106695,-0.03064596,-0.0555289,0.03276283,0.11495238,-0.01837707,0.02484717,0.04426553,0.03598438,-0.02285484,0.12625304,-0.0680221,-0.00595932,-0.00667672,0.00565909,-0.01467167,-0.04724216,-0.06630501,-0.03077926,-0.03658605,0.01665276,-0.01146582,0.01005318,-0.07549483,-0.03461063,-0.03977526,0.00144555,-0.00718393,0.00053075,-0.06851619,0.04160775,-0.01198095,0.07401257,0.02647648,0.0646555,0.07305543,-0.00674767,0.0581649,0.01863131,0.01141588,0.04505803,0.00849404,0.00707158,-0.0634028,0.01552516,0.08407751,-0.03788116,0.03694664,0.01048495,0.02759512,0.02566969,0.01704654,-0.04446674,-0.00334999,-0.06656303,-0.01407877,0.0953012,-0.00692656,0.11158736,-0.01925225,-0.0156768,-0.01614933,0.03214676,-0.07628504,-0.01452564,0.04114301,0.0225181,-0.01833013,0.04960452,-0.03654119,0.01539176,0.01856236,0.0071778,0.02914217,0.09757645,-0.01306019,-0.07469937,-0.01732311,0.0423279,0.02873376,-0.07590572,-0.06106385,0.05167687,-0.01093515,-0.04210692,0.04438246,0.05179243,-0.05143439,-0.04688234,0.01348181,-0.01525858,-0.00257989,-0.04908446,-0.03004673,0.02099198,-0.0120878,-0.04583933,0.03209563,-0.057483,-0.0215259,0.06710869,-0.00003369,0.02626608,0.01868335,-0.01093621,0.00192656,-0.04660932,-0.08963167,0.0145426,0.07068875,-0.03546109,0.07917392,0.05257404,-0.00962631,0.05703486,-0.0628208,0.02475716,-0.02831233,-0.01840109,0.02469842,0.03514753,-0.07645373,-0.00903464,0.05624628,0.085019,-0.07009976,-0.03939199,0.01544848,0.04429806,0.0215033,0.04903226,-0.06264821,0.02933699,-0.10581154,-0.21062681,0.03300949,-0.00970121,-0.02293815,-0.04187791,-0.04956024,-0.02806466,-0.06064691,-0.05180835,0.00155879,0.15404186,-0.03754755,0.02207162,0.011567,-0.00438409,0.02266009,-0.02285983,-0.07081981,-0.05640626,0.00843098,0.01571622,0.00198855,-0.0241671,-0.09261276,0.03688142,-0.04958888,0.13959008,0.02663305,0.05286957,-0.00817997,0.07626311,-0.00576175,-0.00170704,-0.07542515,-0.00020514,0.0025973,0.00464413,-0.01904837,0.06288946,0.03925068,-0.02366116,0.02688218,-0.02451078,-0.10013965,0.05152323,-0.04268787,-0.03415789,0.03970125,0.00699196,0.04848089,0.0055627,-0.02076687,0.10824898,0.00019202,0.00236611,-0.00892109,-0.01909143,0.0076597,-0.01347613,0.06258979,0.01249855,-0.00027908,-0.02097308,-0.04798872,0.04918274,0.01164924,0.03910774,-0.00064244,0.03783613,-0.03193521,-0.03107896,0.07260867,-0.00405516,0.00716274,0.06231521,-0.00438641,-0.04953264,-0.05140935,0.0260429,0.02605349,-0.01444324,-0.03081004,-0.02191568,0.03004441,-0.00860395,0.00432149,-0.04429901,0.01109158,0.02414896,-0.03459744,-0.03082146,0.00288032,-0.07596191,-0.04715897,0.06974643,0.03215451,-0.26096368,0.02103709,0.01709605,-0.03402108,-0.0543042,-0.05861709,0.05521635,-0.03300953,-0.01066949,-0.00149359,-0.00515132,0.05224501,-0.03595167,-0.00666023,0.05204541,0.06696522,0.10660706,-0.03228328,0.07742675,-0.06258591,0.03325666,0.05446151,0.26436794,0.03498743,0.03619999,0.04637126,-0.03508902,0.03512784,0.04619622,0.01506506,-0.03785632,0.0145756,0.13658026,-0.02241428,-0.01895742,0.01651365,0.03950837,-0.01559113,-0.00611936,-0.01934155,-0.02440252,0.01746978,-0.02711729,0.01002641,0.08927089,-0.05986601,-0.04817838,-0.1115789,-0.0213918,0.04750394,-0.06700749,0.05642251,0.03059042,-0.03017085,-0.0148723,0.03959299,-0.01756475,-0.03780949,-0.03827521,-0.03874934,0.02454532,-0.03664569,0.06683865,0.02288766,0.01625979],"last_embed":{"hash":"p0i2rr","tokens":135}}},"text":null,"length":0,"last_read":{"hash":"p0i2rr","at":1751288818510},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.fromSourceMap(sourceMapConsumer, sourceMapGeneratorOptions)#{3}","lines":[485,492],"size":239,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"p0i2rr","at":1751288818510}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.addMapping(mapping)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06263039,-0.04945163,0.06980333,-0.08514374,-0.02401026,-0.01586352,-0.08508757,-0.01919676,-0.02618738,0.06688584,-0.00971731,-0.09037888,0.00610461,0.01719822,0.01706593,-0.06151767,-0.051429,0.07724885,-0.02572539,-0.028383,0.07347879,0.00103718,0.00511324,-0.03904632,0.00295458,0.09299193,0.02103775,-0.02709264,0.03348376,-0.20258784,-0.02602857,-0.01420375,-0.02290043,0.01337368,-0.02837919,-0.03553122,-0.01629206,0.03816737,-0.02731572,-0.00211939,0.05482163,0.02777212,-0.05184759,0.0005109,-0.0050849,-0.04479384,-0.00832787,0.02098783,0.01670425,-0.02701049,-0.02851475,-0.00462907,0.01720144,0.00545208,0.0196387,0.06152393,0.05813704,0.10151317,0.01502588,0.01081969,0.0236716,0.0364687,-0.18381938,0.03646603,0.05947478,0.04831353,-0.03135407,0.00046962,0.02806139,0.01966483,-0.02484337,0.05243849,0.04672145,0.03751785,0.05638987,-0.05214164,-0.0352519,0.05563407,0.00877732,0.01447728,-0.0970752,0.0045033,0.00870724,0.05923092,0.00992131,-0.03568237,0.03837826,-0.02586369,0.08026738,0.03157741,0.01094394,-0.06295504,0.01874662,0.10302757,-0.05369001,-0.00583285,0.01607644,0.03223434,-0.00325859,0.14134429,-0.05988313,0.00961052,0.02355606,0.01856244,-0.0352619,-0.0270716,-0.04556959,-0.02427922,-0.03114909,0.00868194,-0.02073395,0.01571436,-0.11258139,-0.06347844,-0.01348685,-0.03996703,0.01539514,0.00553965,-0.07309595,0.05172985,0.00502353,0.06981601,0.01237535,0.02707009,0.06515961,0.00836699,0.0484574,0.04179152,0.01713,0.05287419,0.02195264,0.04386583,-0.08119673,0.02138737,0.02339677,-0.03503346,-0.00449461,-0.02859749,0.031933,0.02922904,0.02116762,-0.04140957,-0.03432184,-0.03370168,-0.01412512,0.10024422,-0.02371507,0.07786647,-0.02180762,0.01216631,-0.04675803,0.03987144,-0.06221221,0.00962525,0.01334751,0.01673787,0.03487014,0.00534491,-0.03069362,0.02468185,0.01527293,-0.01840146,-0.00696056,0.10238593,0.0021931,-0.08662524,-0.04876326,0.04938641,0.01276349,-0.07500457,-0.05071566,0.04275828,-0.00483275,-0.01535443,0.03394245,0.02345345,-0.06085509,-0.04683862,0.0419066,0.00157632,0.00186358,-0.03922031,-0.0363832,0.00592797,-0.03037437,-0.0480412,0.00368215,-0.02416305,-0.05370876,0.04447827,0.02107656,0.09098543,0.02687224,-0.00717506,-0.00623779,-0.05118293,-0.03259999,-0.00793746,0.0739594,-0.04742521,0.08710279,0.02328801,-0.02518262,0.02350027,-0.04630356,0.01345074,0.00468619,0.02038961,0.0278023,0.03410681,-0.10860731,-0.02633811,0.04846692,0.05953554,-0.08785598,-0.02078952,0.02038241,0.01899224,0.02454186,0.06999765,-0.08029485,0.02619481,-0.08072374,-0.21871687,-0.00180287,0.02392939,-0.02656369,-0.03957381,-0.02122772,-0.01221056,-0.02715868,-0.08432948,-0.00267574,0.12869652,-0.04391309,0.00193174,0.01069583,-0.03612081,0.00066973,-0.00262383,-0.08336303,-0.03754251,0.00189556,0.00938918,0.04892744,-0.02122172,-0.10147697,0.0338618,-0.07616086,0.12770315,0.03534288,0.03747491,-0.02253374,0.05733112,-0.06208301,0.00520841,-0.08361776,0.0016675,0.01155277,0.00167531,0.03834066,0.04290283,0.02247825,-0.01393582,0.00518501,-0.02555667,-0.0695011,0.04041839,-0.02060389,-0.03773623,-0.00074605,0.03654693,0.05214923,-0.02198838,-0.0219137,0.09469831,0.00661997,-0.02519701,-0.00023108,-0.02599126,-0.0121242,0.01185617,0.0560738,0.02225053,0.01994869,-0.00185945,-0.07418041,0.07065591,0.04499668,0.00499463,-0.02151513,0.03570917,-0.03678213,-0.02071611,0.06271725,0.03870313,0.01776316,0.06456338,-0.03600382,-0.02931722,-0.03168976,0.01052989,0.00475421,-0.02935271,-0.07064926,0.00000585,0.04654428,-0.03058632,0.04619117,0.0257869,0.0088322,-0.00831791,-0.06175934,-0.02379757,0.00830985,-0.03010722,-0.00597475,0.09150714,0.00175812,-0.26606968,0.04529306,0.01786831,0.01298787,-0.03273567,-0.01725713,0.04399715,-0.00134635,0.00697666,-0.01639304,-0.04501703,0.06049417,0.00107704,-0.05652019,0.00545893,0.05869614,0.07205858,-0.04414,0.08361812,-0.09991363,0.03981139,0.03732663,0.25956586,0.01784866,0.04432955,0.05341795,-0.03211477,0.01060665,0.05693844,0.03551845,-0.01474964,0.0242833,0.10844389,0.0292162,-0.00792601,0.03050093,0.04194038,-0.00801753,-0.01655938,-0.03821837,-0.04039132,0.01686764,-0.0126824,0.05137392,0.0812193,-0.06767956,-0.02271781,-0.10722415,-0.02344766,0.02370361,-0.05833737,0.0533246,0.01025444,0.00505981,-0.00222283,0.01034381,0.00759802,-0.0038938,-0.03723169,0.0065114,0.00761648,-0.04925666,0.06900363,0.02733344,-0.01513434],"last_embed":{"hash":"f3zpr9","tokens":200}}},"text":null,"length":0,"last_read":{"hash":"f3zpr9","at":1751288818579},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.addMapping(mapping)","lines":[493,514],"size":659,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"f3zpr9","at":1751288818579}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.addMapping(mapping)#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05600526,-0.06524677,0.07516251,-0.06548076,-0.03914988,-0.01458792,-0.08020382,-0.01441416,-0.02129281,0.05599418,-0.0091093,-0.089247,0.00987155,0.03872648,0.02909228,-0.04306322,-0.04440488,0.08509164,-0.02741138,-0.03632995,0.08540126,0.00726652,0.01497903,-0.04151854,0.01148838,0.08414643,0.02822425,-0.02333441,0.03078981,-0.19120625,-0.01407294,-0.0324458,-0.03638648,0.02503347,-0.02148924,-0.03988774,-0.01694441,0.05756086,-0.0230687,0.00822627,0.05117022,0.03658661,-0.04006704,-0.00379403,-0.00574036,-0.0706035,0.01043039,0.01028804,0.01087255,-0.03450151,-0.00795062,-0.02445496,0.01650625,-0.00096656,0.0092059,0.04660883,0.0590871,0.09408194,0.01678226,0.01161703,0.02025601,0.0564832,-0.18080306,0.04305673,0.05134461,0.04934135,-0.02178555,-0.00314454,0.01153892,0.03504806,0.00787338,0.06309939,0.04818384,0.0476542,0.05556456,-0.04383473,-0.04521962,0.04903742,0.01798808,-0.00431377,-0.09474338,-0.01106091,-0.00657166,0.01894687,0.01694844,-0.04271487,0.02450838,-0.00865816,0.08269406,0.00902248,0.00327905,-0.06687904,0.02147664,0.11160427,-0.07987083,0.00577312,0.01799212,0.03051036,-0.02906079,0.14069931,-0.06289366,0.01237993,-0.01523527,-0.00804485,-0.02289792,-0.03245804,-0.07042491,-0.0314889,-0.02716846,-0.00998113,-0.01482094,0.01220986,-0.08467314,-0.06205665,-0.01532541,-0.04270762,0.03066858,0.00115422,-0.05414098,0.05473105,0.0166555,0.06211077,0.02735647,0.01239657,0.06258842,0.01755093,0.03952676,0.04331601,0.01842952,0.05057805,0.02708568,0.04702793,-0.06907127,-0.00106731,0.03365736,-0.04605578,-0.02105847,-0.0425924,0.02886763,0.02375335,0.02518895,-0.02771319,-0.03514696,-0.0462346,-0.00748945,0.07412285,-0.00621435,0.06233539,-0.04157769,0.00285042,-0.04004059,0.03809804,-0.04183898,0.01650014,0.01427781,0.0099891,0.05071038,0.03095713,-0.02390216,0.04185112,0.02453372,-0.01386271,-0.00534918,0.11684666,0.00898335,-0.09252609,-0.0492851,0.02986192,0.00601845,-0.07173894,-0.0485086,0.04566756,-0.01158925,-0.00026399,0.02962585,0.01047317,-0.04893089,-0.06814597,0.01830645,0.01095468,-0.00843885,-0.04526468,-0.02053545,0.00820858,-0.02189676,-0.04351963,0.01480376,-0.03032389,-0.04096373,0.01935662,0.01685372,0.10721545,0.03107243,-0.02465985,0.01012415,-0.05397061,-0.04294051,-0.00783355,0.07658057,-0.05974515,0.11097971,0.04386775,0.01523492,0.04065149,-0.04017852,0.01974669,-0.012882,0.00408015,0.03688055,0.03802303,-0.10094723,-0.03423223,0.05595713,0.05634126,-0.08228634,-0.01753207,0.00768258,0.02342718,0.01332027,0.06001558,-0.06010268,0.0641989,-0.10173005,-0.23217349,0.01181914,0.02758761,-0.01564338,-0.04131801,-0.01704834,0.00947536,-0.03667225,-0.07277115,0.01691163,0.10537284,-0.03316192,-0.01772981,0.01509738,-0.01960474,-0.00863946,-0.00528416,-0.08297373,-0.02274751,0.0056935,0.00524218,0.057414,-0.00590891,-0.09396661,0.01544699,-0.06916922,0.13396016,0.06515521,0.0313037,-0.04094501,0.03851521,-0.02456001,0.00395724,-0.08090176,-0.00462203,0.00990766,-0.00333066,0.03142582,0.06708653,0.0203183,-0.03686732,0.00878398,-0.02219484,-0.10116152,0.0445801,-0.02706838,-0.03755273,-0.00963438,0.03987825,0.04291644,-0.01232371,0.00585767,0.07220908,0.01115987,-0.0312345,-0.00507409,-0.02547381,-0.00709551,0.0139517,0.04331471,0.02090621,0.01726614,0.00028751,-0.06707378,0.07576292,0.02904523,-0.00781932,-0.02292474,0.03975081,-0.04586767,-0.0402157,0.07870812,0.03780293,0.00082248,0.04955465,-0.02547592,-0.03487097,-0.00796802,0.00633757,0.01228541,-0.00956103,-0.05585353,0.00061668,0.03492064,-0.04387172,0.05748035,-0.01371029,0.03173123,0.00819145,-0.05625298,-0.04464748,0.02589775,-0.02234192,0.0067666,0.08480263,-0.00460565,-0.26371512,0.05526802,0.02310415,-0.0004094,-0.02090241,-0.00787975,0.03970141,-0.02291478,-0.01614908,0.01351093,-0.02025648,0.04913339,-0.00562905,-0.04517461,-0.00240031,0.05637747,0.06737328,-0.03482354,0.07734095,-0.10721333,0.02885568,0.03999911,0.26715425,0.00184701,0.03263137,0.06711835,-0.04157571,0.02697928,0.06981652,0.02623431,-0.04601212,0.02610309,0.12078969,0.01873321,0.00252555,0.01606065,0.04737759,-0.03128263,-0.01817805,-0.01589752,-0.04436832,0.02460443,-0.0139583,0.05664805,0.0595559,-0.07958388,-0.0243262,-0.10712108,-0.00779518,0.01688173,-0.073337,0.01751983,0.00043426,0.01737088,0.01373713,0.02685061,0.01286337,-0.01994995,-0.04642924,-0.00169224,-0.02872377,-0.02582854,0.06745357,0.01281555,-0.02367239],"last_embed":{"hash":"wyx5sr","tokens":114}}},"text":null,"length":0,"last_read":{"hash":"wyx5sr","at":1751288818668},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.addMapping(mapping)#{5}","lines":[505,514],"size":201,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"wyx5sr","at":1751288818668}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03859382,-0.02165894,0.05498678,-0.09253564,0.00703197,-0.02140657,-0.10416563,-0.02967145,-0.0271244,0.01618242,0.01083715,-0.06858285,0.00557028,0.01003257,0.03751261,-0.04822766,0.00310825,0.06330676,-0.02140622,0.02165846,0.09681304,-0.02731895,0.06390414,-0.03033489,-0.00520577,0.08111377,0.0446066,-0.05255122,-0.0007541,-0.15873505,-0.00584208,-0.05189942,-0.08613613,0.03685515,0.00103978,-0.00996925,-0.03252931,-0.00199992,-0.00795546,0.02507696,0.04061534,0.04269366,-0.01061836,-0.01449225,-0.00494824,-0.03632564,0.00280606,0.02423261,0.01452478,-0.05071089,-0.03310799,0.01551761,0.02014706,-0.00776678,0.00448578,0.08060518,0.07143459,0.10068523,0.03919457,0.03376515,-0.02597994,0.03525868,-0.19569628,0.03582292,0.08082666,0.05072026,-0.02221417,0.02716412,0.02635065,-0.01135912,-0.04500818,0.07267237,0.04156797,0.02567931,0.04947556,-0.02842112,-0.00554235,0.05027343,-0.02451528,-0.00205275,-0.06719543,-0.0016191,-0.01008931,0.04874854,0.02388656,-0.02354535,0.00486687,0.01580389,0.0872556,0.01673759,-0.02117971,-0.0561631,0.03941599,0.10667308,-0.04859067,0.00326768,-0.01348744,0.06402242,0.01504565,0.11758242,-0.07434224,0.00741631,-0.00570989,0.00840867,-0.00492572,0.01279184,-0.04526609,0.00735289,-0.0072312,0.01993365,-0.00219249,0.00274808,-0.08286425,-0.07276558,-0.02570739,-0.00901704,0.01313656,0.00892749,-0.06276714,0.04701734,0.01107051,0.06238688,0.0269259,0.03841497,0.06551919,-0.01954708,0.08170957,0.01445431,0.00949396,0.04717493,0.00844741,0.04846892,-0.07946226,-0.02905777,0.01586038,0.01359465,0.01295664,-0.02115251,-0.010383,0.01380663,0.011888,-0.05464717,-0.02479004,-0.01795909,-0.02654212,0.10598189,-0.01581544,0.07958687,-0.02432376,-0.01082597,-0.0513632,0.01986941,-0.05723852,0.00787365,0.03007478,0.00175635,-0.00894786,-0.01338498,-0.05714207,0.01797397,0.00037186,-0.03986645,0.0062592,0.10367232,-0.02980091,-0.08672637,-0.03847744,0.02759016,0.03424962,-0.08949795,-0.0517052,0.01212116,0.01372692,-0.02493185,0.02572502,0.03927957,-0.04359158,-0.03401407,0.00470266,0.01413267,0.02201349,-0.04481678,-0.05519502,0.04706718,-0.05163129,-0.0484095,0.02502055,-0.05342266,-0.01249095,0.07413311,-0.02890786,0.0739652,0.02619729,-0.04629742,0.00125497,-0.05043467,-0.06067555,0.02777444,0.03608593,-0.03794092,0.09325971,0.03725969,-0.00800305,0.05268756,-0.08876194,0.00702508,-0.04040951,0.0014371,0.04105935,0.02633089,-0.10115473,-0.04624809,0.04118721,0.06567866,-0.08727064,-0.04528788,0.05644159,0.02116174,0.04564141,0.0642399,-0.0551591,-0.0121336,-0.08779869,-0.20742227,0.01282324,-0.00755722,-0.07329911,-0.00334262,-0.00431226,-0.03484659,-0.02352082,-0.07673476,0.00838438,0.17476587,-0.00057362,-0.0256827,-0.01173818,-0.03578217,-0.01713532,0.00548868,-0.03636125,-0.02720462,-0.0074817,0.0049155,-0.00348089,-0.06561343,-0.08291201,0.01978205,-0.0456164,0.1066491,0.04953105,0.03653425,-0.04809847,0.04875262,-0.0142208,0.00666471,-0.10832316,-0.04335907,-0.00494631,-0.01271145,0.02467422,0.05428733,0.06232349,-0.0032187,-0.00010011,-0.01456305,-0.07971766,0.04565632,-0.0276185,-0.0522442,-0.00004639,0.00173141,0.02572128,-0.02798641,0.00513813,0.06425641,0.03863261,-0.03310479,-0.00178294,-0.01238621,0.00902422,-0.00098559,0.05088129,0.02749777,0.02867473,0.01225577,-0.06475005,0.08556587,0.03319018,0.01762419,-0.03812275,0.03753884,-0.03379746,-0.00105301,0.05229083,0.02267783,0.00425978,0.0230407,-0.02958043,-0.01781832,-0.02474072,0.00668185,0.0019136,0.02993684,-0.01764414,-0.01993898,0.03665807,-0.03465239,0.04033279,-0.00410404,0.01251024,0.04621665,-0.02467638,-0.03137356,0.01626934,-0.01952898,-0.01362213,0.1184701,0.03077493,-0.26890627,0.06030715,0.01694253,-0.02078866,-0.04052772,-0.04166419,0.07719719,-0.01634256,-0.00125888,-0.00225893,0.00107684,0.05131495,-0.04998679,-0.05968137,0.01391106,0.05155298,0.10321176,0.01587256,0.10251606,-0.05859784,0.02437827,0.06367347,0.26223305,-0.00960855,-0.01376166,0.07172373,-0.05051913,0.05530613,0.07621795,0.03110719,-0.00263828,0.02900139,0.09296938,0.00720539,-0.02434789,-0.00657046,0.04435021,-0.01161372,0.01235713,-0.00948379,-0.02192725,0.00884414,-0.01556424,0.01906626,0.0570692,-0.06002189,-0.05680782,-0.09471527,-0.03455461,-0.00712456,-0.07187726,0.03345899,0.02320147,-0.010989,-0.03558945,0.02364629,0.00228686,-0.0326705,-0.02493724,-0.03616593,0.0106453,-0.0392399,0.07269323,0.05010671,-0.00023715],"last_embed":{"hash":"unjtf","tokens":149}}},"text":null,"length":0,"last_read":{"hash":"unjtf","at":1751288818780},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)","lines":[515,527],"size":361,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"unjtf","at":1751288818780}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04725403,-0.06301761,0.04903605,-0.10738076,0.00612278,-0.0321411,-0.08858504,-0.04116415,-0.05232422,0.00655557,0.03516082,-0.07906543,0.00955779,0.01255814,0.02985888,-0.01221351,-0.04939621,0.08931516,0.00316195,0.01530438,0.08446882,-0.01852308,0.0329686,-0.01065401,0.03868588,0.0995313,0.04403941,-0.05989796,-0.00809971,-0.20590693,-0.01383298,-0.03763204,-0.03964892,0.0085247,-0.04558127,0.00122299,-0.02702207,0.01558689,-0.00757205,0.00029904,0.03343069,0.01281504,-0.04661065,0.00114421,0.00152774,-0.03241894,-0.0307272,0.00867436,-0.01865162,-0.02362686,-0.03239169,-0.02057614,-0.00333931,0.00154052,0.01238782,0.05712917,0.09973974,0.09395222,0.01455715,0.0407189,0.00175977,0.05769102,-0.19173446,0.0326447,0.1127473,0.02763778,-0.02426076,-0.02674805,0.00146456,0.00128858,-0.05227891,0.03112429,0.00606948,0.04674112,0.01888569,-0.07742909,-0.02123681,0.03413647,0.01445495,-0.01809256,-0.0675043,0.03311351,0.01420223,0.05242991,0.02211558,-0.03785301,0.02042826,-0.01935592,0.05525203,-0.00802931,0.02284691,-0.05337279,0.06260565,0.08865738,-0.04876602,-0.02122216,0.01240456,0.06231684,0.00805055,0.11024879,-0.08309173,0.00785449,-0.02692286,0.05690127,0.02972036,-0.02373343,-0.04929464,0.02074864,-0.03516009,0.04934308,-0.01556579,-0.01829356,-0.05402329,-0.03376919,0.00263775,-0.0060142,0.05188136,0.01198491,-0.084373,0.03727418,0.00748244,0.03098324,0.00561576,0.04046694,0.02905984,-0.04219681,0.0501713,-0.00135631,0.01125767,0.02013724,0.00231106,-0.012706,-0.07434025,0.00886389,0.05290593,0.00864235,0.00157068,-0.03119332,0.01878244,0.01318672,0.06308931,-0.06494543,-0.03142899,-0.01209722,-0.01601312,0.1536561,-0.06246948,0.08684621,-0.01501534,-0.01498198,-0.05760291,-0.00925617,-0.12816162,0.02407707,0.03077615,0.02684741,-0.00044873,-0.00315293,-0.04435363,0.04809485,0.01462357,-0.03840682,0.00562112,0.09053317,-0.02455258,-0.05823687,-0.01476381,0.01984288,0.05007501,-0.04421493,-0.04883764,0.01907968,0.03932118,-0.04505545,0.01032918,0.03568847,-0.05691142,-0.06677853,0.00364242,0.03062944,-0.00788029,-0.03111822,-0.02617768,-0.00545668,-0.00478274,-0.0281163,0.00362833,-0.01503518,-0.03114409,0.04551294,-0.02158133,0.0486019,0.02931158,-0.04268666,0.00991959,-0.04261616,-0.02085677,0.02427734,0.06390864,-0.05266282,0.0955726,0.01899467,-0.00331006,0.04636448,-0.03825954,0.02516852,-0.03456553,0.02439789,-0.01401157,0.02083112,-0.09277385,-0.04903612,0.02534088,0.0451658,-0.06005545,0.0022125,0.04067088,0.02852399,0.04919465,0.1081617,-0.06331167,0.00037244,-0.05310262,-0.19829603,0.00490226,-0.00753206,-0.01977418,0.00876556,-0.05539598,-0.04600951,-0.0097974,-0.08608015,0.01440513,0.12868944,-0.0401641,0.01325565,0.04647631,-0.0409572,-0.01669323,-0.00295075,-0.04775737,-0.04277302,0.01250918,0.01836394,0.02604413,-0.06218087,-0.06030986,0.07413214,-0.01982335,0.13360117,0.08014546,0.02170584,0.00957987,0.04820566,-0.01977249,-0.02043508,-0.10664701,-0.02409697,-0.02485543,0.0201981,-0.00554975,0.0791164,0.04607125,-0.03880714,0.00565202,-0.02470604,-0.05163365,0.03307343,0.01107222,-0.05386629,0.04076535,0.03244366,0.03374967,-0.03933864,0.0091225,0.09649061,0.02942102,0.0044715,0.00326421,-0.02599862,-0.01615354,-0.00576728,0.04510265,0.03435572,-0.00620761,0.00409453,-0.05890842,0.07028402,0.03052952,0.03030243,-0.00056154,0.06496689,0.01031421,-0.01772013,0.05524682,0.01298146,0.00636388,0.03760781,-0.03296219,-0.01079624,-0.0048268,0.04897421,-0.04878752,-0.00782945,-0.07886039,0.02068402,0.01492944,-0.02611908,0.00885578,0.03241922,-0.00383823,0.00720292,-0.01929434,-0.0235668,-0.00642948,-0.06129308,-0.00699389,0.05584754,0.02068663,-0.25400111,0.02708534,0.03203633,-0.0115387,-0.04909133,-0.04354588,0.07324229,0.00489057,-0.01241148,-0.06168368,-0.02133653,0.09933003,-0.00945038,-0.04289697,0.03256979,0.03825268,0.07471522,-0.02265927,0.09633745,-0.09951741,0.01978754,0.05868381,0.2435313,-0.02849851,0.02227281,0.05865156,-0.06109583,0.04227744,0.06105032,0.02072614,-0.02456347,-0.00802746,0.0958657,-0.0006652,0.00772396,0.01595707,0.0132791,-0.00539339,0.03259321,-0.0155089,-0.01230171,-0.00826909,-0.02889357,0.00281769,0.10786852,-0.04493263,-0.01605768,-0.10279509,-0.04002796,0.05127014,-0.04118553,0.05151901,0.01612756,-0.02611008,-0.04074249,0.03006847,-0.04159877,-0.01704586,-0.05674135,-0.04453033,0.0392688,-0.03651512,0.09444042,0.04798193,0.01094887],"last_embed":{"hash":"1xz6jlj","tokens":309}}},"text":null,"length":0,"last_read":{"hash":"1xz6jlj","at":1751288818863},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])","lines":[528,551],"size":1071,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1xz6jlj","at":1751288818863}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04480079,-0.04071332,0.05557183,-0.11840869,-0.00039429,-0.03193547,-0.07380002,-0.03634183,-0.03678913,0.00757,0.0220653,-0.097873,0.01038847,0.02293725,0.00862518,-0.02388296,-0.0594745,0.0927593,-0.00561383,-0.00092929,0.10373253,-0.01003312,0.00987011,-0.00731934,0.01497151,0.09207863,0.05319238,-0.05831788,-0.00583402,-0.19808443,-0.01469551,-0.06623473,-0.03094976,0.00814567,-0.04736336,-0.01128443,-0.03527587,0.01449466,-0.0199884,0.01201316,0.03722204,0.01441085,-0.06921936,-0.00332894,0.00983827,-0.04935421,-0.01823199,0.01931845,-0.01369779,-0.03466773,-0.03456955,-0.012296,0.00755161,-0.0024402,0.02053619,0.05402725,0.09343234,0.07925802,0.03700748,0.03538861,-0.00815778,0.05345602,-0.19024889,0.04081297,0.089942,0.05534473,-0.01809557,-0.03085183,0.01081777,-0.00093575,-0.03390909,0.0499299,0.02663865,0.03137642,0.02525562,-0.0676207,-0.01390071,0.03212961,0.02286734,-0.01275435,-0.07275075,0.03295989,0.02891757,0.05313329,0.03114677,-0.04084532,0.01505278,-0.01754796,0.06446683,-0.01730806,0.01774386,-0.04309827,0.04083773,0.0871254,-0.05108422,-0.02057489,0.00531815,0.04366324,0.02059174,0.13390999,-0.086959,0.00405567,-0.01432825,0.03704797,0.02268287,-0.02775044,-0.06391384,0.00971703,-0.03693428,0.03080151,-0.0263052,-0.01288254,-0.0612332,-0.05135797,-0.01301696,-0.02927252,0.03586717,0.01564165,-0.0745845,0.04546295,0.002378,0.05753522,-0.0007912,0.05492726,0.05928248,-0.03197549,0.06380218,0.00000753,0.00537539,0.03571044,-0.0095304,-0.00308841,-0.07508475,0.0099165,0.06614244,0.00790024,0.00476645,-0.03949022,0.01055982,0.02734062,0.03440894,-0.0461733,-0.02668121,-0.02860845,-0.02633913,0.13473889,-0.04969662,0.09074338,-0.02038047,-0.01372695,-0.05155084,0.02610978,-0.11744136,0.02649848,0.03163091,0.03037497,0.00003445,-0.01226587,-0.05047496,0.048848,0.00936289,-0.03155999,0.03131637,0.08800642,-0.03477105,-0.06627742,-0.02348207,0.03006398,0.0333484,-0.05696297,-0.06134228,0.0302886,0.0254206,-0.02875864,0.02053608,0.04856207,-0.05635995,-0.06151372,0.00406838,0.02134919,-0.01218756,-0.03873042,-0.0243677,-0.00209636,-0.00337756,-0.01510389,-0.00006539,-0.01155146,-0.01585364,0.02304454,-0.01343892,0.05054152,0.02040919,-0.03979645,0.02009624,-0.0458921,-0.03770959,0.0268756,0.04744267,-0.04730794,0.08654349,0.02736075,-0.01171731,0.05818554,-0.0249515,0.02907363,-0.02299485,0.02128968,-0.01402332,0.02752189,-0.0987881,-0.040317,0.04238769,0.05632015,-0.06559736,-0.00849942,0.03500041,0.02212842,0.02480645,0.10146523,-0.06082805,0.00736187,-0.06352448,-0.20428571,0.01440608,-0.00214532,-0.02597109,-0.00415413,-0.04333523,-0.05675806,-0.01231747,-0.06689196,0.00794513,0.13645396,-0.02924563,-0.00106224,0.04868428,-0.04624614,-0.01110782,-0.00755757,-0.0565531,-0.04749967,0.0052906,0.03830508,0.01787902,-0.06094377,-0.06007433,0.06591912,-0.02707831,0.1250252,0.06535847,0.00314023,0.00269268,0.06246197,-0.03437418,-0.03143582,-0.10236881,-0.01843365,-0.01810049,0.01807581,0.0035454,0.09127519,0.03646232,-0.03361662,0.02669197,-0.03355536,-0.08046183,0.03994278,-0.00024958,-0.04350268,0.02957935,0.0184887,0.01926104,-0.03187241,0.00477733,0.08977851,0.01768282,0.01486031,-0.00100825,-0.03860418,-0.0134623,-0.02067456,0.03856905,0.03132564,-0.00940258,-0.00662616,-0.06053327,0.08265524,0.01553743,0.03699715,-0.01314887,0.05445571,-0.00657846,-0.02640703,0.05395852,0.01970041,0.01966979,0.04923096,-0.02456403,-0.00721197,-0.00917921,0.05904825,-0.01921014,-0.00111269,-0.04737085,0.01469405,0.00104473,-0.03482161,0.03028482,0.01616454,-0.0023229,0.00451069,-0.02543884,-0.01248641,0.01395225,-0.04130479,-0.0169521,0.07333487,0.01156839,-0.26295328,0.03479116,0.03735084,0.0040919,-0.03248374,-0.04190243,0.06669324,-0.01476773,-0.00421576,-0.03623927,-0.04483351,0.07881633,-0.01370912,-0.03307103,0.0406942,0.05021852,0.08611598,-0.0375669,0.08892472,-0.10961157,0.02504478,0.05747475,0.262463,-0.0128254,0.01791808,0.06242049,-0.06114205,0.04519594,0.05793467,0.01507234,-0.01855888,-0.00152117,0.09155338,0.00845696,-0.01419852,0.04200888,0.02522303,-0.00313993,0.0259084,-0.0085528,-0.0065755,-0.00388096,-0.02199077,0.00866312,0.08868754,-0.04849694,-0.03602954,-0.09780816,-0.05350875,0.04954506,-0.04576,0.04322658,0.0216372,-0.0365834,-0.04088361,0.03684222,-0.02072418,-0.01267978,-0.03649186,-0.02291843,0.03993377,-0.0352628,0.08784526,0.04830863,0.01124423],"last_embed":{"hash":"1g5zhf3","tokens":121}}},"text":null,"length":0,"last_read":{"hash":"1g5zhf3","at":1751288819006},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])#{1}","lines":[530,534],"size":237,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1g5zhf3","at":1751288819006}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0397475,-0.04777453,0.04674967,-0.09973133,-0.01091659,-0.02834294,-0.0707943,-0.02384283,-0.04405953,-0.00576876,0.01710667,-0.06825842,0.00799269,0.01465238,0.05307148,-0.01247453,-0.06378163,0.10174815,-0.0071932,-0.00291945,0.10199699,-0.00366955,0.02786372,-0.02431244,0.05157378,0.11969569,0.0479571,-0.0507813,0.0160893,-0.19375722,-0.01570752,-0.02016493,-0.04601297,0.00536924,-0.04044398,0.0094243,-0.03562539,0.02917491,-0.01163513,-0.00095884,0.02695728,0.02160652,-0.05494895,-0.00274824,-0.00202689,-0.03653094,-0.03424219,-0.00277362,-0.02457015,-0.03302373,0.00530249,-0.01103281,-0.00125956,0.00677339,0.01015991,0.05605806,0.10163202,0.10118869,0.00645761,0.05832803,0.01170354,0.04440229,-0.18217349,0.02812505,0.1207504,0.02081623,-0.01895266,-0.05273249,0.00612673,0.01510762,-0.04757855,0.03002228,0.00137696,0.04476314,0.02536958,-0.0661568,-0.03912239,0.01211614,0.01506287,-0.00359525,-0.05209252,-0.00152495,0.00509741,0.04606877,0.01300254,-0.0245185,0.0137111,-0.0178336,0.05949816,-0.00789831,0.0123262,-0.04588718,0.0595916,0.07935692,-0.04805908,-0.01664404,-0.00109681,0.05101733,0.00203099,0.1252605,-0.07409535,0.01138759,-0.03743503,0.05758429,0.02683342,-0.01845817,-0.04373123,0.01376504,-0.03009704,0.04624756,-0.01874955,-0.00609803,-0.06381942,-0.03018206,-0.00704496,-0.0071166,0.05264587,0.00600341,-0.06255811,0.00953005,0.01233264,0.01850683,0.00701904,0.02399388,0.02635234,-0.03552442,0.05756351,-0.00046638,-0.00964078,0.00716398,0.01521665,-0.02803057,-0.07266735,-0.0013277,0.05542466,0.00836998,-0.00298191,-0.02635586,0.03705658,0.03341211,0.07167225,-0.05401793,-0.0277414,-0.01945017,-0.00752593,0.15873826,-0.05718243,0.09144122,-0.01874141,-0.00730007,-0.06904075,-0.0206115,-0.11819978,0.02253355,0.01764519,0.02961639,-0.02883664,-0.00074585,-0.01242178,0.03346052,0.01353027,-0.01780477,-0.01357249,0.10593646,-0.0286828,-0.06034703,-0.00362849,0.00564345,0.05207776,-0.0322805,-0.05450584,0.01975388,0.02812703,-0.05589895,0.02388734,0.01448462,-0.0725162,-0.05507552,-0.00284749,0.02042808,0.02009312,-0.02648154,-0.01245423,-0.00529522,-0.01238853,-0.03832975,0.01066839,-0.02458627,-0.03745794,0.05602397,-0.03354689,0.03408179,0.02012984,-0.03286892,0.02496243,-0.0280834,-0.01122532,0.01435863,0.06195411,-0.04751102,0.08922216,0.01451147,-0.00180286,0.04374422,-0.06510795,0.03664088,-0.03275118,0.00748288,-0.00397693,0.02740653,-0.1028749,-0.0505027,0.03145966,0.0387333,-0.05233049,-0.00200418,0.02658199,0.0326431,0.05530445,0.10689962,-0.06382342,0.01734447,-0.03384435,-0.20037445,0.01080067,-0.00749936,-0.01883922,0.03735238,-0.0479247,-0.0237278,-0.01291518,-0.08114546,0.02035141,0.11225231,-0.02922677,0.01793612,0.05501794,-0.0433378,-0.00231593,0.00483363,-0.05969856,-0.02993492,0.01217242,0.00485088,0.03487192,-0.04685076,-0.0769169,0.08463613,-0.00960358,0.1316127,0.06062867,0.02259389,0.00622388,0.03321376,-0.03795322,-0.01060841,-0.11515753,-0.05370416,-0.00691554,0.01314106,-0.03035242,0.0869002,0.04008783,-0.05201906,0.00328888,-0.02117597,-0.05054133,0.03120849,0.00919822,-0.05968951,0.03099117,0.02867504,0.04303791,-0.03603694,0.00728779,0.0960417,0.04336738,0.00574686,-0.0019301,-0.03461025,-0.02044833,-0.00392759,0.05406032,0.01958645,0.02302065,0.00365066,-0.04789051,0.09047052,0.0266866,0.02057896,-0.01555965,0.06969564,0.02246738,-0.02013282,0.05725981,-0.00144745,0.00020314,0.02372408,-0.02692622,-0.0239238,0.00085934,0.05622413,-0.0451234,-0.01219279,-0.0969018,0.01938593,0.01722186,-0.02456174,-0.01190362,0.01842034,-0.00813148,0.02393906,-0.02118434,-0.04854004,-0.00959475,-0.07721362,0.00528725,0.02818572,0.02199993,-0.25584486,0.03457688,0.03628173,-0.01111412,-0.06780677,-0.04650068,0.0793903,-0.00421898,-0.01014358,-0.0659707,-0.00525605,0.0945481,0.00013958,-0.01310019,0.02658522,0.05347906,0.09304347,-0.03329332,0.08232306,-0.10710919,0.02487304,0.05113412,0.25022784,-0.01467728,0.02530157,0.05062946,-0.06046172,0.03789848,0.05548083,0.02157952,-0.01938662,-0.00735526,0.09586639,-0.02305788,0.02141384,0.00171856,0.01594418,0.00232385,0.02988829,-0.02345002,-0.01882784,-0.0015883,-0.03947051,0.00343507,0.10225888,-0.03862923,-0.02286637,-0.09291428,-0.05266421,0.05120667,-0.04490024,0.04868603,0.02540434,-0.03213681,-0.02693821,0.03299557,-0.05684192,-0.01642722,-0.0712934,-0.04324898,0.03372588,-0.00334977,0.07647736,0.04428003,0.02266137],"last_embed":{"hash":"y0kc8a","tokens":172}}},"text":null,"length":0,"last_read":{"hash":"y0kc8a","at":1751288819065},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])#{6}","lines":[542,551],"size":448,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"y0kc8a","at":1751288819065}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.toString()": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02747607,-0.01899727,0.02924703,-0.06644705,-0.02854003,-0.04842354,-0.10181507,-0.0315566,-0.04674671,0.0260371,-0.01127944,-0.07271437,0.00439998,0.02588228,0.01993079,-0.02584622,-0.05504076,0.04910159,-0.03215655,-0.00224423,0.09856509,-0.00476129,0.03163976,-0.04077535,-0.01883909,0.1005726,0.06340337,-0.0447429,-0.01454295,-0.16705136,-0.01314287,-0.06318985,-0.05745953,0.00137351,-0.00270043,-0.01548923,-0.00823299,0.02462611,-0.03059135,0.04584961,0.03925924,0.05329934,-0.02510129,-0.01705437,-0.00878588,-0.04545353,-0.03127019,-0.00125752,0.00284641,-0.03439472,-0.03853485,0.00273406,-0.00103091,-0.03243668,0.00836869,0.05898005,0.05406276,0.11851338,0.01109557,0.00949097,-0.01063295,0.05143505,-0.18422498,0.03405942,0.04889312,0.05527022,-0.01203312,0.01162423,0.02399606,-0.0106937,0.02406738,0.06017298,0.04530768,0.03182364,0.05352838,-0.05484622,-0.01844678,0.03442236,0.01791716,-0.00576939,-0.0769673,-0.00165053,-0.00802391,0.06028167,0.03685163,-0.00645384,0.02774753,0.0049265,0.07195382,0.0072847,-0.01936492,-0.05505232,0.03006646,0.10701104,-0.08819777,0.00191882,-0.00560004,0.06157649,-0.01028484,0.14419821,-0.07429101,0.01373458,0.01194908,0.00076196,-0.02469017,-0.01963712,-0.04069285,-0.0140604,-0.02402318,0.00466274,-0.02789021,0.02157009,-0.07726718,-0.05821703,-0.02138915,-0.03097848,0.02113071,0.02452408,-0.05258495,0.03898954,0.02194207,0.09529704,0.01479044,0.01495155,0.06893437,0.00143463,0.06454859,0.02522822,0.0433971,0.08285613,0.00409012,0.07011705,-0.11174808,-0.01110275,0.0201317,-0.03656129,0.00463883,-0.02127918,-0.00851872,0.02396928,0.02408213,-0.05055865,-0.05058296,-0.02457581,-0.00952274,0.05358696,-0.0332009,0.07498885,-0.02383062,0.00633531,-0.01841417,0.04210533,-0.04687548,0.02141286,0.01386076,0.00534765,-0.00959343,0.01304763,-0.01606101,0.02130621,0.00391477,-0.0187979,0.01272047,0.10168175,-0.00269649,-0.0837992,-0.05016132,0.05696816,0.04118805,-0.07141259,-0.05636132,0.04305897,-0.00273561,-0.02288122,0.02163034,0.01868838,-0.04329675,-0.04408682,0.04058018,0.02538045,0.04467399,-0.04291736,-0.05203453,0.01023992,-0.01965579,-0.07216625,-0.00160862,-0.05680148,-0.01376836,0.02195697,0.01904061,0.04566234,0.00607013,-0.0272269,0.02636959,-0.0174625,-0.03581496,-0.01912792,0.04712607,-0.0433196,0.10941412,0.04494319,-0.02560429,0.05261969,-0.06645355,0.00867734,-0.00297754,-0.0120283,0.02698581,0.02928506,-0.10426298,-0.02721009,0.08976002,0.08527293,-0.08069476,-0.06030868,0.00986563,0.02594408,0.01977458,0.06847166,-0.09625448,-0.01785593,-0.09388354,-0.21685326,0.02472477,0.04347494,-0.05283197,-0.04450281,-0.02294761,-0.04221434,-0.03704844,-0.05365909,-0.00124546,0.12972918,-0.01694681,0.01133585,-0.00977057,-0.02179926,-0.02084808,-0.00916776,-0.05197467,-0.00004268,0.00933348,0.00024058,-0.00371382,-0.0357427,-0.11135094,0.01580991,-0.06434936,0.14308652,0.05526414,0.03665583,-0.03634864,0.04543483,-0.04567464,0.00596648,-0.06822578,-0.01248025,-0.01998226,0.0053276,0.047701,0.0456372,0.06047708,-0.01402398,0.0059755,-0.0267681,-0.07272186,0.04191208,-0.05744934,-0.03970103,-0.02114626,0.02936259,0.06327245,0.00951251,-0.0011638,0.09358384,-0.00567289,0.00501722,-0.00668525,-0.02229191,0.00295533,-0.01681498,0.02097671,0.03219702,0.01104683,0.00061183,-0.08009898,0.06177894,0.03366298,0.02122549,-0.02450844,0.0399365,-0.03922157,-0.03031819,0.08857971,0.02216908,0.00824584,0.02844102,-0.0141347,-0.04294831,-0.03711599,0.01444236,0.00664836,-0.02974408,0.01574829,0.03278713,0.00626591,-0.02283301,0.02605474,-0.03363775,0.04247749,0.03315322,-0.02749108,-0.01652433,0.02806594,-0.00051949,-0.02958975,0.10300153,-0.00093734,-0.2501415,0.04692902,0.05204833,0.01646015,-0.04353787,-0.01632773,0.06687941,-0.01964117,-0.02126415,-0.01468798,-0.00821274,0.02653205,-0.02241775,-0.02125136,0.02724129,0.06755424,0.09891381,-0.03092652,0.08505435,-0.07286584,0.05810823,0.06108332,0.25724363,-0.00492822,0.02029173,0.06680806,-0.07019634,0.04372656,0.07984526,0.03869877,-0.03527008,0.0335611,0.12273365,-0.00880487,-0.02200528,-0.01152429,0.01861112,-0.03017557,-0.02887774,-0.03134351,-0.01357195,0.01868167,-0.01884121,0.02188384,0.08702208,-0.05068222,-0.03479547,-0.0707111,-0.046752,0.03003918,-0.07081254,0.04500459,0.00593293,-0.00865081,-0.00652103,0.04198574,0.00626459,-0.03127961,-0.03378389,-0.0097106,-0.00416272,-0.04790712,0.07693008,0.03462449,0.00289108],"last_embed":{"hash":"1whvmib","tokens":166}}},"text":null,"length":0,"last_read":{"hash":"1whvmib","at":1751288819143},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.toString()","lines":[552,560],"size":296,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1whvmib","at":1751288819143}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.toString()#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02543051,-0.01490976,0.0296263,-0.0623568,-0.02993016,-0.04759501,-0.10040969,-0.02942425,-0.04648105,0.02419539,-0.01100564,-0.07262225,0.00638091,0.02564492,0.01886579,-0.0264719,-0.05622404,0.04822863,-0.033375,0.00114286,0.10265382,-0.01008685,0.03175677,-0.04267555,-0.01687757,0.10215864,0.06416438,-0.04801085,-0.01548509,-0.16712956,-0.01159735,-0.06564942,-0.0609659,-0.00176465,-0.00421813,-0.01516957,-0.01049319,0.02681051,-0.02980486,0.04647064,0.03820435,0.05591838,-0.02209647,-0.0170306,-0.0079635,-0.04700083,-0.03524077,-0.00310119,0.00370487,-0.03678415,-0.03578674,0.00335677,-0.00367936,-0.03552246,0.00478616,0.05691321,0.05355872,0.12103231,0.0142031,0.00878083,-0.01083873,0.05311505,-0.18175133,0.03410235,0.04563906,0.05777839,-0.01443977,0.00968199,0.02258075,-0.00745259,0.02597149,0.05990798,0.04661393,0.03071141,0.05691721,-0.05543071,-0.01882939,0.0325211,0.01615189,-0.00276777,-0.07404045,-0.00248995,-0.01023988,0.06209864,0.03567252,-0.00806471,0.0235762,0.00276125,0.06871381,0.00932675,-0.01851545,-0.05363213,0.02921539,0.10461611,-0.09094934,0.00563877,-0.0079009,0.05970653,-0.00859575,0.14676951,-0.07363179,0.01189211,0.01182517,0.00075917,-0.02061479,-0.01781426,-0.040907,-0.0141517,-0.02336519,0.00453303,-0.02624639,0.02370241,-0.07855322,-0.05729778,-0.02167606,-0.03191917,0.01941441,0.02562509,-0.05444356,0.03944645,0.02041772,0.0958944,0.01204022,0.01453959,0.06904309,0.0043757,0.06536789,0.02361736,0.04243928,0.08298448,0.00406683,0.069622,-0.11619493,-0.0126856,0.01935365,-0.03566565,0.00264683,-0.01991685,-0.00939921,0.02374494,0.02173277,-0.04654358,-0.05112848,-0.0239592,-0.00676618,0.05390079,-0.03611993,0.07403799,-0.02318154,0.00945106,-0.01969972,0.04096525,-0.04529252,0.01936145,0.01421411,0.00646729,-0.01539473,0.01267788,-0.01457355,0.02031657,0.00103981,-0.01687604,0.01169188,0.10133982,-0.00532997,-0.08318862,-0.05082992,0.05941217,0.04432733,-0.06830238,-0.0583283,0.04092427,-0.00246838,-0.02548792,0.02192275,0.01539262,-0.0448298,-0.0483492,0.03960496,0.02781118,0.04768496,-0.04291066,-0.05038746,0.00686557,-0.01829064,-0.07266277,-0.00345327,-0.05803405,-0.01587278,0.02054573,0.01749095,0.04601173,0.0037198,-0.02770871,0.02727786,-0.01654756,-0.03383654,-0.02046795,0.04449323,-0.04172283,0.10797997,0.04645342,-0.02376438,0.05431157,-0.06558413,0.00814025,-0.00281948,-0.01618187,0.02627843,0.02807718,-0.10298365,-0.02469368,0.09512076,0.08746218,-0.08130792,-0.05908721,0.00798055,0.02548908,0.0185025,0.06752389,-0.09514634,-0.01597133,-0.0920969,-0.21605766,0.02145582,0.04539644,-0.05535936,-0.04432224,-0.02544633,-0.04345019,-0.03430004,-0.05008339,-0.00502897,0.12678491,-0.01343342,0.01109122,-0.00806442,-0.02493617,-0.01817322,-0.0099495,-0.05030411,0.00187421,0.0079599,-0.00184636,-0.00609513,-0.03965385,-0.11061946,0.01205888,-0.06349874,0.14583069,0.0546541,0.03662462,-0.03646905,0.04612621,-0.04581908,0.00619299,-0.06387383,-0.0118373,-0.0236985,0.00697231,0.04528996,0.04479989,0.06016226,-0.01121147,0.00704339,-0.03078636,-0.07425551,0.04345798,-0.0591003,-0.03928209,-0.0196938,0.03130481,0.06575577,0.00706673,-0.00279558,0.09252106,-0.0065695,0.01107105,-0.0107765,-0.02616648,0.0024975,-0.01692358,0.02104028,0.0346671,0.01127692,0.00123243,-0.08033717,0.06126146,0.03105333,0.02432884,-0.02239041,0.04316768,-0.03818728,-0.02780559,0.08881634,0.02207421,0.01011155,0.02776551,-0.01087185,-0.04227614,-0.03820396,0.01231128,0.00648465,-0.03019506,0.02110132,0.0335961,0.00215571,-0.02118029,0.02565997,-0.03679328,0.04457225,0.03268861,-0.02425722,-0.01743763,0.03234228,0.00243054,-0.03188403,0.101421,-0.0028617,-0.25047672,0.04750013,0.0545369,0.01729092,-0.04471139,-0.01373935,0.06866149,-0.0183709,-0.02417912,-0.01319746,-0.00760936,0.02594477,-0.02054023,-0.0200426,0.02636681,0.06604072,0.10025591,-0.02934104,0.08353888,-0.0737398,0.0614904,0.05758309,0.25553474,-0.00593974,0.01673674,0.06609231,-0.070218,0.0438751,0.07690713,0.0373421,-0.03504226,0.03632771,0.12500921,-0.01393046,-0.01980102,-0.00944592,0.01715828,-0.03100006,-0.02998625,-0.03417514,-0.00974982,0.01949651,-0.02017985,0.01967153,0.08504269,-0.04938013,-0.03273004,-0.06312958,-0.04757874,0.03018682,-0.06910705,0.04710438,0.00435837,-0.00992192,-0.00433247,0.03925959,0.01158591,-0.03214578,-0.0294844,-0.01031573,-0.00273854,-0.04896191,0.07714463,0.03611939,0.00588451],"last_embed":{"hash":"1u0v3we","tokens":163}}},"text":null,"length":0,"last_read":{"hash":"1u0v3we","at":1751288819224},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceMapGenerator#SourceMapGenerator.prototype.toString()#{1}","lines":[554,560],"size":250,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1u0v3we","at":1751288819224}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06698218,-0.04764706,0.02847604,-0.09062339,0.01857814,-0.00384373,-0.06411752,-0.03102003,-0.02848651,0.02322515,0.0050295,-0.05176149,0.04866396,0.02782028,0.01078582,-0.03316122,-0.03270442,0.06584907,-0.02283072,-0.004783,0.03213895,-0.02426635,0.0210243,-0.04527088,0.00511529,0.10657187,0.0323888,-0.04636014,0.01903235,-0.19732572,-0.0215052,0.0053052,-0.01011623,0.00230381,0.011646,-0.03180688,0.01286655,0.00171462,-0.00539923,0.04515744,0.02416166,0.0131999,-0.03312445,-0.00917223,-0.02554071,-0.01252084,-0.04043763,0.02328404,-0.002382,-0.01858773,-0.02476478,0.00994819,-0.0057039,-0.01002762,0.03555965,0.06792941,0.05330776,0.11420386,0.02147869,0.06885642,0.01591297,0.03916367,-0.18445934,0.03552065,0.06817283,0.04028802,-0.0481611,0.00361936,-0.0011057,-0.01363316,-0.02026647,0.02415035,0.02722916,0.04931339,0.01728439,-0.04330523,-0.03107724,0.05128628,-0.01325135,-0.00022998,-0.08506855,0.02097216,0.01048746,0.05426068,0.02103337,-0.00412204,0.05779712,0.00248155,0.08200517,0.031946,-0.02615033,-0.0613408,0.02200349,0.09981537,-0.0315547,-0.00079451,0.04245491,0.0518294,0.01282755,0.12428827,-0.05588073,0.00080079,0.00850333,0.02418325,-0.01740173,-0.03240218,-0.0262703,-0.05542047,-0.04682884,0.01019842,-0.04614304,-0.00648709,-0.05195155,-0.06752201,-0.0086091,-0.01513046,0.02745988,-0.02489963,-0.04141466,0.02720983,-0.00012144,0.06476292,-0.00530684,0.04172629,0.0550463,0.01618065,0.01219796,0.03990241,0.01452416,0.08199888,0.06410822,0.09067171,-0.07004191,0.02001612,-0.01429276,-0.02012689,0.00756649,-0.0076891,0.01693041,-0.00783837,0.03325624,-0.066108,-0.0266679,0.00001052,-0.02244408,0.12509426,-0.04308608,0.08395801,0.01811747,-0.00682147,-0.01891772,0.02227785,-0.10949142,-0.02543674,-0.0238358,0.00410685,0.01884225,0.00784933,-0.06677093,0.01368247,-0.00004517,-0.03571182,-0.02934887,0.10741222,0.01400543,-0.05370243,-0.05514614,0.04837666,0.05312254,-0.0711292,-0.05001833,-0.01729426,0.02163256,-0.03274009,0.02541502,-0.01072551,-0.0591639,-0.03748069,0.06461412,0.02052114,0.00497966,-0.04181942,-0.06162161,0.01788931,-0.00308943,-0.05166084,0.00316083,-0.04379918,-0.04593965,0.06930353,-0.02182976,0.06922227,0.00847004,0.02113434,-0.0288901,-0.03338323,-0.05211212,-0.007978,0.05837722,-0.05038411,0.1120158,0.00533309,-0.02550659,0.0157854,-0.04161093,0.01859615,0.0157333,0.0111239,0.03929745,0.02879998,-0.10374171,-0.01570531,0.04504318,0.05303892,-0.04450392,-0.02818525,0.03455116,0.02289121,0.0319307,0.09172986,-0.0633396,-0.04016502,-0.04303097,-0.21220069,-0.0129471,0.03221475,-0.04059346,-0.02907192,-0.05105647,-0.01595128,-0.04512022,-0.07830553,0.04168024,0.12056374,0.00286143,-0.01733943,-0.00989855,-0.03709003,-0.00182497,0.02521485,-0.07852062,-0.01860582,-0.01705172,0.01214045,0.01123707,-0.04976537,-0.09116969,0.03325251,-0.02119539,0.12993681,0.01740169,0.05440754,-0.00522884,0.02864381,-0.05121623,-0.01126349,-0.07143453,0.00756078,-0.01478003,-0.00738903,0.00769268,0.01655783,0.02130358,-0.02672791,-0.04021169,-0.01911605,-0.06419652,0.05217101,-0.01909738,-0.06277747,-0.02956688,0.03158739,0.05733524,-0.01462966,0.00846796,0.13177682,0.03661346,-0.0114059,0.05164911,-0.03506252,-0.02190061,-0.0193447,0.04515851,0.01467127,0.00340939,-0.02191359,-0.07216059,0.05083651,0.06828202,0.02063825,-0.00251118,0.03837298,-0.00517601,0.009754,0.09519435,0.05289109,-0.02005977,0.02070767,-0.01236637,-0.03227325,-0.0402147,0.01083233,0.01398713,0.00270716,-0.07644217,0.04066158,0.05069746,-0.03919041,0.03198652,0.0509935,0.00256744,0.04168908,-0.06654295,-0.04326072,0.01266863,-0.03813779,-0.03774412,0.06906322,0.00932552,-0.27893567,0.0280524,-0.02654494,-0.0356655,-0.01783783,-0.01484893,0.03859389,-0.01872434,-0.01980449,-0.03090881,-0.03827118,0.07457955,0.00439745,-0.06126923,0.00041639,0.07091949,0.06162913,-0.01107538,0.08554751,-0.03880668,0.05303332,0.03768387,0.24191643,0.00210534,0.02694531,0.05649163,-0.01925316,0.04978338,0.06231774,0.03727897,-0.03786315,0.00940075,0.13940366,0.02071645,-0.02430963,0.02660497,0.01323131,0.00386329,-0.01402867,-0.06200669,-0.01426451,0.00428225,-0.01161366,0.0203968,0.10430226,-0.04361205,-0.00819486,-0.10639403,-0.00452561,0.03568907,-0.08674683,0.07993577,0.02108727,-0.01595482,0.0017569,-0.02960632,-0.01241706,-0.01813399,-0.04520177,0.00711158,-0.01504839,-0.08265322,0.06879745,0.05187635,-0.02465729],"last_embed":{"hash":"oq51gu","tokens":403}}},"text":null,"length":0,"last_read":{"hash":"oq51gu","at":1751288819300},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode","lines":[561,766],"size":6234,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"oq51gu","at":1751288819300}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04243708,-0.02754988,0.04451001,-0.09948022,0.02665173,-0.02176874,-0.06072085,-0.03808214,-0.04457385,0.02409608,-0.02228108,-0.05783721,0.05444739,0.04157462,0.03518301,-0.01701457,-0.04020289,0.07692821,-0.01512196,-0.01299535,0.03942753,0.00977794,0.00781786,-0.04656835,-0.01685122,0.11248917,0.04114196,-0.05186923,0.01293087,-0.18191794,-0.01251558,-0.00187012,-0.03698541,0.00514519,-0.00299301,-0.00929987,0.00258854,0.00149522,-0.02208013,0.03010601,0.04283632,0.02414181,-0.0387793,-0.00986932,-0.03819145,-0.02049929,-0.03234453,0.02049091,-0.02386162,-0.02858733,-0.01550393,0.00584992,-0.00612421,-0.01379511,0.0411852,0.07495347,0.04389657,0.11579484,0.01931081,0.0768124,0.00685868,0.04118859,-0.16819811,0.01720939,0.04674961,0.06026213,-0.0356061,0.01566847,-0.00308638,0.00390724,-0.01274128,0.01049925,0.02957439,0.03877721,0.02453779,-0.04986256,-0.02095702,0.02608288,-0.01286722,-0.01814963,-0.07851108,0.00609514,0.0216257,0.05037016,0.03347428,-0.01328602,0.03851974,0.00128443,0.10240985,0.03319383,-0.03245172,-0.07300733,0.01531755,0.10379758,-0.01543094,0.00705364,0.03743138,0.04358842,0.0403894,0.14256451,-0.0590847,0.0015409,0.01480425,-0.00298733,-0.03757197,-0.02151656,-0.01527606,-0.0504164,-0.06025504,0.00122314,-0.04779523,-0.01187853,-0.05343208,-0.04499236,-0.0253001,-0.02180329,0.02132668,0.00404642,-0.0271592,0.0232735,-0.00321676,0.08279969,-0.00457371,0.04568966,0.06858209,-0.00214627,0.0103501,0.02741029,0.00883952,0.06567543,0.03224717,0.10236519,-0.06057521,0.01990991,-0.0039656,-0.02807352,0.01843198,-0.03252536,0.0163324,0.01800235,0.02125452,-0.02283451,-0.01292346,-0.03136664,-0.00863578,0.11595955,-0.02902145,0.09206677,0.01231438,-0.01447635,-0.03108644,0.03899647,-0.08875177,-0.01738022,-0.01368907,0.01351351,0.00675419,0.01572872,-0.05218873,0.01552214,-0.00468838,-0.03239026,-0.03816092,0.12174932,-0.00333934,-0.0547886,-0.04402899,0.04916666,0.06807896,-0.06880188,-0.05718464,0.00275796,0.0216775,-0.03234102,0.01237943,-0.01059614,-0.06956618,-0.01843009,0.06158603,0.00674914,0.02705124,-0.0541346,-0.06661438,0.02593491,0.00393332,-0.05903318,0.01009877,-0.04871457,-0.05194257,0.07979042,-0.01423392,0.05285702,-0.00015989,0.01107795,-0.01630303,-0.0413336,-0.05511901,0.00148187,0.04586915,-0.050382,0.0777724,0.00969213,-0.03232789,0.02307695,-0.01833602,0.01870307,0.01724353,0.01276514,0.02092016,0.03240248,-0.11275708,-0.01892783,0.05997789,0.07430314,-0.05675939,-0.04112264,0.03721819,0.05504833,0.01141843,0.06856854,-0.06093877,-0.05122443,-0.04501582,-0.21556064,-0.02862016,0.0233673,-0.02820123,-0.02581895,-0.06503102,-0.01505651,-0.04586072,-0.0889348,0.04385564,0.11501061,0.00390825,-0.0079848,-0.00784036,-0.02764803,-0.01110238,0.01323097,-0.07267078,-0.03357786,-0.01697444,0.02591215,0.01215525,-0.03081485,-0.09752423,0.03861446,-0.0041607,0.12629545,0.00537506,0.07732679,-0.0018648,0.04818236,-0.06073467,-0.01914581,-0.0835466,-0.0071665,0.01281797,0.03016106,-0.02630549,0.03501064,-0.00242415,-0.02444623,-0.03143714,-0.00376949,-0.07605027,0.0620263,-0.03127665,-0.06380517,-0.06394374,0.02992115,0.03853809,-0.01059982,0.00161232,0.13578111,0.01784354,-0.02451611,0.04297931,-0.05362988,-0.01931264,-0.02487799,0.0356234,0.01289551,-0.00292479,-0.01032649,-0.05646076,0.05484304,0.05881862,0.01692176,-0.01450587,0.03198088,-0.00416102,-0.0025537,0.10125662,0.02922068,-0.03006099,0.03015946,-0.00387431,-0.02903887,-0.03922474,0.01450835,0.02178944,0.01244067,-0.04383441,0.0153939,0.05123314,-0.03284449,0.02213357,0.02196642,-0.01215578,0.02906655,-0.0682778,-0.04344188,0.02342505,-0.05039576,-0.01722271,0.06047062,0.00401748,-0.28180674,0.02184161,0.00364237,-0.02125357,-0.03519634,-0.01352898,0.05683642,-0.02835109,-0.01397328,-0.01522765,-0.02633419,0.08804671,-0.01832695,-0.06142851,0.01737512,0.08613893,0.098919,-0.00081878,0.08975769,-0.05751644,0.03878638,0.04172757,0.25473642,0.00013123,0.0283924,0.0756914,-0.02784429,0.04933443,0.04440372,0.03517932,-0.02471248,0.03342409,0.11901404,0.00840118,-0.02224435,0.02824228,0.0261762,0.01815504,0.00204837,-0.02569476,0.00679715,-0.00003597,-0.01713094,0.01443669,0.08134303,-0.03860779,-0.04456961,-0.09611134,-0.01325736,0.01947053,-0.08797736,0.05901814,0.00977253,-0.02608768,0.00822197,-0.01178375,-0.0051017,-0.02206385,-0.04394026,0.0265555,-0.01004463,-0.08398203,0.04797754,0.05061273,-0.02774855],"last_embed":{"hash":"454nzi","tokens":104}}},"text":null,"length":0,"last_read":{"hash":"454nzi","at":1751288819521},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#{1}","lines":[563,568],"size":366,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"454nzi","at":1751288819521}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09377331,-0.05778212,0.02624039,-0.07619067,0.00895143,0.00599964,-0.04970149,-0.02301434,-0.01259095,0.01976117,0.03302283,-0.06044144,0.04525908,0.01417793,-0.01284409,-0.05548923,-0.00507597,0.06402008,-0.02509517,0.00045472,0.0296416,-0.03657477,0.03833723,-0.03586571,0.02526465,0.09217378,0.02525643,-0.04365453,0.02603465,-0.20717143,-0.03755288,0.00409029,0.00315937,-0.00483104,0.03000531,-0.04942227,0.01608524,-0.01457151,0.00132188,0.05097471,0.01492451,0.00629794,-0.04142938,0.00229949,-0.01958598,-0.0078536,-0.03448699,0.02604904,0.02490232,-0.01985882,-0.01978824,0.03458097,-0.00124675,0.0008044,0.03136144,0.07138709,0.05568931,0.10622918,0.024166,0.05888778,0.02693007,0.03433558,-0.1824117,0.04296482,0.08034939,0.01717488,-0.05154273,-0.02718593,-0.00176256,-0.00449857,-0.01815433,0.03608805,0.01234464,0.06774358,0.03497209,-0.02851623,-0.04691246,0.05425803,-0.02223297,0.01011602,-0.08827664,0.03342222,-0.00270018,0.04418645,-0.02507758,0.01188432,0.06731934,-0.00098016,0.06292772,0.01968291,-0.02274393,-0.04900821,0.03355416,0.09590831,-0.04793776,-0.02530851,0.04914016,0.06033935,0.01032518,0.12389787,-0.06567818,-0.00616192,-0.00216372,0.06578536,0.01629846,-0.02466952,-0.02628241,-0.06872481,-0.02767638,0.02562908,-0.02875775,0.00261622,-0.04804523,-0.0917929,0.01118096,-0.00506697,0.02247513,-0.03759856,-0.06732357,0.02660396,0.00166373,0.03993342,-0.00246666,0.02356806,0.01231773,0.0256924,0.01994732,0.05072906,0.01198777,0.08622847,0.08009251,0.04874495,-0.07381975,0.00346713,-0.01360769,0.00129883,0.01696944,0.00605469,0.01044355,-0.03178566,0.02049397,-0.09597173,-0.0070252,0.01504622,-0.04772737,0.11555658,-0.0507045,0.06476041,0.01605777,0.01641816,-0.02617603,0.00519028,-0.10863205,-0.02186703,-0.03655721,-0.01770209,0.03756106,0.00468976,-0.07661982,0.01309029,0.02050103,-0.04348755,-0.02562872,0.09166619,0.01408187,-0.05277419,-0.06681561,0.04108986,0.02129674,-0.06702699,-0.0240738,-0.0186937,0.00523723,-0.01689869,0.04891958,0.0164562,-0.05519341,-0.0611659,0.03562441,0.02463053,-0.01468518,-0.01770712,-0.03267603,0.00343893,-0.01591982,-0.03696645,0.00199514,-0.03101752,-0.0242463,0.03668722,-0.03103669,0.08903856,0.01840869,0.02776415,-0.03881167,-0.0156106,-0.04988608,-0.01243964,0.05598259,-0.04884753,0.11678399,0.00057827,-0.02000419,0.02077774,-0.05241856,0.01105439,0.00880436,0.00961655,0.02270305,0.02189857,-0.10303176,-0.0285824,0.02435491,0.04591239,-0.02926529,-0.03707295,0.02470439,-0.00472841,0.04618025,0.0922714,-0.04954527,-0.0259347,-0.04827354,-0.22140972,0.0039527,0.02401995,-0.06248605,-0.03022633,-0.01987322,-0.00397689,-0.0371281,-0.05128921,0.04206821,0.13118014,-0.00766104,-0.02726768,-0.00669671,-0.03722518,0.01917055,0.00950591,-0.0848559,-0.01544365,-0.0215157,-0.00760024,0.00884369,-0.06422995,-0.07507093,0.02935575,-0.01762085,0.12917589,0.03297057,0.03957574,-0.00930161,0.01697333,-0.04744597,0.00076823,-0.06309737,0.00763574,-0.02249631,-0.03526691,0.03746615,0.00061524,0.02634444,-0.02689964,-0.04080224,-0.02836779,-0.06568283,0.04851297,-0.00712864,-0.04886176,-0.00419278,0.02750233,0.07152139,-0.01376725,0.02060256,0.10020249,0.08216166,-0.00074877,0.05903126,-0.01911111,-0.00450822,-0.00961815,0.04071459,0.01201088,0.01988235,-0.03120198,-0.07808289,0.05742691,0.05861503,0.00217097,0.00444837,0.05343101,0.0015824,0.01920941,0.08584072,0.07294622,-0.02062614,0.00483129,-0.02183339,-0.0313024,-0.04121294,0.00784909,0.0155499,0.00278416,-0.09740573,0.03909943,0.03508068,-0.02592361,0.05545773,0.06212799,0.01284554,0.04915829,-0.05274753,-0.02422778,-0.01113067,-0.02632608,-0.04593503,0.08500712,0.00547489,-0.27167615,0.04052164,-0.03190066,-0.03997995,-0.02045571,-0.01988743,0.01546516,-0.01040264,-0.0274557,-0.0399247,-0.03717716,0.05362586,0.02203006,-0.05146813,-0.02033112,0.0579541,0.03530794,-0.01453437,0.08627903,-0.04291906,0.05114303,0.03581976,0.23490362,-0.00209393,0.02134143,0.02860542,-0.00956122,0.04747926,0.05376083,0.06496578,-0.02532051,-0.00443937,0.14304899,0.0346113,-0.01884519,0.03196898,0.0148939,0.01350494,-0.02266142,-0.08299372,-0.04203181,0.02367746,-0.03663851,0.0248703,0.11144307,-0.05903852,0.01273262,-0.11526562,-0.00191531,0.04270085,-0.05848295,0.08358449,0.01518157,0.01003415,-0.01126922,-0.03751986,-0.01025181,-0.02201424,-0.03097979,-0.00463634,-0.00612009,-0.06164093,0.08647561,0.04292647,-0.01736962],"last_embed":{"hash":"1gw13g4","tokens":320}}},"text":null,"length":0,"last_read":{"hash":"1gw13g4","at":1751288819576},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])","lines":[569,592],"size":855,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1gw13g4","at":1751288819576}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07695265,-0.05555994,0.05429576,-0.0736132,0.0194099,-0.00200368,-0.06833304,-0.02633086,-0.00166145,0.03033541,0.02093105,-0.04424382,0.01317902,0.0417007,0.02123535,-0.03286059,-0.02030219,0.08006436,-0.03149405,-0.00569129,0.03690213,0.00545733,0.00755085,-0.04955355,0.02733411,0.11149741,0.04346084,-0.04842955,0.03453077,-0.17644781,-0.01951171,0.02381631,-0.03345394,-0.00833151,0.01598815,-0.06145892,0.01824017,-0.00556073,-0.02122673,0.0725394,0.0422944,0.02429063,-0.05686659,-0.00315365,0.00247738,-0.03425234,-0.02636348,0.0028126,0.01738466,-0.01942928,-0.02853455,-0.00619925,0.02909533,-0.04159595,0.03720037,0.07774729,0.03999374,0.08128157,0.02695736,0.03644158,0.01796977,0.04273076,-0.16996856,0.0262044,0.06478583,0.02564685,-0.03578752,-0.01337942,-0.00227713,-0.00635999,0.01403534,0.03524901,0.01994693,0.06901246,0.0283514,-0.0435768,-0.02346062,0.03129982,0.00596894,-0.00247022,-0.08080892,0.03658216,-0.00049817,0.05827573,-0.00308643,0.0117962,0.04388833,0.01651083,0.06869926,0.02472994,-0.00912627,-0.07782298,0.03473853,0.11282898,-0.04073773,0.00115368,0.01974039,0.06341239,-0.01262846,0.13664079,-0.0593808,-0.00890694,-0.01266079,0.01402644,0.01720598,-0.02851589,-0.03654526,-0.06471937,-0.04204878,0.0214845,-0.04194073,0.00470764,-0.05533076,-0.09002129,-0.03058742,-0.0072942,0.00842659,-0.00456181,-0.07466283,0.03386078,0.01039035,0.06123815,-0.00144048,0.02559196,0.02872407,0.00653982,0.03289609,0.03704212,0.02489411,0.11677042,0.06708608,0.07782145,-0.08466621,0.01556095,-0.00574506,0.00089385,0.0085527,-0.01998102,0.00164885,-0.03027832,0.03668186,-0.04155152,-0.02837242,-0.04281482,-0.01947939,0.10350237,-0.06908848,0.07068326,0.01531013,-0.00016024,-0.03489625,0.00277231,-0.09060799,-0.01653698,-0.0358851,-0.03234044,0.02026423,-0.01087917,-0.03445265,0.0106018,0.00940466,-0.01838799,-0.00546333,0.09852608,0.0006743,-0.07076439,-0.05813583,0.05922808,0.04839303,-0.07057901,-0.04208549,0.01448967,0.00631226,0.00242687,0.0397008,0.00606008,-0.05188687,-0.05405222,0.01335771,0.02523537,-0.02579692,-0.03790201,-0.03791014,-0.00137111,-0.02604733,-0.04142372,0.02916344,-0.05339173,-0.02785714,0.03851289,0.00477316,0.09102833,0.00357464,0.00878724,-0.04560536,-0.03404994,-0.05111845,0.02501952,0.07259265,-0.04709697,0.12388443,0.01078605,-0.00423591,0.05137571,-0.06701715,-0.0102211,-0.01817143,-0.00889938,0.01417077,0.03479631,-0.10227648,-0.02729004,0.0406165,0.05269207,-0.01650487,-0.04157139,0.01066079,0.03410606,0.01292954,0.08635929,-0.06982902,-0.02835199,-0.05891597,-0.20138237,0.02003268,0.02414829,-0.06326313,-0.02903417,-0.03734994,-0.01652158,-0.03227803,-0.05396475,0.04194395,0.1466967,-0.02210952,-0.04975573,-0.00927041,-0.00345819,0.0077818,0.01063973,-0.08960205,-0.02535535,-0.00502083,0.01092378,0.00411269,-0.07374933,-0.06493431,0.00982281,-0.02794096,0.12567492,0.0306972,0.04242157,-0.00992084,0.01945543,-0.01125539,0.00119766,-0.10162684,0.00033982,0.01050019,0.01374523,0.04444526,0.01982288,0.01362867,-0.01495592,-0.02656324,-0.03650017,-0.06163612,0.06935951,-0.03490573,-0.05219251,0.00414781,0.03021769,0.06825185,-0.00743078,0.04026061,0.06146113,0.05714318,-0.04046069,0.03574782,-0.01788816,-0.01853758,-0.0121332,0.03318594,0.0033967,-0.01540685,-0.04773576,-0.07751127,0.06960359,0.06703314,-0.00644192,-0.02386604,0.06532266,-0.0387782,-0.01068045,0.07167496,0.07136896,-0.03611242,0.01009467,-0.00840907,-0.04898673,-0.02665255,0.05281731,0.02126543,0.01722084,-0.0679676,0.01573316,0.02396799,-0.02091313,0.03772573,0.00426193,0.01217079,0.04371189,-0.06696332,-0.03099795,-0.01280699,-0.03017434,-0.02403186,0.10652519,0.0078096,-0.269407,0.03861993,0.02102955,-0.0618884,-0.02508754,0.02179404,0.01813874,-0.05548086,-0.04230416,-0.01875538,-0.02362131,0.03993719,-0.00713617,-0.0212277,0.01002549,0.06777069,0.04722687,0.01094838,0.08609922,-0.03630519,0.03851197,0.05749146,0.23883826,-0.04361171,0.03018256,0.05501909,-0.04696949,0.04691051,0.04559753,0.05630047,-0.02493117,0.01962219,0.13824408,0.01528706,-0.02651199,0.01947981,0.01743615,-0.02623352,0.00715027,-0.02885082,-0.01109775,-0.00295632,-0.03078167,0.04087395,0.10386216,-0.09377068,-0.03424451,-0.11626146,-0.02002411,0.03822596,-0.073714,0.07286599,0.02415323,0.01043387,-0.00072354,-0.011397,-0.0055089,-0.02624996,-0.02729968,-0.00576697,-0.00328848,-0.05715216,0.08635186,0.05452041,-0.00643342],"last_embed":{"hash":"97mhxd","tokens":200}}},"text":null,"length":0,"last_read":{"hash":"97mhxd","at":1751288819775},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{8}","lines":[583,592],"size":307,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"97mhxd","at":1751288819775}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07669375,-0.04638673,0.06195479,-0.08913604,0.01233602,-0.02198648,-0.09629346,-0.02928523,-0.01065759,0.03018721,0.02425651,-0.0485174,0.01681864,0.04194988,0.03112309,-0.03666195,-0.04233462,0.05531932,-0.03096681,0.00070674,0.04157072,0.01353304,0.00542778,-0.04600335,-0.00783929,0.11374224,0.0350439,-0.07020964,0.03267042,-0.16881382,-0.02226651,0.0190378,-0.03377965,-0.00708658,0.01392942,-0.05381421,0.01716221,-0.02944129,-0.02471158,0.07176334,0.04337411,0.04379276,-0.06386922,-0.0034213,0.0111849,-0.02966923,-0.04139291,0.00959479,0.02746355,-0.0401644,-0.02296213,-0.00425271,0.0262472,-0.04547841,0.04381854,0.070384,0.0433999,0.09666438,0.01656563,0.04076809,0.00460051,0.03455277,-0.17054436,0.0230279,0.06232693,0.02888371,-0.03453568,-0.00342623,0.01079872,-0.00420045,0.00997591,0.02537881,0.01937424,0.0684333,0.03011522,-0.04297113,-0.02398602,0.01935977,-0.00628753,0.00132045,-0.08344112,0.04323062,-0.00113147,0.05827917,0.00491326,0.01229774,0.02938209,0.02188782,0.07308163,0.01779753,-0.00903959,-0.07152358,0.02786186,0.10896572,-0.03979063,-0.01319948,0.03279628,0.05571,0.00155794,0.13132693,-0.05577339,-0.00263684,-0.00731637,0.01371184,0.00928832,-0.01218096,-0.01276878,-0.05023528,-0.05606011,0.02162842,-0.04560265,0.00444985,-0.05653818,-0.08535477,-0.03011551,-0.01937737,-0.00034127,-0.00257461,-0.08149188,0.04364798,0.00849687,0.07384591,-0.00672324,0.02892777,0.04477784,0.00416842,0.04311341,0.03555386,0.02539614,0.1198617,0.06157237,0.08377312,-0.08755781,0.00938823,-0.01298797,0.00050449,0.02147306,-0.02727575,0.01344026,-0.0310298,0.03478853,-0.04638934,-0.02371144,-0.04667508,-0.02319147,0.10397523,-0.06023373,0.06757622,0.0100788,-0.00058729,-0.03840604,0.02155691,-0.07333751,-0.02954029,-0.02640195,-0.03554657,0.0156095,-0.01073221,-0.02947953,0.00866249,-0.00012462,-0.02314728,-0.01059583,0.08785408,0.00317168,-0.06408574,-0.04409117,0.05364154,0.05484311,-0.08042081,-0.042199,0.01316541,-0.00877048,-0.00393321,0.05131209,0.01352116,-0.05449799,-0.0456113,0.02377313,0.02097344,-0.00730792,-0.02162147,-0.04369027,0.01813285,-0.0238048,-0.03468898,0.03668682,-0.05752744,-0.02785089,0.03471965,0.00108664,0.08252425,-0.01237552,0.01343477,-0.05143484,-0.03978508,-0.04188422,0.03107429,0.06711262,-0.01280294,0.11271337,-0.00177235,-0.03233475,0.04369722,-0.07621901,-0.00394642,-0.01599235,-0.01299607,0.00547738,0.04418503,-0.09840254,-0.01369344,0.05304312,0.05085416,-0.02243793,-0.04540184,0.01836768,0.04755954,0.00977664,0.09753137,-0.08328999,-0.02885463,-0.06997427,-0.20146364,0.0121459,0.02428268,-0.06950724,-0.02979873,-0.02186139,-0.01924821,-0.02613748,-0.04529418,0.04844198,0.15539211,-0.03458831,-0.0304514,-0.02657373,0.00890764,0.00244096,0.00791474,-0.08614209,-0.02075162,-0.01085061,0.0114979,-0.00620382,-0.06408046,-0.07422983,0.03779584,-0.04106045,0.12519041,0.02147146,0.02788534,-0.01871363,0.03453977,-0.02816653,-0.00672663,-0.10779234,-0.00269594,0.01398389,0.02243861,0.05391391,0.0170511,0.02232135,-0.01627428,-0.00941064,-0.04246745,-0.05186296,0.06355099,-0.03370333,-0.0431497,0.00687647,0.02928334,0.05994204,-0.01867635,0.02127176,0.06855864,0.05598295,-0.04373943,0.01741219,-0.01534713,-0.01531186,-0.0155287,0.03185296,-0.0037396,-0.0127619,-0.04290313,-0.06992359,0.08006005,0.06812476,-0.00872325,-0.02171103,0.04877631,-0.02870314,0.00195622,0.06138564,0.05440952,-0.01906142,0.03550319,-0.0205457,-0.05797223,-0.0289022,0.06020138,0.01303316,0.02295527,-0.06616805,0.00602168,0.02315244,-0.02474853,0.02277142,0.00835926,0.01724247,0.03529409,-0.06820548,-0.01413687,-0.02054489,-0.02270327,-0.01360971,0.09263762,0.02011949,-0.27096036,0.04586776,0.02859892,-0.05339884,-0.04566645,0.02146913,0.02873697,-0.0543982,-0.04540902,-0.01302364,-0.03161442,0.05457319,-0.00079159,-0.01880402,0.0156011,0.06417333,0.04585034,-0.00132186,0.09744575,-0.04034645,0.03916756,0.05127535,0.23503159,-0.0271743,0.03605194,0.06700505,-0.04982084,0.04527832,0.05293284,0.06967676,-0.0134671,0.0215004,0.12861976,0.01837625,-0.03892387,0.01962671,0.02152844,-0.01553725,0.00959624,-0.0251048,0.00702517,0.00652419,-0.02538032,0.03273958,0.10628752,-0.08218693,-0.03393522,-0.11345388,-0.02089813,0.03314206,-0.07422539,0.06688453,0.04170676,-0.00767025,-0.01884135,0.00742845,-0.00777976,-0.0214867,-0.03102193,-0.00389275,-0.00410176,-0.06793654,0.06024393,0.05329603,-0.00421868],"last_embed":{"hash":"1wg8cvr","tokens":187}}},"text":null,"length":0,"last_read":{"hash":"1wg8cvr","at":1751288819870},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#new SourceNode([line, column, source[, chunk[, name]]])#{9}","lines":[585,592],"size":261,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1wg8cvr","at":1751288819870}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05579595,-0.06004091,0.02712637,-0.08247367,0.02421392,-0.01926439,-0.0824995,-0.01517284,-0.07157152,0.04254924,-0.00260401,-0.04807269,0.02297047,0.02852851,0.04236237,-0.02094523,-0.0219695,0.06914567,-0.01472293,0.01463742,0.08244838,0.00062432,0.040508,-0.04860092,0.01689231,0.11497916,0.04448368,-0.0334686,0.0250823,-0.15869534,-0.00271775,-0.02281391,0.00054868,0.02675031,-0.01217435,-0.02010676,0.00306369,-0.00362793,-0.02482402,0.03835704,0.01769255,0.02643421,-0.04949696,-0.00550117,0.01021025,-0.02562516,-0.01654959,0.01733905,0.00095876,-0.04992995,-0.02775384,-0.00961902,-0.01648498,-0.00857976,0.02737056,0.05318357,0.05349465,0.09646421,0.00809538,0.05719624,0.00267864,0.05018691,-0.17663452,0.00581116,0.0762353,0.05015205,-0.0320101,0.00460738,0.00430965,-0.02166732,-0.02526747,0.03227087,0.02522384,0.02821814,0.02806572,-0.06813877,-0.01161909,0.0349394,0.01921114,-0.00351462,-0.093499,0.05165861,-0.03495884,0.04707201,0.00811042,-0.00461199,0.01984525,0.01666848,0.06580608,0.01733889,-0.03121082,-0.07616886,0.03837859,0.10282709,-0.03707392,-0.02672283,0.00155558,0.05594786,0.00267898,0.13494165,-0.05893892,0.0197158,-0.03658186,0.00931882,0.01430095,-0.02744716,-0.01366017,-0.01505897,-0.05097733,0.04072971,-0.0210743,0.00090939,-0.0843102,-0.06084276,-0.00377692,-0.01248061,0.04035934,0.01334389,-0.06844223,0.05951916,-0.00209574,0.05549419,0.00286116,0.00427339,0.01202221,-0.00500489,0.03533171,-0.00236546,0.01566488,0.06464709,0.02639523,0.05236153,-0.08817858,0.03003247,0.01686948,-0.03000548,-0.00197879,-0.02036031,-0.01235661,-0.02548187,0.02835244,-0.04420051,-0.00911207,-0.0435369,-0.00343708,0.08337639,-0.02833285,0.08018015,0.00653983,-0.01793212,-0.0027776,0.00554139,-0.07003465,-0.01728549,-0.01174995,0.02419859,0.01316645,0.00014645,0.00170601,0.01497054,0.02012285,-0.03978022,-0.002085,0.09575193,0.00126867,-0.08131211,-0.02997958,0.03581645,0.06057502,-0.0473926,-0.02503008,0.02660126,0.01311186,-0.03290682,0.04128175,0.01321973,-0.0604885,-0.0440973,0.02770169,0.00872036,-0.00462883,-0.04224664,-0.05077548,0.00786096,0.00115691,-0.04726108,0.01913061,-0.04207056,-0.05849725,0.04703872,-0.00085812,0.03880101,0.01450555,-0.0134779,0.00539125,-0.06004578,-0.04323463,0.02891217,0.05026872,-0.0369035,0.1257858,0.02043483,-0.01423179,0.06152488,-0.08370006,-0.00022879,0.01026292,-0.00424943,-0.00117889,0.0171341,-0.09890959,-0.05101231,0.07808526,0.06951849,-0.07407799,-0.03911997,0.03800641,0.0526765,0.03997487,0.06379492,-0.09094534,-0.02601161,-0.06993341,-0.19604357,0.03346564,0.00404578,-0.04322661,-0.03715275,-0.03329555,-0.04746316,-0.04383923,-0.05378634,0.01279301,0.17100008,-0.01906882,0.02747742,-0.00200522,-0.02095345,0.01868437,0.00886458,-0.06196873,-0.03936515,0.01663341,-0.00498278,-0.00663475,-0.03426539,-0.08100267,0.01966115,-0.01027309,0.13647515,0.02548626,0.05625252,-0.00522608,0.0520501,-0.03799201,0.00395372,-0.0848669,-0.04035939,0.00071556,0.01158989,0.01033445,0.04964563,0.03022186,-0.03235515,-0.01196703,-0.00479029,-0.06352632,0.03628423,-0.04193627,-0.05465679,0.00130876,0.02800661,0.05507628,-0.01716636,-0.00281699,0.13373354,0.03287074,-0.03687629,0.00253465,0.00785004,-0.0149066,-0.01746571,0.07921592,0.03196664,0.00607172,-0.01067313,-0.05206415,0.04895272,0.03452402,0.00031366,-0.00863666,0.08828192,0.00038513,0.01505775,0.07509787,0.01317904,-0.00359206,0.02360469,-0.03443064,-0.04358974,-0.06980476,0.02235879,-0.02305441,-0.00162453,-0.04551204,0.0106089,0.05878085,-0.03557322,0.00027949,-0.02742733,0.03045223,0.05234367,-0.04684247,-0.04595575,0.00887747,-0.06114855,-0.02153567,0.07699218,0.01920061,-0.26451537,-0.0174693,0.00873728,-0.03702579,-0.05767928,-0.04628364,0.1170826,0.00328457,-0.01928328,-0.04639709,0.00943752,0.06023041,-0.02305605,-0.04889301,0.03906948,0.05986834,0.06694262,-0.00634324,0.11367231,-0.06585401,0.03229489,0.05064502,0.24782646,-0.01891541,0.03045436,0.06903413,-0.05030441,0.05132603,0.06779734,0.04656199,0.00027408,0.03916436,0.12917767,-0.01596488,-0.00240701,-0.0080675,0.02278023,-0.0016264,0.00226854,-0.02186,-0.0192462,-0.01727968,-0.02390055,-0.00359354,0.10641004,-0.04488611,-0.07328073,-0.10944716,-0.02207037,0.0168781,-0.08983163,0.0583846,0.0093006,-0.01039208,-0.00956689,0.02104754,-0.00774572,-0.0277595,-0.04365642,-0.03098834,0.0138195,-0.07924362,0.08733235,0.06150961,-0.01645318],"last_embed":{"hash":"udt7z4","tokens":224}}},"text":null,"length":0,"last_read":{"hash":"udt7z4","at":1751288819954},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])","lines":[593,609],"size":586,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"udt7z4","at":1751288819954}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04552869,-0.06727201,0.02904035,-0.08686087,0.03769972,-0.04502489,-0.07921855,-0.01296623,-0.04509921,0.00698667,0.00081162,-0.06262551,0.0022118,0.03373457,0.03732185,-0.03987051,-0.00235757,0.07528436,-0.02051492,0.03853035,0.08294738,0.01125547,0.03637366,-0.02618501,0.01197059,0.11793839,0.03763714,-0.04180942,0.01398462,-0.14611711,-0.0071374,-0.05562038,-0.03865856,0.02525483,0.0065322,-0.0249695,0.0146441,-0.03234849,-0.01253063,0.02907073,0.02378651,0.03229025,-0.06570407,-0.02211632,0.00873111,-0.03755023,-0.02799003,0.02066928,0.00621422,-0.05826522,-0.02515738,-0.00711986,-0.01100568,-0.02092781,0.02273519,0.08200447,0.06237941,0.10046511,0.02909229,0.05744839,-0.00072855,0.04382433,-0.18222438,0.03240599,0.07315399,0.0529956,-0.02909238,-0.00163825,0.00387552,-0.00121918,-0.02791716,0.03395169,0.00670813,0.0338755,0.02058527,-0.06702545,-0.00157124,0.02830338,0.01608871,-0.01298192,-0.07465973,0.05318751,-0.00549902,0.0369363,0.01090087,-0.00447766,0.01679,0.01350475,0.06087024,0.02175952,-0.02974051,-0.05544412,0.04867567,0.10898425,-0.03538777,-0.0249684,0.02748991,0.05535931,0.00295526,0.12409332,-0.06501283,0.01745809,-0.03145424,0.02077025,-0.00673507,-0.02243949,-0.04158971,-0.01510016,-0.04434744,0.03783593,-0.03497123,-0.0129906,-0.0931364,-0.04311096,-0.00415064,0.00934492,0.03251123,0.02623167,-0.06031521,0.0477167,0.02095418,0.04925455,0.01519264,0.02664379,0.00500974,-0.00555136,0.06558638,-0.0075241,0.01182865,0.07542569,0.0201028,0.03791276,-0.09737388,0.01854474,0.00573421,0.0162946,0.00992156,-0.03456841,-0.00647773,0.0016541,0.01181896,-0.07811861,-0.00235132,-0.03330088,-0.00517178,0.09589504,-0.02228798,0.06576502,0.00235928,-0.02492372,0.0080786,0.03504651,-0.07970492,-0.00599569,-0.02539142,0.00486918,0.01035442,-0.0095236,-0.01833248,0.02090832,0.01129921,-0.03770463,-0.00596638,0.09289103,0.01201811,-0.06393044,-0.04674901,0.02235996,0.04628365,-0.06451437,-0.02419144,0.00968046,0.00580345,-0.02557343,0.04525287,0.02701809,-0.0553864,-0.0615581,-0.00095665,-0.00452103,-0.0052484,-0.02944872,-0.04429994,0.00751155,0.01436538,-0.04129836,0.01711722,-0.048701,-0.03399007,0.02634416,-0.00213164,0.0270218,0.02932161,-0.01435284,0.00170693,-0.04103563,-0.04086772,0.03695846,0.03511727,-0.03251151,0.15186033,0.04388512,-0.03011163,0.06511147,-0.08610032,0.01817771,-0.00497745,0.00287236,0.00020069,-0.00444101,-0.09498037,-0.04277448,0.06800272,0.07541973,-0.06511625,-0.04197082,0.05306663,0.05638266,0.05359975,0.07830867,-0.09108995,-0.01470541,-0.07920142,-0.20589641,0.03988134,-0.00082357,-0.02790823,-0.03163584,-0.02040201,-0.03991411,-0.03233944,-0.04680296,0.03977805,0.15518104,-0.02786435,0.02177531,-0.00328497,-0.0155707,0.03273886,-0.00141083,-0.06761407,-0.05216711,0.0217774,-0.00949186,-0.03450477,-0.06180447,-0.07188158,0.04747751,-0.00342618,0.14226642,0.01701592,0.0317643,-0.03030205,0.06116654,-0.02015344,0.00637048,-0.09047084,-0.04217628,0.00158765,0.01496927,0.01008486,0.03684927,0.03309152,-0.03579371,-0.00341251,0.00433603,-0.06716221,0.02726194,-0.01744646,-0.04617787,0.00857979,0.0254772,0.04376211,-0.02428774,0.02123953,0.10314891,0.04932931,-0.03440213,0.01417237,0.01641846,-0.02005819,-0.00676893,0.06219579,0.01959131,0.01461706,-0.02031256,-0.05156002,0.07006215,0.02382774,0.00314483,-0.01639699,0.07566474,0.03162964,-0.01445086,0.03575211,-0.00404174,0.00887055,0.01920426,-0.01080742,-0.05327967,-0.03090775,0.02318104,-0.01245909,0.00945219,-0.03164557,-0.00249735,0.03704346,-0.02643207,0.00997454,-0.01285709,0.01414458,0.04052289,-0.04847168,-0.04019585,-0.00142224,-0.06798118,-0.0287775,0.07942066,0.01303698,-0.25815901,0.00167547,0.03262731,-0.02696077,-0.07589455,-0.04789756,0.10624722,-0.02607902,-0.01907162,-0.02340666,0.0093288,0.05720113,-0.03282176,-0.02494152,0.03389133,0.0548418,0.06034904,0.02124375,0.12716787,-0.07584973,0.05187228,0.05567335,0.25826612,-0.03426072,0.01018989,0.0757305,-0.06571022,0.06398977,0.06812163,0.05800792,0.0028901,0.02668436,0.11931525,-0.00301381,-0.00521325,-0.03027856,0.02259095,-0.01559203,0.0168139,-0.01726846,-0.01159024,-0.00024399,-0.04376328,-0.00939838,0.11471567,-0.05590892,-0.07993864,-0.11422157,-0.03394501,0.00722193,-0.07295277,0.03129857,0.01845516,-0.01891208,-0.01855981,0.01224668,-0.00747853,-0.03615228,-0.04281611,-0.04420936,0.01494573,-0.06572727,0.08029074,0.02872814,-0.01800273],"last_embed":{"hash":"sfekk8","tokens":159}}},"text":null,"length":0,"last_read":{"hash":"sfekk8","at":1751288820041},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])#{5}","lines":[602,609],"size":267,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"sfekk8","at":1751288820041}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.add(chunk)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04006827,-0.05271496,0.05728926,-0.06745115,-0.00128971,-0.01960404,-0.06949521,-0.03699082,-0.02591635,0.01609775,-0.00638816,-0.07110504,0.02182295,0.04422808,0.03676039,-0.03061798,-0.07593735,0.05629556,-0.02746249,-0.03759437,0.05912273,0.01748802,0.03575031,-0.01092049,-0.01487634,0.09702373,0.02036408,-0.066948,0.03088343,-0.14922926,0.00418238,0.00576199,-0.03932076,0.00479613,-0.01752813,-0.02664428,0.01506986,0.02615857,-0.03987184,0.06225107,0.05140202,0.03554844,-0.05596802,-0.02443084,0.00784568,-0.00266238,-0.00567084,-0.00455857,0.01103051,-0.04197382,-0.02944891,-0.03739712,0.02792992,-0.02154356,0.0182733,0.08552005,0.01714444,0.12527066,0.01190558,0.05507306,0.03074049,0.02310722,-0.1390857,0.04659499,0.09359206,0.06140603,-0.04487367,0.00128436,0.00709271,0.00788434,0.02000711,0.04537252,0.0770703,0.05765533,0.02130866,-0.05769061,-0.01627652,0.01836826,0.02540698,0.00728111,-0.11710042,-0.0169247,0.0213811,0.05506403,-0.00305812,-0.01363339,0.03950452,-0.00645522,0.04593538,0.02826311,-0.01246165,-0.06237433,0.01555982,0.07357197,-0.04576982,0.00414802,0.02886377,0.02198392,0.00908299,0.15872276,-0.07395207,0.01308822,0.0040602,0.00626277,-0.01739947,-0.01423319,-0.0338725,-0.05131012,-0.04023408,0.00645971,-0.00234446,0.01364512,-0.0668961,-0.08356598,-0.02121609,-0.02943841,0.01925185,0.01044282,-0.08814985,0.02641435,0.02365467,0.08157511,0.0154676,-0.00581959,0.04895721,0.00805312,0.03316507,0.02933397,0.02122095,0.10002107,0.03058858,0.07932951,-0.06536154,0.03213637,0.0035844,-0.02244215,-0.00221417,-0.01608991,-0.00353479,0.01503626,0.03854497,-0.04139214,-0.03771988,-0.05852433,-0.00444843,0.08004919,-0.02385789,0.08992671,0.0179026,-0.01788771,-0.05126902,0.02506081,-0.08832812,-0.00536434,-0.03442101,0.00828212,0.02627587,-0.00743801,-0.00042545,0.02590943,-0.0123138,-0.0155201,0.0153684,0.0520016,0.01261049,-0.06698512,-0.04417967,0.05971806,0.07007251,-0.08060355,-0.04629947,0.0463666,-0.02592085,0.01294344,0.04992228,0.02831251,-0.06136557,-0.02992739,0.04781839,0.01158975,0.02820855,-0.02335914,-0.04143167,-0.00579494,-0.01085868,-0.05040519,0.01628723,-0.04939923,-0.03179539,0.0437123,-0.02241122,0.0721136,0.01634847,-0.01345364,-0.00871329,-0.06404363,-0.03552317,-0.01005206,0.04594051,-0.0241544,0.10258469,-0.00161763,-0.02060157,0.02119802,-0.04587054,0.02146485,0.00418533,-0.00132384,0.02571632,0.01616562,-0.11030655,-0.04258859,0.08124597,0.08503392,-0.06273942,-0.0463673,0.02553018,0.0385901,0.02381962,0.06183188,-0.09496757,-0.03842576,-0.05246817,-0.19989532,-0.00566097,0.05182045,-0.0511368,-0.07070353,-0.03377355,0.0060804,-0.04347495,-0.08992589,0.03860066,0.12935998,-0.0352204,-0.01493873,-0.03358505,-0.01859727,0.00626222,0.01723172,-0.0601772,-0.02407872,0.02133872,0.01345153,0.01942343,-0.04199808,-0.08296935,0.00931059,-0.04547838,0.13480334,0.02394611,0.03921385,-0.03445149,0.0623941,-0.00833575,-0.01976476,-0.0839102,-0.03242096,0.034103,0.00520345,0.0238713,0.02804526,0.01968471,-0.03295432,-0.01541802,-0.02505569,-0.06299511,0.04521866,-0.03130785,-0.05767306,-0.03815889,0.05724963,0.03154349,-0.02946804,0.01437268,0.08571661,0.04297237,-0.0171746,0.00978671,-0.00264653,0.00629224,-0.00432459,0.07341451,-0.01043634,0.01064972,-0.04173008,-0.07349899,0.07281605,0.05674507,0.00402466,-0.02858409,0.04629267,-0.02886595,-0.00192263,0.08961453,0.00769432,-0.01364267,0.01171607,-0.02849036,-0.07038385,-0.03111241,0.04041193,0.01061542,0.01137597,-0.05603524,-0.02133613,0.05110468,-0.04723198,0.02617532,0.0033924,0.00442751,0.03227723,-0.06100047,-0.05175469,0.00776311,-0.0166163,-0.01417739,0.08317264,0.00231403,-0.26019281,0.04708247,0.01462396,-0.0419956,-0.01490832,0.01175214,0.06672455,-0.02419025,-0.01196343,-0.02575512,-0.0294677,0.05896487,-0.01696222,-0.03775828,0.00543482,0.07459718,0.04911049,-0.00140918,0.09273062,-0.09698446,0.02494587,0.05839005,0.2623809,-0.02369914,0.02882155,0.06575312,-0.06898453,0.03354529,0.02716542,0.06212869,-0.017241,0.05343839,0.11009783,-0.0053894,-0.05439573,-0.01378815,0.04476132,-0.00199733,0.02012092,-0.03132887,-0.02120603,-0.00251338,-0.03375757,0.0655085,0.09210547,-0.05025198,-0.03472565,-0.12428065,-0.03957247,0.03390296,-0.09366522,0.04069255,0.00722235,0.00134732,-0.0104356,-0.01106607,-0.0129122,-0.01300403,-0.03967897,0.04335056,-0.00813002,-0.0384489,0.0556559,0.05815338,-0.00436994],"last_embed":{"hash":"16boso8","tokens":145}}},"text":null,"length":0,"last_read":{"hash":"16boso8","at":1751288820113},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.add(chunk)","lines":[610,622],"size":340,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"16boso8","at":1751288820113}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.prepend(chunk)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04673913,-0.03504415,0.06360052,-0.06450968,0.03875647,-0.02565401,-0.08133392,-0.05447023,0.00823891,0.01385242,-0.03177705,-0.06344313,0.02565481,0.03500039,0.02277304,-0.00456925,-0.05274726,0.04687645,-0.00811248,-0.01816746,0.06838319,0.01683696,0.03427583,-0.04564311,0.01728376,0.08412804,0.03712009,-0.04263959,0.04201045,-0.1557606,0.0220274,-0.00303737,-0.04252279,-0.00726277,0.01781852,-0.04897223,0.02651484,0.01881135,-0.04455968,0.06253419,0.04353208,0.05318767,-0.06325103,-0.03146272,0.01010419,0.01886352,0.01838383,-0.03505067,0.01177159,-0.03603851,-0.04390451,-0.05043299,0.02256897,-0.02183798,0.03102143,0.07928835,0.01996511,0.10098054,0.00177863,0.06744189,-0.00087615,0.02464885,-0.14797987,0.04384728,0.09681584,0.06213097,-0.03507023,0.00195112,0.00623314,0.00770781,0.01575317,0.04796569,0.0543032,0.05686058,0.01983601,-0.02316032,-0.00519307,0.01005047,0.0366159,0.02604785,-0.11748905,-0.01132664,0.01719794,0.04498191,-0.03100507,-0.02351427,0.05178869,-0.00118439,0.05018425,0.01239105,-0.0293235,-0.07461184,0.00615779,0.07733008,-0.03236324,-0.00286149,0.05019352,-0.00097652,0.00722449,0.14112958,-0.09695175,0.02890527,0.03070525,0.00025862,-0.0173028,-0.03097934,-0.04162901,-0.04631186,-0.03626498,-0.02244576,-0.01004291,-0.01066842,-0.03970318,-0.09656445,0.0052279,-0.03758521,0.03815698,0.01747271,-0.07577498,0.02250254,0.04449087,0.09162135,0.00772065,-0.0004612,0.02797062,0.00449891,0.04472334,0.02883111,0.00675587,0.09615494,0.03193966,0.09020228,-0.05100038,0.03133835,-0.00486251,-0.02763593,0.01705725,-0.02302707,-0.01360323,0.01615974,0.02733213,-0.03424786,-0.01741096,-0.04491051,-0.02606345,0.0741927,-0.02472406,0.09667893,0.02319526,-0.02309813,-0.06827686,0.03308372,-0.0713258,-0.01787648,-0.04432376,0.00555579,0.00087793,0.00425305,-0.00696338,0.02768532,-0.02296166,-0.02104215,0.00996341,0.04804236,0.01637227,-0.06583036,-0.03755197,0.05634148,0.07232398,-0.07352449,-0.04595432,0.02556453,-0.01822213,-0.01144457,0.04993919,0.03094197,-0.06502689,-0.02112994,0.06056929,0.00631231,0.02976743,-0.02094818,-0.03603676,-0.02512385,-0.00345176,-0.08250654,-0.01266818,-0.04076493,-0.00836501,0.04252237,-0.02053631,0.05439608,0.01452352,-0.01579323,-0.02978504,-0.02430301,-0.03581782,-0.00336329,0.05829706,-0.02303779,0.09604163,0.02507798,-0.02258343,0.02781245,-0.06297703,0.01744805,0.03323222,-0.01375587,0.02556672,0.0280421,-0.09308407,-0.0516795,0.07441613,0.06942174,-0.0500512,-0.05892088,0.01087505,0.06178784,0.01670848,0.04717344,-0.06134975,-0.05800561,-0.06418944,-0.20536442,0.01358407,0.04637831,-0.05561296,-0.05755641,-0.0480703,-0.00027331,-0.05063829,-0.08845592,0.0175432,0.11427791,-0.04287596,-0.01475216,-0.00109474,-0.04549345,0.01283214,0.00810318,-0.06263884,-0.02521909,0.03058257,0.00644134,-0.01968295,-0.06907154,-0.0868264,0.00161332,-0.03292673,0.14707024,0.02606539,0.03084253,-0.04821896,0.03892225,0.00116196,-0.01296936,-0.08272277,-0.02446009,0.01500995,0.01376543,0.03849295,0.03954164,0.01057245,-0.02285646,0.00379071,-0.01812599,-0.07560608,0.04588379,-0.0338691,-0.09032503,-0.00322094,0.04443938,0.06871762,-0.02345303,0.0025904,0.09008539,0.05386504,-0.00443855,0.00844484,-0.02071389,0.00405263,-0.02749887,0.0564107,0.00770119,-0.01355164,-0.06456135,-0.05930396,0.05123648,0.06430241,0.00583492,-0.02432513,0.05519665,-0.06421963,-0.00699217,0.07617238,0.01241811,-0.00891429,0.030223,-0.00846558,-0.07465454,-0.04489425,0.04305071,0.03405,0.01744088,-0.02939735,-0.0146341,0.04397864,-0.0233052,0.01871724,0.02611909,0.02450871,0.05562731,-0.04260881,-0.03770299,0.01850561,-0.00379728,-0.03558623,0.09434018,-0.00619461,-0.26627228,0.04471321,0.0077494,-0.03644169,0.00251949,-0.0143857,0.06876045,-0.04183979,-0.00368632,0.01419396,-0.03406711,0.04611691,0.00953556,-0.02768002,0.00475474,0.08143068,0.05967896,0.01703744,0.08889528,-0.09970972,0.01135818,0.01840595,0.2524966,-0.0074475,0.02963455,0.06618199,-0.07037258,0.03445921,0.0633876,0.06637776,-0.0190048,0.06496155,0.11492728,-0.00231546,-0.04567694,-0.00784592,0.02183352,0.02554094,0.00514614,-0.02317245,-0.00967574,-0.00855062,-0.01878338,0.05917125,0.05917849,-0.06543206,-0.0317729,-0.11971753,-0.03776329,0.00303464,-0.0912207,0.03757999,-0.0217215,-0.01878959,-0.03061219,-0.01754174,-0.00596502,-0.01743621,-0.04684781,0.04167933,-0.00462908,-0.01496874,0.06265593,0.08214889,-0.0122575],"last_embed":{"hash":"829hb4","tokens":136}}},"text":null,"length":0,"last_read":{"hash":"829hb4","at":1751288820187},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.prepend(chunk)","lines":[623,633],"size":303,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"829hb4","at":1751288820187}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.setSourceContent(sourceFile, sourceContent)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03162082,-0.02414167,0.06331428,-0.09813988,0.01489738,-0.01620666,-0.09894202,-0.04105286,-0.03880483,0.01819858,0.00539653,-0.0699976,-0.00253119,0.014834,0.06838836,-0.05008275,-0.0126306,0.04274004,-0.01841329,0.03665585,0.08894865,-0.007,0.06753349,-0.02083511,-0.01241175,0.08666266,0.05341687,-0.06531487,-0.01224423,-0.16341284,-0.01789057,-0.0381461,-0.08299036,0.03031799,0.0097745,-0.00665151,-0.00203601,-0.00131811,-0.00992328,0.02469191,0.04951437,0.03497144,-0.01186608,-0.015108,-0.00355378,-0.02496948,0.01566505,0.01323585,0.00092632,-0.05649913,-0.01759994,0.00020791,0.00714502,-0.0053073,0.00527706,0.09621995,0.05682042,0.08578308,0.01762641,0.04386971,-0.01377115,0.03813389,-0.18164729,0.03151341,0.07972426,0.0567152,-0.03498077,0.0252373,0.01815364,-0.026655,-0.03891771,0.05984481,0.0467482,0.03822602,0.03603719,-0.04116088,-0.00146344,0.03206734,-0.03321217,-0.02419057,-0.06813636,0.02629425,0.00438904,0.04757763,0.02458147,-0.02588767,0.00623172,0.00767047,0.07370751,0.00968911,-0.02376667,-0.06730063,0.04000797,0.10620071,-0.05194953,-0.01230288,-0.01409835,0.07392031,0.01362248,0.12060264,-0.08334626,-0.00009817,-0.01622949,0.0040676,-0.00676802,0.02254589,-0.03241954,-0.00429234,-0.01632831,0.03110531,-0.00600027,-0.0044186,-0.08939172,-0.08351267,-0.02673325,-0.0133236,0.01766284,-0.00071806,-0.06486474,0.04996774,0.02230547,0.0680037,0.02133306,0.03768326,0.04546033,-0.01918341,0.08467542,0.00259531,0.00423547,0.03555882,0.01802309,0.05141621,-0.06914125,-0.03195824,0.00767025,0.01873752,0.00912931,-0.01151542,-0.02214847,-0.00960647,0.01880058,-0.06328298,-0.01710702,-0.02166547,-0.02521,0.10920819,-0.02637921,0.07872855,-0.01337089,-0.01222298,-0.05199126,0.0249654,-0.08046335,0.01812883,0.01805788,-0.01284344,-0.00647681,-0.03380556,-0.06208529,0.02581086,0.0093354,-0.04848885,0.01753313,0.10115452,-0.02548335,-0.08622331,-0.04997832,0.02531182,0.06563967,-0.09220018,-0.04671314,0.01876732,0.01925745,-0.01519868,0.03559596,0.04565597,-0.04250791,-0.02608504,0.01628758,0.01466201,0.0169796,-0.0321367,-0.0517255,0.03645497,-0.04504503,-0.02528244,0.03088564,-0.0521808,-0.02102209,0.06490096,-0.04169566,0.08093961,0.02217721,-0.04119597,-0.00974536,-0.05145099,-0.04561618,0.0310999,0.03762939,-0.03483545,0.07729356,0.02995434,-0.00135205,0.05242322,-0.0777797,0.00515276,-0.0449407,0.01695233,0.0321234,0.01407398,-0.11004057,-0.04207063,0.04490266,0.06933167,-0.07519728,-0.03847586,0.04861796,0.02904596,0.05041379,0.06807902,-0.06067951,-0.02036584,-0.07444914,-0.19991635,0.00025549,0.0037834,-0.07920184,-0.00932414,-0.00334594,-0.03311355,-0.01537072,-0.0741194,0.02812239,0.17298096,-0.01316122,-0.02364599,-0.01505119,-0.01822498,-0.02541553,0.01851529,-0.04464039,-0.0332019,0.00035386,0.00720724,-0.00097259,-0.07999454,-0.07920185,0.01676234,-0.03568227,0.09883909,0.03898903,0.02907218,-0.05162496,0.04133265,-0.01374336,0.00077302,-0.11679251,-0.03504607,-0.01768562,-0.01929237,0.02579287,0.02871468,0.04693313,0.00322971,-0.01577956,-0.0115534,-0.08226813,0.03597294,-0.01443735,-0.04623212,-0.00248888,0.01642737,0.01212837,-0.02909407,0.02505947,0.05509463,0.04225982,-0.03477298,0.00034593,-0.00010947,0.0220785,-0.00236739,0.05940374,0.03542443,0.034223,-0.00571808,-0.06736912,0.08688185,0.03671963,-0.0003048,-0.04966279,0.04798768,-0.0254217,0.01808206,0.04980756,0.02669255,0.00134926,0.02227804,-0.03105866,-0.01261886,-0.01075649,0.02011027,0.00507798,0.04692068,-0.03070534,-0.02028718,0.04002873,-0.02859099,0.0493997,0.00718414,-0.0047848,0.03954612,-0.04305759,-0.04656217,0.01455787,-0.00845889,-0.00517036,0.12640294,0.01896142,-0.26280594,0.07016451,0.02984488,-0.01934618,-0.05465038,-0.03192058,0.06321932,-0.01004123,-0.01006498,-0.01121177,0.00370488,0.06877968,-0.03824477,-0.05965133,0.00604452,0.05427897,0.07764666,0.03512795,0.11828192,-0.05730675,0.02176091,0.07265379,0.25408602,-0.01911267,-0.00003641,0.07637154,-0.05105095,0.06105292,0.07468633,0.04757247,0.0085732,0.02846338,0.09330747,0.01467936,-0.02702988,0.00258058,0.05943842,-0.00246575,0.01412767,-0.0016822,-0.01213469,-0.00584263,-0.01181772,0.03704102,0.04927919,-0.07458743,-0.06136372,-0.09027122,-0.03386908,-0.00057846,-0.08129296,0.0386888,0.01594688,-0.01864012,-0.04648672,0.00955197,0.01594446,-0.02233852,-0.01534108,-0.01939827,0.00179165,-0.05760445,0.05819475,0.04852398,0.00507985],"last_embed":{"hash":"17ndg6z","tokens":164}}},"text":null,"length":0,"last_read":{"hash":"17ndg6z","at":1751288820252},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.setSourceContent(sourceFile, sourceContent)","lines":[634,647],"size":398,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"17ndg6z","at":1751288820252}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walk(fn)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04697419,-0.07093256,0.02732494,-0.06437515,-0.00661439,0.02303939,-0.0509275,-0.02800543,0.00546797,0.01253161,0.00038205,-0.03391961,0.03461995,0.02019529,0.02662771,-0.02601291,-0.03073449,0.0815722,-0.01560381,-0.02171283,0.06641781,-0.01921427,0.01772656,-0.05976145,-0.01090711,0.10887336,0.05422503,-0.03348609,0.01623982,-0.18858215,-0.01588983,-0.01645755,-0.04579534,0.00625368,0.01271857,0.00200563,0.01400129,-0.00677733,-0.02212013,0.05050404,0.02354511,0.03648733,-0.02609342,-0.01551131,0.02269063,-0.04040958,-0.03477386,-0.01630736,-0.00945003,-0.01194109,-0.03547464,-0.01001027,0.02631856,-0.03128559,0.04850273,0.10351925,0.05673605,0.07424619,0.01098428,0.06393992,0.04445524,0.00999916,-0.17217299,0.05423818,0.06546299,0.02817295,-0.06194756,-0.01940808,0.01846342,0.04375456,-0.00230983,0.01954111,0.02737068,0.04582624,0.01946016,-0.05337382,-0.01861657,0.03748194,-0.0009158,-0.02550691,-0.113252,0.00901748,0.00676268,0.05900155,0.02865793,0.02442947,0.00554811,-0.03239052,0.06525574,0.0010914,-0.01312546,-0.06233317,0.03519648,0.08555409,-0.05388761,-0.03028456,0.00922162,0.0160126,0.00438997,0.13683006,-0.04854632,0.0526564,0.02291833,0.02605293,-0.03894518,-0.04924242,-0.00581804,-0.09244398,-0.02097481,0.03265788,-0.03895619,-0.03685222,0.00149127,-0.06271151,-0.02114336,-0.02885721,0.01914537,-0.00121841,-0.04032027,0.06575217,0.00665626,0.07045773,0.03945151,-0.00360897,0.02729686,0.02777795,0.04640095,0.03609488,0.02567582,0.09039756,0.07340395,0.07769662,-0.06900278,0.01966619,-0.02301168,0.00253576,-0.0184626,-0.01423253,-0.00807256,-0.01923859,0.01912274,-0.05952043,-0.02139005,-0.03301788,-0.03250696,0.05817953,-0.04860607,0.07008382,0.01604919,-0.00773091,-0.03001601,0.02338598,-0.03056533,-0.03312788,-0.04551084,0.02082566,0.00041313,0.05694011,-0.03425148,0.01792755,-0.0487128,-0.01836615,0.01570833,0.08211514,-0.00756892,-0.05743808,-0.00618981,0.0366056,0.03691072,-0.09863921,-0.04585546,0.01098495,-0.04243563,0.00332492,0.03400557,-0.00214407,-0.06286699,-0.05358613,0.02328779,0.02183435,0.00345621,-0.05442856,-0.03814711,0.04037495,0.00312441,-0.03486252,-0.00794983,-0.04781153,-0.03398736,0.05310069,0.01090922,0.02509683,0.07947572,-0.02353517,-0.05814761,-0.02270601,-0.05600993,-0.03205874,0.02113633,-0.01001779,0.12939152,0.00441236,-0.02533522,0.02034922,-0.06995018,-0.00132643,0.00284516,-0.01631564,0.00275539,0.00177428,-0.11603781,0.00833795,0.0623876,0.05596266,-0.05107832,-0.05093312,-0.00000114,0.00210566,0.01301312,0.06017997,-0.07862543,-0.04300376,-0.06640939,-0.18798818,-0.02905392,0.06128105,0.00610307,-0.03637687,-0.00429532,-0.04451977,-0.0484867,-0.06651063,0.00820514,0.11203776,0.018054,0.00875205,0.01522741,-0.029279,0.02862597,0.03563928,-0.05774025,-0.01036768,0.01393546,0.01998883,-0.00083168,-0.03408015,-0.15576193,0.00879469,-0.02532362,0.1620506,0.05632707,0.04143427,-0.02033461,0.07051829,-0.01049535,0.00852748,-0.04150837,-0.00843417,0.03247732,-0.00338023,-0.01476683,0.0439379,-0.01364722,-0.04947332,-0.00443636,-0.02316773,-0.06827487,-0.01254771,-0.01743172,-0.04152343,-0.05323849,0.05533093,0.01832967,0.00987817,0.00170466,0.10828234,0.05570201,-0.02608593,0.00739438,0.03221767,-0.02715038,-0.03578855,0.03664784,-0.03714921,0.03740652,-0.01001852,-0.06881233,0.09793359,0.05831554,0.01922446,-0.01513665,0.03702494,0.00895747,-0.00382202,0.07667074,0.01592675,-0.0472376,0.01852175,0.01266107,-0.00222102,-0.002109,-0.02346667,0.02110769,0.01632151,-0.06077369,-0.0194189,0.02814853,-0.04177987,0.02055378,0.04277378,-0.07071909,0.0419142,-0.09216295,-0.05489512,0.03826404,-0.00930782,-0.05822254,0.05158683,0.04537262,-0.26306307,0.00131549,0.01579788,-0.01939707,-0.0175992,-0.01820672,0.07071254,0.001924,0.00484825,-0.00991709,-0.00415379,0.05324049,0.03700731,-0.04132481,0.01213434,0.05716503,0.05011715,0.06510953,0.07267358,-0.03715938,0.06970547,0.08623959,0.24196534,-0.03027734,0.0409547,0.05254608,-0.11696825,0.03437045,0.04004057,0.014009,-0.01244353,0.034875,0.11253683,0.04554063,0.0024589,0.01450285,0.01877509,0.01185625,-0.0257948,-0.07843615,-0.02124985,-0.00183674,0.01777968,0.01128405,0.09196184,-0.11208537,0.00549405,-0.05551175,-0.01315169,0.01960501,-0.07920624,0.05076895,-0.03623884,-0.01273321,0.01930623,0.02198692,-0.00487335,-0.00236272,-0.1013604,-0.02200763,0.00236496,-0.05511739,0.07863392,0.06136462,0.02815368],"last_embed":{"hash":"f7wkx","tokens":332}}},"text":null,"length":0,"last_read":{"hash":"f7wkx","at":1751288820323},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walk(fn)","lines":[648,672],"size":781,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"f7wkx","at":1751288820323}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walk(fn)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03845449,-0.06315029,0.03326986,-0.07222854,-0.00726924,0.02631602,-0.03111232,-0.02341323,-0.00312957,0.00995805,0.00593992,-0.02530135,0.04071205,0.0087005,0.02298462,-0.02109639,-0.0269467,0.07397249,-0.00921835,-0.03326234,0.08158705,-0.00924179,0.01369679,-0.05641163,-0.00102424,0.09836045,0.05902237,-0.05422197,-0.00658317,-0.17965037,-0.00439525,-0.02923176,-0.04084821,0.00221886,-0.01477619,0.00797971,0.01533403,0.02216923,-0.02390739,0.05876145,0.01733094,0.04593292,-0.02140835,-0.00340414,0.01462717,-0.01954453,-0.01419922,0.00157299,-0.02307861,-0.01754601,-0.02343765,-0.01366548,0.03072343,-0.0346625,0.05035831,0.11152723,0.0558756,0.07002607,0.02171182,0.05591619,0.04640618,0.00913937,-0.16427836,0.04419062,0.059625,0.0365905,-0.04236899,-0.02424804,0.0228857,0.03900675,-0.01675312,0.01825057,0.00890339,0.03466946,0.01689803,-0.05471354,-0.01166505,0.02908085,0.00830453,-0.02640226,-0.11879031,0.00792197,0.00620092,0.05906738,0.01033511,0.02284448,0.00683931,-0.03412296,0.07883321,-0.00717733,-0.02331856,-0.06409167,0.03054998,0.10023553,-0.04616448,-0.03183587,0.01439596,0.03397125,0.02776787,0.15316455,-0.06304639,0.0491571,0.02528649,0.01633415,-0.03246764,-0.04830477,-0.01005966,-0.08325639,-0.00999197,0.02371325,-0.03760872,-0.03049467,0.00108826,-0.06227063,-0.01903243,-0.05044079,0.02010787,-0.00111143,-0.0279972,0.05982952,0.00237286,0.08495691,0.0408165,-0.00649005,0.04004321,0.01806676,0.0559271,0.0179503,0.01561949,0.06720397,0.06308758,0.06827687,-0.05778257,0.01500888,-0.00551875,0.01636593,-0.00298494,-0.04477799,-0.02569411,-0.01270646,0.01743886,-0.03663026,-0.02154862,-0.04111982,-0.02778359,0.07182886,-0.04453659,0.08807115,0.01627935,0.00401974,-0.04149146,0.04275483,-0.02020162,-0.03165573,-0.03829718,0.03075582,-0.00129947,0.05092464,-0.02648852,0.02013075,-0.04297474,-0.01417233,0.01474331,0.07864278,-0.01503639,-0.063752,-0.01324066,0.0279272,0.02648661,-0.09367252,-0.04943777,0.02805119,-0.03531431,0.01641156,0.02149341,-0.01004711,-0.07124823,-0.05750472,0.02034566,0.01446896,0.01273554,-0.05448101,-0.03035309,0.02592981,0.00485011,-0.04081113,-0.0167369,-0.03901347,-0.03035515,0.07062982,0.01643416,0.01566439,0.08808326,-0.02806122,-0.04651996,-0.01902212,-0.06159247,-0.01870517,0.0207925,-0.01228068,0.09992441,-0.00770077,-0.0299812,0.02289444,-0.07144699,-0.00163739,0.00524856,-0.00645686,-0.02222157,0.00716206,-0.1397754,0.00450956,0.0582924,0.0460505,-0.06766913,-0.05261138,-0.01148637,0.00689474,-0.00654798,0.04945135,-0.05654144,-0.03958174,-0.07943428,-0.18857948,-0.03595578,0.06608586,0.00948286,-0.03534577,0.0009851,-0.02621097,-0.05021513,-0.06002064,0.01351049,0.10818949,0.02944435,0.00742577,0.0106383,-0.01575455,0.02887474,0.0352602,-0.07199928,-0.01134098,0.00182123,0.02608685,0.01385836,-0.02760014,-0.14717712,0.03093902,-0.02028602,0.15834349,0.06667233,0.05322571,-0.01940983,0.06122249,-0.01645497,-0.0144401,-0.04715234,-0.0167289,0.03247911,-0.00295253,-0.04637436,0.044379,-0.02361237,-0.05162804,-0.00315741,-0.0173022,-0.10088427,0.00861326,-0.01963884,-0.03634464,-0.05650896,0.05248421,0.00311991,-0.00483175,-0.00183192,0.10947049,0.05142707,-0.02168007,-0.00170726,0.01836327,-0.03531958,-0.05138971,0.03342239,-0.03129845,0.03833139,-0.01867323,-0.05339147,0.09487998,0.05468808,0.00911061,-0.02132034,0.03116561,0.00646768,-0.01164749,0.09577277,-0.00100312,-0.06444752,0.04392298,0.00139765,0.00986626,-0.01753874,-0.0062507,0.0217605,0.03413101,-0.06176861,-0.02852428,0.04193227,-0.04335396,0.02788077,0.03640613,-0.05198295,0.03250603,-0.09632398,-0.05357838,0.04352258,-0.00089481,-0.06415889,0.05183473,0.05126766,-0.26669049,0.0205982,0.03112187,-0.00093328,-0.03039167,-0.03062897,0.06607199,0.00948359,0.00831822,-0.00366887,0.0096166,0.04870466,0.03044158,-0.04357979,0.00070388,0.04795994,0.06450366,0.06360422,0.07427932,-0.03255041,0.06080729,0.08296513,0.2522164,-0.0317371,0.0455272,0.04665551,-0.11200853,0.03857384,0.03828521,-0.00178426,0.01097185,0.0186555,0.08108656,0.0357529,0.00698709,0.01802853,0.02717538,0.0311978,-0.01833849,-0.07271955,-0.0070894,-0.00468746,-0.0086631,-0.00396854,0.08431465,-0.11946786,0.00067013,-0.05448156,0.00069576,0.02218615,-0.06731128,0.04285561,-0.03382171,-0.02765438,0.00651242,0.01978933,0.01338431,-0.01221247,-0.10847184,-0.011083,0.00182851,-0.04716187,0.07568178,0.0776742,0.03046865],"last_embed":{"hash":"13q0f57","tokens":95}}},"text":null,"length":0,"last_read":{"hash":"13q0f57","at":1751288820446},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walk(fn)#{1}","lines":[650,653],"size":214,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"13q0f57","at":1751288820446}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walk(fn)#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06761052,-0.05790023,0.03731392,-0.04917391,0.00570715,0.01356506,-0.0655363,-0.01520162,0.00885011,0.00768825,-0.00613748,-0.06432217,0.00898919,0.03987135,0.01301866,-0.02484959,-0.0292662,0.09026699,-0.00392121,-0.0077113,0.06234517,-0.01126054,0.01950191,-0.05720102,-0.03659406,0.12555319,0.06635723,-0.00570496,0.03452326,-0.17401916,-0.03913635,0.00688985,-0.05799262,-0.00052223,0.02825926,-0.00549953,0.00738564,-0.04146542,-0.02250552,0.03939279,0.03682477,0.0188145,-0.03361797,-0.02446091,0.04235025,-0.05390736,-0.04762917,-0.00040649,0.00163971,-0.0254677,-0.02194019,0.0087319,0.03246064,-0.03607315,0.02876917,0.10123911,0.05711884,0.08760268,0.01772749,0.0674341,0.04536664,0.00859909,-0.16700633,0.03506675,0.05375273,0.01677131,-0.05883974,-0.04373163,0.01443472,0.03970237,0.01575839,0.02202873,0.06016861,0.05303611,0.03218599,-0.05543062,-0.02595717,0.0296158,-0.0082974,-0.02146951,-0.08195994,0.01439537,-0.00185894,0.04311145,0.03594993,0.01670575,0.01379487,-0.02251091,0.03266569,0.0043355,0.00124661,-0.05310106,0.04666679,0.09124019,-0.05671525,-0.01958877,0.00668144,0.00393347,0.00315323,0.14353901,-0.04820751,0.04460987,0.0183769,0.0293456,-0.02662066,-0.02500845,0.01356434,-0.08096004,-0.02431201,0.03589204,-0.03026449,-0.00769254,-0.00435021,-0.08354229,-0.04012812,-0.01480699,0.00226673,-0.00140946,-0.06247155,0.08156544,0.01125474,0.04226614,0.04791675,0.00242529,0.013078,0.02592671,0.03562327,0.05096161,0.01068306,0.09543917,0.06157508,0.0691246,-0.07870318,0.01443823,-0.02659748,0.01433915,-0.03098617,-0.02420696,-0.01764722,0.00872169,0.02120627,-0.06757487,-0.0067825,-0.0379919,-0.04455163,0.03245397,-0.03917734,0.05012345,0.0293162,-0.013444,-0.01190767,0.02000592,-0.06371596,-0.02822093,-0.03172206,-0.01627525,0.01618501,0.04650703,-0.06252286,0.02007249,-0.05119615,-0.04071715,0.00624285,0.09678198,-0.00393059,-0.0436859,-0.00842513,0.03697016,0.0253551,-0.10732336,-0.03879973,-0.00610308,-0.03612576,-0.01324751,0.07373871,0.03530547,-0.039521,-0.04564575,-0.00034899,0.02678765,-0.01336114,-0.04447442,-0.03048331,0.02621598,0.00132166,-0.01232443,0.02023952,-0.05300073,-0.02802803,0.0457672,-0.00148399,0.04083839,0.04927203,-0.0150712,-0.07036802,-0.04280648,-0.03848227,-0.01442372,0.01747693,-0.01762639,0.16020377,0.0140294,-0.00803836,0.03553375,-0.05486896,0.00492714,-0.001405,-0.0225797,-0.00442784,0.01861357,-0.10493809,0.00215416,0.08661547,0.06315738,-0.04500997,-0.0588253,0.00829728,0.0123517,0.0218485,0.06242073,-0.0871599,-0.03462179,-0.06426608,-0.21156815,-0.01421484,0.03732954,-0.0208354,-0.01020204,-0.00157929,-0.05886522,-0.03412598,-0.059437,0.02406815,0.11950277,-0.01564367,-0.00757418,0.02687475,-0.04211244,0.03484392,0.0186226,-0.0486417,-0.02158397,0.01310419,-0.00544796,-0.02293832,-0.05740647,-0.15336446,0.01595032,-0.00280775,0.14299256,0.02566179,0.03017242,-0.03897773,0.08826382,-0.00654911,0.02488144,-0.06024887,-0.00059736,0.02257705,0.02429517,0.03452507,0.04335058,-0.02404529,-0.0439488,0.00735559,-0.02249738,-0.04711253,0.00075479,-0.01841853,-0.05427061,-0.03177573,0.03184972,0.03328152,0.00345657,0.0252439,0.08279664,0.04395491,-0.05332927,0.0172755,0.02120591,-0.01451811,-0.01717639,0.0341972,-0.03251448,0.04193231,-0.00269125,-0.06843649,0.10727825,0.05614844,0.01779711,-0.00909791,0.05867879,-0.00904437,0.00774088,0.04754078,0.04624688,-0.01056673,-0.00313517,-0.00566273,-0.02532503,0.03322137,-0.01207861,0.02049155,-0.00404604,-0.03776973,-0.0138735,0.00042507,-0.05883118,0.01260436,0.04577338,-0.06051677,0.05010332,-0.05785821,-0.0375535,0.01489606,-0.02177574,-0.06904974,0.08360262,0.03114549,-0.25568685,-0.00795646,0.0359144,-0.02866778,-0.0290812,-0.01611297,0.07675086,-0.00833805,-0.00887992,0.0044226,-0.01252507,0.0420108,0.02088611,-0.04290128,0.03827358,0.05489418,0.03868526,0.0423009,0.0639948,-0.06332351,0.06557927,0.08242115,0.25278851,-0.07524635,0.03199794,0.06575791,-0.08589965,0.02553376,0.03553088,0.03779057,-0.01294702,0.01986958,0.12421587,0.034037,-0.01233764,0.01515057,0.01716689,-0.00570186,-0.01488771,-0.07286084,-0.03849044,0.00231709,0.03067518,0.04758482,0.09213397,-0.09760432,-0.02571537,-0.05946106,-0.04599129,0.01874514,-0.09938748,0.04939475,-0.02021125,0.0062534,0.01090704,0.0112895,-0.00131205,-0.00794983,-0.07925461,-0.0039048,-0.00216865,-0.04041114,0.07530119,0.04369286,0.00171172],"last_embed":{"hash":"1idqvvd","tokens":277}}},"text":null,"length":0,"last_read":{"hash":"1idqvvd","at":1751288820485},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walk(fn)#{2}","lines":[654,672],"size":530,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1idqvvd","at":1751288820485}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walk(fn)#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06929159,-0.05392719,0.02961658,-0.04697404,0.01150876,0.00605555,-0.08577973,-0.02327329,0.00252864,0.01196393,-0.00244024,-0.06160007,0.00356264,0.03329832,0.02935395,-0.02810488,-0.02814936,0.08185291,-0.00381334,0.00122117,0.06598481,-0.01112461,0.01869579,-0.04541577,-0.03349701,0.11786568,0.06298245,-0.00875486,0.03825904,-0.17208464,-0.04218379,0.00390151,-0.04087884,0.00807517,0.03900057,-0.00607807,0.00325632,-0.04124848,-0.02805869,0.03551004,0.02155591,0.00956929,-0.0402193,-0.02381103,0.04362381,-0.06647018,-0.04381961,0.00136276,0.00379287,-0.02387261,-0.01431616,-0.00138103,0.0196427,-0.03008318,0.02979827,0.09351326,0.05076082,0.08618915,0.01181552,0.06513501,0.04928515,0.01089206,-0.1691108,0.02850926,0.05932785,0.02278568,-0.07039977,-0.04674309,0.00441965,0.02412587,0.00909778,0.03046698,0.06893746,0.05372452,0.02469448,-0.05296966,-0.02791379,0.02723675,-0.00110456,-0.02663054,-0.09460628,0.01258378,-0.00592844,0.05861094,0.03891667,0.01209505,0.01958927,-0.02062746,0.03650416,0.00282779,-0.01258412,-0.05961731,0.04121174,0.08625879,-0.06074961,-0.02102041,0.00459841,0.01335562,-0.00152014,0.14819582,-0.04508194,0.02796866,0.00955142,0.02659112,-0.02377009,-0.02423715,0.01275436,-0.08214206,-0.01620874,0.05245242,-0.02296927,-0.00023388,-0.02711557,-0.08267448,-0.03487545,-0.02512847,0.00537829,0.00946394,-0.06091877,0.07545349,0.01434465,0.04233686,0.03829034,-0.00900733,0.00914689,0.02842174,0.03111404,0.04627767,0.0169298,0.1033422,0.05526325,0.07225047,-0.07701291,0.0107871,-0.01972798,0.01425838,-0.01948591,-0.0008121,-0.00420983,0.00493476,0.02336712,-0.0655482,-0.01566872,-0.03479249,-0.03706491,0.03770067,-0.04308297,0.057831,0.03208059,-0.00865218,-0.02304446,0.01885762,-0.06898253,-0.01169555,-0.02713398,-0.0229605,0.0187204,0.04739043,-0.05246569,0.01975931,-0.03373779,-0.04255535,0.0103071,0.09747267,-0.00777985,-0.04885697,-0.01198384,0.03301603,0.02863499,-0.09385528,-0.04443101,-0.00139718,-0.01310055,-0.01106404,0.0748478,0.02706161,-0.02549269,-0.04218057,0.0023343,0.03351214,-0.0167351,-0.03803015,-0.03555104,0.03511477,0.0052327,-0.00526682,0.02219021,-0.05262675,-0.02883227,0.0445901,-0.00321313,0.04058925,0.04040657,-0.00991286,-0.05477532,-0.03843433,-0.03599507,-0.02678284,0.02045053,-0.02485375,0.15091872,0.02253062,-0.00836777,0.03643218,-0.04171655,0.01575411,-0.01273435,-0.01589477,0.00215686,0.02136526,-0.1074301,0.00562794,0.09000844,0.0660558,-0.05840854,-0.05048119,0.00597541,0.01693938,0.03254401,0.07118809,-0.10063394,-0.03644364,-0.0723686,-0.21234989,-0.0030274,0.0384919,-0.02958419,-0.01570798,0.00405805,-0.06311399,-0.04432624,-0.06458358,0.02758712,0.13762628,-0.02332344,-0.00212788,0.02534525,-0.0423484,0.03515276,0.02181828,-0.05109339,-0.0219686,0.00886458,-0.00400045,-0.03010212,-0.05062612,-0.14453705,0.01455652,-0.01280547,0.14242835,0.02786176,0.02093231,-0.04612132,0.07550034,-0.00964467,0.02620697,-0.07830312,-0.00215905,0.02921377,0.00899219,0.03445243,0.04873572,-0.00891259,-0.05106685,-0.00911237,-0.01768386,-0.04875704,0.00951308,-0.01727058,-0.05968638,-0.01451725,0.02635793,0.03763496,-0.00125568,0.03380345,0.08306061,0.04964019,-0.05345975,0.03124922,0.01705219,-0.00887888,0.00272182,0.03412016,-0.03332827,0.04737677,-0.00972295,-0.07533031,0.10638041,0.05483606,0.01024235,-0.0140324,0.06442918,-0.00973235,0.01387791,0.06153068,0.04850309,-0.01984173,0.00156343,-0.01610562,-0.03124996,0.02821405,-0.00971602,0.02192988,-0.00272427,-0.05378329,-0.00575249,0.00135009,-0.05109777,0.02841129,0.04419234,-0.06333015,0.05314362,-0.05154883,-0.02777178,0.0069325,-0.01888397,-0.05934687,0.08431734,0.02575959,-0.26050445,-0.01334749,0.03073099,-0.02523183,-0.01900027,-0.02451514,0.07541604,-0.0190674,-0.01740349,0.00630993,-0.01899301,0.03901829,0.01250344,-0.04158827,0.03004283,0.05941421,0.04179343,0.02387123,0.075099,-0.06986982,0.05686152,0.06990115,0.24944338,-0.05886633,0.02339014,0.0655428,-0.08362611,0.03534067,0.0391167,0.05421815,-0.01969094,0.01647338,0.12366484,0.03881077,-0.0142783,0.01670967,0.00905551,-0.01767811,-0.01818448,-0.06442518,-0.04482464,0.00207429,0.02581321,0.04902484,0.09309433,-0.09031895,-0.02538253,-0.05900528,-0.03911217,0.01057922,-0.10433394,0.04074882,-0.01406003,0.01824704,0.00919349,0.01170966,0.00289525,-0.00847411,-0.07556937,-0.01501637,-0.00234998,-0.04257157,0.07581025,0.03589072,0.0058025],"last_embed":{"hash":"uwkafi","tokens":266}}},"text":null,"length":0,"last_read":{"hash":"uwkafi","at":1751288820596},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walk(fn)#{3}","lines":[656,672],"size":497,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"uwkafi","at":1751288820596}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walkSourceContents(fn)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.040941,-0.0415858,0.03451271,-0.08398239,0.00089667,-0.00496706,-0.07394522,-0.03530789,-0.00274777,0.01324567,-0.00486172,-0.05286338,0.02072892,0.01946715,0.03791321,-0.02299789,-0.01087371,0.05740479,-0.02206595,-0.01699091,0.07683824,-0.00897769,0.04625453,-0.06727991,-0.01707303,0.08980188,0.0638975,-0.03814159,-0.00579331,-0.17624336,-0.02547905,-0.02241747,-0.05508709,0.02826511,-0.00497337,-0.00201076,0.00279864,-0.01015161,-0.0193294,0.06006769,0.05227061,0.03777466,0.0006484,-0.01089855,0.03883765,-0.01334212,-0.03017335,0.00250742,-0.01506197,-0.01699302,-0.02449996,-0.00220501,0.02115086,-0.04190085,0.02531026,0.09440142,0.05102648,0.08008826,0.01298208,0.07021581,0.02964699,0.02013402,-0.17036149,0.04831142,0.0530881,0.03035881,-0.04509643,-0.00511293,0.02707192,0.02528346,-0.00347517,0.01644087,0.03127742,0.03519474,0.03013024,-0.07437921,-0.01070822,0.04825293,-0.03061077,-0.02694678,-0.08659523,0.00985234,0.00984263,0.05354302,0.03781891,0.0205844,0.01202901,-0.00891246,0.05739699,0.03010553,0.00247792,-0.05512024,0.04015666,0.08731306,-0.06816468,-0.03314717,0.02277997,0.06068199,0.00896001,0.12318923,-0.06177694,0.02755365,0.03899015,0.02134376,-0.02432432,-0.02086285,-0.01334744,-0.06296524,-0.02947886,0.03724737,-0.02351117,-0.03473682,-0.01364834,-0.06867435,-0.04521635,-0.01938088,0.00859257,0.00036302,-0.07229723,0.06792863,0.0211363,0.05456147,0.04348888,0.01106691,0.04116122,0.0113009,0.05384783,0.02661416,0.02434072,0.06667525,0.03464069,0.09936779,-0.07203489,0.0102501,-0.00133392,0.01357299,-0.01887542,-0.02074299,-0.03834743,0.00518285,0.0355083,-0.0483289,-0.02448341,-0.03626118,-0.03319405,0.05547756,-0.01820549,0.07486994,0.02245094,-0.0027384,-0.01673322,0.02616226,-0.06298374,-0.0526622,-0.02581258,0.01921549,0.00262517,0.00339294,-0.04466262,0.02645077,-0.04986538,-0.02937496,0.02693731,0.1108404,0.00131305,-0.06121709,-0.00119943,0.04021944,0.05075929,-0.10524949,-0.04051697,0.00611769,-0.0222066,-0.01905867,0.04251939,0.01041032,-0.05714229,-0.0768341,0.02612443,-0.0043745,0.01054171,-0.0634672,-0.06236824,0.04207112,-0.00789454,-0.03756063,0.00946699,-0.04593016,-0.01196165,0.09487776,-0.00513505,0.02918407,0.06768476,-0.03844393,-0.0669087,-0.04790379,-0.05623638,-0.00198222,0.02750113,-0.02093272,0.13338484,0.01907484,-0.01042231,0.01322879,-0.08100287,-0.02026303,-0.01392906,-0.00292092,-0.00439604,0.01090141,-0.11873806,0.0021023,0.06884882,0.04749772,-0.06643741,-0.05754609,0.03183358,0.03114562,0.01610093,0.06709868,-0.07260379,-0.06516214,-0.05914968,-0.19134,-0.02636094,0.0274468,-0.02952162,0.00166101,0.00136399,-0.05431227,-0.03661509,-0.08050356,-0.00186228,0.11906771,0.02580032,-0.02011559,-0.00368964,-0.00872557,0.020998,0.03395651,-0.04342433,-0.02012679,-0.00135183,0.01948326,-0.0125956,-0.03873624,-0.14016519,0.02918532,-0.03183573,0.15485753,0.03363528,0.04815856,-0.02265185,0.06863715,0.01242446,-0.01514722,-0.0335675,-0.0193239,0.00220527,0.00506478,0.01926994,0.03213837,-0.00373376,-0.0132423,-0.01295319,-0.02391263,-0.0715877,0.02508243,-0.02224918,-0.03246289,-0.05204856,0.0381784,0.0144818,0.00017164,0.00765861,0.09671333,0.04684826,-0.030198,-0.01271765,0.01255281,-0.01680386,-0.04168138,0.03271434,-0.00937244,0.0100145,0.00110682,-0.05141429,0.08960233,0.05272291,0.00114729,-0.01611708,0.04095096,-0.01345876,0.02594399,0.0623654,0.01233711,-0.03770405,0.03338299,-0.00170449,-0.02327304,-0.00767136,0.00420547,0.01629954,0.03580443,-0.05000601,-0.01404881,0.02615189,-0.04568234,0.02185994,0.02158294,-0.03973527,0.04146669,-0.08403762,-0.04435764,0.03730961,-0.02509895,-0.06110281,0.08296768,0.04593499,-0.27275461,0.02261786,0.03110692,-0.02699064,-0.03082377,-0.01687589,0.07398494,0.02194266,0.00730372,-0.02031243,0.00864485,0.05257456,-0.0044988,-0.06854492,0.03113352,0.06331102,0.05009686,0.06169757,0.08296064,-0.03841941,0.04219709,0.07994952,0.25948876,-0.06760061,0.03418301,0.0666314,-0.08692776,0.05078415,0.06432688,-0.00741524,-0.01266919,0.03811886,0.10628285,0.01419936,-0.00526628,0.0322986,0.05854288,0.02621001,-0.00308602,-0.07676829,0.00321263,-0.02097126,0.01497864,0.0053023,0.08004968,-0.09473477,-0.03631926,-0.05752014,-0.02749796,0.02969733,-0.09012254,0.07018481,-0.02328382,-0.02930059,-0.00572951,-0.00855614,-0.00122891,-0.00839511,-0.07671401,-0.01899205,-0.00424365,-0.05284323,0.06035886,0.04968339,0.01775799],"last_embed":{"hash":"sazq4","tokens":335}}},"text":null,"length":0,"last_read":{"hash":"sazq4","at":1751288820734},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walkSourceContents(fn)","lines":[673,694],"size":776,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"sazq4","at":1751288820734}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walkSourceContents(fn)#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05912558,-0.03578141,0.04225891,-0.06716049,-0.00377606,-0.0010002,-0.07363264,-0.01448607,0.00847371,0.01315842,-0.0209123,-0.0694386,0.01385829,0.04370548,0.01792818,-0.02394076,-0.02373509,0.06659498,-0.01786907,-0.00302373,0.07257125,-0.00996746,0.03850323,-0.07266826,-0.02358742,0.1149696,0.0646906,-0.0191817,0.02636345,-0.16741289,-0.03446097,-0.01474624,-0.05572355,0.00958695,0.01079527,0.00265408,0.0000599,-0.01931253,-0.01967894,0.06009595,0.05103906,0.03673472,-0.01529757,-0.02253062,0.02910785,-0.00765974,-0.02509043,0.0140792,-0.01691766,-0.03428254,-0.03329622,-0.00146982,0.0353491,-0.02690308,0.02637817,0.11050526,0.04336764,0.09951495,0.03719664,0.0582449,0.02565736,0.01149134,-0.16613911,0.02762545,0.06726645,0.04301647,-0.03173691,-0.0231417,0.03509307,0.03186386,0.014466,0.02450825,0.05570361,0.0483651,0.03468005,-0.0396716,-0.02217906,0.03997934,-0.01630461,-0.00557867,-0.07075655,-0.00120463,0.00308581,0.05089278,0.04542333,0.01557719,0.01478271,-0.00132539,0.0542519,0.02558489,-0.00112987,-0.07155167,0.043186,0.08458541,-0.05806294,0.00234443,-0.00257697,0.02915477,0.00906022,0.13026716,-0.05310156,0.04043718,0.02514561,0.02531691,-0.02694176,-0.01760855,-0.00369385,-0.06104682,-0.02920221,0.01780603,-0.02908177,-0.00265032,-0.02149169,-0.08777779,-0.06489555,-0.02688385,0.0153074,0.00091211,-0.06794044,0.07630122,0.0152683,0.05842647,0.04462719,0.01268564,0.02880429,0.01515091,0.04452484,0.04673692,0.00274046,0.07426121,0.04316193,0.0819068,-0.08019496,0.00887047,-0.0114192,0.00414492,-0.02778835,-0.01510542,-0.03109239,0.0082908,0.0380211,-0.0414916,-0.02309049,-0.04014685,-0.0342016,0.06073086,-0.01473665,0.07141387,0.02537663,-0.00165564,-0.00802165,0.01473315,-0.06765017,-0.04498009,-0.01426023,-0.00047864,-0.00224123,0.01398408,-0.05179546,0.01031607,-0.04592748,-0.03823778,0.0062117,0.103956,0.00311583,-0.06486794,-0.01453021,0.03382898,0.045438,-0.10060202,-0.04049871,0.00430906,-0.02822722,-0.01489475,0.04465442,0.03157064,-0.05595823,-0.05672995,0.02081951,0.01323806,0.01183258,-0.05920462,-0.06360526,0.0255809,-0.01186296,-0.03804151,0.03037016,-0.06172924,-0.0239872,0.08363413,-0.00007503,0.04275167,0.04506742,-0.0099435,-0.05239236,-0.05091159,-0.0509291,-0.00625081,0.02786671,-0.02090465,0.1548024,0.00778609,-0.00684013,0.04170638,-0.07617886,-0.00912392,0.00431533,-0.0225112,0.00057991,0.02685869,-0.11184659,-0.01656667,0.06279765,0.05844686,-0.05705247,-0.05684461,0.01915469,0.0308448,0.02113896,0.054336,-0.08450212,-0.03212899,-0.05367042,-0.20771992,-0.00725103,0.02249338,-0.04364591,-0.00474655,-0.00297627,-0.0482679,-0.03346615,-0.06196381,-0.00005803,0.12285701,0.0087167,-0.02301149,0.00534325,-0.03805111,0.01312479,0.01297458,-0.04700771,-0.01315357,-0.00094942,0.00738255,-0.01928564,-0.06018717,-0.14260024,0.01667913,-0.01743199,0.14033896,0.00794494,0.03565466,-0.05245209,0.07194722,-0.00600351,-0.00041594,-0.05312313,-0.02595316,0.021218,0.01016665,0.03489831,0.03743226,-0.00932407,-0.01902226,0.00886133,-0.02468054,-0.05854554,0.03153525,-0.04047975,-0.04731356,-0.04445767,0.02061629,0.0469604,-0.01966046,0.01083698,0.07312839,0.02878816,-0.05087766,-0.00219529,0.00703084,-0.01922448,-0.04230089,0.05467476,-0.01528891,0.02578565,0.0034294,-0.05786118,0.10167851,0.06082385,0.01947316,-0.02810719,0.03469404,-0.02646841,0.01827522,0.05801754,0.04650342,-0.01035882,0.00209377,0.0019429,-0.04312021,0.00893015,0.00511685,0.01920683,-0.0019678,-0.03609403,-0.01618955,0.0218162,-0.05010815,0.02289444,0.02589127,-0.03912355,0.0359043,-0.05795321,-0.04477867,0.03352269,-0.03353794,-0.06161723,0.09429535,0.03110699,-0.26733601,0.02461778,0.03655003,-0.03804911,-0.03773636,-0.00448803,0.07900041,-0.00372676,-0.00550767,-0.01856145,-0.00618076,0.03924631,0.00497554,-0.04481368,0.03811122,0.06088544,0.0643399,0.0403247,0.06658997,-0.05712674,0.04540329,0.08457775,0.27013266,-0.0612806,0.02559588,0.06109918,-0.06665415,0.03513479,0.06171391,0.02026197,-0.01819091,0.03229163,0.13847531,0.02238861,-0.0267177,0.00978482,0.02958623,0.00195653,-0.01544063,-0.06178014,-0.01518336,0.00586872,0.02451715,0.04057898,0.09261779,-0.08842547,-0.04702612,-0.07943438,-0.04838763,0.02035691,-0.09890302,0.05914336,0.00718483,-0.01885882,0.00007932,0.00629833,-0.01891752,-0.0065269,-0.06882294,0.00361019,0.00032513,-0.04532938,0.04482416,0.0465145,-0.00484008],"last_embed":{"hash":"1jmwzs2","tokens":303}}},"text":null,"length":0,"last_read":{"hash":"1jmwzs2","at":1751288820886},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walkSourceContents(fn)#{2}","lines":[678,694],"size":583,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1jmwzs2","at":1751288820886}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walkSourceContents(fn)#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05883095,-0.03423589,0.042127,-0.06855792,-0.00280011,-0.01148045,-0.09881461,-0.02695405,0.00150865,0.01804529,-0.0185548,-0.06165,0.00472009,0.03914896,0.03302817,-0.03034309,-0.02483369,0.05491843,-0.02035941,0.00704521,0.07512134,-0.00831581,0.04306132,-0.06091435,-0.01956016,0.10932271,0.06354778,-0.03242059,0.02829337,-0.16477989,-0.03198659,-0.01608929,-0.0379288,0.01801244,0.02304591,0.00547558,-0.00089206,-0.01345099,-0.02917364,0.05349746,0.0364974,0.02165528,-0.02046379,-0.01957837,0.02791329,-0.00911781,-0.01324413,0.01684956,-0.01336993,-0.03696035,-0.02485292,-0.01587954,0.01879069,-0.02007035,0.03218966,0.10269801,0.03352643,0.10185234,0.03620105,0.05079582,0.02144745,0.01302234,-0.17414249,0.02191313,0.07267951,0.04856772,-0.0412342,-0.0239072,0.02988202,0.01196437,0.00487982,0.03445677,0.05964996,0.04826717,0.02443725,-0.03724273,-0.02246602,0.0366397,-0.01365047,-0.00929434,-0.08697334,0.00001496,-0.00036436,0.06762509,0.05042731,0.00664126,0.01868187,-0.00290132,0.05998703,0.02619524,-0.01618645,-0.07672442,0.03451098,0.07975709,-0.06316791,0.00228482,0.00061601,0.04823626,0.00326095,0.13836916,-0.05410561,0.02159041,0.01412817,0.01901218,-0.02473541,-0.01405064,-0.0021824,-0.0592877,-0.0272164,0.02941555,-0.01994585,0.00710728,-0.05295358,-0.09199121,-0.05591684,-0.03480294,0.02334791,0.01820967,-0.06698013,0.06446756,0.02055138,0.0621625,0.03473752,0.00351811,0.02951309,0.01235042,0.04208475,0.04002351,0.00292659,0.07637115,0.03147501,0.09020334,-0.07641216,0.0069175,-0.00476815,0.00332615,-0.00902249,0.00710046,-0.0164092,0.00355978,0.04603118,-0.03765465,-0.0305264,-0.03945387,-0.02253138,0.07445405,-0.01528589,0.08272046,0.02876871,0.00639443,-0.02245755,0.01272837,-0.07094621,-0.02573186,-0.00810078,-0.00970386,0.00127738,0.00916229,-0.03802398,0.00569097,-0.02340037,-0.0387212,0.01318647,0.10400631,-0.0005251,-0.07530377,-0.01862171,0.02827894,0.05366864,-0.08603957,-0.0451722,0.01604092,-0.0024155,-0.01041428,0.03841352,0.02028747,-0.04736205,-0.05244184,0.02973817,0.02064787,0.01377643,-0.05440758,-0.07106785,0.03441089,-0.00886046,-0.03040725,0.03262675,-0.06105333,-0.01958882,0.08757737,0.00300093,0.04331697,0.02980866,-0.00632122,-0.0335721,-0.05348556,-0.05109607,-0.01811205,0.03311938,-0.03192839,0.13625565,0.012205,-0.00657673,0.04042657,-0.06469647,0.00869055,-0.00390645,-0.0127779,0.01157469,0.0284169,-0.11198044,-0.01757025,0.06133679,0.06133394,-0.07568279,-0.04663452,0.02227679,0.03899802,0.03244,0.06466664,-0.09507242,-0.03827175,-0.06713992,-0.20044717,0.0044226,0.02284671,-0.05561697,-0.0125018,0.00010748,-0.05369561,-0.04335608,-0.06986801,0.00935483,0.14764774,-0.00632135,-0.01640955,-0.002996,-0.03125951,0.00627276,0.01714698,-0.04849603,-0.01866256,-0.00803177,0.01668494,-0.0270802,-0.05828455,-0.1265457,0.0163534,-0.03286825,0.13601334,0.01017653,0.02401198,-0.05750295,0.05067257,-0.01605047,-0.00241081,-0.07476316,-0.03264134,0.02218786,-0.00407847,0.03679656,0.04100708,0.01436344,-0.02448985,-0.0088175,-0.02022296,-0.0559408,0.0422482,-0.03432992,-0.05418667,-0.0219926,0.01107225,0.05515204,-0.02742738,0.02001619,0.06955952,0.02959364,-0.05002194,0.01145129,0.00011117,-0.01638295,-0.02407423,0.05849247,-0.01529712,0.02672621,-0.00738673,-0.06959918,0.10114226,0.06290077,0.01764458,-0.03623812,0.03986745,-0.02901652,0.02519036,0.07197652,0.04931292,-0.01759032,0.0151173,-0.01345492,-0.04583225,-0.00342826,0.01590991,0.0204949,0.00631271,-0.06053169,-0.0052241,0.02971477,-0.03812982,0.03956157,0.01973276,-0.03203377,0.03413998,-0.05369182,-0.03470042,0.02877878,-0.0318492,-0.04815919,0.09636007,0.02236723,-0.27157646,0.02862277,0.02947375,-0.03337451,-0.02644901,-0.0115405,0.07518044,-0.01382425,-0.01419999,-0.0254916,-0.01633889,0.03947406,-0.00994646,-0.04778322,0.02283134,0.06928659,0.07120471,0.01843116,0.08052957,-0.05872879,0.03737833,0.06792417,0.26812685,-0.04332369,0.01800829,0.0606489,-0.06533723,0.04633493,0.06297266,0.03759027,-0.02403148,0.02531036,0.12930301,0.02713426,-0.02889356,0.0161362,0.02265561,-0.01560294,-0.01471576,-0.04621773,-0.01636338,0.01114066,0.0158908,0.04032277,0.09538937,-0.08087626,-0.04891645,-0.0831475,-0.03427294,0.01453201,-0.10688718,0.0466299,0.02020855,-0.01102272,-0.00748945,0.0073463,-0.01551174,-0.01242904,-0.05953245,-0.00162393,-0.00454072,-0.04768023,0.04377845,0.03756889,-0.00497277],"last_embed":{"hash":"xtm8z3","tokens":292}}},"text":null,"length":0,"last_read":{"hash":"xtm8z3","at":1751288821037},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.walkSourceContents(fn)#{3}","lines":[680,694],"size":550,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"xtm8z3","at":1751288821037}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.join(sep)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04111735,-0.06560092,0.03282122,-0.0874621,-0.0256937,-0.04875477,-0.05564076,-0.03567576,-0.0153569,-0.00275554,-0.02465074,-0.05378943,0.04869253,0.04199872,0.05655769,-0.04347547,-0.09574603,0.07453731,-0.03617789,0.00046118,0.00728287,-0.02004831,0.01984795,-0.0351092,0.01603916,0.1421655,0.01813024,-0.04626869,0.02525396,-0.17044094,-0.01177134,-0.05859865,-0.02200465,0.01247835,0.02979218,-0.01854213,-0.00249127,0.00545346,-0.0204606,0.04376578,0.02435932,-0.00232392,-0.05007189,0.00590491,0.01205825,-0.02118713,-0.02919516,0.02972506,0.03216758,-0.03621136,-0.01236998,-0.03918745,0.02066967,0.0106561,0.02437217,0.10617275,0.04475291,0.0919262,0.04249344,0.06979664,0.01981478,0.00981213,-0.12342366,0.03207903,0.06123193,0.04481167,-0.02812393,-0.0430499,-0.01538747,0.02527409,0.02134392,0.04713046,0.04698655,0.06021902,0.00520648,-0.04856584,-0.01959194,-0.02131691,-0.00056193,-0.02427004,-0.10823797,0.0024061,0.0097423,0.06070063,0.00552503,0.02250416,0.05456716,-0.01762204,0.02841744,-0.0054057,-0.03237645,-0.06511182,0.05197785,0.0645915,-0.04436524,-0.00361101,0.04344096,0.04557157,0.01119322,0.15274833,-0.06380014,0.03761258,0.00894637,-0.01627343,-0.05084782,0.01065464,-0.03843745,-0.04513183,-0.05520115,0.00546324,-0.04729862,-0.01369908,-0.05333152,-0.06318415,-0.00107998,0.00018218,0.02334563,-0.00313092,-0.05524692,0.05174036,0.01409385,0.06931941,-0.02493085,0.03620885,0.02649757,-0.00282896,0.01741579,0.02415312,0.02608691,0.10149956,0.07993972,0.04310233,-0.08591172,-0.02037841,0.00407522,-0.00243033,-0.0169393,-0.04046175,0.00006855,-0.00935847,0.03201573,-0.06597348,0.023971,-0.05388331,-0.00263081,0.07345755,-0.06351431,0.07731704,-0.00272091,-0.01544067,-0.05972905,0.03245042,-0.08039501,-0.02171772,-0.03419788,0.01377584,0.01517966,0.04046622,-0.03602425,0.00476139,0.0039183,-0.01058868,-0.00555726,0.12585975,-0.0036461,-0.08058377,-0.04306953,0.05077788,0.05017694,-0.05813322,-0.03706307,0.03984344,-0.00638584,-0.01672523,0.05473412,0.00650958,-0.04207671,-0.03278228,0.05626267,-0.00524113,-0.00710738,-0.05660373,-0.04463074,0.01776938,-0.0045097,-0.03809341,-0.00236154,-0.04656759,-0.0071392,0.037408,0.00149184,0.08239323,-0.00710302,0.01556775,-0.02533204,-0.01113475,-0.05357789,-0.03177186,0.04255984,-0.03386654,0.10957724,0.0425445,-0.01169972,0.01895327,-0.06452154,0.03404841,0.03330657,-0.01767961,0.05086597,0.03250348,-0.11835245,-0.04102955,0.05288105,0.06781177,-0.05937386,-0.03371283,0.05363731,0.04371635,0.05421162,0.08065559,-0.08692469,-0.05563433,-0.01372767,-0.18292946,0.02666702,0.00108491,-0.02445296,-0.04721092,-0.00920372,-0.03655339,-0.04994963,-0.06159598,0.02420043,0.1705599,0.00872791,-0.02704234,-0.0000712,-0.03578476,0.00630777,0.01427652,-0.02180765,0.00111206,0.01752535,0.00363595,0.02115331,-0.03846961,-0.0676588,0.03906652,-0.02735662,0.13162746,0.0300015,0.02082051,-0.03262212,0.06282196,0.00474432,-0.02666447,-0.05532374,-0.06804278,0.01141283,0.00613892,-0.00898425,0.01196491,0.00152947,-0.0447427,0.00584041,-0.03864628,-0.08938798,0.05128755,-0.02949785,-0.0839059,-0.01882986,0.03259484,0.08293558,0.03978888,-0.00562,0.06232427,0.06389978,-0.04035057,0.03928744,-0.01666875,-0.002633,-0.01920899,0.05750064,-0.00759467,0.01584352,-0.02509474,-0.08627672,0.0617466,0.03456226,0.00967893,-0.02281175,0.04791882,0.01433415,0.00085419,0.04326799,0.01215935,0.0039838,-0.00917599,0.03603827,-0.05327708,0.00985314,0.03929336,0.05940389,-0.01088862,-0.07107612,-0.02547416,0.05064321,-0.01377095,0.03904609,0.03347757,-0.00335199,0.03722331,-0.07131516,-0.0614083,0.00498574,-0.045954,0.02949203,0.04646913,-0.00474878,-0.26279983,0.03536015,0.01500503,-0.04473159,-0.01911036,0.00137812,0.0412053,-0.05196494,-0.00317619,-0.02354346,0.00531382,0.06065884,-0.02086645,0.00253357,0.00300601,0.07936341,0.05517982,0.00202661,0.06755688,-0.05203323,0.0219665,0.05443626,0.25861374,-0.02494473,0.01958663,0.04356842,-0.05080582,0.05212304,0.03858328,0.02848,-0.03086281,-0.00099449,0.15635535,-0.00513542,-0.03926311,0.01002342,0.03501972,-0.00773981,0.03592025,-0.02132399,-0.06630286,-0.01056939,0.01521237,0.02438029,0.11241342,-0.06059592,-0.04219583,-0.08734714,-0.02559249,0.03194896,-0.0800738,0.03965636,0.00567139,-0.0026468,-0.03046884,-0.01040303,-0.026009,0.00243849,-0.08421482,0.00380111,-0.00651776,-0.06221232,0.0557605,0.06037473,-0.00412217],"last_embed":{"hash":"mv2e4b","tokens":211}}},"text":null,"length":0,"last_read":{"hash":"mv2e4b","at":1751288821153},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.join(sep)","lines":[695,710],"size":450,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"mv2e4b","at":1751288821153}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.join(sep)#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05432307,-0.05354372,0.03068331,-0.08138803,-0.02209206,-0.04711327,-0.05569666,-0.02714399,-0.00817388,0.00046279,-0.0423985,-0.07091804,0.05206042,0.0418772,0.05138097,-0.0534133,-0.07678074,0.06034536,-0.02205166,-0.00222866,-0.00115482,-0.01373255,0.01206854,-0.02160037,0.02554187,0.13859956,0.01830637,-0.03547356,0.02628321,-0.16413337,-0.02821706,-0.06122128,-0.01670034,0.00956168,0.04581483,-0.02891158,-0.00390828,0.00740633,-0.01785325,0.04977594,0.03579884,0.00165024,-0.04456544,0.00456642,0.02128796,-0.03147589,-0.03625527,0.02706756,0.01764662,-0.03430841,-0.01184463,-0.03184603,0.0171158,-0.00130782,0.02573859,0.0965181,0.04875832,0.11038662,0.0437279,0.06331136,0.01213036,0.0083064,-0.13879499,0.02907283,0.05957188,0.03241613,-0.02225571,-0.05455151,-0.00768209,0.01483477,0.01368354,0.05366005,0.03810134,0.05066654,0.0122014,-0.04357442,-0.02573293,-0.01789603,-0.00286777,-0.02585925,-0.09437274,0.01425181,0.00236378,0.04668646,0.00427793,0.01644452,0.06360333,-0.00370879,0.03302728,-0.01675084,-0.01322187,-0.06829632,0.06353367,0.07358507,-0.05104761,0.00987739,0.03175998,0.05925295,0.01995793,0.15664898,-0.0634957,0.0310384,0.01159874,-0.00520446,-0.03470755,0.0085566,-0.03096003,-0.04747076,-0.04352047,0.02581204,-0.04652816,-0.00815661,-0.05668971,-0.0737116,-0.00060937,-0.00937059,0.02420896,-0.00380147,-0.06657647,0.06006286,0.02982738,0.06396394,-0.01278779,0.02097595,0.02260555,0.01057965,0.02408054,0.03664371,0.02120983,0.11038915,0.06762165,0.05029565,-0.0827894,-0.02241032,-0.00505668,-0.00353218,-0.00903678,-0.03048405,-0.00913356,-0.00413447,0.02354331,-0.06158552,0.01753505,-0.04151972,-0.00609021,0.06300534,-0.05876704,0.07061554,0.0091167,-0.01147703,-0.04812626,0.03235108,-0.08408159,-0.03128388,-0.02705429,-0.01300997,0.01404693,0.04322948,-0.03324766,0.01094706,0.00910024,-0.02140652,-0.01050987,0.12700564,0.00447615,-0.07995204,-0.04394498,0.05434613,0.03460949,-0.05726259,-0.04662087,0.03385607,-0.00682297,-0.03501039,0.0551153,0.02282819,-0.02749119,-0.03208426,0.0442844,-0.00153436,-0.00869438,-0.06469081,-0.06979118,0.01746617,-0.00755343,-0.02781533,0.01107156,-0.05522822,-0.01453525,0.05294902,0.01748993,0.09424993,-0.0013908,0.01837976,-0.02005241,-0.01807964,-0.04700818,-0.01945759,0.03306508,-0.02394259,0.12298572,0.04061227,-0.0122774,0.02084392,-0.0559239,0.0338985,0.04353876,-0.01612628,0.04371461,0.05096253,-0.1192413,-0.03427007,0.04303573,0.07094069,-0.0564119,-0.03893812,0.04332907,0.02569184,0.04410938,0.08366847,-0.09108756,-0.0316299,-0.02336498,-0.19227852,0.02909749,0.000864,-0.03674262,-0.02705159,-0.00338252,-0.0325514,-0.04066417,-0.03300926,0.01929856,0.19234487,0.00271074,-0.02788011,-0.00742871,-0.02622423,0.00883034,0.00733682,-0.02786241,-0.00964809,-0.00098192,0.00830756,0.00971833,-0.02819231,-0.07157311,0.04552921,-0.02368351,0.13115363,0.01796012,0.02683762,-0.03599067,0.0721955,0.00335387,-0.01183337,-0.08183443,-0.06211116,0.01549388,0.00544294,0.01012978,0.01413996,-0.00407962,-0.04243579,-0.00161202,-0.03431366,-0.08008632,0.05364181,-0.02191481,-0.08359523,-0.00605095,0.02228892,0.07260845,0.03703511,0.00988536,0.05996028,0.05349116,-0.04271025,0.0448832,-0.01257232,-0.01903343,-0.00191342,0.06398601,0.01093769,0.02294492,-0.01517192,-0.09362873,0.07598811,0.03670337,-0.00013902,-0.028062,0.04276571,-0.00369537,-0.0002223,0.03157597,0.01704648,0.00932132,-0.0278172,0.02935261,-0.06811103,0.01925698,0.03852746,0.04217491,-0.01891377,-0.07135837,-0.0215815,0.03003387,-0.0334706,0.03471263,0.03430836,-0.01858444,0.04549893,-0.05241164,-0.04920315,-0.00856741,-0.04443983,0.006027,0.05778063,-0.00545931,-0.26976416,0.03111024,0.02985183,-0.03102149,-0.01920358,0.00303111,0.0380386,-0.05211054,-0.00908988,-0.01066315,0.00103173,0.0404316,-0.02243486,-0.00618185,0.02593328,0.07406698,0.06251235,-0.00971168,0.0732192,-0.06021976,-0.00166858,0.05589498,0.24909572,-0.02448973,0.02086676,0.04753722,-0.03708244,0.03654434,0.03231062,0.0330955,-0.04374379,-0.00895313,0.16055652,-0.0181214,-0.04573943,0.01914003,0.02946466,-0.01578744,0.03168602,-0.01909983,-0.05046117,0.00461677,0.02267841,0.01802807,0.12469958,-0.06925666,-0.06464051,-0.08901858,-0.02715816,0.03240082,-0.08170991,0.03027713,0.01111795,-0.00736503,-0.02320631,-0.0025147,-0.0167427,0.01621355,-0.07646057,-0.01794665,-0.0174221,-0.07228412,0.050538,0.0380547,-0.00746059],"last_embed":{"hash":"4qqkzr","tokens":178}}},"text":null,"length":0,"last_read":{"hash":"4qqkzr","at":1751288821231},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.join(sep)#{2}","lines":[700,710],"size":293,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"4qqkzr","at":1751288821231}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.join(sep)#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05411366,-0.05095675,0.02260929,-0.0694413,-0.0139278,-0.04556096,-0.06545142,-0.02208816,-0.01008519,0.00521291,-0.02551224,-0.07063965,0.04630688,0.04406122,0.04797187,-0.03974776,-0.08064923,0.05403596,-0.01347887,0.01247355,-0.00175261,-0.01276894,0.01555062,-0.02794395,0.02861094,0.14135024,0.02240508,-0.03127968,0.03912885,-0.15875064,-0.02511026,-0.0377905,-0.03308393,0.01647907,0.03590154,-0.02406387,0.0016937,0.00994128,-0.0191556,0.05675879,0.04360858,0.00843683,-0.04551623,-0.0160463,0.02533231,-0.03121964,-0.03571914,0.02201999,0.01161929,-0.01984868,-0.00722904,-0.03813847,0.00939649,-0.00739304,0.02659172,0.10742727,0.038825,0.10630913,0.04222361,0.06351105,0.00676783,0.02835499,-0.14379442,0.0271916,0.04679467,0.04783609,-0.02337315,-0.04735858,-0.00796594,0.00107753,0.01351783,0.06321108,0.03734079,0.05668235,0.02121341,-0.03195791,-0.03336533,-0.00783215,-0.00735658,-0.01452486,-0.10332263,0.00075352,0.01798216,0.05460557,0.00459052,0.01431757,0.05968653,0.00541115,0.04904122,0.00007624,-0.01836207,-0.05644266,0.05252872,0.06695364,-0.05204952,0.02166096,0.03221384,0.06210841,0.00668821,0.15125537,-0.06945719,0.02289297,0.01301131,0.00122658,-0.03554301,0.00968317,-0.04799642,-0.06046154,-0.04006274,0.02359191,-0.04440695,-0.02028905,-0.06186832,-0.08650029,-0.00112492,-0.01517767,0.020043,0.00335752,-0.07484423,0.03977567,0.02845807,0.07071186,0.00002303,0.02081393,0.0265009,0.00762065,0.02477593,0.03849095,0.0241964,0.10760199,0.0589056,0.07292067,-0.07898974,-0.02809593,-0.00860988,0.00778332,-0.00185082,-0.03029971,-0.00361974,-0.00018678,0.02730094,-0.07519072,0.02448697,-0.03803981,0.00298045,0.05765378,-0.03168486,0.06522349,0.00283262,-0.00857363,-0.04584366,0.03868998,-0.09597886,-0.0182192,-0.03137821,-0.01261234,0.00791439,0.0469917,-0.03062646,-0.00427632,0.00313386,-0.02561097,-0.00650449,0.1080071,0.01445125,-0.07891932,-0.03080848,0.04131646,0.02273825,-0.05900886,-0.0353503,0.01732842,0.00100513,-0.02141291,0.04988026,0.03044577,-0.01719058,-0.02658347,0.03743979,0.01252394,-0.00663724,-0.05694576,-0.07772329,0.01165745,-0.00663204,-0.03161772,0.00975196,-0.05772313,-0.01111588,0.04689017,0.01048987,0.09881152,-0.00384626,0.01552167,-0.0264399,-0.02745868,-0.04393688,-0.02314315,0.04880281,-0.02400069,0.12238846,0.02565726,-0.02635198,0.01336472,-0.04440977,0.019622,0.03298758,-0.00278309,0.04295313,0.05269172,-0.12589605,-0.02430217,0.03842121,0.07606422,-0.06896584,-0.04746645,0.02926248,0.03403265,0.02661959,0.08056357,-0.09584884,-0.02583714,-0.01395616,-0.19590519,0.02450136,-0.00352255,-0.04448762,-0.02830464,-0.01116338,-0.01908038,-0.0451722,-0.03771941,0.02574032,0.19359419,0.00310645,-0.02241567,-0.01623237,-0.02788084,0.00033802,0.00889413,-0.04580782,-0.00406593,-0.0106958,0.01219543,0.00718499,-0.01235858,-0.08565635,0.04941497,-0.01562232,0.13430901,0.00917162,0.02038259,-0.0373746,0.07220234,-0.00548178,-0.00527738,-0.07180992,-0.05488597,0.03310379,0.00778337,0.03994114,0.00694277,-0.00349283,-0.04567536,-0.01286545,-0.02725235,-0.08558019,0.04322144,-0.01908054,-0.07903463,-0.01421363,0.00271995,0.06292946,0.01520524,0.02538038,0.07540279,0.04455797,-0.04035524,0.03480017,-0.00331213,-0.02823665,0.01268034,0.07041446,0.01211422,0.032487,-0.02213025,-0.09365689,0.08157434,0.02004291,-0.00269725,-0.03439573,0.05188546,-0.01579264,0.00783745,0.04965984,0.01959538,0.01331827,-0.03307933,0.00695075,-0.07272831,0.01837044,0.04800816,0.03808245,-0.02964143,-0.07525525,-0.01250548,0.02981253,-0.0377655,0.03862484,0.03542647,-0.01682731,0.05412358,-0.04170517,-0.03748126,-0.00041429,-0.03212524,-0.00788835,0.06065236,-0.00259302,-0.2740826,0.02194913,0.02180374,-0.01894847,-0.03068069,0.00071231,0.03475999,-0.06292156,-0.02279092,-0.01534783,-0.00991272,0.05413754,-0.00932845,-0.01737479,0.03291974,0.06982048,0.06024029,-0.0078316,0.07082324,-0.06989006,0.00215564,0.06499813,0.25250816,-0.01855874,0.01858494,0.05575858,-0.04942428,0.02880352,0.02285574,0.04003532,-0.03872511,-0.00188416,0.14304096,-0.00544728,-0.04741167,0.01857927,0.01911027,-0.02243298,0.02461222,-0.0225906,-0.04291852,0.00622016,0.02680378,0.01964381,0.13201961,-0.07862513,-0.04779036,-0.09247956,-0.0205333,0.03115033,-0.08685563,0.03309684,0.02367317,-0.017943,-0.02656421,-0.01481903,-0.00551131,0.01191281,-0.06289112,-0.01066205,-0.01863731,-0.06948756,0.04574923,0.03423602,-0.01199569],"last_embed":{"hash":"1nhgomi","tokens":168}}},"text":null,"length":0,"last_read":{"hash":"1nhgomi","at":1751288821318},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.join(sep)#{3}","lines":[702,710],"size":268,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1nhgomi","at":1751288821318}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.replaceRight(pattern, replacement)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05478828,-0.06468171,0.03405917,-0.08662762,0.00900346,-0.04197695,-0.07372653,-0.02549881,-0.02280513,0.06807752,-0.04175783,-0.02783833,0.03544405,0.03202186,0.02867787,-0.01317787,-0.02709185,0.14350742,-0.0560587,-0.01841857,0.06991554,-0.00464833,0.02605827,-0.03180331,0.03562705,0.11106537,0.01107518,-0.03264061,0.02588071,-0.2034784,-0.00617076,0.0049082,-0.04951402,0.01818501,0.02161613,-0.02217366,0.00510811,-0.00335051,-0.06464734,0.05714022,0.01647274,0.0128667,-0.02667348,-0.00526099,-0.02737425,-0.00394631,-0.00822521,-0.03273163,-0.00585179,-0.00963947,-0.02650674,-0.0759151,0.0254156,-0.00469806,0.08002897,0.09531629,0.04560711,0.06961752,0.04710917,0.02926233,0.01338864,0.0394909,-0.17549005,0.07303324,0.07001583,0.01966416,0.01063767,-0.05423781,0.00045215,0.07040946,-0.02164579,0.04191036,0.00716444,0.05932114,0.03623015,-0.04383648,0.00761371,-0.03056033,0.03462042,-0.00526074,-0.07210717,-0.02414372,-0.00029074,0.0512047,0.00195863,-0.02538394,-0.00806918,0.01689181,0.06755864,0.00017079,-0.00824021,-0.02839066,0.08097075,0.07317412,-0.04525819,0.00163744,-0.03994986,0.05476505,0.01226858,0.1659887,-0.08189943,-0.01325453,0.00079396,-0.02595187,0.03292688,-0.02562413,-0.04281513,-0.04397247,0.00674935,-0.00356235,-0.01434523,0.01831193,-0.0374593,-0.10311337,-0.01373008,-0.00026473,0.02089234,0.03905772,-0.03886667,0.05971047,-0.01970983,0.00536522,-0.0163319,-0.00093574,0.03964996,-0.00568646,0.03267251,0.0331448,-0.02380712,0.08926237,0.02881534,0.0217638,-0.06024349,0.01457785,-0.01168902,0.01906909,0.00767757,-0.00574466,-0.03071463,-0.02708452,0.04256517,-0.09908298,-0.03576112,-0.00118352,0.01252123,0.0879175,-0.0746999,0.04459208,-0.01290208,-0.02314326,-0.06359732,0.02339731,-0.07183796,0.00346919,-0.03444345,0.01707549,0.00051926,0.02589847,-0.03381632,0.01400365,0.01292871,0.00657226,0.0160549,0.11629657,-0.00692075,-0.05081039,-0.06046747,0.06028068,0.04284889,-0.02198422,-0.0299664,0.0320618,0.00773634,-0.0035065,0.05525125,0.02471035,-0.05297912,-0.08527415,0.01347518,0.01594539,0.03565535,-0.0435206,-0.06048796,0.0181751,-0.0069239,-0.06188777,0.01450598,-0.01996214,0.03737891,0.02310595,-0.02972646,0.05816671,0.02216117,-0.03814714,0.02201359,0.00863755,-0.07371304,-0.00401931,0.03674148,-0.02366689,0.10085257,0.04214835,-0.02930897,0.01001801,-0.03239466,0.0589464,0.02778132,-0.04263928,0.04056053,0.05478129,-0.12408327,-0.03183614,0.07152659,0.08803926,-0.03272936,-0.09520048,-0.00048558,0.01265124,0.05146746,0.05359072,-0.06986944,0.00209042,-0.09088268,-0.2115062,0.01398609,0.01879067,-0.00957531,0.01658135,-0.03956967,-0.03236014,-0.03789848,-0.02464359,0.0244994,0.0671074,-0.07605034,-0.01352479,-0.00684528,-0.0001531,-0.0151739,0.06153236,-0.06203989,-0.0339454,-0.01460093,0.01598656,0.00615387,-0.04624261,-0.12597531,0.08282856,-0.00331375,0.12450583,0.03158167,0.05900275,-0.03792912,0.02195882,-0.05463701,0.01902274,-0.0650509,0.01273619,0.05281354,-0.00233435,-0.01061614,0.05366002,-0.03046661,0.01390544,-0.01121395,-0.0271021,-0.08974358,0.03427089,-0.00855504,-0.07804935,-0.05796014,-0.00309519,0.06464297,-0.00046547,0.03647024,0.05034303,0.03793555,0.00462676,-0.00606174,-0.01808337,-0.00877433,0.00314657,-0.00434046,-0.01286353,0.00430666,-0.00011924,-0.04421714,0.04611915,0.03327467,0.0443841,-0.02667366,0.03043173,-0.04516295,-0.01909708,0.06356749,0.01696862,-0.01771954,-0.03433425,-0.01696795,-0.08281057,0.05915515,0.00027479,-0.04031548,0.03892699,-0.04205647,0.02975016,0.0281716,0.01332767,0.02650249,0.01479785,-0.01561397,0.09351532,-0.02526023,-0.07080801,0.01321682,-0.02958949,-0.03121747,0.06222494,-0.01640086,-0.21936543,0.06336446,0.01985638,-0.00300222,-0.05240314,0.00103999,0.0208569,-0.01655956,0.00107701,-0.03694713,-0.06929345,0.0465954,0.00349872,-0.03697596,0.03515872,0.04456478,0.04911971,-0.00813241,0.05410198,-0.05321554,0.04609351,0.01279933,0.25653499,-0.00746157,0.02286776,0.06744589,0.00038524,0.04017754,0.07556061,0.04006205,-0.0474143,0.04281317,0.13186167,0.00836719,-0.01078171,-0.04360828,-0.03168054,-0.00160782,-0.00276861,-0.0277456,-0.0527864,0.0127909,0.01583701,0.00809571,0.11838427,-0.05067648,-0.01291664,-0.02490903,-0.01468766,0.03130549,-0.07656378,0.08525421,-0.01605348,-0.00827864,-0.07058761,0.0230854,-0.06477159,-0.02748704,-0.01512803,0.02248961,0.01578031,-0.03123041,0.03783775,0.07361179,-0.01153851],"last_embed":{"hash":"145q7aw","tokens":137}}},"text":null,"length":0,"last_read":{"hash":"145q7aw","at":1751288821403},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.replaceRight(pattern, replacement)","lines":[711,724],"size":368,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"145q7aw","at":1751288821403}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.toString()": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04192173,-0.03225192,0.0227839,-0.05377039,-0.02343816,-0.04506423,-0.07809877,-0.05060994,-0.0164224,0.00126584,-0.01278401,-0.07367493,0.03007544,0.02923284,0.04866133,-0.01886949,-0.05651539,0.07837136,-0.05381998,0.01997197,0.08793823,0.00796181,0.02680208,-0.04481214,-0.00005729,0.11288361,0.05919455,-0.07337262,0.03082186,-0.17208584,-0.00614248,-0.04093585,-0.05135454,0.00162603,0.03386647,-0.00591874,0.01320574,-0.01832202,-0.03670943,0.05707826,0.03592679,0.01961116,-0.03183149,-0.00556539,0.01483287,-0.05097842,-0.04079453,-0.02460917,-0.00425895,-0.03134068,-0.02257843,-0.0013037,0.00062444,-0.04423416,0.04411343,0.08615443,0.03696073,0.10315027,0.00884763,0.05596216,0.03373307,0.03938385,-0.15079923,0.03633694,0.0512596,0.03143025,-0.03023544,0.00657948,0.00262693,0.02824794,0.04502157,0.02645718,0.03630361,0.03741325,0.01954086,-0.04686486,-0.00705557,0.02812971,0.0067444,0.00225051,-0.09383307,0.00270336,0.00268941,0.07283261,0.04176318,-0.00913129,0.01142955,-0.01437906,0.04705648,0.01249685,-0.02952264,-0.0608092,0.02015584,0.07482948,-0.05752527,-0.00730165,-0.00427281,0.03842464,0.01167936,0.13962221,-0.06937745,0.01246254,0.02245126,-0.01369924,-0.04086385,-0.00293783,-0.01857457,-0.07869998,-0.02955114,0.00750426,-0.02008869,-0.00746324,-0.03684199,-0.06775141,-0.00977108,-0.01408053,0.01965773,0.01076754,-0.03691003,0.04182087,0.0208434,0.0582091,-0.00359739,0.00072462,0.03857153,0.02395096,0.04037645,0.02169496,0.03637774,0.12939037,0.05476048,0.07712896,-0.08839618,0.01359537,-0.00325892,-0.02111644,0.00667649,-0.00201101,-0.00754546,-0.00160883,0.0250575,-0.07906321,-0.05079644,-0.0248217,-0.03033289,0.06762774,-0.03496687,0.0494953,0.00555192,-0.0033659,-0.04200194,0.00326643,-0.06501757,-0.02480944,-0.02460359,-0.00718931,0.00305825,0.01057726,-0.02331511,0.02530943,-0.0170936,-0.01125765,0.00034819,0.10405427,-0.00264919,-0.07340533,-0.02312781,0.05244565,0.07582434,-0.09452478,-0.0417145,0.02161207,-0.00968965,0.00808987,0.03300454,0.01522583,-0.06094784,-0.07638203,0.03082207,0.02692975,0.00916063,-0.05241421,-0.04427153,0.01589268,0.00774581,-0.03879914,-0.00320307,-0.04713661,-0.02882055,0.0422738,0.01040906,0.02165588,0.01707826,-0.01324767,-0.01004848,-0.01532143,-0.04002697,-0.0240405,0.03294298,-0.00686714,0.11248504,0.02139998,-0.03947182,0.01623525,-0.04968082,-0.00054744,0.0255244,-0.02349014,0.00917936,0.02604454,-0.09816198,-0.0042959,0.10277963,0.09521352,-0.05708089,-0.06380383,0.04346054,0.0423616,0.06291989,0.07575328,-0.12107059,-0.07802484,-0.07520029,-0.199433,-0.02302847,0.04974797,-0.05010296,-0.01805475,0.00027922,-0.06060612,-0.04236073,-0.05899787,-0.00240109,0.10760922,-0.00202356,0.0032098,0.0100523,-0.03270706,0.03845319,0.04033787,-0.0199702,-0.01002043,0.03237651,0.02165166,-0.01790224,-0.04725658,-0.11081918,-0.0052167,-0.04311505,0.16392528,0.03701237,0.02573011,-0.01324669,0.03983425,-0.0361531,-0.00144665,-0.05653709,-0.01211574,0.00395514,0.01852091,0.04277647,0.03440142,0.01845103,-0.00433319,-0.01287302,-0.04374286,-0.07810777,0.03827574,-0.02551276,-0.06190585,-0.05453775,0.03337775,0.03463138,-0.00469298,0.01290666,0.09417418,0.03454734,-0.00676571,0.0217365,-0.00058063,0.00287381,0.01921728,0.02137322,-0.01317028,0.01365559,-0.03335979,-0.07418971,0.06852994,0.04207301,0.01809848,-0.02378713,0.04626651,-0.0414821,0.00413644,0.05850809,0.02924753,-0.02734286,-0.00210506,-0.00009235,-0.04921812,-0.00960418,0.00616201,0.02599936,0.00887048,-0.0457015,0.03012552,-0.00499968,0.00554158,0.03179186,0.01295296,-0.00718298,0.06011982,-0.06892852,-0.01000307,0.01573305,0.00883217,-0.05258983,0.08154056,-0.01502313,-0.26852968,0.03107176,0.03885904,-0.01055682,-0.04412303,0.00302172,0.06995349,-0.03615297,-0.03334126,-0.01445469,-0.02588516,0.05234209,-0.02369452,-0.04688914,0.02074063,0.08670333,0.08352741,0.01188477,0.08404645,-0.05734782,0.0550085,0.07952449,0.24784283,-0.01622273,0.0281812,0.0847321,-0.09321556,0.05170019,0.05697368,0.0359122,-0.03548183,0.03658441,0.14078,-0.01565114,-0.02083022,-0.01365608,0.02568484,-0.01501256,-0.0109896,-0.0630173,0.00080579,-0.02108767,-0.00114153,-0.00206366,0.10853817,-0.0488043,-0.01207925,-0.05236042,-0.04778744,0.03426343,-0.09759703,0.07048071,0.00060265,-0.01287045,0.00087075,0.02031822,-0.00520582,-0.03221679,-0.04697064,-0.02188826,-0.00757875,-0.06126954,0.06860624,0.03896291,0.01801982],"last_embed":{"hash":"15acsq3","tokens":176}}},"text":null,"length":0,"last_read":{"hash":"15acsq3","at":1751288821471},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.toString()","lines":[725,743],"size":385,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"15acsq3","at":1751288821471}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.toString()#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03769509,-0.03008103,0.02092042,-0.05344902,-0.02567451,-0.04421145,-0.07648961,-0.04741411,-0.01601228,-0.00237053,-0.00969181,-0.07284721,0.02874011,0.03099738,0.04946004,-0.01816731,-0.05533257,0.07887984,-0.05092547,0.02366449,0.08991016,0.00637107,0.03014038,-0.04098611,0.00027401,0.1100211,0.06144422,-0.07642817,0.02957379,-0.17070898,-0.00911318,-0.0467752,-0.05607412,0.00075768,0.03054051,-0.00586153,0.01229141,-0.01303515,-0.03979336,0.05744907,0.03488508,0.01715219,-0.03385247,-0.00274129,0.01632237,-0.0547648,-0.04202488,-0.0256598,-0.00647884,-0.02947634,-0.022314,-0.00298656,0.00199146,-0.0447058,0.04603352,0.08904852,0.03289875,0.10223193,0.01365499,0.05738121,0.03657359,0.03921003,-0.14798261,0.03874427,0.0494745,0.03230067,-0.03194731,0.00271242,0.003439,0.02869518,0.04541045,0.02994379,0.03569459,0.03835848,0.02350288,-0.04754366,-0.00540868,0.02559204,0.00720563,0.00480429,-0.09087512,0.00135846,0.00178549,0.06971771,0.04176807,-0.00948712,0.00809179,-0.01830054,0.0461205,0.01527029,-0.02874862,-0.06127964,0.01377857,0.07382996,-0.06206988,-0.00453941,-0.00587531,0.04182284,0.00986686,0.14119123,-0.07152802,0.0135519,0.02365698,-0.01696216,-0.03811819,0.00019988,-0.02031235,-0.07754359,-0.02579195,0.00800446,-0.02028199,-0.00369646,-0.03543604,-0.06735063,-0.01117203,-0.01419887,0.01863691,0.01056643,-0.03557835,0.04305385,0.02169869,0.05822103,-0.00447681,-0.00274673,0.03973573,0.02452914,0.0426865,0.01934894,0.03491944,0.1315116,0.05470416,0.07674947,-0.08976746,0.01473262,-0.00460302,-0.01943616,0.0033857,-0.0004634,-0.01204079,0.00167561,0.02476511,-0.07532539,-0.05538028,-0.0208652,-0.0308416,0.06035441,-0.04175797,0.0496578,0.00684684,-0.00421681,-0.04240722,0.00484341,-0.06172776,-0.02990067,-0.02748582,-0.00717583,-0.00189998,0.00972256,-0.02211761,0.02409391,-0.01960858,-0.00697287,0.00369909,0.1062448,-0.00539903,-0.0745466,-0.02011327,0.05127375,0.07866737,-0.09201714,-0.04457249,0.01872697,-0.01258909,0.00738492,0.0316726,0.01383516,-0.06394084,-0.07747921,0.03050479,0.02790915,0.01196803,-0.05346307,-0.04207148,0.01736453,0.01248315,-0.04028158,-0.0052121,-0.04352714,-0.02679652,0.04077044,0.00861094,0.0188192,0.01815438,-0.01186893,-0.0087057,-0.01135777,-0.03698407,-0.02601178,0.03082691,-0.00620926,0.11276338,0.018261,-0.0379025,0.01638506,-0.04934322,-0.00352251,0.03110382,-0.02823311,0.00956577,0.02441366,-0.09890492,-0.00172052,0.10632332,0.09487128,-0.05924502,-0.06052836,0.04221516,0.04336631,0.06297652,0.07605934,-0.12078744,-0.07909621,-0.07294335,-0.20056088,-0.02719183,0.05157351,-0.05111072,-0.01893028,-0.00010245,-0.06063843,-0.0373904,-0.05520169,-0.008523,0.10178231,-0.00128102,0.00297785,0.01055457,-0.03272586,0.04056627,0.04257267,-0.01585909,-0.00748141,0.03080638,0.02082002,-0.02064693,-0.04758711,-0.11153603,-0.00601169,-0.0450091,0.16353253,0.0351027,0.02459168,-0.01380309,0.03535891,-0.03920544,-0.0020593,-0.05639149,-0.01221621,0.0014663,0.01763247,0.04365536,0.03201739,0.01612944,-0.00453483,-0.01261776,-0.04582722,-0.0820791,0.04076092,-0.02524916,-0.06184401,-0.05234051,0.03123729,0.0343194,-0.00698326,0.01474964,0.09304732,0.02940718,-0.00173399,0.0188662,0.00043086,0.00594265,0.02329359,0.01922684,-0.01158162,0.01912046,-0.03564774,-0.07404471,0.06822249,0.03984636,0.02053715,-0.02333826,0.04280667,-0.0435297,0.00372576,0.06218317,0.02948445,-0.02799417,-0.002028,0.00190872,-0.04886065,-0.00918938,0.00880245,0.02645513,0.00923621,-0.03973171,0.03480493,-0.00453648,0.00474901,0.02933948,0.0161244,-0.00701959,0.06402487,-0.06733456,-0.00878297,0.01494919,0.01278018,-0.05464923,0.08111702,-0.01383536,-0.2676149,0.02790429,0.04105299,-0.0127048,-0.0415703,0.0022589,0.06897523,-0.03537169,-0.0333667,-0.01301265,-0.02644213,0.0496184,-0.0246389,-0.04744614,0.01964085,0.08789632,0.08394957,0.01354918,0.08201631,-0.05734706,0.05737991,0.07907962,0.25246423,-0.01577809,0.02365389,0.08403151,-0.09371443,0.05129606,0.05621488,0.03175631,-0.03463977,0.03989116,0.13798915,-0.01857137,-0.0213706,-0.01448489,0.02475696,-0.01202526,-0.01318376,-0.06748989,0.00489231,-0.01838162,-0.00248796,-0.00512915,0.10719394,-0.0476477,-0.01261497,-0.04620412,-0.0491379,0.03335664,-0.09677138,0.07036303,0.00098452,-0.01420387,0.00366224,0.01930576,-0.00490718,-0.03528198,-0.04733134,-0.02137678,-0.00557815,-0.06014604,0.06596719,0.03972347,0.02119914],"last_embed":{"hash":"i0ec5i","tokens":173}}},"text":null,"length":0,"last_read":{"hash":"i0ec5i","at":1751288821530},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.toString()#{1}","lines":[727,743],"size":347,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"i0ec5i","at":1751288821530}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05628852,-0.03456379,0.03454204,-0.07796079,-0.00564469,-0.06068581,-0.06712368,-0.03023754,-0.01823969,0.01376207,0.01054142,-0.08482274,0.01466123,0.01998591,0.03607912,-0.02773424,-0.04583333,0.07453,-0.02751031,0.00583222,0.10425993,0.00268961,0.02114987,-0.06317779,0.01706849,0.11094899,0.05642059,-0.02775561,0.02950494,-0.19079451,-0.01255659,-0.06492332,-0.03299139,-0.01028749,0.02151111,-0.03633043,0.01420898,0.00627326,-0.017781,0.03984662,0.04077422,0.01895892,-0.0480031,-0.01221852,0.00787518,-0.05532208,-0.02605932,0.00009205,-0.00638876,-0.03832348,-0.02230667,0.00909593,-0.00744122,-0.02714655,0.03295173,0.05949466,0.04949022,0.09606615,0.02616656,0.04044632,0.00437956,0.05815134,-0.16283911,0.04039297,0.06815819,0.03080394,-0.02496407,0.00028048,0.00598679,-0.01149514,0.03041413,0.03707557,0.02129751,0.01686661,0.01588978,-0.05175433,-0.01739197,0.04833034,0.00502858,0.00977758,-0.07658582,0.03276245,0.00797325,0.07094608,0.05395623,-0.01379706,0.01617893,-0.01016853,0.05823299,0.01476053,-0.02474409,-0.05504084,0.03235027,0.08716302,-0.05213239,-0.02363302,0.00182664,0.06423123,0.01363055,0.14000413,-0.0656832,-0.00132112,0.01783036,0.00054393,-0.02678574,-0.03478711,-0.04045177,-0.04841502,-0.05690594,0.04042291,-0.00083218,-0.01140744,-0.07825842,-0.08147022,-0.0158504,-0.02241817,0.02973729,0.00364967,-0.05289331,0.04334547,-0.01227505,0.06921748,-0.00560209,0.03249758,0.06432018,0.00687122,0.03718014,0.02848498,0.0379166,0.07912184,0.01407467,0.04487733,-0.0996045,0.02050676,0.03456676,-0.02168311,0.03181738,-0.01238102,-0.02074505,0.01570533,0.03223711,-0.085158,-0.03070527,-0.00818751,-0.03572214,0.08881751,-0.04473709,0.07151882,-0.01792702,0.00223696,-0.02917352,0.00298897,-0.09798063,-0.00854445,-0.00859715,0.02252198,-0.0118991,0.01197893,-0.04132236,0.01603222,0.00207954,-0.01291176,0.01007683,0.09581124,-0.0061314,-0.06963558,-0.02543826,0.05208686,0.06066875,-0.08988382,-0.03632408,0.01411065,0.01082913,-0.02918299,0.02716678,0.00753436,-0.05435488,-0.07039256,0.02585112,0.00204698,-0.00890939,-0.05167873,-0.02846125,0.02179457,-0.01919079,-0.05290687,-0.0157745,-0.05368889,-0.03894018,0.05214342,0.02465228,0.01822415,0.00493992,-0.01286363,-0.00944444,-0.03404511,-0.04492817,0.00629309,0.06150775,-0.04747388,0.11663976,0.03317212,-0.02708103,0.03205917,-0.06208137,-0.00347407,-0.00165099,0.00446122,0.0150719,0.03049527,-0.09905979,-0.01357163,0.06842365,0.08440594,-0.07724435,-0.04771903,0.05167279,0.03057942,0.03931874,0.086353,-0.10600966,-0.05460227,-0.06281807,-0.19611412,0.00578933,0.02569553,-0.05594498,-0.0173794,-0.01451572,-0.06355978,-0.04600319,-0.05180154,-0.01469715,0.12650645,0.00170614,0.01411272,0.02551879,-0.04597562,0.02122453,0.00982006,-0.0294261,-0.01641989,0.02716901,0.01429476,-0.00329858,-0.04262064,-0.09339214,0.00060994,-0.0553428,0.14905153,0.04344301,0.03890393,-0.0194205,0.04566511,-0.03561233,-0.00736981,-0.07388988,-0.01801087,-0.01313157,0.01675738,0.03581846,0.03955302,0.03005769,0.0016684,-0.01029318,-0.03787388,-0.07200213,0.0532344,-0.03639959,-0.05335366,-0.0214537,0.02538568,0.05004603,-0.00192858,-0.00087066,0.08489768,0.01010358,0.00775861,0.02716284,-0.02500505,-0.00758053,0.00970216,0.03083155,0.02608725,0.01062003,-0.04873905,-0.06775595,0.0737777,0.02591407,0.02900719,-0.03361872,0.05539934,-0.0350371,-0.00492939,0.06462,0.01904095,-0.00432224,0.03185517,-0.0233037,-0.01737065,-0.03068347,0.02913156,0.01985451,0.00206886,-0.04506384,0.04621147,0.00865611,-0.00821722,0.02745994,0.00079961,0.01205754,0.05701462,-0.05426713,-0.01690402,0.02325893,-0.03489469,-0.05880133,0.07405693,-0.00920343,-0.24935524,0.02062855,0.03781589,-0.00568968,-0.03369102,-0.01207568,0.08079863,-0.03374989,-0.00132817,-0.01255015,-0.02764721,0.05685174,-0.02713203,-0.03878008,0.01879607,0.09187891,0.10479905,-0.00106684,0.09808586,-0.06727204,0.05788147,0.07930009,0.24262676,-0.01008386,0.04112306,0.05935944,-0.0683427,0.03794878,0.06393494,0.01578004,-0.04940175,0.03420344,0.1436279,-0.01064401,-0.01426425,0.01943474,0.03481587,-0.01582238,-0.00832841,-0.05968459,-0.00963804,-0.0132126,-0.01642528,0.00939175,0.09843108,-0.03760492,-0.0112944,-0.07852025,-0.04289963,0.06371894,-0.08797541,0.07525418,0.02249411,-0.02525436,-0.01125297,0.01532605,-0.02181321,-0.04731753,-0.03110765,-0.03449108,0.01551719,-0.04107312,0.09340386,0.03016905,0.0068049],"last_embed":{"hash":"7annav","tokens":246}}},"text":null,"length":0,"last_read":{"hash":"7annav","at":1751288821598},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])","lines":[744,766],"size":599,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"7annav","at":1751288821598}},
"smart_blocks:Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05434562,-0.0329063,0.03414148,-0.07878477,-0.00952654,-0.05852759,-0.06635439,-0.02545671,-0.01761258,0.01230404,0.01254252,-0.08180537,0.01390031,0.01935739,0.03649298,-0.02613515,-0.04827189,0.07418771,-0.0259512,0.00548349,0.10714896,-0.00014512,0.02386338,-0.059443,0.01961784,0.10756189,0.05983313,-0.03239459,0.0299387,-0.18990362,-0.01258453,-0.06895053,-0.03577333,-0.00955583,0.01820163,-0.0372581,0.01387023,0.01106127,-0.01910914,0.03927654,0.038733,0.01903223,-0.04984341,-0.01090837,0.01000977,-0.05752142,-0.02895014,-0.0026726,-0.00848652,-0.0380676,-0.01939633,0.00871946,-0.00969012,-0.02987043,0.03447233,0.05838242,0.04682295,0.09418155,0.02899144,0.04113862,0.00535842,0.05956248,-0.16052099,0.04151687,0.06922791,0.03349821,-0.02763655,-0.00612734,0.00641388,-0.00923566,0.02882449,0.03976865,0.02254044,0.01543617,0.01919415,-0.05278423,-0.01590815,0.04667881,0.00567469,0.01024473,-0.07410944,0.03003163,0.01061222,0.06874359,0.05376337,-0.01198399,0.01178352,-0.0151468,0.05889594,0.01419079,-0.02298993,-0.05275012,0.03267189,0.08385289,-0.05308827,-0.02244605,-0.0002962,0.06386559,0.01187818,0.14014725,-0.06727227,-0.00550954,0.01521624,-0.00144364,-0.02564869,-0.03319508,-0.04122314,-0.04856632,-0.05551436,0.04252438,-0.00074691,-0.00887864,-0.08071844,-0.08198238,-0.01557208,-0.02461354,0.03243544,0.00079398,-0.05127668,0.04526838,-0.01432643,0.06925113,-0.00544264,0.02997955,0.06277572,0.00700859,0.03749433,0.02965556,0.03819651,0.07781529,0.01250029,0.04501746,-0.10211763,0.01930549,0.03663409,-0.02167615,0.03454553,-0.01169248,-0.0210588,0.01897395,0.03118981,-0.0795745,-0.03320068,-0.00781842,-0.0337123,0.08743189,-0.04498499,0.07137223,-0.0165358,0.00435326,-0.02993393,0.00281499,-0.09598944,-0.01115794,-0.01177655,0.02019035,-0.01568685,0.01234822,-0.03877176,0.01456859,0.0025409,-0.01105164,0.01235073,0.09521845,-0.00978724,-0.07139613,-0.02439324,0.05227279,0.06383286,-0.08713628,-0.04045297,0.01205801,0.00746944,-0.02932666,0.02807197,0.00489649,-0.05631083,-0.06912778,0.02592461,0.00342128,-0.00741285,-0.04817476,-0.02495004,0.02499583,-0.01556082,-0.0524327,-0.01657428,-0.05441311,-0.0392059,0.05114665,0.0229553,0.01836695,0.00625508,-0.01317568,-0.00853448,-0.03321534,-0.04230999,0.00411699,0.06241892,-0.04726212,0.12033052,0.0303221,-0.02592509,0.03384907,-0.06194304,-0.0013844,0.00105277,0.00272702,0.01319428,0.03172091,-0.10078305,-0.01282164,0.07206653,0.08289135,-0.07618482,-0.04692724,0.04856422,0.03217814,0.03912952,0.08684838,-0.10585502,-0.05330586,-0.0565973,-0.19824609,0.00477866,0.02852264,-0.0582368,-0.01579792,-0.01614237,-0.06247985,-0.04696567,-0.04855153,-0.01973531,0.12410483,0.00357515,0.01555701,0.02712778,-0.04739859,0.02354835,0.00940659,-0.02884937,-0.0147721,0.02617334,0.01079159,-0.00311117,-0.03875137,-0.09387166,-0.00314925,-0.05771716,0.15104033,0.04219988,0.03691817,-0.02103562,0.04515728,-0.03528377,-0.01051355,-0.07254951,-0.01820442,-0.01276864,0.01678853,0.03442876,0.04063889,0.02696297,0.00016483,-0.00940268,-0.03948984,-0.07590625,0.05236159,-0.03695424,-0.05497703,-0.02034164,0.02774457,0.05222957,-0.00101091,-0.00024003,0.08376231,0.00983463,0.01521,0.02475015,-0.02741638,-0.00411711,0.01137769,0.02994445,0.02993994,0.01515523,-0.05099456,-0.06752561,0.07493264,0.02281846,0.03084796,-0.03459555,0.05469053,-0.03644643,-0.00514081,0.06557173,0.01542515,-0.00400242,0.03024664,-0.0238811,-0.01720812,-0.028529,0.02583585,0.01732071,-0.00007896,-0.0427577,0.05192418,0.01014799,-0.007594,0.02618107,0.00315313,0.01464612,0.06105141,-0.05268298,-0.01658356,0.02480757,-0.03361715,-0.05757434,0.07265363,-0.00834443,-0.25261685,0.01948976,0.03824417,-0.0048413,-0.03256484,-0.01216762,0.0799289,-0.0356946,-0.00080438,-0.01001693,-0.03245831,0.05448794,-0.0250585,-0.03628102,0.01978332,0.08992358,0.10582599,0.00011434,0.09620732,-0.06926169,0.05990387,0.08002028,0.24421421,-0.00793565,0.03783311,0.056723,-0.06853274,0.04028789,0.06117766,0.01447657,-0.04997544,0.03453187,0.14002231,-0.01274263,-0.01292159,0.01997205,0.03357794,-0.01338818,-0.01050303,-0.06301017,-0.00987295,-0.01239168,-0.01747991,0.00949831,0.09855992,-0.03921462,-0.01101712,-0.07325276,-0.04746857,0.06257555,-0.08805203,0.07821155,0.02119348,-0.02638188,-0.01062178,0.01514578,-0.02025651,-0.05049453,-0.02929749,-0.03629091,0.01958173,-0.03789626,0.09195281,0.03074487,0.00961492],"last_embed":{"hash":"1uerww8","tokens":243}}},"text":null,"length":0,"last_read":{"hash":"1uerww8","at":1751288821723},"key":"Projects/Piecework/node_modules/source-map-js/README.md#Source Map JS#API#SourceNode#SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])#{1}","lines":[746,766],"size":530,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1uerww8","at":1751288821723}},
