
"smart_sources:Projects/Piecework/node_modules/styled-jsx/readme.md": {"path":"Projects/Piecework/node_modules/styled-jsx/readme.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0358662,-0.02830899,0.04559233,-0.05360533,0.02723911,0.0078146,-0.10619555,-0.02448735,-0.01480043,-0.03311314,-0.00457495,-0.00037096,0.04459805,-0.00552959,0.03774206,0.0000532,0.02420882,0.04748409,-0.04672616,0.02189092,0.02543748,-0.00721956,0.00802914,-0.04449809,-0.02120154,0.03862961,-0.02189245,-0.02304513,0.02474697,-0.18472104,0.03162256,-0.01923743,-0.00119877,0.00701579,0.02468425,-0.05010521,-0.01959416,0.06440002,-0.07286635,0.01164511,0.03806913,0.04797927,-0.03993039,-0.04044061,-0.0073639,-0.04851465,0.04069164,-0.0078353,-0.03539333,-0.00804699,-0.02258337,-0.02820316,0.08781952,-0.00330915,0.00906648,0.15217657,0.04719279,0.06768421,-0.00864044,-0.00355294,0.00841172,0.06069853,-0.15573458,0.0727849,0.01422339,0.02391778,-0.05442353,-0.02932897,0.04883746,0.00131137,0.03259685,0.07707055,0.00597406,0.08517477,-0.01350047,-0.02430083,0.02163417,-0.04283333,0.04131224,-0.01997333,-0.09489116,-0.03120427,-0.01865679,0.01611897,0.02717852,0.01631824,0.03057967,0.00541656,0.05696054,0.03309353,-0.03198449,-0.08547869,0.03380113,0.00746547,-0.02304849,-0.01862215,0.02743475,0.0266666,-0.0349015,0.11993996,-0.06372388,0.05933637,0.0600879,0.00281968,0.06453451,-0.02203641,-0.04596472,-0.05533079,-0.03841526,-0.00903479,0.00901952,-0.01718327,-0.0452106,-0.05836847,-0.03542014,-0.00566702,-0.01588029,-0.00477183,0.02177075,0.00970913,0.01369273,0.07470123,0.0200099,-0.01468945,0.03172715,0.03415287,0.01617982,0.03305025,-0.01783695,0.10679025,0.01162778,0.09175281,-0.06919184,-0.06724979,-0.02748283,0.02809197,0.00919709,0.0229259,-0.03261069,0.01172256,0.02270359,-0.01559012,0.04298688,-0.06322075,0.03578066,0.02223362,-0.0382699,0.06858222,-0.01721229,-0.00935166,-0.05836197,0.04398878,-0.08825009,0.01781972,-0.03515149,0.01587589,-0.00419061,0.01778485,-0.08266252,0.01941242,-0.00161398,-0.04612329,0.00800276,0.02802179,0.04743445,-0.10044655,-0.03393598,0.05997851,0.00270226,-0.08389876,-0.02973571,0.02876814,-0.02479648,0.01683473,0.03091264,0.01207979,-0.06463733,-0.02443637,0.06806517,0.03485534,0.08063476,-0.0417182,-0.00098289,0.05363279,-0.02721022,-0.0544703,0.041204,-0.04642536,-0.05640654,-0.02379913,-0.01798504,-0.03608245,-0.01433022,0.01805609,0.00214156,-0.01713289,-0.06779534,-0.01030965,0.02227488,-0.00791617,0.14396463,0.01666583,0.01259829,0.07233904,-0.0540569,0.05390155,-0.01031556,-0.01914826,0.01836089,-0.01996761,-0.11598997,0.03116631,0.09334871,0.08994941,-0.01679702,-0.0412664,0.01848378,0.07375918,0.02824174,0.06538649,-0.00091266,-0.00466853,-0.07497595,-0.22850609,-0.00904463,0.00254357,-0.03920641,-0.0143505,-0.0202987,0.03150905,-0.02933856,-0.05218398,0.0555542,0.11402536,0.02778943,0.01812171,-0.02777985,-0.03531465,0.01642471,0.01710247,-0.05746204,-0.07699622,-0.04117272,0.04036111,-0.02276237,-0.05738929,-0.09601762,0.06164183,-0.0087425,0.16914989,0.03428279,0.00613514,-0.03475189,0.04403513,-0.02392789,-0.02924854,-0.10073764,0.0670614,0.04948373,0.06818455,-0.0231619,0.02015203,0.03944914,-0.02212375,-0.02324187,0.01016374,-0.05242706,0.0398916,-0.04554946,-0.04941873,-0.01769216,-0.01981946,0.03148022,0.01502435,0.04895014,0.04027186,0.12009822,-0.02078725,0.02700009,-0.02887689,-0.06386434,0.01740456,0.00658245,-0.00116035,-0.00116328,-0.02003578,-0.08301639,0.05920076,0.05097208,0.02667709,-0.02995797,0.03743368,-0.04316676,-0.08159728,0.0905996,0.01584791,-0.01374805,0.03829755,0.03716366,-0.05477914,0.09276346,0.0121553,0.02041332,0.01833574,-0.00449938,0.02666559,-0.03595771,-0.03218197,-0.00770123,-0.02655484,-0.03702908,0.03607576,-0.0321213,-0.10326719,0.00124016,-0.03184685,0.02170927,0.06315341,0.0116915,-0.22044007,-0.01785081,0.01471833,0.0149114,-0.07361784,-0.00055431,0.05540017,-0.10040901,-0.01763594,-0.00474502,0.00312526,0.03116226,0.00486375,-0.00790175,0.00474469,0.00284121,0.06670884,-0.01757877,0.06990738,-0.07496762,0.00199085,0.01036641,0.24494851,-0.02326968,-0.0074005,0.03687507,-0.03581975,-0.02512057,0.06094528,0.07477748,0.01850585,0.01215059,0.12037081,0.0186822,-0.03938415,-0.02682391,-0.038039,0.01310799,0.02573775,-0.0010264,0.01748434,0.02713334,-0.01291337,-0.00135904,0.05411068,-0.11631796,-0.03880733,-0.01761586,0.0117818,0.01109775,-0.03233893,0.0587514,-0.01006233,0.00480267,0.02814557,-0.00472136,-0.07225183,-0.02180875,-0.04355161,0.02302863,0.00046905,-0.0554533,0.04060796,0.03921093,0.00381425],"last_embed":{"hash":"qhddac","tokens":469}}},"last_read":{"hash":"qhddac","at":1751288831401},"class_name":"SmartSource","last_import":{"mtime":1751244542658,"size":31703,"at":1751288765735,"hash":"qhddac"},"blocks":{"#styled-jsx":[1,1071],"#styled-jsx#{1}":[3,8],"#styled-jsx#{2}":[9,9],"#styled-jsx#{3}":[10,14],"#styled-jsx#{4}":[15,15],"#styled-jsx#{5}":[16,17],"#styled-jsx#{6}":[18,18],"#styled-jsx#{7}":[19,20],"#styled-jsx#{8}":[21,24],"#styled-jsx#{9}":[25,25],"#styled-jsx#{10}":[26,26],"#styled-jsx#{11}":[27,31],"#styled-jsx#{12}":[32,34],"#styled-jsx#{13}":[35,35],"#styled-jsx#{14}":[36,41],"#styled-jsx#{15}":[42,42],"#styled-jsx#{16}":[43,43],"#styled-jsx#{17}":[44,44],"#styled-jsx#{18}":[45,46],"#styled-jsx#Getting started":[47,81],"#styled-jsx#Getting started#{1}":[49,81],"#styled-jsx#Configuration options":[82,112],"#styled-jsx#Configuration options#{1}":[84,85],"#styled-jsx#Configuration options##`optimizeForSpeed`":[86,100],"#styled-jsx#Configuration options##`optimizeForSpeed`#{1}":[88,100],"#styled-jsx#Configuration options##`sourceMaps`":[101,104],"#styled-jsx#Configuration options##`sourceMaps`#{1}":[103,104],"#styled-jsx#Configuration options##`styleModule`":[105,108],"#styled-jsx#Configuration options##`styleModule`#{1}":[107,108],"#styled-jsx#Configuration options##`vendorPrefixes`":[109,112],"#styled-jsx#Configuration options##`vendorPrefixes`#{1}":[111,112],"#styled-jsx#Features":[113,125],"#styled-jsx#Features#{1}":[115,115],"#styled-jsx#Features#{2}":[116,116],"#styled-jsx#Features#{3}":[117,117],"#styled-jsx#Features#{4}":[118,118],"#styled-jsx#Features#{5}":[119,119],"#styled-jsx#Features#{6}":[120,120],"#styled-jsx#Features#{7}":[121,121],"#styled-jsx#Features#{8}":[122,122],"#styled-jsx#Features#{9}":[123,123],"#styled-jsx#Features#{10}":[124,125],"#styled-jsx#Using in Next.js":[126,129],"#styled-jsx#Using in Next.js#{1}":[128,129],"#styled-jsx#How It Works":[130,341],"#styled-jsx#How It Works#{1}":[132,144],"#styled-jsx#How It Works#Why It Works Like This":[145,153],"#styled-jsx#How It Works#Why It Works Like This#{1}":[147,148],"#styled-jsx#How It Works#Why It Works Like This#{2}":[149,149],"#styled-jsx#How It Works#Why It Works Like This#{3}":[150,150],"#styled-jsx#How It Works#Why It Works Like This#{4}":[151,151],"#styled-jsx#How It Works#Why It Works Like This#{5}":[152,153],"#styled-jsx#How It Works#Targeting The Root":[154,173],"#styled-jsx#How It Works#Targeting The Root#{1}":[156,173],"#styled-jsx#How It Works#Global styles":[174,195],"#styled-jsx#How It Works#Global styles#{1}":[176,195],"#styled-jsx#How It Works#One-off global selectors":[196,220],"#styled-jsx#How It Works#One-off global selectors#{1}":[198,220],"#styled-jsx#How It Works#Dynamic styles":[221,317],"#styled-jsx#How It Works#Dynamic styles#{1}":[223,224],"#styled-jsx#How It Works#Dynamic styles#Via interpolated dynamic props":[225,270],"#styled-jsx#How It Works#Dynamic styles#Via interpolated dynamic props#{1}":[227,270],"#styled-jsx#How It Works#Dynamic styles#Via `className` toggling":[271,294],"#styled-jsx#How It Works#Dynamic styles#Via `className` toggling#{1}":[273,294],"#styled-jsx#How It Works#Dynamic styles#Via inline `style`":[295,317],"#styled-jsx#How It Works#Dynamic styles#Via inline `style`#{1}":[297,317],"#styled-jsx#How It Works#Constants":[318,341],"#styled-jsx#How It Works#Constants#{1}":[320,341],"#styled-jsx#Server-Side Rendering":[342,714],"#styled-jsx#Server-Side Rendering#{1}":[344,345],"#styled-jsx#Server-Side Rendering#{2}":[346,346],"#styled-jsx#Server-Side Rendering#{3}":[347,348],"#styled-jsx#Server-Side Rendering#{4}":[349,401],"#styled-jsx#Server-Side Rendering#Content Security Policy":[402,418],"#styled-jsx#Server-Side Rendering#Content Security Policy#{1}":[404,418],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component":[419,714],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#{1}":[421,422],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#{2}":[423,423],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#{3}":[424,424],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#{4}":[425,426],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#External styles":[427,485],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#External styles#{1}":[429,485],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles outside of components":[486,508],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles outside of components#{1}":[488,508],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#The `resolve` tag":[509,559],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#The `resolve` tag#{1}":[511,559],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Using `resolve` as a Babel macro":[560,605],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Using `resolve` as a Babel macro#{1}":[562,595],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Using `resolve` as a Babel macro#Usage with [`create-react-app`](https://create-react-app.dev)":[596,605],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Using `resolve` as a Babel macro#Usage with [`create-react-app`](https://create-react-app.dev)#{1}":[598,605],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles in regular CSS files":[606,714],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles in regular CSS files#{1}":[608,689],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles in regular CSS files#Next.js":[690,714],"#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles in regular CSS files#Next.js#{1}":[692,714],"#styled-jsx#CSS Preprocessing via Plugins":[715,857],"#styled-jsx#CSS Preprocessing via Plugins#{1}":[717,790],"#styled-jsx#CSS Preprocessing via Plugins##Plugin options":[791,847],"#styled-jsx#CSS Preprocessing via Plugins##Plugin options#{1}":[793,847],"#styled-jsx#CSS Preprocessing via Plugins##Example plugins":[848,857],"#styled-jsx#CSS Preprocessing via Plugins##Example plugins#{1}":[850,851],"#styled-jsx#CSS Preprocessing via Plugins##Example plugins#{2}":[852,852],"#styled-jsx#CSS Preprocessing via Plugins##Example plugins#{3}":[853,853],"#styled-jsx#CSS Preprocessing via Plugins##Example plugins#{4}":[854,854],"#styled-jsx#CSS Preprocessing via Plugins##Example plugins#{5}":[855,855],"#styled-jsx#CSS Preprocessing via Plugins##Example plugins#{6}":[856,857],"#styled-jsx#Rendering in tests":[858,898],"#styled-jsx#Rendering in tests#{1}":[860,888],"#styled-jsx#Rendering in tests##styled-jsx/css in tests":[889,898],"#styled-jsx#Rendering in tests##styled-jsx/css in tests#{1}":[891,898],"#styled-jsx#FAQ":[899,951],"#styled-jsx#FAQ#Warning: unknown `jsx` prop on &lt;style&gt; tag":[901,906],"#styled-jsx#FAQ#Warning: unknown `jsx` prop on &lt;style&gt; tag#{1}":[903,906],"#styled-jsx#FAQ#Can I return an array of components when using React 16?":[907,923],"#styled-jsx#FAQ#Can I return an array of components when using React 16?#{1}":[909,923],"#styled-jsx#FAQ#Styling third parties / child components from the parent":[924,947],"#styled-jsx#FAQ#Styling third parties / child components from the parent#{1}":[926,947],"#styled-jsx#FAQ#Build a component library with styled-jsx":[948,951],"#styled-jsx#FAQ#Build a component library with styled-jsx#{1}":[950,951],"#styled-jsx#Syntax Highlighting":[952,1037],"#styled-jsx#Syntax Highlighting#{1}":[954,957],"#styled-jsx#Syntax Highlighting#Atom":[958,969],"#styled-jsx#Syntax Highlighting#Atom#{1}":[960,969],"#styled-jsx#Syntax Highlighting#Webstorm/Idea":[970,997],"#styled-jsx#Syntax Highlighting#Webstorm/Idea#{1}":[972,997],"#styled-jsx#Syntax Highlighting#Emmet":[998,1011],"#styled-jsx#Syntax Highlighting#Emmet#{1}":[1000,1011],"#styled-jsx#Syntax Highlighting#Syntax Highlighting [Visual Studio Code Extension](https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-syntax)":[1012,1025],"#styled-jsx#Syntax Highlighting#Syntax Highlighting [Visual Studio Code Extension](https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-syntax)#{1}":[1014,1025],"#styled-jsx#Syntax Highlighting#Autocomplete [Visual Studio Code Extension](https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-languageserver)":[1026,1033],"#styled-jsx#Syntax Highlighting#Autocomplete [Visual Studio Code Extension](https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-languageserver)#{1}":[1028,1033],"#styled-jsx#Syntax Highlighting#Vim":[1034,1037],"#styled-jsx#Syntax Highlighting#Vim#{1}":[1036,1037],"#styled-jsx#ESLint":[1038,1045],"#styled-jsx#ESLint#{1}":[1040,1045],"#styled-jsx#TypeScript":[1046,1055],"#styled-jsx#TypeScript#{1}":[1048,1055],"#styled-jsx#Credits":[1056,1065],"#styled-jsx#Credits#{1}":[1058,1058],"#styled-jsx#Credits#{2}":[1059,1059],"#styled-jsx#Credits#{3}":[1060,1060],"#styled-jsx#Credits#{4}":[1061,1061],"#styled-jsx#Credits#{5}":[1062,1062],"#styled-jsx#Credits#{6}":[1063,1063],"#styled-jsx#Credits#{7}":[1064,1065],"#styled-jsx#Authors":[1066,1071],"#styled-jsx#Authors#{1}":[1068,1068],"#styled-jsx#Authors#{2}":[1069,1069],"#styled-jsx#Authors#{3}":[1070,1071]},"outlinks":[{"title":"![build status","target":"https://github.com/vercel/styled-jsx/actions/workflows/main.yml/badge.svg?branch=main","line":3},{"title":"v2 branch","target":"https://github.com/vercel/styled-jsx/tree/v2","line":7},{"title":"Getting started","target":"#getting-started","line":9},{"title":"Configuration options","target":"#configuration-options","line":10},{"title":"`optimizeForSpeed`","target":"#optimizeforspeed","line":11},{"title":"`sourceMaps`","target":"#sourcemaps","line":12},{"title":"`styleModule`","target":"#stylemodule","line":13},{"title":"`vendorPrefixes`","target":"#vendorprefixes","line":14},{"title":"Features","target":"#features","line":15},{"title":"How It Works","target":"#how-it-works","line":16},{"title":"Why It Works Like This","target":"#why-it-works-like-this","line":17},{"title":"Targeting The Root","target":"#targeting-the-root","line":18},{"title":"Global styles","target":"#global-styles","line":19},{"title":"One-off global selectors","target":"#one-off-global-selectors","line":20},{"title":"Dynamic styles","target":"#dynamic-styles","line":21},{"title":"Via interpolated dynamic props","target":"#via-interpolated-dynamic-props","line":22},{"title":"Via `className` toggling","target":"#via-classname-toggling","line":23},{"title":"Via inline `style`","target":"#via-inline-style","line":24},{"title":"Constants","target":"#constants","line":25},{"title":"Server-Side Rendering","target":"#server-side-rendering","line":26},{"title":"External CSS and styles outside of the component","target":"#external-css-and-styles-outside-of-the-component","line":27},{"title":"External styles","target":"#external-styles","line":28},{"title":"Styles outside of components","target":"#styles-outside-of-components","line":29},{"title":"The `resolve` tag","target":"#the-resolve-tag","line":30},{"title":"Styles in regular CSS files","target":"#styles-in-regular-css-files","line":31},{"title":"CSS Preprocessing via Plugins","target":"#css-preprocessing-via-plugins","line":32},{"title":"Plugin options","target":"#plugin-options","line":33},{"title":"Example plugins","target":"#example-plugins","line":34},{"title":"Rendering in tests","target":"#rendering-in-tests","line":35},{"title":"FAQ","target":"#faq","line":36},{"title":"Warning: unknown `jsx` prop on &lt;style&gt; tag","target":"#warning-unknown-jsx-prop-on-style-tag","line":37},{"title":"Can I return an array of components when using React 16?","target":"#can-i-return-an-array-of-components-when-using-react-16","line":38},{"title":"Styling third parties / child components from the parent","target":"#styling-third-parties--child-components-from-the-parent","line":39},{"title":"Some styles are missing in production","target":"https://github.com/vercel/styled-jsx/issues/319#issuecomment-*********","line":40},{"title":"Build a component library with styled-jsx","target":"#build-a-component-library-with-styled-jsx","line":41},{"title":"Syntax Highlighting","target":"#syntax-highlighting","line":42},{"title":"ESLint","target":"#eslint","line":43},{"title":"TypeScript","target":"#typescript","line":44},{"title":"Credits","target":"#credits","line":45},{"title":"\"styled-jsx/babel\", { \"optimizeForSpeed\": true }","target":"\"styled-jsx/babel\", { \"optimizeForSpeed\": true }","line":92},{"title":"`:host`","target":"https://www.html5rocks.com/en/tutorials/webcomponents/shadowdom-201/#toc-style-host","line":158},{"title":"css-modules","target":"https://github.com/css-modules/css-modules","line":198},{"title":"CSP","target":"https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP","line":404},{"title":"`resolve`","target":"#the-resolve-tag","line":475},{"title":"`babel-plugin-macros`","target":"https://github.com/kentcdodds/babel-plugin-macros","line":562},{"title":"`create-react-app`","target":"https://create-react-app.dev","line":596},{"title":"Create React App","target":"https://create-react-app.dev","line":598},{"title":"Using `resolve` as a Babel macro","target":"https://github.com/vercel/styled-jsx/blob/main/readme.md#using-resolve-as-a-babel-macro","line":604},{"title":"require.resolve","target":"https://nodejs.org/api/globals.html#globals_require_resolve","line":773},{"title":"styled-jsx-plugin-sass","target":"https://github.com/giuseppeg/styled-jsx-plugin-sass","line":852},{"title":"styled-jsx-plugin-postcss","target":"https://github.com/giuseppeg/styled-jsx-plugin-postcss","line":853},{"title":"styled-jsx-plugin-stylelint","target":"https://github.com/giuseppeg/styled-jsx-plugin-stylelint","line":854},{"title":"styled-jsx-plugin-less","target":"https://github.com/erasmo-marin/styled-jsx-plugin-less","line":855},{"title":"styled-jsx-plugin-stylus","target":"https://github.com/omardelarosa/styled-jsx-plugin-stylus","line":856},{"title":"Config Merging options","target":"https://babeljs.io/docs/en/options#config-merging-options","line":862},{"title":"the `resolve` tag from `styled-jsx/css`","target":"#the-resolve-tag","line":926},{"title":"article","target":"https://medium.com/@tomaszmularczyk89/guide-to-building-a-react-components-library-with-rollup-and-styled-jsx-694ec66bd2","line":950},{"title":"open a PR","target":"https://github.com/vercel/styled-jsx/pull/new/main","line":956},{"title":"Atom editor","target":"https://atom.io/","line":960},{"title":"`language-babel`","target":"https://github.com/gandm/language-babel","line":960},{"title":"extend the grammar for JavaScript tagged template literals","target":"https://github.com/gandm/language-babel#javascript-tagged-template-literal-grammar-extensions","line":960},{"title":"source","target":"https://github.com/gandm/language-babel/issues/324","line":962},{"title":"installing the package","target":"https://github.com/gandm/language-babel#installation","line":962},{"title":"babel-language settings entry","target":"https://cloud.githubusercontent.com/assets/2313237/22627258/6c97cb68-ebb7-11e6-82e1-60205f8b31e7.png","line":968},{"title":"Visual Studio Code Extension","target":"https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-syntax","line":1012},{"title":"vscode-styled-jsx-stylus","target":"https://marketplace.visualstudio.com/items?itemName=samuelroy.vscode-styled-jsx-stylus","line":1020},{"title":"Visual Studio Code Extension","target":"https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-languageserver","line":1026},{"title":"vim-styled-jsx","target":"https://github.com/alampros/vim-styled-jsx","line":1036},{"title":"rijs","target":"https://github.com/rijs/fullstack","line":1058},{"title":"glamor","target":"https://github.com/threepointone/glamor","line":1059},{"title":"stylis.js","target":"https://github.com/thysultan","line":1060},{"title":"styled-components","target":"https://github.com/styled-components","line":1061},{"title":"stylis","target":"https://github.com/thysultan/stylis.js","line":1061},{"title":"ember","target":"https://github.com/emberjs","line":1062},{"title":"vuejs","target":"https://github.com/vuejs","line":1063},{"title":"babel","target":"https://github.com/babel","line":1064},{"title":"@rauchg","target":"https://twitter.com/rauchg","line":1068},{"title":"▲Vercel","target":"https://vercel.com","line":1068},{"title":"@nkzawa","target":"https://twitter.com/nkzawa","line":1069},{"title":"▲Vercel","target":"https://vercel.com","line":1069},{"title":"@giuseppegurgone","target":"https://twitter.com/giuseppegurgone","line":1070}],"last_embed":{"hash":"qhddac","at":1751288822497}},"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03609468,-0.02704421,0.0465814,-0.05299635,0.02799066,0.00748196,-0.10586904,-0.02456997,-0.01427708,-0.03345878,-0.00510944,0.00060198,0.04332576,-0.00358466,0.03822244,-0.00054306,0.02437573,0.04708198,-0.04665117,0.02043319,0.02702608,-0.00458269,0.00903296,-0.04401678,-0.02218359,0.03863314,-0.02007667,-0.02219768,0.02498814,-0.18481478,0.0306672,-0.01838978,-0.00114554,0.00772596,0.02205624,-0.04642159,-0.01906228,0.06529225,-0.07355373,0.01257,0.03812062,0.05028304,-0.03692181,-0.04037659,-0.0100231,-0.04843036,0.04183216,-0.00805574,-0.03685078,-0.00940917,-0.02477536,-0.02695488,0.0867681,-0.00406415,0.00971539,0.15114111,0.04862145,0.06759282,-0.01038207,-0.00462699,0.00834074,0.06167134,-0.15749271,0.07147105,0.01363185,0.0220944,-0.05483692,-0.02682984,0.04675564,0.00087087,0.03133367,0.0780233,0.00572894,0.08486094,-0.01214881,-0.02517159,0.01998097,-0.04168651,0.03787426,-0.01941429,-0.09559556,-0.03314304,-0.01648944,0.0151189,0.02544316,0.01716844,0.03042775,0.00388621,0.06054781,0.03295053,-0.03362539,-0.08693714,0.03466318,0.0098737,-0.0205495,-0.01662191,0.02977083,0.02684233,-0.03260145,0.11982076,-0.06567048,0.05949044,0.05933044,0.00219408,0.06484783,-0.02150915,-0.04475645,-0.05593271,-0.0379476,-0.0100289,0.00775419,-0.01908025,-0.04666177,-0.05674603,-0.03474609,-0.00711731,-0.01402004,-0.0052558,0.02212154,0.00958782,0.0126988,0.07501856,0.01945542,-0.01583289,0.03390503,0.03346503,0.01763152,0.03440924,-0.01809091,0.10633294,0.01104902,0.09053895,-0.07099734,-0.06847531,-0.02884859,0.02871095,0.00858569,0.0210581,-0.03126599,0.00902832,0.02158892,-0.01453631,0.04202325,-0.06393196,0.03650821,0.02358726,-0.03745198,0.0685782,-0.01855088,-0.00940731,-0.059194,0.04575906,-0.08657074,0.01893635,-0.03630592,0.01527429,-0.00402609,0.0139706,-0.08194362,0.01936397,-0.00073072,-0.04556903,0.00456439,0.028006,0.04548206,-0.09830034,-0.03499301,0.05980499,0.00313827,-0.08608726,-0.02803514,0.02972488,-0.02355274,0.01882091,0.03112257,0.01184189,-0.06560256,-0.02525282,0.06608952,0.03334232,0.08121315,-0.04314759,-0.00203937,0.05457079,-0.0261383,-0.05245677,0.04102872,-0.04644781,-0.05434493,-0.02013547,-0.01779219,-0.0381285,-0.01437104,0.01763588,0.00013149,-0.01526305,-0.06861538,-0.01000103,0.02342204,-0.00990952,0.14247769,0.01813672,0.01108077,0.07307418,-0.05504891,0.0542317,-0.01061733,-0.02071388,0.01884252,-0.02026957,-0.1169375,0.02969874,0.09135675,0.08908229,-0.01777409,-0.04239508,0.01923327,0.07636983,0.02939853,0.06282664,-0.00179079,-0.00833558,-0.07661649,-0.22967905,-0.00976168,0.00369283,-0.03877212,-0.0125956,-0.02014376,0.0329323,-0.03025573,-0.05081159,0.05631202,0.11128332,0.02646987,0.01656343,-0.02573469,-0.03507259,0.01742621,0.01318651,-0.05534023,-0.08087161,-0.04228255,0.0394231,-0.0216895,-0.05846388,-0.09691472,0.06430454,-0.00864959,0.16905899,0.03307396,0.00879514,-0.03535686,0.04480493,-0.02462211,-0.0297983,-0.10054918,0.06653438,0.04956168,0.06679691,-0.02270306,0.01874697,0.03997645,-0.01863524,-0.02568633,0.01140208,-0.05278739,0.03826252,-0.04589929,-0.04666778,-0.01769058,-0.02000424,0.03114627,0.01491453,0.04872205,0.04145693,0.12003244,-0.01936881,0.0264392,-0.03030704,-0.06395291,0.01522907,0.00551487,0.00070436,0.00073543,-0.01778349,-0.08098442,0.05829033,0.05262258,0.02651235,-0.02770966,0.03656524,-0.04214083,-0.08138227,0.08988954,0.01663504,-0.0110537,0.03674622,0.03642014,-0.05394601,0.09357846,0.01131247,0.02126033,0.02042568,-0.00225783,0.02870747,-0.03409423,-0.03317856,-0.00727178,-0.02675696,-0.03985928,0.03414039,-0.03342485,-0.10374518,0.00153538,-0.03306073,0.02077873,0.06262087,0.01035368,-0.21944243,-0.01572003,0.01420358,0.01586927,-0.07253136,0.00152407,0.05404976,-0.10184462,-0.017902,-0.00357244,0.00316538,0.03274995,0.00190467,-0.01088624,0.00601872,0.0035453,0.06713658,-0.01629775,0.07016644,-0.07615063,0.00413303,0.00940183,0.24571812,-0.02420769,-0.00870272,0.03792164,-0.03394144,-0.02614444,0.06159263,0.0744537,0.01896663,0.01402486,0.12320424,0.01773653,-0.03852144,-0.02573077,-0.03753892,0.01340757,0.02682069,-0.00184749,0.02027798,0.02876532,-0.01543638,-0.00011302,0.05205017,-0.11587977,-0.03866565,-0.01873292,0.01005461,0.01158351,-0.03098904,0.06032163,-0.00954636,0.00648226,0.03002147,-0.00532202,-0.07217568,-0.02178893,-0.04250703,0.02422107,0.00113091,-0.05582501,0.04049671,0.03950712,0.0020213],"last_embed":{"hash":"qhddac","tokens":470}}},"text":null,"length":0,"last_read":{"hash":"qhddac","at":1751288822754},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx","lines":[1,1071],"size":31695,"outlinks":[{"title":"![build status","target":"https://github.com/vercel/styled-jsx/actions/workflows/main.yml/badge.svg?branch=main","line":3},{"title":"v2 branch","target":"https://github.com/vercel/styled-jsx/tree/v2","line":7},{"title":"Getting started","target":"#getting-started","line":9},{"title":"Configuration options","target":"#configuration-options","line":10},{"title":"`optimizeForSpeed`","target":"#optimizeforspeed","line":11},{"title":"`sourceMaps`","target":"#sourcemaps","line":12},{"title":"`styleModule`","target":"#stylemodule","line":13},{"title":"`vendorPrefixes`","target":"#vendorprefixes","line":14},{"title":"Features","target":"#features","line":15},{"title":"How It Works","target":"#how-it-works","line":16},{"title":"Why It Works Like This","target":"#why-it-works-like-this","line":17},{"title":"Targeting The Root","target":"#targeting-the-root","line":18},{"title":"Global styles","target":"#global-styles","line":19},{"title":"One-off global selectors","target":"#one-off-global-selectors","line":20},{"title":"Dynamic styles","target":"#dynamic-styles","line":21},{"title":"Via interpolated dynamic props","target":"#via-interpolated-dynamic-props","line":22},{"title":"Via `className` toggling","target":"#via-classname-toggling","line":23},{"title":"Via inline `style`","target":"#via-inline-style","line":24},{"title":"Constants","target":"#constants","line":25},{"title":"Server-Side Rendering","target":"#server-side-rendering","line":26},{"title":"External CSS and styles outside of the component","target":"#external-css-and-styles-outside-of-the-component","line":27},{"title":"External styles","target":"#external-styles","line":28},{"title":"Styles outside of components","target":"#styles-outside-of-components","line":29},{"title":"The `resolve` tag","target":"#the-resolve-tag","line":30},{"title":"Styles in regular CSS files","target":"#styles-in-regular-css-files","line":31},{"title":"CSS Preprocessing via Plugins","target":"#css-preprocessing-via-plugins","line":32},{"title":"Plugin options","target":"#plugin-options","line":33},{"title":"Example plugins","target":"#example-plugins","line":34},{"title":"Rendering in tests","target":"#rendering-in-tests","line":35},{"title":"FAQ","target":"#faq","line":36},{"title":"Warning: unknown `jsx` prop on &lt;style&gt; tag","target":"#warning-unknown-jsx-prop-on-style-tag","line":37},{"title":"Can I return an array of components when using React 16?","target":"#can-i-return-an-array-of-components-when-using-react-16","line":38},{"title":"Styling third parties / child components from the parent","target":"#styling-third-parties--child-components-from-the-parent","line":39},{"title":"Some styles are missing in production","target":"https://github.com/vercel/styled-jsx/issues/319#issuecomment-*********","line":40},{"title":"Build a component library with styled-jsx","target":"#build-a-component-library-with-styled-jsx","line":41},{"title":"Syntax Highlighting","target":"#syntax-highlighting","line":42},{"title":"ESLint","target":"#eslint","line":43},{"title":"TypeScript","target":"#typescript","line":44},{"title":"Credits","target":"#credits","line":45},{"title":"\"styled-jsx/babel\", { \"optimizeForSpeed\": true }","target":"\"styled-jsx/babel\", { \"optimizeForSpeed\": true }","line":92},{"title":"`:host`","target":"https://www.html5rocks.com/en/tutorials/webcomponents/shadowdom-201/#toc-style-host","line":158},{"title":"css-modules","target":"https://github.com/css-modules/css-modules","line":198},{"title":"CSP","target":"https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP","line":404},{"title":"`resolve`","target":"#the-resolve-tag","line":475},{"title":"`babel-plugin-macros`","target":"https://github.com/kentcdodds/babel-plugin-macros","line":562},{"title":"`create-react-app`","target":"https://create-react-app.dev","line":596},{"title":"Create React App","target":"https://create-react-app.dev","line":598},{"title":"Using `resolve` as a Babel macro","target":"https://github.com/vercel/styled-jsx/blob/main/readme.md#using-resolve-as-a-babel-macro","line":604},{"title":"require.resolve","target":"https://nodejs.org/api/globals.html#globals_require_resolve","line":773},{"title":"styled-jsx-plugin-sass","target":"https://github.com/giuseppeg/styled-jsx-plugin-sass","line":852},{"title":"styled-jsx-plugin-postcss","target":"https://github.com/giuseppeg/styled-jsx-plugin-postcss","line":853},{"title":"styled-jsx-plugin-stylelint","target":"https://github.com/giuseppeg/styled-jsx-plugin-stylelint","line":854},{"title":"styled-jsx-plugin-less","target":"https://github.com/erasmo-marin/styled-jsx-plugin-less","line":855},{"title":"styled-jsx-plugin-stylus","target":"https://github.com/omardelarosa/styled-jsx-plugin-stylus","line":856},{"title":"Config Merging options","target":"https://babeljs.io/docs/en/options#config-merging-options","line":862},{"title":"the `resolve` tag from `styled-jsx/css`","target":"#the-resolve-tag","line":926},{"title":"article","target":"https://medium.com/@tomaszmularczyk89/guide-to-building-a-react-components-library-with-rollup-and-styled-jsx-694ec66bd2","line":950},{"title":"open a PR","target":"https://github.com/vercel/styled-jsx/pull/new/main","line":956},{"title":"Atom editor","target":"https://atom.io/","line":960},{"title":"`language-babel`","target":"https://github.com/gandm/language-babel","line":960},{"title":"extend the grammar for JavaScript tagged template literals","target":"https://github.com/gandm/language-babel#javascript-tagged-template-literal-grammar-extensions","line":960},{"title":"source","target":"https://github.com/gandm/language-babel/issues/324","line":962},{"title":"installing the package","target":"https://github.com/gandm/language-babel#installation","line":962},{"title":"babel-language settings entry","target":"https://cloud.githubusercontent.com/assets/2313237/22627258/6c97cb68-ebb7-11e6-82e1-60205f8b31e7.png","line":968},{"title":"Visual Studio Code Extension","target":"https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-syntax","line":1012},{"title":"vscode-styled-jsx-stylus","target":"https://marketplace.visualstudio.com/items?itemName=samuelroy.vscode-styled-jsx-stylus","line":1020},{"title":"Visual Studio Code Extension","target":"https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-languageserver","line":1026},{"title":"vim-styled-jsx","target":"https://github.com/alampros/vim-styled-jsx","line":1036},{"title":"rijs","target":"https://github.com/rijs/fullstack","line":1058},{"title":"glamor","target":"https://github.com/threepointone/glamor","line":1059},{"title":"stylis.js","target":"https://github.com/thysultan","line":1060},{"title":"styled-components","target":"https://github.com/styled-components","line":1061},{"title":"stylis","target":"https://github.com/thysultan/stylis.js","line":1061},{"title":"ember","target":"https://github.com/emberjs","line":1062},{"title":"vuejs","target":"https://github.com/vuejs","line":1063},{"title":"babel","target":"https://github.com/babel","line":1064},{"title":"@rauchg","target":"https://twitter.com/rauchg","line":1068},{"title":"▲Vercel","target":"https://vercel.com","line":1068},{"title":"@nkzawa","target":"https://twitter.com/nkzawa","line":1069},{"title":"▲Vercel","target":"https://vercel.com","line":1069},{"title":"@giuseppegurgone","target":"https://twitter.com/giuseppegurgone","line":1070}],"class_name":"SmartBlock","last_embed":{"hash":"qhddac","at":1751288822754}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02983793,-0.02961729,0.04643108,-0.05577549,0.04717633,0.00712683,-0.11305989,-0.01283512,-0.02233721,-0.03384603,-0.00543257,-0.01509655,0.04006767,-0.01762187,0.0559584,0.01516703,0.03092391,0.0505324,-0.05347045,0.03323053,0.02681406,-0.00909883,0.01859536,-0.02258487,-0.01174171,0.0315251,-0.03402405,-0.02943762,0.01308976,-0.15214017,0.00360791,-0.01273536,-0.00395834,0.01477182,0.04269011,-0.05432503,-0.01450963,0.08003451,-0.07340583,0.02077095,0.04779983,0.03574616,-0.0284052,-0.02302853,-0.00941223,-0.04769083,0.03548894,-0.01199872,-0.03399689,-0.02023643,0.00431853,-0.033782,0.08221921,0.00340319,0.02395228,0.14683054,0.03308624,0.04983277,-0.01428628,-0.01354377,0.02377528,0.050921,-0.16688386,0.06341967,0.00494917,0.02575313,-0.04698493,-0.02527822,0.04331575,0.00161797,0.03632471,0.06211606,-0.00748882,0.07836167,-0.017137,-0.02911716,0.03758629,-0.04631615,0.04363601,-0.02347682,-0.09740136,-0.01018033,-0.00570144,0.01837848,0.01596549,0.02026207,0.02442253,0.0156387,0.05802297,0.04335568,-0.02225873,-0.09159438,0.05105669,0.01702794,-0.04319159,-0.03464605,0.00068153,0.0232075,-0.0268441,0.12953824,-0.06218278,0.05634825,0.08242866,0.00475313,0.07290365,-0.01764741,-0.03606534,-0.06274918,-0.041477,-0.03437324,0.01057629,-0.00536674,-0.02808416,-0.0430799,-0.02083986,-0.00528614,0.00147925,0.00274073,-0.00220609,0.02446629,0.01207095,0.07257145,0.05096278,-0.01223692,0.03690766,0.02105878,0.01771058,0.02916469,-0.02057556,0.10307566,0.00202088,0.08623799,-0.07038283,-0.07022087,-0.02176142,0.0264778,0.01035622,0.01898826,-0.0319938,0.0131306,0.00867005,-0.03149056,0.05648649,-0.05407985,0.02338751,-0.01510016,-0.04444828,0.05817421,-0.01656047,-0.02276828,-0.07059229,0.04896372,-0.06983247,0.0193819,-0.0167381,0.00649312,-0.01047489,0.03487633,-0.07199842,0.02096704,0.01588182,-0.0553916,0.00810309,0.04847267,0.05926218,-0.10639271,-0.03009517,0.06297036,0.01238584,-0.06886476,-0.02830981,0.04774611,-0.0295787,0.00485489,0.03343758,0.00806413,-0.05067355,-0.0139802,0.06542014,0.03532869,0.07515378,-0.04696539,0.00645299,0.0593962,-0.01848807,-0.03881954,0.02884443,-0.05146866,-0.05145864,-0.01396432,-0.01817028,-0.02483524,-0.02381429,0.00745898,0.0008732,-0.01963102,-0.06563353,-0.03482902,0.00873606,-0.0000034,0.14285325,0.0213817,0.00346871,0.06227659,-0.05421544,0.06775501,-0.00452992,-0.01562708,0.00471319,-0.00903654,-0.12059152,0.03119731,0.11126479,0.0830657,-0.01970391,-0.03392202,0.01854672,0.06198696,0.0016867,0.06078468,-0.00388861,0.00218826,-0.07391542,-0.22894333,-0.01371514,-0.00164914,-0.04906281,0.00217723,-0.01356994,0.0295605,-0.03535629,-0.05311248,0.04533418,0.11871052,0.03349367,0.01010267,-0.04141864,-0.02854106,0.04179958,0.01369903,-0.07407913,-0.07738109,-0.04415569,0.02927907,-0.03268177,-0.04439809,-0.09745188,0.05971961,0.00650085,0.16955784,0.06360703,0.00342482,-0.04372479,0.03629785,-0.03701717,-0.0204591,-0.10252506,0.06200279,0.04157571,0.04306317,0.00755518,0.01588841,0.03765699,-0.02422,-0.01947937,0.00461108,-0.0637645,0.05425852,-0.06866841,-0.0345487,-0.02849194,-0.00782834,0.02006921,0.00524116,0.05957575,0.04970947,0.11417465,-0.02204861,0.02823239,-0.04203574,-0.04601841,0.00808186,0.01957748,0.00654995,0.00507449,-0.02424414,-0.09138176,0.05585555,0.04986699,0.00739734,-0.03299715,0.03487463,-0.04307364,-0.07935941,0.07252216,0.01955388,-0.0414912,0.05104223,0.0493037,-0.05540971,0.09505927,0.02244832,0.02135551,0.00626262,-0.02541143,0.02506294,-0.02572671,-0.0407028,0.00515318,-0.05071211,-0.03761923,0.04435573,-0.03313487,-0.0979634,0.00153644,-0.02368527,0.02069722,0.0646618,0.01855258,-0.21232916,-0.02337388,0.0111702,0.00853087,-0.07196307,-0.00503492,0.05783955,-0.11040635,-0.03486323,0.00320829,0.0253211,0.01933649,-0.00014859,-0.0058183,0.00455775,0.00105093,0.07457674,-0.01439933,0.0639286,-0.06045442,0.00739791,0.01602688,0.2421253,-0.00232273,-0.02900581,0.00901803,-0.04769741,-0.01796196,0.04525725,0.07331587,0.01166605,0.00348544,0.1223777,0.01592477,-0.03068085,-0.00642049,-0.05111398,0.01621392,0.02022571,0.00378255,0.01892767,0.02326311,-0.04912484,-0.02153279,0.05437152,-0.11278953,-0.05023582,-0.01810216,0.02477825,0.00714908,-0.04674911,0.05533588,-0.03785685,0.01998903,0.03410227,-0.00567647,-0.05591952,-0.03917387,-0.05375721,0.01725679,-0.00770238,-0.03956445,0.06185208,0.02624354,-0.00395632],"last_embed":{"hash":"pa61zj","tokens":189}}},"text":null,"length":0,"last_read":{"hash":"pa61zj","at":1751288822949},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#{1}","lines":[3,8],"size":432,"outlinks":[{"title":"![build status","target":"https://github.com/vercel/styled-jsx/actions/workflows/main.yml/badge.svg?branch=main","line":1},{"title":"v2 branch","target":"https://github.com/vercel/styled-jsx/tree/v2","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"pa61zj","at":1751288822949}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02000452,-0.00369073,0.07516084,-0.0528015,-0.04910661,0.04073307,-0.06822,-0.03352606,0.00366305,-0.04042638,-0.02880768,-0.02147433,0.04036867,0.02406437,0.04060239,-0.0163373,0.02223027,0.10152286,-0.01343849,-0.00621845,0.0807787,0.04266892,0.01958431,-0.03624211,-0.00602082,0.0618453,-0.00578828,0.00692551,0.02633511,-0.18563682,0.028326,-0.03341747,-0.03445446,-0.0018934,0.03785519,-0.02674635,-0.01494603,0.05535085,-0.04355895,0.05638831,0.02128462,0.06137599,-0.0255134,-0.06836447,-0.00272567,-0.04063144,0.01886743,0.00425011,-0.00955684,-0.01440633,-0.07311126,-0.0419357,0.06749793,-0.01228094,-0.01525967,0.13401787,0.06145109,0.06923113,0.06079267,0.00821175,-0.02142187,0.04900391,-0.14231949,0.06313683,0.01511637,0.03021807,-0.06369139,-0.01156813,0.03932329,0.02860377,0.02642225,0.05610572,0.00782464,0.10020909,0.02718752,-0.02832794,-0.00574983,-0.06852073,0.01523005,-0.04850836,-0.05648344,-0.05894748,-0.00170086,-0.02529943,0.0299038,0.02063316,0.04045432,-0.0467497,0.04349234,0.06402073,-0.06410034,-0.11109634,0.05025891,0.03646568,-0.04686998,0.00698138,0.04774116,0.03265605,0.0006104,0.13195653,-0.09077764,0.04256765,0.02435582,-0.004101,0.02166356,-0.02827929,0.02803196,-0.06953926,-0.06676593,-0.01784036,0.01804351,-0.02335414,-0.06498246,-0.05523627,-0.0679035,0.00040867,-0.02538228,0.01237324,0.03157322,-0.02199855,0.04806969,0.05518886,-0.00285562,-0.00605344,0.00044408,0.03175556,0.02746692,0.0392513,0.00421011,0.08948725,-0.00187348,0.11188474,-0.04929875,-0.07758219,-0.00678,0.01864093,0.00042033,-0.0225798,-0.03410992,0.00871847,0.00321958,-0.02257184,0.00051466,-0.07369521,0.03667747,0.05242579,-0.04265816,0.08264106,-0.00746798,0.02220028,-0.06198157,0.04960798,-0.06902094,0.02214381,-0.05740059,0.0208681,0.0210362,0.03348667,-0.04427516,0.03671077,-0.00186611,-0.01666768,-0.01553714,0.04670521,0.01237002,-0.08138736,-0.04509322,0.04540043,0.02238093,-0.07450112,-0.02949044,0.02087607,-0.01562209,0.04208055,0.04758083,0.04430337,-0.04412584,-0.03591586,0.02575204,0.02206691,0.09299622,-0.06836873,-0.02445412,0.01485234,-0.03633819,-0.04818448,0.02156276,-0.01961021,-0.0269076,0.01097987,-0.02066382,-0.0165585,-0.00902209,0.02812123,-0.01597844,-0.02216089,-0.0420641,-0.00424886,0.04834875,-0.02909757,0.1122133,0.02176651,-0.00756931,0.08948471,-0.06329309,0.01285896,-0.00088299,-0.01905514,0.02418464,-0.01579105,-0.07578825,0.01489904,0.09656866,0.11013692,-0.02704912,-0.05011519,0.00785529,0.05334463,0.02233099,0.01937642,-0.01021921,-0.04730101,-0.08791834,-0.23566131,0.01269959,0.0374861,-0.02357127,-0.04023405,-0.03419535,0.03354197,-0.01841088,-0.04431072,0.08890415,0.09276217,0.03018689,0.00624848,-0.02002549,-0.02635628,-0.00760515,-0.00960711,-0.0471659,-0.05760594,-0.03304456,0.0116619,0.00401252,-0.08197236,-0.09609609,0.04217488,-0.01032258,0.15161049,0.01773506,0.03866521,-0.01287363,0.06895509,-0.01356493,-0.02910092,-0.08259844,0.05431704,0.0448623,0.09212512,-0.02733153,0.01058531,-0.01649616,0.00318299,-0.04410725,0.0067244,-0.06658969,0.07469426,-0.04487687,-0.05330079,-0.05111241,-0.02308702,0.04377699,0.01328612,0.01446544,0.02270228,0.09907211,-0.04295942,-0.0162537,-0.03298183,-0.06200236,0.01562157,0.0139093,-0.01313986,-0.02860383,-0.003032,-0.06462947,0.01860632,0.05366042,0.00526994,-0.03473693,-0.00842782,-0.03407676,-0.03368115,0.085149,0.04999189,0.00601069,0.02363143,-0.00265321,-0.04922839,0.11148007,-0.02717753,0.03099266,0.03898979,0.01615278,0.05518697,0.00834962,-0.00139817,0.00309356,-0.04978442,-0.05576592,0.02254581,-0.03671173,-0.08004789,0.00770338,-0.038091,0.03930946,0.03621816,-0.04071832,-0.24847607,0.01472419,0.02630368,0.03419645,-0.06666943,0.03383999,0.03128409,-0.09557381,-0.02308815,-0.00746053,-0.02545132,0.04346789,0.0221092,-0.01632427,-0.0070455,-0.0006745,0.08311949,-0.01727337,0.08209052,-0.07810001,0.0212955,0.00353513,0.24581195,-0.03872055,0.05770019,0.03916817,-0.03909152,0.0209964,0.03661305,0.04967815,0.04822481,0.02139466,0.07825167,-0.00438749,-0.03966564,-0.01357202,0.01571705,0.0160829,0.01293622,-0.0017442,0.01470796,0.01606403,-0.05188252,0.01932969,0.04071373,-0.08287966,-0.05041821,-0.02508074,-0.01448719,0.0431882,-0.00971572,0.05421175,-0.00896857,-0.00490022,0.0470354,-0.00119091,-0.03983793,0.00699683,-0.04204055,0.0072065,-0.01237533,-0.04771341,0.02868746,0.02166605,0.00550419],"last_embed":{"hash":"krkoos","tokens":95}}},"text":null,"length":0,"last_read":{"hash":"krkoos","at":1751288823004},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#{8}","lines":[21,24],"size":205,"outlinks":[{"title":"Dynamic styles","target":"#dynamic-styles","line":1},{"title":"Via interpolated dynamic props","target":"#via-interpolated-dynamic-props","line":2},{"title":"Via `className` toggling","target":"#via-classname-toggling","line":3},{"title":"Via inline `style`","target":"#via-inline-style","line":4}],"class_name":"SmartBlock","last_embed":{"hash":"krkoos","at":1751288823004}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00438147,0.00607114,0.0633627,-0.04993975,0.05041607,-0.01253625,-0.09372357,-0.04242387,-0.02610732,-0.04962174,0.0086302,-0.00836019,0.04511714,-0.0081315,0.03375845,0.00759453,0.02256217,0.08180393,-0.03260097,0.00100855,0.02294349,0.00192169,0.02601997,-0.04339467,-0.04028682,0.06181435,-0.03744217,-0.04249378,0.02033951,-0.18146124,-0.00458077,-0.00497492,-0.00312849,0.01840618,0.02725451,-0.01931293,-0.00897154,0.0136116,-0.021338,0.05428007,0.00294831,0.06257503,-0.05143703,-0.00629048,-0.00072399,-0.0375533,0.00815518,0.01307712,-0.02783496,-0.04714946,-0.0271121,-0.02513377,0.01537789,-0.01655039,0.02109215,0.15020233,0.04600683,0.04356246,0.00460376,0.03202394,-0.02281563,0.06098023,-0.16530672,0.08916964,0.03447729,0.04177583,-0.05488034,-0.00487336,0.04008497,0.03843271,0.0204453,0.02829523,0.00225115,0.11213847,0.02528068,-0.04189286,0.01950268,-0.06825822,0.00771131,-0.02100014,-0.07839641,0.00008855,-0.01286521,-0.03363523,0.0221148,0.03062583,0.04440393,-0.00952045,0.04436779,0.07845034,-0.04964597,-0.10926501,0.07303387,0.03547581,0.00575994,-0.05754734,0.00661106,0.04201673,0.02350189,0.14419182,-0.1057917,0.02961369,0.05398958,0.00519623,0.05455985,0.02541547,0.04102604,-0.05938483,-0.06829599,-0.00466797,0.00571225,-0.03681974,-0.06338296,-0.07968432,-0.02378299,-0.04165244,-0.0265789,0.03430748,0.0384088,-0.01833991,0.02691987,0.04719746,0.01390083,-0.03860215,0.00332413,0.02062718,0.01667932,0.05370848,0.00007188,0.07083042,-0.00315101,0.00113668,-0.04704509,-0.02203414,-0.03256338,0.05346368,0.00340544,-0.02064547,-0.02930857,0.013217,0.02306648,-0.06020505,0.00524234,-0.06158505,0.00709164,0.04844015,-0.03782894,0.0821486,-0.00953576,-0.0346311,-0.07796665,0.07420272,-0.07093128,0.04618296,-0.0297322,0.00355179,0.03547492,-0.04039658,-0.06140966,0.05584946,0.01204159,-0.0109078,-0.02250986,0.08903028,0.03227907,-0.07641791,-0.0527302,0.05007036,0.0182155,-0.09120333,-0.03559242,0.01639299,-0.01522591,0.02868184,0.06400759,0.04599797,-0.03690097,-0.0465476,0.03277114,0.04996973,0.08001369,-0.06772225,-0.03205547,0.04453145,-0.04806102,-0.07037564,0.03512387,-0.03711101,-0.01738524,-0.03780099,-0.03059143,-0.03457629,0.03291782,0.00339694,-0.00367553,-0.03648334,-0.05538308,-0.02883456,0.02188743,-0.00642933,0.15453732,0.01183573,-0.01373815,0.07463387,-0.04959809,0.05841268,-0.01137832,-0.0271669,0.0072901,-0.04481736,-0.14554693,0.01874806,0.09588782,0.10664841,0.02323145,-0.06033829,0.02127675,0.04249725,0.02099119,0.03138169,0.00137243,-0.00649127,-0.10794595,-0.21303897,0.01056457,0.03021371,-0.05023644,-0.03433464,-0.02961691,0.02423082,0.00295101,-0.03315689,0.09295057,0.09370147,0.01181447,0.02234509,-0.0328684,-0.04101186,0.00529355,0.03294498,-0.02786163,-0.06186634,-0.00455187,0.02222969,-0.00940305,-0.06152951,-0.06169893,0.02067378,0.01045051,0.13950455,0.04793914,0.024142,-0.02898814,0.05878166,0.0031737,-0.00204961,-0.06883296,0.02118222,0.05037447,0.01223527,-0.02538498,-0.00769777,0.01067116,0.0125128,0.00314176,0.0016538,-0.04528784,0.04012977,-0.02689343,-0.04839547,-0.03997851,0.01131527,0.02079671,-0.03206513,0.03980739,0.01703449,0.08912289,-0.00368752,0.00121399,-0.03044507,-0.0689859,0.00101577,0.00010653,-0.02877319,-0.02765814,0.00680328,-0.09110764,0.0661209,0.046739,0.01190389,-0.03754159,0.01296649,-0.03720171,-0.03000546,0.07378242,0.06359774,-0.04767701,-0.00155151,-0.0251196,-0.03855392,0.12110231,0.00011825,0.0313619,0.03471202,-0.02753464,0.06659337,-0.01221345,-0.03238475,-0.007077,-0.04374184,-0.06497289,0.03833012,-0.01860242,-0.05894109,0.04655193,-0.04596537,0.02421289,0.06949754,0.00017119,-0.23263362,0.02244778,0.0391579,-0.04148032,-0.06080675,-0.00245545,0.03465173,-0.07003057,-0.03754434,-0.00170651,-0.02581344,0.03579493,-0.00223575,-0.00104349,-0.01227559,-0.00852052,0.0688375,0.00026762,0.10004935,-0.08680829,0.00121175,0.01759123,0.24887049,-0.0493499,0.01211108,0.06187154,-0.03158992,-0.01402257,0.00469686,0.10019489,0.05625264,0.06694093,0.05201324,0.00065116,-0.01243914,-0.01742731,-0.00373872,0.02404889,0.03828749,0.01286945,0.00901644,-0.00803036,-0.04087926,0.0049846,0.04753863,-0.06379989,-0.07938866,-0.00320435,-0.01137563,-0.02308833,0.00419507,0.02734743,-0.02214446,-0.00680462,0.03363996,0.0018837,-0.06064786,0.0058063,-0.01707358,0.02573284,0.02271351,-0.00380779,-0.01351922,0.0292967,0.04244151],"last_embed":{"hash":"7sa998","tokens":122}}},"text":null,"length":0,"last_read":{"hash":"7sa998","at":1751288823038},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#{11}","lines":[27,31],"size":315,"outlinks":[{"title":"External CSS and styles outside of the component","target":"#external-css-and-styles-outside-of-the-component","line":1},{"title":"External styles","target":"#external-styles","line":2},{"title":"Styles outside of components","target":"#styles-outside-of-components","line":3},{"title":"The `resolve` tag","target":"#the-resolve-tag","line":4},{"title":"Styles in regular CSS files","target":"#styles-in-regular-css-files","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"7sa998","at":1751288823038}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#{14}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03623343,-0.01181061,0.03134717,-0.01563719,0.02654446,0.02034668,-0.08578263,0.03367313,-0.01962831,-0.04088281,0.03183783,0.01798914,0.03236892,-0.01827541,0.01767479,0.02548257,0.02689116,0.06009381,-0.03397302,0.00893971,0.05571982,0.00753463,-0.01899333,-0.05377939,0.01551088,0.04359818,-0.03681823,-0.07033166,-0.00826922,-0.21684179,0.02936358,-0.00804074,-0.01454386,-0.00344371,0.05840702,-0.00860452,-0.0157771,0.06587799,-0.01416141,0.05081133,-0.02543053,0.10574833,-0.0155977,-0.03911996,-0.00889014,-0.01541551,-0.01057752,-0.02501031,-0.00328592,0.00493024,-0.05762369,0.00898218,0.05183947,0.0089282,0.05736814,0.11864853,0.0353401,0.03930318,-0.00397769,0.03776897,0.02049088,0.04389414,-0.16500612,0.06182543,0.05333964,0.05909557,-0.05169117,-0.03647938,0.01788639,-0.00105716,0.02724163,0.04492833,0.02975962,0.1179941,-0.00714313,-0.03753208,0.02237608,-0.04756908,0.03218283,0.00003312,-0.11388446,-0.02136593,-0.02524209,-0.01572136,0.04184904,-0.06032685,0.03603116,0.01342089,0.01056322,0.04062324,-0.05146368,-0.0602296,0.05407806,0.04258889,-0.05715137,-0.00268065,0.04793368,0.04137463,0.00444888,0.14298406,-0.07245499,0.04156085,0.04464442,0.0115235,0.02867072,-0.04907002,0.00184099,-0.03357475,-0.04718968,-0.02393989,-0.00275289,-0.02289252,-0.00978065,-0.10295799,-0.02086995,-0.03498051,-0.01078247,-0.00410329,0.01392436,-0.03789277,0.06387138,0.0638872,0.00687024,-0.01068336,0.01153462,0.02371658,-0.0003587,0.01705596,-0.00097924,0.12158035,-0.00042819,0.00557957,-0.01788276,-0.03151813,0.02291703,0.0299129,0.03747523,-0.04014929,0.00116984,0.04147294,-0.00373463,-0.08965337,0.04771967,-0.02103267,-0.00046708,0.02221654,-0.02743185,0.06075037,-0.02236383,0.03192459,-0.09798231,0.08423195,-0.06469508,0.03600284,-0.0316738,-0.01337678,-0.01303853,-0.0100967,-0.04085588,0.04266939,0.01643093,0.01753071,0.00601474,0.00719266,0.0072977,-0.08314987,-0.02431146,0.08892852,0.03225528,-0.0899149,-0.03600796,0.05960706,-0.02741424,-0.0147778,0.05918508,-0.0143947,-0.0225371,0.01311188,0.03564069,0.05762292,0.05478947,-0.05839502,-0.01073946,0.02937461,0.01363887,-0.06499445,0.0305363,-0.04171626,-0.00706994,-0.02399961,-0.02282741,-0.04656821,-0.01114087,0.03295875,-0.00820162,-0.00528964,-0.04754647,-0.02748324,0.06108088,-0.0294648,0.1416868,-0.00056161,-0.01131257,0.07945464,-0.0675633,0.04702661,0.04619984,-0.04737588,0.01573197,0.00327494,-0.09439033,0.01352499,0.08283125,0.06529325,-0.04429841,-0.06260128,0.00941848,0.08890993,0.00466506,0.01905078,0.0321422,-0.03779632,-0.07452584,-0.20370606,0.05812084,0.00882273,-0.05640523,-0.10225904,-0.02847009,0.03061383,-0.02437438,-0.03977033,0.08682621,0.07486589,0.03111088,0.00689397,-0.00456721,-0.06871587,-0.01174486,0.00687775,-0.0414687,-0.06640705,-0.00704326,-0.01867275,-0.02568696,-0.02585702,-0.10301702,0.04768704,-0.02369103,0.17264701,0.07956894,-0.0406475,-0.03474852,0.06773403,-0.00323158,-0.02943888,-0.03573611,0.07007314,0.01166933,0.01435743,-0.02890447,0.01455407,-0.02556088,-0.03073671,-0.04353469,-0.00028153,-0.00768803,0.04031967,-0.04305766,-0.04848794,-0.03418025,-0.036417,0.09274939,0.00841322,0.01544354,0.02446947,0.10929171,-0.01706257,0.01870887,-0.04104536,-0.01648543,-0.01682493,0.01102707,-0.00396193,0.06030516,-0.02880983,-0.11891505,0.01746182,0.03675929,0.00160093,-0.06422327,-0.02990844,-0.02514094,-0.04478674,0.11046286,0.01623706,-0.02303237,0.04326687,-0.02075877,-0.03177892,0.03810134,0.02000095,0.05439956,0.02015194,-0.03455579,0.0282554,0.0114337,0.0258433,-0.0096308,-0.03131978,-0.05196447,0.02832132,-0.02753015,-0.03471043,0.01158013,-0.05144355,0.01401193,0.05291213,-0.0072579,-0.22362211,-0.01074903,0.03975262,0.02255847,-0.01539395,0.01135771,0.06505381,-0.08379062,-0.05305571,0.01906522,-0.017762,0.02035926,-0.00114396,-0.02067744,-0.00017453,0.03395971,0.08007899,0.04084679,0.08739839,-0.05139482,-0.0057525,0.01330914,0.24007671,-0.02177055,0.01570069,-0.01056837,-0.04508809,-0.03011167,0.06105518,0.06904616,0.00686049,0.01590867,0.078609,-0.00909328,-0.02691524,-0.04592882,-0.02047735,-0.04484364,-0.00339356,0.0310466,-0.00156898,-0.00955733,-0.05661229,0.04071127,0.07218622,-0.106362,-0.05395136,-0.05216849,-0.01158921,-0.01985832,-0.01913125,0.04715694,0.00449295,-0.02626638,0.0194131,-0.02663371,-0.04202812,-0.01930019,-0.06756313,0.01567094,0.03448969,0.01370257,0.05229497,0.08740747,0.01105895],"last_embed":{"hash":"1x6cbtr","tokens":220}}},"text":null,"length":0,"last_read":{"hash":"1x6cbtr","at":1751288823087},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#{14}","lines":[36,41],"size":558,"outlinks":[{"title":"FAQ","target":"#faq","line":1},{"title":"Warning: unknown `jsx` prop on &lt;style&gt; tag","target":"#warning-unknown-jsx-prop-on-style-tag","line":2},{"title":"Can I return an array of components when using React 16?","target":"#can-i-return-an-array-of-components-when-using-react-16","line":3},{"title":"Styling third parties / child components from the parent","target":"#styling-third-parties--child-components-from-the-parent","line":4},{"title":"Some styles are missing in production","target":"https://github.com/vercel/styled-jsx/issues/319#issuecomment-*********","line":5},{"title":"Build a component library with styled-jsx","target":"#build-a-component-library-with-styled-jsx","line":6}],"class_name":"SmartBlock","last_embed":{"hash":"1x6cbtr","at":1751288823087}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Getting started": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03091423,-0.01250362,0.06541264,-0.07950007,0.03332018,0.02405436,-0.13084576,-0.01325379,0.02143388,-0.04103888,-0.02233778,-0.00336591,0.03932129,0.00582186,0.03598365,0.02491393,0.01649748,0.05773626,-0.07478663,0.003677,0.03394525,0.0414306,-0.01383084,-0.03737009,-0.01771223,0.04255976,-0.02616066,-0.0201276,0.03486957,-0.18192933,0.03309806,0.00796394,-0.03359272,-0.02019494,0.0430573,-0.00357117,-0.02977774,0.0398554,-0.01294506,0.02723652,0.01502551,0.01013527,-0.02631862,-0.03325342,-0.01768927,-0.04067504,0.02672102,0.03098289,0.00710267,-0.0420092,-0.01820286,-0.04487759,0.05030231,-0.03671177,0.0425953,0.11942989,0.08183599,0.056468,0.00531076,0.01462281,0.0051562,0.03319234,-0.15243624,0.11863844,0.03313405,0.0133548,-0.06143405,-0.0089125,0.04128829,-0.00379626,-0.01771429,0.03781109,-0.00233258,0.08909795,0.0078176,-0.06488174,0.0422243,-0.07925516,0.054805,-0.01427787,-0.07395267,-0.03926349,-0.00211212,-0.00256696,0.0360256,-0.03114217,0.01783298,-0.00752441,-0.00750137,0.02317377,-0.01599697,-0.13178167,0.0140765,0.001612,-0.03290711,-0.00071533,0.02160629,0.05074454,-0.01232375,0.13003255,-0.07663756,0.01923322,0.05623088,-0.01798975,0.06588945,-0.03195902,-0.03773603,-0.03427915,-0.04133115,-0.03544499,0.00523356,0.00353749,-0.05375583,-0.08094008,-0.02530689,-0.03980651,-0.04048341,0.00044618,0.01444105,-0.02389241,0.04382461,0.02368301,0.04829025,-0.00233279,0.02083452,0.03244467,-0.01094918,0.04981278,0.00165696,0.05614798,-0.00447423,0.07970232,-0.03132147,-0.0280821,-0.0054143,0.0727742,-0.03792833,0.01936377,-0.04583178,0.01154108,0.03014538,-0.03225083,-0.01110609,-0.05790471,0.02498621,0.00174289,-0.03312056,0.07882682,-0.01477078,-0.00547055,-0.10285415,0.07204796,-0.08194429,0.00599849,-0.02208815,0.01897639,0.02525094,0.00471322,-0.06371488,0.04419402,-0.00373796,-0.04268206,0.0145828,0.04106034,0.03941569,-0.08007864,-0.02621536,0.03270521,0.03105594,-0.0926041,-0.04969105,0.05020613,-0.02515832,-0.00899508,0.06414416,0.03755515,-0.01040643,-0.02382287,0.06513295,0.0086065,0.0566154,-0.03659683,-0.02460643,0.02698987,0.0086591,-0.08171788,0.01613917,-0.05039218,-0.0521029,-0.00833328,0.00876332,0.02578413,0.03225159,-0.00254053,0.02067794,0.00230351,-0.06142287,-0.04169206,0.05574367,-0.01386033,0.11135575,-0.01663006,0.01513151,0.05005575,-0.07823347,0.04643199,0.02602968,-0.01906199,0.08708689,-0.01945921,-0.14595085,0.02029651,0.11537274,0.09835546,-0.03421537,0.02986399,0.01881428,0.05045236,0.04027222,0.0393749,-0.00721097,0.0043289,-0.09641221,-0.2174976,0.03710449,0.07988462,-0.07206058,-0.0107353,-0.01230553,0.03956781,-0.00894187,-0.01124931,0.08980485,0.12607111,-0.00726076,0.04119199,0.01014037,-0.04339633,0.01765428,0.00141068,-0.04690113,-0.05461027,-0.00182415,0.01175942,-0.02195878,-0.05723622,-0.09673683,0.06306546,-0.0156453,0.12589315,0.05707606,0.02491891,-0.07255368,0.0448216,-0.01771905,-0.02785761,-0.1305142,0.06195264,0.01896738,0.0283287,-0.00009823,0.01854842,0.01268557,0.01328837,-0.01470986,-0.00511274,-0.02036499,0.05908258,-0.05962694,-0.06856846,-0.04789514,-0.01948458,0.05203383,-0.02453184,0.0162236,0.06860439,0.08041234,-0.02875824,-0.00877417,-0.01249334,-0.06185106,0.01751527,0.01059626,-0.00833795,-0.00156592,0.015615,-0.10971047,0.01180903,0.02159605,0.02059562,-0.02748,0.02622932,-0.01974059,-0.07288996,0.07142546,0.04456483,0.02035266,0.01238669,-0.00615633,-0.07062983,0.09118965,0.0219236,0.03031846,0.01230291,-0.04619877,0.01561499,0.00956376,-0.03320011,0.0347461,-0.04094122,-0.02700925,0.02510493,0.01799766,-0.08148596,-0.00402583,-0.00229392,0.00688129,0.03914772,-0.01632883,-0.20776719,0.01695714,0.02202149,-0.00685641,-0.03275152,-0.02809346,0.0654808,-0.07480825,-0.04667897,0.00157705,-0.01572605,0.03775705,-0.01362061,-0.0332803,0.00189741,-0.01063472,0.05182524,0.03454915,0.0744469,-0.06908944,-0.00323342,0.0070399,0.25092542,-0.0225636,-0.00775753,0.0443501,-0.03496798,-0.02732743,0.03459437,0.09517646,0.0304342,0.06300447,0.08143094,0.0168315,-0.05346923,-0.02777418,0.01604431,-0.00322254,0.04449826,-0.00037322,-0.01668908,0.00793228,-0.03629309,0.04018101,0.03520068,-0.14014462,-0.04819036,0.00651882,-0.02050651,-0.01994189,-0.0161289,0.02965782,0.00761557,-0.02274724,0.02330581,-0.01134832,-0.04423369,0.02153961,-0.02840881,0.03883804,0.01274443,-0.0680021,0.03932538,0.04610125,0.02536082],"last_embed":{"hash":"1f2ngnc","tokens":217}}},"text":null,"length":0,"last_read":{"hash":"1f2ngnc","at":1751288823188},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Getting started","lines":[47,81],"size":569,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1f2ngnc","at":1751288823188}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Getting started#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02974381,-0.01355705,0.06389168,-0.08021771,0.03302276,0.02366521,-0.13144077,-0.01454003,0.02278379,-0.03978096,-0.02272821,-0.00301647,0.03730335,0.00656774,0.03566807,0.02696874,0.01557933,0.05931915,-0.07563055,0.00462515,0.03584441,0.04220618,-0.01265149,-0.03910143,-0.01770035,0.0421141,-0.02489377,-0.0193634,0.03509978,-0.18220371,0.03370091,0.00580297,-0.03592833,-0.021339,0.0445842,-0.00474495,-0.03042183,0.03941818,-0.0138254,0.02820943,0.01609818,0.00769237,-0.02603159,-0.03463819,-0.01807952,-0.03981922,0.02679933,0.03065116,0.00815151,-0.0418759,-0.01340113,-0.04641231,0.04617063,-0.0369065,0.04083863,0.11978969,0.08517072,0.05893386,0.00539998,0.01617356,0.00220948,0.03140745,-0.15192895,0.11837969,0.03252291,0.0139028,-0.06147975,-0.00721691,0.04206058,-0.00284502,-0.0189326,0.03679669,-0.00070199,0.08784054,0.00810761,-0.06509136,0.0424058,-0.07874109,0.05581385,-0.01429192,-0.07297159,-0.0405211,-0.00265534,-0.00110413,0.03762838,-0.03223031,0.01742738,-0.00832293,-0.00806373,0.02126923,-0.01662486,-0.13394512,0.01284483,0.00074418,-0.03534656,-0.00016437,0.02063613,0.04873697,-0.01494999,0.12905744,-0.07519668,0.0181736,0.05680031,-0.01897607,0.06430788,-0.03201265,-0.03804281,-0.03413458,-0.0409557,-0.03607894,0.00416518,0.00449074,-0.05313716,-0.08335028,-0.02611146,-0.04070355,-0.0433392,0.00223918,0.0131226,-0.02400308,0.04678523,0.02403083,0.05131723,-0.00218614,0.02117391,0.03360513,-0.01254753,0.05012947,0.00437991,0.05683302,-0.00539808,0.07862657,-0.03027676,-0.02675589,-0.00515865,0.07176127,-0.03796739,0.02222971,-0.04420094,0.01239152,0.03085122,-0.03211385,-0.01304214,-0.05848823,0.02348921,0.00202993,-0.03316893,0.08210585,-0.01306621,-0.00677115,-0.102569,0.07202131,-0.08177248,0.00631199,-0.02322852,0.02090017,0.02338711,0.00595107,-0.0621992,0.04348213,-0.00655465,-0.04223412,0.01512694,0.03941205,0.03913877,-0.07922573,-0.02303078,0.029782,0.03266946,-0.09084924,-0.05156273,0.04972035,-0.02550705,-0.00997255,0.06663233,0.03673064,-0.01084158,-0.02351179,0.06341873,0.00954083,0.05585501,-0.03609988,-0.02345926,0.02478256,0.01152093,-0.08414934,0.01602767,-0.04847442,-0.05323174,-0.00726806,0.00759335,0.02780225,0.03073074,-0.00561894,0.02187056,0.00443071,-0.06168049,-0.04152456,0.05586412,-0.01149892,0.11174086,-0.01552869,0.01468948,0.04898596,-0.07892723,0.04889107,0.02586685,-0.01872794,0.08842364,-0.01699271,-0.14831348,0.0198865,0.11542048,0.09896994,-0.03291615,0.03207436,0.01847846,0.04764079,0.04065168,0.04038436,-0.00945061,0.00428429,-0.0947856,-0.2155702,0.03757524,0.08160722,-0.07088041,-0.00773301,-0.01215899,0.03664318,-0.01042436,-0.01181255,0.08684472,0.12667783,-0.00883533,0.0409437,0.01167852,-0.04497748,0.01765912,0.00477756,-0.04729293,-0.05227664,-0.00093089,0.01212163,-0.02099884,-0.05548314,-0.09524101,0.06322058,-0.01653438,0.12546563,0.05679381,0.02379854,-0.07320239,0.04307367,-0.01554025,-0.02886635,-0.13279268,0.06330046,0.01499261,0.02640899,-0.002244,0.01750475,0.01334715,0.01212959,-0.01306766,-0.00497751,-0.01886195,0.05826164,-0.05882255,-0.06804155,-0.04629472,-0.02156447,0.05129331,-0.0263279,0.01469763,0.07019622,0.07724611,-0.02747469,-0.01005112,-0.01317608,-0.0604122,0.01972973,0.01092454,-0.0075145,-0.00286988,0.0156493,-0.11175089,0.01067824,0.02185478,0.01885083,-0.02653664,0.02788025,-0.0181959,-0.07068711,0.06890089,0.0434715,0.02337015,0.01171753,-0.00811829,-0.0719612,0.09123613,0.02313342,0.03097139,0.0120862,-0.04755275,0.01608956,0.00904838,-0.03280224,0.03641916,-0.03935569,-0.02711035,0.02654613,0.01884178,-0.08190186,-0.00724039,0.00035601,0.00706557,0.03949112,-0.01721708,-0.20676853,0.01911359,0.02042675,-0.00612152,-0.0316764,-0.02894822,0.06580526,-0.07205941,-0.04761677,-0.00025133,-0.01664143,0.03836966,-0.01269034,-0.03097786,0.00019881,-0.01192583,0.05144835,0.03530437,0.07597835,-0.07241289,-0.00110381,0.00662321,0.25023523,-0.02192376,-0.00780889,0.04302413,-0.03472418,-0.02653226,0.03147851,0.09492686,0.02761051,0.06425804,0.0822916,0.01660466,-0.05323634,-0.02455885,0.01647012,-0.0039629,0.04463441,-0.00111331,-0.01693388,0.00369246,-0.03411087,0.04032092,0.03529911,-0.14175621,-0.04620007,0.0062567,-0.01768287,-0.02238086,-0.01626131,0.02976694,0.00742029,-0.02567974,0.02236031,-0.00860367,-0.04433432,0.02514812,-0.02984405,0.03805048,0.01214182,-0.06735207,0.04062753,0.04740783,0.02423448],"last_embed":{"hash":"gztvn0","tokens":216}}},"text":null,"length":0,"last_read":{"hash":"gztvn0","at":1751288823277},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Getting started#{1}","lines":[49,81],"size":549,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"gztvn0","at":1751288823277}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Configuration options": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01366933,-0.01180211,0.04772042,-0.04657476,0.04835133,0.02411329,-0.11368438,-0.04718682,-0.01604433,-0.01810087,-0.03048317,0.00268677,0.04802383,0.00745719,-0.00442023,0.0204506,0.03225777,0.04501278,-0.0401471,0.02017083,0.07334755,0.00188896,-0.02137768,-0.08750734,-0.02505884,0.00955436,0.01122103,-0.03045846,-0.01000004,-0.21587475,0.07269572,0.00067882,-0.04236802,-0.02097841,0.00108722,-0.04483408,-0.01722497,0.02082445,-0.04758122,0.00839401,0.01822742,0.07301471,-0.0367208,-0.03321129,-0.0043689,-0.00349879,0.02716394,0.03712747,-0.0485354,-0.0080437,-0.04088729,-0.03381728,0.09047008,-0.04406743,0.00572715,0.08415874,0.0683573,0.07793387,0.02644293,0.01937627,-0.02527706,0.05231119,-0.16694336,0.05454179,0.01127898,0.01412804,-0.065015,-0.05240389,0.01887141,-0.03257398,0.00968212,0.07210755,0.01353212,0.0640843,0.04489085,-0.04181742,0.00782066,-0.02589093,0.06574453,0.00889384,-0.04319761,-0.0407718,-0.0207584,-0.0018453,0.05368751,-0.04341311,0.01190634,0.01871149,0.02306451,0.03536106,-0.02065444,-0.1143262,0.05507442,0.03893245,0.01817426,0.0283935,0.0054258,0.02203212,-0.03789233,0.13032749,-0.07887785,0.05045868,0.00563023,0.01536071,0.04424288,-0.04269208,-0.04032958,-0.04128072,-0.00352935,-0.03186912,0.006282,-0.00599616,-0.02651539,-0.05977545,-0.02263168,-0.01118165,-0.01822663,0.00878515,-0.00252507,0.00527643,0.01747856,0.03977421,0.04255616,0.01974082,0.02483019,0.01894366,0.00080778,0.04544878,0.00711745,0.06392693,-0.01047806,0.07097831,-0.0408602,-0.01873166,0.01350049,-0.02590272,-0.00493467,-0.00395827,-0.03424534,-0.00720803,0.01740449,-0.02888571,0.00637363,-0.04325561,0.06181744,0.08551347,-0.02369126,0.10719184,-0.00436031,-0.02415206,-0.07475257,0.0151766,-0.09585842,0.03005385,-0.03822344,0.04516577,0.01355136,0.0144479,-0.06356207,0.04463177,-0.01531243,-0.02831037,0.02265457,0.03681806,0.00367472,-0.06286271,-0.05696916,0.05292987,0.02360422,-0.05402742,-0.077865,0.00436243,-0.01821883,0.03403064,0.02783112,0.01274813,-0.02848883,-0.03071447,0.01495627,0.0421123,0.07123784,-0.02582194,-0.02202415,0.03548255,0.00947866,-0.07135233,0.05397278,-0.05752205,-0.01654738,-0.00229783,-0.02832725,-0.01908823,0.02934003,-0.01480467,0.02027619,-0.01747171,-0.03473948,0.03572506,0.04611476,-0.03439622,0.13943072,0.01930471,0.03052445,0.07599937,-0.05945098,0.04630105,-0.00792453,-0.03702486,0.04699242,-0.01825748,-0.12884942,0.00567385,0.07897864,0.06757683,-0.02770946,0.02619546,-0.00043337,0.05425721,0.01896441,0.05639298,0.0034424,0.01809747,-0.10345855,-0.24770398,0.02018231,0.03722038,-0.05629943,-0.01763931,-0.02821429,0.00775944,-0.02827739,-0.00165439,0.07386087,0.12699594,0.01346984,0.0013312,0.01440743,-0.04855188,-0.0164326,-0.02129696,-0.08493559,-0.05972899,-0.0265383,0.02482159,-0.01751838,-0.05530461,-0.10469306,0.06316224,-0.02064342,0.12710623,0.00613501,0.04084552,-0.06663534,0.00466498,-0.0111129,-0.01187369,-0.12383667,0.03594181,0.04229977,0.05628236,-0.00596256,0.02783373,0.02251262,-0.01782712,-0.01968018,-0.01419027,-0.08393627,0.081441,-0.05684893,-0.06024916,-0.02860381,-0.00823433,0.05352974,-0.00931519,-0.00014316,0.04638143,0.10931022,0.02363488,-0.02680568,-0.04993635,-0.03169348,0.01061927,0.00686357,0.00244928,-0.02569797,0.00293142,-0.09388568,0.01294671,0.00495024,0.03244417,-0.04888187,0.02974306,-0.03067114,-0.0834337,0.11967675,0.01314656,0.01064885,0.03495264,0.00175478,-0.0733901,0.05809553,0.01253479,0.00619914,0.04750945,0.01458672,0.01502199,0.03049205,-0.03122591,-0.00909588,0.01380476,0.00685429,0.05587958,0.02661683,-0.07449108,-0.00130614,-0.0531317,-0.01578912,0.10125141,0.00517628,-0.21315983,-0.00357758,0.03926124,-0.02739238,-0.02736842,-0.00900901,0.08346796,-0.07770573,-0.02367483,0.0125248,-0.03201839,0.06623618,0.01011854,-0.01283198,0.0328841,0.00047688,0.04935136,-0.02065757,0.09301731,-0.09916405,0.01265477,0.02965681,0.24005812,0.00635066,-0.00502612,0.0863401,-0.03239536,-0.00832998,0.05580087,0.06684841,0.0473038,0.03852344,0.07144638,0.0006623,-0.03257656,-0.03567737,0.00830874,-0.00535735,0.03247083,0.00702281,-0.02818364,0.00005221,-0.01158038,0.01705422,0.02667529,-0.10278629,-0.04533629,0.02638513,-0.00231938,0.00869851,-0.0104843,0.04802108,0.01229675,-0.00247443,0.0037844,0.0130902,-0.02390897,0.01236289,-0.03423234,0.00833188,0.03451056,-0.10309876,-0.00035587,0.07134595,-0.01031597],"last_embed":{"hash":"118qdwz","tokens":246}}},"text":null,"length":0,"last_read":{"hash":"118qdwz","at":1751288823347},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Configuration options","lines":[82,112],"size":731,"outlinks":[{"title":"\"styled-jsx/babel\", { \"optimizeForSpeed\": true }","target":"\"styled-jsx/babel\", { \"optimizeForSpeed\": true }","line":11}],"class_name":"SmartBlock","last_embed":{"hash":"118qdwz","at":1751288823347}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Configuration options##`optimizeForSpeed`": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02213013,-0.00287802,0.03021905,-0.03850509,0.05018359,0.03043951,-0.10208213,-0.04521853,-0.01047455,0.01086207,-0.01279875,-0.00255572,0.0448568,0.01133434,-0.00775503,0.00836796,0.03573212,0.03382949,-0.03525835,0.03116822,0.06992105,0.00030033,-0.03329178,-0.10066438,-0.02953151,0.00261251,0.00955495,-0.02316048,-0.00930716,-0.22105959,0.0609907,-0.01409166,-0.03084527,-0.03532949,-0.01121703,-0.05219189,-0.0285887,0.02114061,-0.07085907,0.03187528,0.01923071,0.08784542,-0.04041464,-0.03250116,-0.01021004,0.01295489,0.03697471,0.03336972,-0.05697224,-0.00451755,-0.03900215,-0.03876106,0.09743883,-0.03935059,0.00092581,0.07253774,0.05426518,0.09207474,0.01937648,0.02965038,-0.0266971,0.04943138,-0.16188714,0.04294672,0.00629237,0.02272447,-0.06857392,-0.06024199,0.00595338,-0.02883148,0.01941131,0.06685531,0.02999032,0.08389005,0.0380149,-0.02152441,-0.00459666,-0.01392559,0.0481811,0.01472889,-0.04507903,-0.06848307,-0.02934511,-0.00536626,0.04184829,-0.02696937,0.00647669,0.03100756,0.04136056,0.05058379,-0.03173165,-0.11473188,0.06549264,0.03124717,0.00933184,0.03797654,0.02382541,0.0107971,-0.04351037,0.13693208,-0.07633674,0.04648423,0.00764409,0.02184703,0.03903638,-0.0282505,-0.02049882,-0.04730226,-0.00406435,-0.02645023,0.0007449,-0.01030771,-0.01705615,-0.05626136,-0.03158417,-0.01447765,-0.02073311,0.02182652,0.01387397,-0.00547107,0.02614579,0.03793469,0.02464627,-0.00035437,0.01839253,0.02061696,0.00344951,0.03976469,0.01775403,0.07177415,-0.00224733,0.05607364,-0.03033241,-0.01895483,0.01472253,-0.03318456,0.00796821,-0.01320494,-0.03922535,-0.00945737,0.01805867,-0.03318788,0.00191946,-0.07141097,0.06228181,0.10318986,-0.01127666,0.11700493,-0.01425026,-0.04703935,-0.08384987,0.01246628,-0.08617409,0.03577005,-0.04945948,0.0638381,0.01613308,0.01782235,-0.04397577,0.04069331,-0.03076642,-0.0251318,0.01603192,0.05066586,0.00774201,-0.05288705,-0.04222947,0.03231356,0.02572635,-0.05736332,-0.08018816,-0.00719637,-0.02096912,0.04242305,0.03313809,0.00898646,-0.04383769,-0.02623394,0.00297056,0.03761743,0.09029455,-0.02641753,-0.02596238,0.03836592,0.01700688,-0.07766236,0.06103094,-0.04602542,-0.01839238,-0.01104261,-0.03391748,-0.02993272,0.02670125,-0.01389693,0.00049709,-0.030938,-0.0283845,0.03432709,0.04038592,-0.03137865,0.1268951,0.02608107,0.01538836,0.07304156,-0.06221619,0.03092161,-0.01400507,-0.02810663,0.02573518,-0.01074473,-0.13557868,-0.00116726,0.07427748,0.05634209,-0.01125315,0.0078935,-0.01516636,0.0647024,0.00934541,0.048856,0.0047345,0.03012882,-0.09752654,-0.2437584,0.01857744,0.0218316,-0.05485328,-0.00817109,-0.01630289,0.00771094,-0.03791495,0.00872148,0.06843536,0.12565017,0.01183463,-0.00945008,0.00936256,-0.03113343,-0.0287891,-0.0137115,-0.0859587,-0.04193722,-0.01708827,0.01419903,-0.00187759,-0.02354485,-0.09307183,0.05965788,-0.02520571,0.14094101,0.00188727,0.03129556,-0.06303196,0.00171669,-0.01338769,-0.0201342,-0.12332819,0.02952431,0.04622585,0.05570858,-0.01870331,0.01940573,0.01899105,-0.03806128,-0.01423196,-0.00499846,-0.09230021,0.07249717,-0.05586242,-0.06004746,-0.01116293,-0.02373459,0.04702603,0.00496436,0.00146417,0.04017289,0.08730647,0.03593224,-0.01504438,-0.0460432,-0.01986744,0.00808531,0.01354922,0.01250444,-0.04944712,-0.00512117,-0.0895595,0.02828369,0.0030966,0.03848616,-0.06516233,0.0230876,-0.02372088,-0.08795769,0.12793829,0.01683416,0.01186655,0.05763015,0.00294361,-0.07160493,0.04418986,0.00783932,0.00241022,0.05876058,0.02582632,0.02287816,0.03392774,-0.02047162,-0.00792043,0.01736668,0.01496751,0.03658891,0.01597202,-0.05945273,0.00011676,-0.07513531,-0.01674821,0.10217202,0.00669894,-0.21773599,0.00992867,0.03294915,-0.02548637,-0.03105417,-0.00382699,0.07728649,-0.06383269,-0.02720411,0.05103175,-0.04462785,0.08045138,0.00914563,0.00717038,0.02498246,0.00742916,0.04286879,-0.04346982,0.09105305,-0.10833304,0.01752575,0.04207325,0.23993519,0.02713553,-0.00716025,0.08006836,-0.01461842,0.00066653,0.0629568,0.05717764,0.02611161,0.03020381,0.06725369,-0.00934193,-0.04115815,-0.028993,0.0135662,0.01122527,0.01124846,0.00863027,-0.0323344,0.00188325,0.01926457,0.0131898,0.01783831,-0.08055522,-0.04377084,0.01441963,0.00661648,0.01367315,0.00006869,0.03685281,0.02113338,-0.01873819,0.01723051,0.0280021,-0.03647533,0.02019176,-0.03746798,0.01700215,0.02546661,-0.08933165,-0.01363646,0.06951415,-0.00741764],"last_embed":{"hash":"1j72iko","tokens":158}}},"text":null,"length":0,"last_read":{"hash":"1j72iko","at":1751288823451},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Configuration options##`optimizeForSpeed`","lines":[86,100],"size":406,"outlinks":[{"title":"\"styled-jsx/babel\", { \"optimizeForSpeed\": true }","target":"\"styled-jsx/babel\", { \"optimizeForSpeed\": true }","line":7}],"class_name":"SmartBlock","last_embed":{"hash":"1j72iko","at":1751288823451}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Configuration options##`optimizeForSpeed`#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01940865,-0.00336222,0.02931947,-0.03833544,0.04816644,0.03155879,-0.10393141,-0.04560724,-0.01089795,0.01273672,-0.01380288,-0.00163927,0.04238488,0.01240758,-0.00988584,0.00781858,0.03284227,0.03563248,-0.03776382,0.02942025,0.07210111,0.00363801,-0.03064347,-0.09719376,-0.02634856,0.00341514,0.00976818,-0.02374592,-0.00841943,-0.22190711,0.06093577,-0.01448018,-0.03372648,-0.03480867,-0.00819081,-0.05228719,-0.02868843,0.02232178,-0.06698024,0.03189082,0.0195644,0.08778908,-0.03894074,-0.03361568,-0.00829629,0.01704706,0.03756008,0.03764989,-0.05612886,-0.00258971,-0.03766521,-0.03870409,0.0997612,-0.03972252,0.00181755,0.07556216,0.05383859,0.09098128,0.02354778,0.02847805,-0.02641898,0.05188834,-0.16231857,0.0423187,0.00456415,0.02496536,-0.07053725,-0.05701702,0.00542887,-0.02914661,0.02260128,0.06294822,0.02877118,0.08201633,0.04279017,-0.02176999,-0.00435052,-0.01379314,0.04998336,0.01482846,-0.04512024,-0.06572289,-0.02914085,-0.00396662,0.03980248,-0.02852745,0.0054013,0.03003959,0.03942392,0.04844274,-0.02979889,-0.11682191,0.06658722,0.03267788,0.00795212,0.03425363,0.02135395,0.01136176,-0.04083077,0.13706715,-0.07730845,0.04675644,0.00795673,0.0207564,0.03775436,-0.02760187,-0.01905525,-0.04679058,-0.00577768,-0.02366315,0.00170531,-0.00888667,-0.02105554,-0.05656707,-0.03350786,-0.01515271,-0.02104984,0.02374923,0.01198737,-0.00667862,0.02276042,0.03973779,0.02855699,0.00208922,0.01779581,0.02050323,0.00331185,0.04167886,0.02085925,0.06771411,-0.00166382,0.05912338,-0.03418568,-0.01915791,0.01156769,-0.03415241,0.00566832,-0.01506852,-0.03904969,-0.0108713,0.01976694,-0.03123775,0.00142557,-0.06930688,0.06372974,0.10284171,-0.01089419,0.11818952,-0.01418506,-0.04537159,-0.08237874,0.00949075,-0.08677085,0.03452405,-0.05150913,0.06582747,0.01642372,0.01856275,-0.04463596,0.04236712,-0.025326,-0.02538096,0.0140702,0.05171287,0.00775494,-0.05072693,-0.04173313,0.03428767,0.02441409,-0.05739249,-0.08118364,-0.00850782,-0.02300111,0.0422984,0.03463831,0.01191084,-0.04609321,-0.02921829,0.00044944,0.03791894,0.08771233,-0.02732394,-0.02351309,0.03937439,0.01825175,-0.07961479,0.06203972,-0.0472515,-0.01664293,-0.00970733,-0.02830845,-0.02955996,0.02767907,-0.01552196,-0.00089547,-0.03101294,-0.03016846,0.03389417,0.03807698,-0.03070183,0.12513104,0.02523122,0.01415932,0.07282081,-0.06052258,0.03334385,-0.01060072,-0.02643968,0.02970446,-0.0102892,-0.13219742,-0.00288858,0.07259478,0.05657472,-0.01182882,0.00965415,-0.01571231,0.06272409,0.00640558,0.04953475,0.00513704,0.03191966,-0.09966058,-0.2446977,0.01775339,0.01994846,-0.05434445,-0.00851702,-0.01821622,0.00676049,-0.03425671,0.00611298,0.06746801,0.12379706,0.01454152,-0.01105602,0.0093961,-0.02955405,-0.03298904,-0.01728344,-0.08845556,-0.04352871,-0.01680533,0.01471605,-0.00328718,-0.02518168,-0.09358144,0.0577489,-0.02537556,0.14072159,0.00328693,0.03103279,-0.06487781,0.00303483,-0.01202371,-0.02385573,-0.12459917,0.02999355,0.04684805,0.05704291,-0.02191986,0.0217536,0.01747095,-0.0351653,-0.01436973,-0.00736247,-0.09122234,0.07154544,-0.05443326,-0.05851368,-0.01171826,-0.02457108,0.04846442,0.00334412,-0.00008021,0.0397429,0.08701883,0.02989647,-0.01621697,-0.04870831,-0.01798645,0.00974633,0.0109067,0.0080273,-0.05091733,-0.00371283,-0.08998436,0.02875078,0.00557609,0.03660011,-0.06787647,0.01953058,-0.02579186,-0.08417458,0.12940454,0.01957864,0.00969701,0.05356341,0.0012772,-0.07083565,0.04722448,0.00797047,0.00158978,0.05877012,0.02257839,0.02310822,0.03535176,-0.02068407,-0.00680787,0.01481165,0.01365952,0.03658835,0.01512726,-0.06149685,-0.0005121,-0.07398804,-0.019667,0.10335658,0.00314341,-0.21525615,0.00710071,0.03135898,-0.02461814,-0.03039181,-0.00223878,0.0770322,-0.06098079,-0.02557385,0.04989034,-0.04373747,0.08190461,0.00852084,0.00629423,0.02275411,0.00593925,0.04369913,-0.04354065,0.09269354,-0.1121075,0.01881067,0.04159162,0.24255386,0.02699443,-0.00403482,0.07922954,-0.0140825,-0.00249796,0.05985336,0.05959512,0.0263437,0.03159118,0.06648337,-0.0075348,-0.03793615,-0.02469169,0.01738333,0.01254168,0.01180526,0.00527286,-0.03314309,0.00264409,0.01738557,0.01448399,0.01785858,-0.08173419,-0.04704527,0.01593809,0.00681409,0.01479907,-0.00059379,0.03399025,0.02275825,-0.01939588,0.01931302,0.02933599,-0.03535618,0.02062743,-0.03610341,0.01583727,0.02771235,-0.09082421,-0.01361459,0.06752174,-0.00587195],"last_embed":{"hash":"1w1uwnu","tokens":155}}},"text":null,"length":0,"last_read":{"hash":"1w1uwnu","at":1751288823521},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Configuration options##`optimizeForSpeed`#{1}","lines":[88,100],"size":381,"outlinks":[{"title":"\"styled-jsx/babel\", { \"optimizeForSpeed\": true }","target":"\"styled-jsx/babel\", { \"optimizeForSpeed\": true }","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"1w1uwnu","at":1751288823521}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Features": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0282161,-0.00925724,0.04255199,-0.02623253,0.02192903,0.05111318,-0.11662258,-0.01551481,-0.01373617,-0.01025354,0.02708876,-0.01226713,0.04453915,0.03072507,0.01509452,-0.00182126,0.06872471,0.04577829,-0.03526138,-0.00227652,0.04605674,-0.02885097,-0.0087935,-0.05269341,-0.02772405,0.00562255,-0.03107435,-0.02619316,0.0027652,-0.21148552,0.03806955,-0.01104244,0.02567743,0.00635458,0.06271945,-0.0465521,0.01032491,0.05420101,-0.06546289,0.031069,0.00521993,0.06473698,-0.06682415,-0.00665373,-0.00438995,-0.06027619,0.02974174,0.00326284,-0.04108163,-0.01949564,-0.03282595,-0.03242286,0.06828737,-0.01798878,0.00413113,0.09708189,0.0448318,0.04770848,0.02334765,-0.00193823,-0.01743758,0.05416185,-0.13699497,0.05617974,0.0398885,0.04698426,-0.04470321,-0.0670177,0.01353021,0.01159088,-0.00265631,0.06506925,0.00145217,0.10390172,0.01216739,-0.03059426,-0.00485884,-0.01341672,0.03024653,-0.02518758,-0.06062003,-0.05431009,0.02017633,-0.00107909,0.00664308,0.02306334,0.0319754,0.01557417,0.01208975,0.06684716,-0.04944478,-0.06645723,0.05447808,0.00243215,-0.00467285,-0.03062999,0.03915168,0.02489889,-0.01711743,0.12863618,-0.06700016,0.03152096,0.02739483,-0.0043217,0.08571376,-0.00559538,0.00261483,-0.04503155,-0.03739648,-0.01182188,-0.00272946,-0.00941591,-0.02931019,-0.03169512,-0.02108328,-0.04280558,-0.02661627,-0.01074666,0.03378702,0.02658253,0.01736373,0.08854785,0.05855595,-0.0188904,0.03480227,0.01051927,0.0280907,0.03074963,-0.00690548,0.101263,0.02237108,0.06815001,-0.01943851,-0.02660816,0.0013806,-0.0018674,0.00960618,0.02769987,-0.03872783,0.04952369,0.00524756,-0.01560543,0.04333412,-0.04518147,0.06576418,0.04667323,-0.03163197,0.08942434,-0.02222416,-0.01309695,-0.08072778,0.05466917,-0.08685323,-0.02282748,-0.05638205,0.00881174,-0.00584225,0.02976366,-0.07099236,0.03420219,0.0024646,-0.00524661,0.00964439,0.0691484,0.03691677,-0.12704031,-0.04883963,0.07150313,-0.00553911,-0.06119568,-0.07393839,0.00762647,-0.01073164,-0.01049946,0.01800613,0.02924733,-0.06943816,-0.05197873,0.04749334,0.03289369,0.09278408,-0.03648189,-0.03656943,0.07225732,-0.0179643,-0.08980417,0.02127285,-0.0687658,-0.00597687,-0.01726695,0.00542465,-0.02499532,-0.00014179,0.01381967,-0.00588359,-0.00829017,-0.06203277,-0.01447085,0.01191174,-0.0367056,0.1278839,-0.00218545,-0.0121766,0.05366182,-0.01539492,0.04890683,-0.0269909,0.01208523,0.01484768,0.00842941,-0.1058021,0.0089021,0.09134692,0.0697528,0.00966519,-0.01605427,0.03057521,0.06439239,0.00682991,0.0316527,0.00317114,0.01239983,-0.14042506,-0.23458415,0.03532756,0.00311401,-0.0380548,0.00241024,-0.03024863,0.04611008,-0.03124224,0.00166471,0.04635644,0.11326619,0.0246537,-0.00370745,0.00395136,-0.05277212,-0.00703365,-0.03701066,-0.061483,-0.10497109,-0.03153164,0.03252024,0.0197482,-0.06524482,-0.06868991,0.07138541,0.02294706,0.15712062,-0.01160929,0.00537989,-0.05624501,0.02803973,-0.00441352,-0.04278646,-0.10311011,0.02254598,0.06295825,0.0725601,-0.03955663,0.03692516,0.0230856,-0.04112215,-0.0412107,-0.00882765,-0.08405405,0.05411319,-0.03453311,-0.01916633,-0.03004627,0.01022501,0.06230078,0.02138706,-0.03725379,0.03422989,0.09952484,-0.01021268,0.00351546,-0.02755113,-0.04631602,-0.01109081,0.05489963,-0.01554216,-0.03023952,-0.03426753,-0.09566478,0.04779028,0.04227807,0.02543657,-0.03591786,-0.02092135,-0.06610741,-0.07722925,0.09575225,0.02379607,0.00162225,0.03183638,0.00021237,-0.02217832,0.09360791,0.01438133,0.02508168,0.06825861,0.00849782,0.03212548,0.02823576,-0.00020281,0.00648593,-0.02635243,-0.03985555,0.00841892,-0.02956705,-0.05419292,0.02671809,-0.07820114,0.0003143,0.09025692,-0.01653996,-0.24436601,0.0067905,0.01924946,-0.0216534,-0.04263231,-0.0081896,0.06662811,-0.05911849,-0.05577342,0.02738099,-0.03096818,0.05065351,0.00059882,-0.01130439,-0.00841145,-0.00264892,0.09987435,-0.05090939,0.07191685,-0.07506912,-0.00743681,-0.00748415,0.23272994,-0.01682455,0.01205349,0.03927085,-0.02350125,-0.01586707,0.04799625,0.05096002,0.00984347,0.03112395,0.08233459,0.02543888,-0.04164132,-0.01888619,-0.00834344,0.01616502,0.03723037,0.00677206,0.02456577,-0.01949112,0.00965059,0.01897994,0.03384872,-0.09621517,-0.05471739,-0.01149852,-0.01608026,0.01108046,-0.01047316,0.05831271,0.01052637,-0.02924532,0.03411693,0.02261347,-0.0785661,-0.01966581,-0.03875975,0.00876396,0.01996975,-0.01405299,-0.00191717,0.05343896,0.00488254],"last_embed":{"hash":"1gaubm2","tokens":150}}},"text":null,"length":0,"last_read":{"hash":"1gaubm2","at":1751288823586},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Features","lines":[113,125],"size":473,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1gaubm2","at":1751288823586}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01120359,-0.00935712,0.0473778,-0.06661469,0.04732665,0.00771554,-0.09070399,-0.01232294,0.00104755,-0.01163512,0.00195191,0.02392802,0.02736623,0.02383189,0.0177087,0.00933988,-0.0002362,0.06440126,-0.0555533,0.00552847,0.03804116,0.00592634,-0.02348131,-0.05046265,0.00766944,0.03381899,-0.00008115,-0.00646679,0.02903609,-0.20142564,0.02675109,-0.01880712,-0.04379772,-0.00858773,0.02538044,-0.05034781,-0.02045402,0.02584489,-0.0453206,0.04614609,0.02245056,0.04893385,-0.01882926,-0.03496729,-0.02544258,-0.02462049,-0.00633706,0.00237727,-0.0153817,0.00032026,-0.02671914,-0.03816937,0.04590232,-0.02790342,0.02553439,0.13178357,0.07251181,0.06617458,-0.01696781,0.03589211,-0.01696757,0.05059617,-0.14656663,0.10192744,0.04058507,0.02413861,-0.08784635,-0.00425962,0.00829069,0.00849494,0.0083387,0.04870683,-0.00835263,0.11090411,-0.01165424,-0.04302721,0.02234551,-0.0296084,0.02178652,-0.04359645,-0.04931299,-0.05121094,0.00886149,-0.01155318,0.03575392,0.02039932,0.04011576,-0.03673509,0.03121197,0.02972006,-0.04637053,-0.11439717,0.04632572,-0.00727297,-0.00171608,0.01650175,0.01698132,0.04672627,-0.02350736,0.10957615,-0.08116815,0.01347305,0.01820829,0.00119764,0.07791532,-0.02333206,-0.0253682,-0.04879133,-0.04006359,-0.01499952,-0.01888922,-0.03144181,-0.07086248,-0.04930766,-0.07256164,-0.03612035,-0.01826524,-0.0131929,0.01993605,-0.03420324,0.03597298,0.01242656,0.03196919,-0.01539413,0.03780922,0.05845352,0.02322293,0.03536747,-0.02265116,0.10068434,0.0107136,0.05192349,-0.04583306,-0.04569137,-0.03945703,0.05602724,0.02750257,0.02135328,-0.0480955,0.00644954,0.0397669,-0.02754326,-0.02175109,-0.04947443,0.02935244,0.02865872,-0.05912191,0.05497409,-0.01321666,-0.00489371,-0.07452013,0.04785582,-0.09161623,0.02747607,-0.03437877,0.02731666,0.00667599,0.01525791,-0.04665462,0.0283551,-0.03515189,-0.0006221,0.02385467,0.02996922,0.02493883,-0.07123737,-0.06019718,0.08691269,0.02114574,-0.10398767,-0.04459013,0.02137188,0.01007937,0.00229679,0.03173851,0.01076751,-0.04128883,-0.03559097,0.06889637,0.03347316,0.08640611,-0.04511792,-0.03251958,0.04652566,0.0133907,-0.08293217,0.02979104,-0.05351839,-0.04878431,-0.02066188,-0.02599951,0.01213898,-0.01703748,-0.00503246,0.02253627,0.01806239,-0.06851681,-0.02880307,0.04640085,-0.03041808,0.14900133,0.0101527,-0.0022347,0.04075623,-0.05252549,0.06214999,0.02835928,-0.02033907,0.02143202,-0.01645891,-0.15256745,0.02795505,0.06412039,0.09923173,-0.01800168,-0.01894761,0.03007827,0.05528229,0.06284797,0.03437811,-0.00415304,-0.02616213,-0.09160936,-0.20478186,0.0176058,0.06469671,-0.05404136,-0.00741585,-0.03549137,0.04463103,0.01073195,-0.02522612,0.03963227,0.12087573,0.00249713,0.02925513,-0.00068951,-0.0659116,-0.00571185,-0.0049595,-0.05027574,-0.05292556,-0.02788266,0.0463448,-0.01880284,-0.01202364,-0.10256652,0.06784222,0.01797936,0.16040452,0.02889685,0.03543097,-0.02812774,0.03751341,-0.02513379,-0.00157374,-0.0845952,0.02392572,0.01580296,0.04649931,-0.0367904,0.03053852,0.02009652,-0.01037075,-0.02429337,0.01920633,-0.0482864,0.03217144,-0.04558769,-0.08078937,-0.04849768,0.00674649,0.07197652,0.02780705,0.02155997,0.05857223,0.09661625,-0.02178132,0.00254331,0.00629704,-0.06601325,0.00309025,0.03839204,-0.03231619,-0.02261783,0.00212106,-0.11335517,0.04756506,0.06093875,-0.0005674,-0.030549,0.04270551,-0.02634761,-0.06635742,0.11788564,0.06215197,-0.00484716,-0.00176114,0.01471448,-0.05690138,0.10278071,0.00190016,0.02932976,0.03166261,0.00751331,0.0375177,0.00383914,-0.00366578,0.01031009,-0.02184525,-0.02526061,0.04005654,-0.02251056,-0.11507989,0.00861943,-0.04205986,0.02430605,0.02476519,-0.01879962,-0.2322145,-0.01648589,0.0195637,0.00196442,-0.02935288,-0.00340062,0.03576691,-0.06757758,-0.05198658,0.00992854,-0.01319598,0.01736632,-0.00013671,-0.01628019,-0.01096218,0.01993375,0.05090002,0.0074395,0.08172742,-0.06930414,0.00814357,0.00264134,0.25284466,-0.02144186,0.03889673,0.03842578,-0.02597717,-0.01451169,0.04171896,0.08475022,0.01126803,0.033887,0.11256666,-0.01028041,-0.0417569,-0.04300266,-0.00462704,-0.02655004,0.03467363,-0.00678228,0.01656315,0.00221497,-0.01512335,0.03009233,0.02973706,-0.10964012,-0.03665594,-0.0267746,-0.02421997,-0.02292318,-0.00134802,0.07780487,0.02661787,-0.00756697,-0.00072201,0.00040306,-0.09852584,0.01810354,-0.04106069,0.01087611,0.00435598,-0.02582259,0.02200429,0.07913984,0.01136179],"last_embed":{"hash":"w22o05","tokens":502}}},"text":null,"length":0,"last_read":{"hash":"w22o05","at":1751288823648},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works","lines":[130,341],"size":5257,"outlinks":[{"title":"`:host`","target":"https://www.html5rocks.com/en/tutorials/webcomponents/shadowdom-201/#toc-style-host","line":29},{"title":"css-modules","target":"https://github.com/css-modules/css-modules","line":69}],"class_name":"SmartBlock","last_embed":{"hash":"w22o05","at":1751288823648}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03098516,-0.01252019,0.06104846,-0.06766481,0.02400366,0.01080965,-0.07723721,-0.00874994,-0.01569807,-0.02622382,-0.01078496,-0.01461378,0.00454254,0.00681255,0.0077031,0.01932556,0.02547632,0.04519039,-0.06869201,0.01408348,0.06506325,0.03133146,-0.02694396,-0.03608804,0.00547337,0.05997711,-0.00145583,-0.00367558,0.02682266,-0.18817887,0.00812427,-0.02768067,-0.03527564,-0.01325088,0.06482054,-0.01938472,-0.04297261,0.03479699,-0.0306668,0.02617665,0.01648395,0.02482748,-0.03086189,-0.0280125,-0.0049938,-0.02870553,0.0161296,0.02551169,0.00336528,-0.02652287,-0.03653645,-0.01469513,0.05317791,-0.03252234,0.03767522,0.13680084,0.08855344,0.04611964,0.00395099,0.02490828,-0.00938455,0.04199053,-0.16370757,0.09804809,0.04579545,0.0152217,-0.08161309,-0.00135533,0.01228202,-0.00902299,0.00152041,0.01824368,0.00108883,0.09522049,-0.01377049,-0.04731172,0.02001873,-0.04185955,0.01770785,-0.04416947,-0.07056921,-0.03581161,0.00358627,-0.01246889,0.03554035,0.02879613,0.03865435,-0.00277905,0.02083019,0.02911194,-0.04044284,-0.12314082,0.04401097,0.0176987,-0.02008918,0.02135246,0.01371827,0.04440343,0.02815112,0.12812917,-0.07080697,0.02448723,0.03493981,0.00987429,0.04440112,-0.01966088,-0.02543633,-0.06305828,-0.05370093,-0.02068137,-0.01444906,-0.02878516,-0.04288974,-0.06174736,-0.06081882,-0.03401377,-0.01723726,-0.00905305,0.02067968,-0.00776182,0.03474412,0.03777934,0.01649645,-0.0053804,0.00948288,0.05119348,0.01518072,0.04620555,-0.01225121,0.09069978,0.00059668,0.08023443,-0.04525858,-0.03299977,-0.03493297,0.04942949,0.0015615,-0.01146246,-0.05598995,-0.00057687,0.02261039,-0.04966851,-0.01910561,-0.03691966,0.01293236,0.02239697,-0.0579615,0.05606375,0.00532739,-0.0034421,-0.07841626,0.04462519,-0.0936799,0.02555012,-0.02889048,0.03552638,0.03051021,0.02165118,-0.05012654,0.04262,-0.01410953,-0.01689245,0.00761235,0.04060624,0.03069628,-0.06302597,-0.03111107,0.06729683,0.04389581,-0.09825233,-0.03051695,0.03377227,-0.01454308,0.02258166,0.04782382,0.03643928,-0.01446637,-0.06040217,0.07578029,0.01170252,0.08440181,-0.06114111,-0.03258498,0.04234593,0.0097945,-0.05719824,0.03945449,-0.05396748,-0.02097463,0.00794473,0.02534062,0.03067534,0.02048049,0.00367108,-0.00270716,0.00288241,-0.07351895,-0.01886492,0.03973734,-0.02841094,0.15792726,0.00182047,-0.00805285,0.05837343,-0.05992566,0.0379547,0.02237287,-0.02715764,0.04434903,0.01151232,-0.13472554,0.01161076,0.0922746,0.09504506,-0.03654175,-0.01457575,0.0203877,0.04505342,0.04747209,0.03596001,-0.01451452,-0.03172068,-0.09062439,-0.22185045,0.02268349,0.05697552,-0.05796985,-0.02327322,-0.01213359,0.04182256,0.00046234,-0.00663596,0.08002648,0.14637531,0.00026676,0.03033655,-0.00322978,-0.04310767,0.00769962,-0.02684397,-0.0409788,-0.05430269,-0.02720297,0.0400205,-0.05693875,-0.06804837,-0.10912072,0.06159049,0.00553384,0.14617199,0.04055949,0.03580734,-0.04746104,0.0561598,-0.0357805,-0.00483486,-0.08865488,0.04683052,-0.00337216,0.02821408,-0.0125517,0.0250503,-0.00214878,-0.00461999,-0.00621588,0.00444759,-0.02840276,0.04321115,-0.05662292,-0.07088728,-0.06395553,0.00669182,0.08751567,-0.00933327,0.03675916,0.05584666,0.10527661,-0.02036089,0.01485074,-0.00974287,-0.06301183,-0.00952972,0.00727133,-0.02051898,-0.03260867,0.00126928,-0.08789512,0.02859523,0.04177085,0.01021207,-0.03590231,0.0174035,-0.00394492,-0.05519729,0.09882042,0.05899144,-0.00627808,-0.00416498,0.0149705,-0.06559812,0.08044533,0.02558938,0.02007362,0.01623144,0.01553061,0.02814598,-0.00409898,-0.00912597,0.00503153,-0.0426387,-0.03509963,0.0546505,-0.02215557,-0.08807233,0.00897748,-0.01692971,0.03827809,0.04215653,-0.03250096,-0.22767617,0.00275101,0.03136964,0.00871469,-0.05112638,-0.00947017,0.05495771,-0.08372431,-0.05524709,0.00041977,-0.01773627,-0.00656132,-0.0126931,-0.02467291,-0.00449165,-0.00209683,0.06712855,-0.00615805,0.09775194,-0.05515645,0.00042745,-0.00484899,0.25702685,-0.02818951,0.03230904,0.03224184,-0.02977534,0.00016806,0.05145786,0.08198179,0.02172774,0.05723527,0.10981572,-0.00278157,-0.06057695,-0.0385404,-0.00478051,-0.00529337,0.01613379,0.00768714,-0.00049097,-0.00074819,-0.03896252,0.00481553,0.0419995,-0.09810915,-0.05493388,-0.03692483,-0.04579079,-0.03129261,-0.0245457,0.0636127,0.01344317,-0.00416201,0.0240332,0.00350461,-0.0734878,-0.00524564,-0.02671712,0.00702755,-0.00113341,-0.05327407,0.04883102,0.06770924,0.00085582],"last_embed":{"hash":"179ko9s","tokens":157}}},"text":null,"length":0,"last_read":{"hash":"179ko9s","at":1751288823904},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#{1}","lines":[132,144],"size":300,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"179ko9s","at":1751288823904}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Why It Works Like This": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01304704,0.00103372,0.05403782,-0.06731093,0.02084257,-0.014055,-0.04941139,-0.03127572,0.00640234,-0.01206784,0.01568058,0.00579723,0.04538263,0.03222574,0.04294421,0.00406355,0.01494869,0.0582797,-0.04306139,-0.01802387,0.03390216,0.03096841,-0.03693951,-0.04029287,-0.01694768,0.02924649,-0.00662151,-0.00650482,0.03163698,-0.2063074,0.05019979,-0.01680343,-0.04259343,-0.01678697,0.02156491,-0.04941149,-0.02672193,0.05408628,-0.04694517,0.05030984,0.03610434,0.06580541,-0.04167344,-0.03273507,-0.01985311,0.01155033,-0.00307265,-0.02049973,-0.0221069,0.0083314,-0.0345945,-0.05883931,0.05093882,-0.00536433,0.00714212,0.12179122,0.04737748,0.07680497,-0.01048324,0.01529851,-0.02864304,0.05020263,-0.11620475,0.09586047,0.02883181,0.02327665,-0.05572118,-0.04029241,0.01145035,0.01179526,0.03049368,0.05563454,0.00034852,0.12108459,0.00490415,-0.0173954,0.03231073,-0.02210195,0.01807263,-0.02878642,-0.06831203,-0.10209493,0.00457923,-0.01416155,0.03904304,0.00753254,0.03616588,-0.03893899,0.04551174,0.04362755,-0.05892669,-0.08453365,0.06018621,0.02712468,-0.00850565,0.01565787,0.00712327,0.06903429,-0.05159454,0.13271856,-0.1252508,0.02030151,-0.00214028,-0.00086153,0.06410023,0.00512271,-0.02756939,-0.06697094,-0.04362862,-0.00687006,0.01609773,-0.0181917,-0.07553166,-0.02460993,-0.06431402,-0.01904505,0.00556629,0.00936145,0.01729573,-0.0400284,0.01600038,0.03642938,0.00307311,-0.01516278,0.03441943,0.04052039,0.03788245,0.03904376,-0.01472804,0.06889613,0.00357645,0.06861226,-0.06181227,-0.06533258,-0.037328,0.03202131,0.0168279,-0.01599846,-0.02131351,0.02013539,0.03807094,-0.0011099,-0.00178168,-0.07256177,0.02439266,0.0563901,-0.04198492,0.08068349,-0.01651452,0.00756716,-0.07472827,0.06196107,-0.10465578,0.02878442,-0.04859148,0.01571043,-0.00740647,0.00442458,-0.05334955,0.03408502,-0.01440163,0.00262592,0.02695759,0.01549297,-0.00441018,-0.05311805,-0.07409623,0.07393517,0.02340369,-0.08012909,-0.06406743,0.01811573,-0.00700213,0.0173267,0.00765374,0.01589455,-0.04788265,-0.04914945,0.05128641,0.02702853,0.09747047,-0.04502465,-0.01410527,0.02796237,0.01140572,-0.08489877,0.04049231,-0.04494438,0.00168926,0.00014343,-0.04536496,-0.00469551,-0.00820816,0.00639272,0.00954676,0.00431999,-0.0549653,-0.02538226,0.0220716,-0.03891086,0.1117888,0.02693942,-0.01073158,0.08814653,-0.01568513,0.06708838,0.0274806,-0.01498018,0.02261594,-0.03241872,-0.14428583,0.0184366,0.04708691,0.09764267,0.01093965,-0.01805078,0.03097697,0.08518286,0.04092163,0.00604358,0.02381147,-0.01555696,-0.08765537,-0.22427163,0.03628459,0.04096181,-0.05311021,-0.00565679,-0.04570757,0.04351834,-0.00558259,-0.0285534,0.06844482,0.0702529,0.03756137,0.01594687,-0.03187012,-0.05028615,-0.0112803,-0.01248943,-0.05370023,-0.07335454,-0.03086076,0.03468839,-0.00768227,-0.00323642,-0.07873679,0.07533081,0.02537755,0.15936811,0.02686631,0.05387432,-0.00618843,0.0256218,-0.00474813,-0.03813294,-0.08336395,0.04834247,0.02866099,0.04046487,-0.03401234,0.04046324,0.0150295,-0.02224886,-0.05322307,0.01946485,-0.0688462,0.06714301,-0.00857196,-0.07765955,-0.02547106,-0.02962676,0.07708995,0.00191335,0.01234659,0.03062553,0.12781349,-0.00492793,0.00076604,-0.01859343,-0.04174644,0.01380682,0.0292,-0.00488215,-0.01476588,0.00151043,-0.11816469,0.04727731,0.03604948,-0.02132394,-0.02368797,0.04424333,-0.03518507,-0.07374381,0.14693609,0.04451269,-0.04083037,0.01330489,0.01181404,-0.02821805,0.09782559,-0.01289594,0.02355349,0.02849597,-0.00497967,0.04814886,-0.00124031,-0.02512729,-0.00667528,-0.03365315,-0.05200249,0.00128342,-0.01949728,-0.07034031,0.04347047,-0.05355159,0.02428531,0.05640195,-0.03222384,-0.23806182,-0.01960601,0.02700738,0.03584234,-0.03760618,0.0169677,0.03017684,-0.0340553,-0.02538607,0.00023012,0.00616984,0.06079555,0.00565228,-0.01981257,-0.03666373,0.014721,0.05674401,-0.04774763,0.08331881,-0.0729029,-0.00788061,-0.01172435,0.25804991,-0.03450542,0.03454173,0.03138065,-0.02661038,-0.00587986,0.05083711,0.07099387,0.01314428,0.01294753,0.09917979,0.00535996,-0.03719685,-0.02700199,0.00922319,-0.0120355,0.03696254,0.00669771,-0.00208737,0.00638417,-0.04814758,0.02044267,0.00889289,-0.07816991,-0.02821417,-0.02700745,-0.03638736,-0.01556706,0.00448212,0.04655005,0.00310051,-0.01415611,0.00266104,-0.01045786,-0.08737578,0.02148631,-0.05234607,0.01967057,0.00110955,-0.00546709,-0.00253104,0.06557629,-0.00899924],"last_embed":{"hash":"157zdj6","tokens":107}}},"text":null,"length":0,"last_read":{"hash":"157zdj6","at":1751288823990},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Why It Works Like This","lines":[145,153],"size":326,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"157zdj6","at":1751288823990}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Targeting The Root": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02027904,-0.00292684,0.04052012,-0.04814189,0.04019428,-0.00439777,-0.08853297,0.0151379,-0.01279101,-0.0113451,0.01690207,0.00104434,0.03451221,0.01855217,0.03959482,0.02512076,-0.00231245,0.10707597,-0.01129594,0.03464634,0.06270508,-0.02538937,-0.00869576,-0.05717949,0.01988672,0.05540929,0.01276089,-0.02387374,0.03203093,-0.18265554,0.00370275,-0.00823836,-0.01607699,-0.01168281,0.02511449,-0.03499661,-0.03238786,0.00650228,-0.04711507,0.01774307,0.04808995,0.02950044,-0.01168343,-0.03907833,-0.02502527,-0.040296,0.02341356,-0.01307618,0.00399879,-0.00079496,0.00134902,-0.03129011,0.05011594,-0.04100385,0.02343641,0.10537563,0.04397081,0.0589002,-0.01084371,0.03874513,-0.00446778,0.06984278,-0.15994175,0.09946571,0.00832979,0.02014962,-0.09058034,-0.01591183,0.0093186,0.05206146,0.04650958,0.06177444,-0.04043914,0.09797657,-0.01026435,-0.03322078,0.00711524,-0.02004787,0.01767993,-0.07100302,-0.05880669,-0.04934064,0.00661925,-0.03772404,0.02646679,0.02293185,0.03228701,-0.03753609,0.03246152,0.03050285,-0.04284954,-0.12095892,0.01027176,-0.03617121,-0.00261125,-0.00053769,0.02497387,0.00482169,-0.02548735,0.13319997,-0.05952227,0.04671673,0.00035874,-0.00965302,0.08018229,-0.03174564,-0.02495814,-0.03417065,-0.0408123,-0.00968338,-0.02177238,-0.03607826,-0.08351536,-0.06200412,-0.04451411,-0.04553564,-0.01180505,-0.00105518,0.01008321,-0.03480498,0.04670221,-0.01935852,0.0651073,-0.01370077,0.0320607,0.04357228,0.02803098,0.03891459,-0.02840519,0.11956026,0.01470612,-0.00752532,-0.0478687,-0.03942988,-0.00430649,0.08641037,0.04936101,0.05157657,-0.06331926,-0.00532114,0.00656333,-0.0537253,0.00971562,-0.04871849,0.00512083,0.01416078,-0.05105044,0.05382055,0.00298008,-0.01390533,-0.06317347,0.04307632,-0.07884985,0.02864246,-0.0764389,-0.00399749,0.00847651,0.01577924,-0.04994733,0.01154735,-0.03223588,-0.00518396,-0.01680039,0.06218822,0.02206876,-0.06902844,-0.06614421,0.08652718,0.02019041,-0.1063641,-0.02460127,0.04659557,0.06527531,-0.01934327,0.03417552,0.02658445,-0.03342241,-0.03033434,0.01605713,0.03660418,0.06133317,-0.04370599,-0.03524289,0.03397377,-0.00075956,-0.04258899,-0.00590811,-0.08537336,-0.04672565,-0.03696169,-0.04285038,0.00417249,-0.03465911,-0.04250052,0.00844663,-0.00324983,-0.04135526,-0.00847391,0.01719042,-0.02413861,0.09635684,0.00454979,-0.00557782,0.04559625,-0.05368223,0.07184003,0.03701073,-0.04304033,0.01338851,-0.02473394,-0.15214059,0.03331861,0.06900372,0.09720212,-0.06325779,-0.0210113,0.0245582,0.05801997,0.08581777,0.06318463,-0.02488712,-0.00141222,-0.05804667,-0.22008462,-0.00780653,0.03491497,-0.0533971,-0.04364112,-0.01937996,0.03743319,0.04225767,-0.03372241,-0.00528737,0.12403217,-0.00886332,0.06329343,-0.00480425,-0.05419526,-0.01200935,0.0086435,-0.05416517,-0.03554369,-0.02624644,0.06950565,-0.05814862,-0.01909637,-0.11358774,0.05129972,-0.00596579,0.14075164,0.0665818,0.03811811,-0.0114116,0.04081239,-0.04311445,0.03637924,-0.09006507,0.00562236,-0.00435974,0.06043801,-0.0418212,0.02089589,0.04987821,-0.01652047,0.00046772,0.00732707,-0.03024785,0.02261235,-0.05914683,-0.05228315,-0.02862409,0.00484051,0.0734069,0.06054381,0.0168717,0.05701154,0.0988631,-0.05033276,0.01947233,0.04017706,-0.05775118,0.00676601,0.01339292,-0.03862273,0.00717079,0.00568894,-0.09983081,0.04648734,0.05505739,0.01212361,-0.04360575,0.04947541,-0.02980231,-0.04765565,0.07493637,0.08519515,0.01173888,0.02146783,0.00001063,-0.05477278,0.07407933,0.02280289,0.00530381,0.04039434,-0.00193919,0.0323663,-0.01576414,0.00715406,0.01844087,-0.03486684,-0.00691079,0.06306458,-0.03092587,-0.10495782,0.00305071,-0.02486273,0.0122716,0.00044371,-0.01151902,-0.2194818,0.00871413,0.00631304,0.03460852,-0.0339129,-0.0034976,0.07988491,-0.08201703,-0.07978559,0.03903974,-0.00433935,0.00249471,-0.02530923,-0.02081661,-0.02236651,0.05858096,0.05204218,0.01543153,0.03407044,-0.07577478,0.02220466,0.02181291,0.24142951,0.00104929,0.02899965,0.02126671,-0.02942252,-0.01824798,0.02886912,0.08307423,0.01447714,0.02929376,0.09222037,-0.01736712,-0.02677245,-0.01883438,0.01911184,-0.00391957,0.05442094,-0.01233937,0.00758885,-0.03284382,-0.03637975,0.02480248,0.03597865,-0.09927163,-0.0568613,-0.01100548,0.00736056,0.00188203,-0.00991107,0.06833974,0.02434243,0.01819281,0.02344204,0.05663856,-0.09713238,0.00135459,-0.03063828,-0.00032336,-0.02475771,-0.0029498,0.02807496,0.07180463,0.03497629],"last_embed":{"hash":"1o8gxk8","tokens":201}}},"text":null,"length":0,"last_read":{"hash":"1o8gxk8","at":1751288824063},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Targeting The Root","lines":[154,173],"size":522,"outlinks":[{"title":"`:host`","target":"https://www.html5rocks.com/en/tutorials/webcomponents/shadowdom-201/#toc-style-host","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"1o8gxk8","at":1751288824063}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Targeting The Root#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02020877,-0.0029267,0.03980677,-0.04751929,0.0418835,-0.00429754,-0.09067762,0.01683595,-0.01449059,-0.01275094,0.01606118,0.00192548,0.0341595,0.02082283,0.03996313,0.02542196,-0.00614739,0.10803233,-0.00655983,0.03400458,0.06370289,-0.02781627,-0.00942183,-0.0611091,0.02156164,0.05447936,0.01425887,-0.02492973,0.02854912,-0.18029878,0.00213432,-0.01040276,-0.01512203,-0.01244796,0.02285995,-0.03455008,-0.03393145,0.00792718,-0.04582451,0.01936996,0.04725081,0.02959078,-0.0106786,-0.04034166,-0.02617318,-0.04300858,0.02330898,-0.01595602,0.00263527,0.00007201,0.00440527,-0.03381335,0.05018705,-0.03730192,0.02351737,0.09822457,0.03917149,0.05635547,-0.01102887,0.04170572,-0.00476979,0.06962732,-0.15695269,0.09442152,0.00549523,0.02086005,-0.0920207,-0.01619368,0.00620892,0.05604602,0.04790362,0.06101598,-0.04107487,0.09747555,-0.00980101,-0.03033515,0.0067352,-0.01879381,0.01454226,-0.07301441,-0.0539268,-0.04796318,0.00947826,-0.04137076,0.02411839,0.02099275,0.03207302,-0.04146751,0.03249048,0.02945239,-0.040272,-0.12112955,0.00856666,-0.03814953,-0.00352097,0.00053255,0.02362751,0.00076126,-0.0279499,0.13448603,-0.05724784,0.04604346,0.00186192,-0.00779909,0.08184413,-0.03046401,-0.02384776,-0.03275812,-0.03594062,-0.00859006,-0.02633363,-0.03425596,-0.08211939,-0.05977587,-0.04126562,-0.04564494,-0.01025907,0.0013604,0.00881149,-0.03779658,0.05009756,-0.02422767,0.07086634,-0.01202863,0.03353467,0.0444936,0.03093296,0.03975269,-0.02956103,0.11813857,0.01523475,-0.01468076,-0.04829069,-0.03699068,-0.00452941,0.08875377,0.05263114,0.05206161,-0.0625089,-0.00301865,0.00136314,-0.05467929,0.00706932,-0.05082794,0.00664231,0.01222707,-0.0509511,0.05294572,0.00214821,-0.01447225,-0.06094983,0.04531109,-0.07804099,0.02925136,-0.07623879,-0.00608046,0.00413901,0.01745326,-0.05025709,0.01089415,-0.0334309,-0.00180733,-0.01779667,0.06540441,0.01902718,-0.06918593,-0.06895216,0.09048891,0.01775664,-0.10564158,-0.02574723,0.04551454,0.06695317,-0.02508061,0.03767032,0.02523243,-0.03537421,-0.03129166,0.01189995,0.03925296,0.06442381,-0.0406846,-0.03391284,0.03698773,-0.00117367,-0.04149803,-0.00859798,-0.08687413,-0.0480065,-0.03607532,-0.04857353,0.00630844,-0.03467076,-0.04719633,0.01114878,-0.00175229,-0.04070442,-0.00804581,0.01587776,-0.02559285,0.09561826,0.00429947,-0.00425008,0.0423283,-0.05252913,0.0744919,0.03607554,-0.04418894,0.01117597,-0.02225334,-0.15429144,0.03471279,0.06772237,0.09408285,-0.0627415,-0.01663932,0.02110738,0.05720793,0.08667378,0.06160785,-0.02506305,-0.00085035,-0.05912028,-0.21932997,-0.00769173,0.03405683,-0.05303139,-0.04248794,-0.01841553,0.03909976,0.04819932,-0.03312728,-0.00925957,0.11960822,-0.00786023,0.0624523,-0.00464344,-0.05413543,-0.01457455,0.01085692,-0.05427215,-0.03664136,-0.02442659,0.06508772,-0.0566526,-0.0166139,-0.1126724,0.05528808,-0.00369354,0.14256926,0.06728355,0.03602125,-0.01287086,0.04063415,-0.04077781,0.04131301,-0.09242141,0.00312876,-0.0067487,0.05845331,-0.04666458,0.02177792,0.05223918,-0.01848741,0.00111609,0.00487082,-0.02763263,0.02297492,-0.0613222,-0.04839582,-0.02755188,0.00469345,0.07320191,0.06371298,0.01638006,0.05942936,0.09627853,-0.04668357,0.01735253,0.04228209,-0.05815053,0.00950492,0.01368406,-0.03815869,0.00987752,0.00440927,-0.10104117,0.04431317,0.05496792,0.01264169,-0.04565031,0.04913172,-0.02995899,-0.04580582,0.07601918,0.08776508,0.01025444,0.01925052,-0.00131107,-0.05497435,0.07722142,0.02248836,0.00474576,0.0448861,-0.00165186,0.03085329,-0.01351566,0.00758337,0.0170374,-0.03077992,-0.00605842,0.0619901,-0.03140498,-0.1039531,0.00031461,-0.02577419,0.01169422,-0.00099169,-0.00888148,-0.21927053,0.0104803,0.00551965,0.0326258,-0.03292315,0.00072732,0.07757264,-0.07978483,-0.08041717,0.03810819,-0.00810404,0.00627052,-0.02433046,-0.01644828,-0.02246528,0.06104666,0.05532223,0.01618779,0.0342832,-0.07684617,0.02300884,0.02198908,0.23993997,0.00257768,0.02874215,0.01965911,-0.02984888,-0.01967423,0.02158606,0.08309181,0.0121788,0.03129728,0.08969552,-0.01876973,-0.02432713,-0.01694093,0.01924381,-0.00434242,0.05435209,-0.01400353,0.00698344,-0.03826303,-0.03589322,0.02826504,0.03297257,-0.09929962,-0.0552098,-0.00708593,0.00638887,0.00134165,-0.00820086,0.07003096,0.02486835,0.02087215,0.0232098,0.05839139,-0.09791915,0.00009794,-0.030578,-0.00083799,-0.02211895,0.00018945,0.02613362,0.07040418,0.03785072],"last_embed":{"hash":"1dywf8u","tokens":199}}},"text":null,"length":0,"last_read":{"hash":"1dywf8u","at":1751288824238},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Targeting The Root#{1}","lines":[156,173],"size":498,"outlinks":[{"title":"`:host`","target":"https://www.html5rocks.com/en/tutorials/webcomponents/shadowdom-201/#toc-style-host","line":3}],"class_name":"SmartBlock","last_embed":{"hash":"1dywf8u","at":1751288824238}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Global styles": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.01167265,-0.02068293,0.05802002,-0.05839412,0.05563993,0.02704573,-0.08341238,-0.03875832,-0.01334912,-0.015857,-0.00408825,0.045697,0.04820746,0.01301128,-0.00711307,-0.01492126,-0.01156457,0.05602431,-0.06753664,0.00678138,0.03919347,0.01014327,0.00120573,-0.05693038,0.00543974,0.01925093,-0.02137726,-0.00741178,0.01277041,-0.21001945,-0.015502,-0.05397103,-0.08167449,0.00903981,-0.00980573,-0.0698957,-0.00841032,0.03475548,-0.03672653,0.04500864,-0.00274418,0.06650615,-0.03005857,-0.02896722,-0.00946922,-0.01612935,-0.01538747,0.01863594,-0.02518632,-0.01171483,-0.02528387,-0.05052282,0.05502902,-0.03357451,0.02145576,0.12183982,0.05814225,0.04605699,-0.00210328,0.03581831,-0.00249555,0.00430011,-0.14573716,0.08217423,0.02633744,0.03684508,-0.04777063,0.0190149,0.0176191,0.0268828,-0.0064287,0.01249197,-0.01225593,0.13688394,0.03139348,-0.02340551,-0.00080351,-0.05008726,0.00920918,0.02690047,-0.01104895,-0.05217111,0.00298957,-0.02848945,0.01869693,-0.01361319,0.02028959,-0.02665142,0.00837608,-0.00789325,-0.02192022,-0.12504995,0.06291875,0.01620984,-0.02552694,0.00005738,0.04761757,0.04868064,-0.04031913,0.11798059,-0.06200573,0.01973035,0.04933674,0.045478,0.05731148,-0.0278127,-0.01115183,-0.02785936,-0.03661722,-0.03616685,0.0125873,-0.04990524,-0.05825244,-0.04961586,-0.05698103,-0.02193783,-0.02910085,0.01563383,0.01984641,-0.02382324,0.03704555,0.03738375,0.04620817,0.01156347,0.03141475,0.03765059,0.00470177,0.01619866,-0.00067845,0.06698664,0.02159978,0.0282397,-0.01391302,-0.03760679,-0.02489488,0.03444545,0.00588376,0.04799023,0.00164051,-0.00063824,0.04564683,-0.01470181,-0.01665024,-0.06317719,0.07107669,0.05828739,-0.03031671,0.06156731,-0.00679514,-0.04148594,-0.09701844,0.01000672,-0.08427581,0.03091401,-0.01106435,0.04193754,0.01047161,-0.00572568,-0.05779641,0.01500711,-0.0562156,0.01675915,-0.00982914,0.05923109,0.04264206,-0.06199162,-0.05383871,0.06387109,-0.01223281,-0.06972009,-0.03690597,-0.0048309,-0.02625372,0.01426993,0.06570189,0.00879957,-0.08533137,-0.03807037,0.06530332,-0.01170762,0.09889298,-0.0170039,-0.01631099,0.04001807,-0.00538289,-0.12906571,0.03315742,-0.01204294,-0.0510284,-0.03184884,-0.01310297,0.00634413,-0.01877634,-0.0297554,0.0312443,-0.01223512,-0.05307715,-0.01908202,0.04450797,-0.02157622,0.18267196,-0.01667386,-0.00773378,0.02557454,-0.03268649,0.04638442,0.04082905,-0.04088297,0.04720996,0.00599457,-0.14141004,0.00989659,0.07455567,0.07978079,0.01787568,-0.01131681,0.00649196,0.04405693,-0.00692477,0.00026067,0.02166075,0.01988867,-0.08828551,-0.21429908,0.01591922,0.04660973,-0.02865743,0.01721426,-0.03282298,0.04804165,-0.0095288,-0.04074613,0.071839,0.07180677,-0.00455218,-0.00524846,0.02852611,-0.02925833,0.02712913,-0.0152156,-0.06025961,-0.06096783,-0.01125332,0.02558036,0.02097221,-0.07101545,-0.09514023,0.05987897,-0.00881719,0.15890417,0.04503158,0.03106881,-0.05012498,0.05729568,0.00581765,-0.01209772,-0.09832275,0.03591943,0.05860754,0.04799918,-0.05459682,0.03897167,-0.02360194,0.01399541,-0.0158943,0.02237435,-0.04272955,0.03396971,-0.03862068,-0.08777273,-0.02325148,-0.00560682,0.04300115,0.00334095,0.01746106,0.05724616,0.1114638,-0.0276718,-0.00816475,-0.04183637,-0.02999288,-0.01169774,0.02984898,-0.01911572,-0.03869361,-0.01437288,-0.08839349,0.02790077,0.06582542,0.00558608,-0.01432029,0.0534371,-0.0096203,-0.04748695,0.12600268,0.0343467,-0.01082576,-0.02540543,0.02974136,-0.06852242,0.11271231,-0.02554313,0.02712575,0.06602865,0.02322176,0.00999359,-0.00226842,0.00501608,-0.00023651,0.00815798,-0.02157176,0.03165046,-0.04821858,-0.1135462,0.00285494,-0.05081193,0.03983523,0.06061856,-0.045013,-0.20750782,-0.01496777,0.03077141,-0.00651041,-0.04701441,-0.0195642,0.00265573,-0.084343,-0.04586102,0.02176817,-0.03766768,0.0474219,0.03325605,0.00663791,0.00225558,-0.0071938,0.03387563,0.05816304,0.09855563,-0.06418999,0.01382148,0.02000787,0.25475326,0.00817926,0.01874128,0.04852364,-0.03570278,-0.01282281,0.0372294,0.069654,0.02758426,0.04120183,0.08119722,-0.00712482,-0.01832122,-0.05455113,-0.01222692,0.02062768,0.03296943,-0.02796341,0.00706891,0.04010931,0.00882603,0.03131569,0.03827319,-0.14796467,-0.02935538,0.0037485,-0.01686073,-0.02076333,0.00985555,0.04499338,0.01569481,-0.01653145,-0.01838036,0.00628855,-0.07823106,-0.00144291,-0.03022373,0.02614921,0.02983717,-0.01818378,-0.02130294,0.04569655,0.02409389],"last_embed":{"hash":"le82xp","tokens":167}}},"text":null,"length":0,"last_read":{"hash":"le82xp","at":1751288824440},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Global styles","lines":[174,195],"size":501,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"le82xp","at":1751288824440}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Global styles#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.01677594,-0.01886061,0.05541557,-0.05687467,0.05639057,0.02910838,-0.07995252,-0.04034881,-0.01270277,-0.01583995,-0.00408983,0.04677972,0.04875792,0.01409959,-0.0069968,-0.01792492,-0.01137602,0.05624615,-0.06539194,0.00569013,0.04170342,0.01148244,0.00408513,-0.0569934,0.00692415,0.01420137,-0.01732592,-0.00684418,0.00995598,-0.20875628,-0.02056417,-0.05483714,-0.08865207,0.007302,-0.01729725,-0.07358994,-0.00902581,0.03835052,-0.03745903,0.04755284,-0.00556858,0.06816432,-0.03041119,-0.03181729,-0.01299501,-0.01542384,-0.0187538,0.01509518,-0.02793714,-0.01022382,-0.0269142,-0.0504087,0.05675207,-0.03134403,0.02045589,0.11908919,0.05423565,0.04575846,-0.00333747,0.03966773,-0.00353894,0.00241489,-0.14287601,0.07941494,0.02202086,0.04030951,-0.04565116,0.02052984,0.01819872,0.03104622,-0.00468,0.01256412,-0.00988232,0.13742574,0.03284791,-0.0233796,-0.00206786,-0.04789944,0.00648813,0.0300121,-0.00373773,-0.05113473,-0.00092809,-0.03055388,0.02011162,-0.01735582,0.01646506,-0.02913192,0.01050277,-0.0102892,-0.02154102,-0.1248626,0.06567235,0.01689845,-0.02529108,-0.00088289,0.04982381,0.04995247,-0.03999718,0.1173652,-0.06305105,0.02056779,0.04993948,0.05324573,0.05923731,-0.02741699,-0.00890453,-0.02704076,-0.03564165,-0.03713165,0.01324608,-0.05228193,-0.05658789,-0.04665369,-0.05624144,-0.02063881,-0.02763362,0.01881381,0.02330883,-0.0242539,0.03848642,0.03530573,0.04960423,0.01000229,0.02929423,0.03814594,0.00375149,0.01659611,0.00290721,0.06070673,0.02183113,0.02342241,-0.0108169,-0.04011419,-0.02445137,0.02871417,0.01036671,0.04815467,0.00636749,-0.00375201,0.04240651,-0.01251109,-0.0137964,-0.06472729,0.0713309,0.059505,-0.0307497,0.06156705,-0.00796845,-0.04186982,-0.09532128,0.00672899,-0.07893356,0.03392756,-0.00835864,0.04256153,0.00825199,-0.00518808,-0.05186429,0.01154433,-0.05789478,0.01738555,-0.00749073,0.05898264,0.04198642,-0.0595288,-0.05605818,0.06563975,-0.0162683,-0.06621402,-0.03474144,-0.00899033,-0.03062141,0.0141382,0.06844211,0.00882607,-0.08811763,-0.03581015,0.06164335,-0.01141022,0.09991137,-0.01416403,-0.01949182,0.04244036,-0.00916942,-0.12830073,0.03183483,-0.01116997,-0.04961749,-0.03496837,-0.01478639,0.00744865,-0.02187392,-0.0322781,0.03202036,-0.01180977,-0.04812849,-0.0193579,0.04600736,-0.02347866,0.18259723,-0.0159418,-0.00722594,0.02067209,-0.03186046,0.04453385,0.04247618,-0.04189069,0.04530815,0.00470982,-0.13793863,0.00758841,0.0710075,0.07631745,0.02216116,-0.01037079,0.00291361,0.03973049,-0.01179326,-0.00122602,0.02470908,0.01936311,-0.08942171,-0.21541546,0.01322757,0.04469992,-0.02686097,0.02059486,-0.03145798,0.04885637,-0.00965682,-0.0421501,0.06866296,0.06921988,-0.00264271,-0.00942264,0.02888691,-0.02604427,0.03365515,-0.01205204,-0.0603244,-0.06110009,-0.00994443,0.02299928,0.02615148,-0.07537937,-0.09700886,0.06042316,-0.00616381,0.16014729,0.04361953,0.03001361,-0.05620867,0.05643818,0.00943555,-0.00707502,-0.09752864,0.03684192,0.05931258,0.04620213,-0.05636519,0.03946065,-0.02523857,0.0125192,-0.01478918,0.02264084,-0.0426963,0.03362681,-0.03732885,-0.0851183,-0.02130906,-0.00725217,0.04339155,0.00221049,0.01461153,0.0564016,0.11021301,-0.02676088,-0.01163097,-0.04554955,-0.02768379,-0.01130332,0.02780762,-0.01992248,-0.04090918,-0.01662521,-0.08976663,0.02768249,0.06721565,0.00384313,-0.01667983,0.05460923,-0.00716538,-0.04781408,0.12809971,0.03115646,-0.01118877,-0.02818268,0.02969526,-0.06631339,0.11513922,-0.03123526,0.02526117,0.06808722,0.02792705,0.00701112,-0.00141811,0.00392906,-0.0057798,0.01231394,-0.02081708,0.03119895,-0.04744172,-0.11486841,0.00530888,-0.05237572,0.03779365,0.05928455,-0.04620899,-0.20670658,-0.01329166,0.03021169,-0.00623835,-0.04255909,-0.01865809,-0.00159514,-0.08308195,-0.0422304,0.02245433,-0.03712998,0.05018788,0.03686829,0.01002065,-0.00085596,-0.00727971,0.03548859,0.05991609,0.10007916,-0.06440619,0.01640631,0.01867081,0.25425825,0.0121595,0.02051531,0.04831515,-0.03786685,-0.00957292,0.03410306,0.0666824,0.03107483,0.03672959,0.08228829,-0.00965028,-0.01384908,-0.05350195,-0.01131608,0.02408545,0.03269751,-0.03183663,0.00303981,0.04139607,0.00713697,0.03386692,0.04008783,-0.14836255,-0.02558048,0.00398927,-0.01548997,-0.02074961,0.01205036,0.04497011,0.01525725,-0.01379321,-0.02119199,0.00558292,-0.078724,-0.00421539,-0.03060601,0.02608734,0.03281395,-0.00941675,-0.0268273,0.0405302,0.02404926],"last_embed":{"hash":"1l4g1cb","tokens":165}}},"text":null,"length":0,"last_read":{"hash":"1l4g1cb","at":1751288824493},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Global styles#{1}","lines":[176,195],"size":482,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1l4g1cb","at":1751288824493}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#One-off global selectors": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.00275581,-0.01032824,0.05046294,-0.03087138,-0.01174705,0.025234,-0.02277776,0.01633347,0.00053459,-0.01581782,-0.00124406,0.05998594,0.03217753,0.02873397,-0.0142502,0.00002797,-0.00998316,0.04385075,-0.06203004,-0.01210988,0.03204281,-0.01378104,-0.02307978,-0.05327809,0.01961233,0.02324808,-0.00139133,-0.04055629,-0.01188937,-0.19953248,-0.03235976,-0.01990706,-0.0328409,-0.02927019,-0.01701794,-0.04020149,-0.00850614,0.02510221,-0.06668837,0.00501493,-0.00562638,0.05531223,-0.03624711,-0.04199559,-0.00952572,-0.02421984,-0.00705345,-0.00770551,-0.03545645,-0.00576564,-0.00991492,0.01370817,0.03017262,-0.04155516,0.0101563,0.12280044,0.072332,0.07792947,-0.01582985,0.01395038,-0.03812183,0.02424379,-0.13920131,0.09036575,0.03783553,0.03874033,-0.03628323,-0.019172,0.04341868,0.03776047,0.01596209,0.00690282,0.01441612,0.116478,0.04208379,-0.04442858,-0.00598367,-0.01424128,0.04601781,-0.01079709,-0.05009348,-0.04931464,-0.03439517,-0.04628202,0.03572961,0.01703283,-0.0053604,-0.02929606,0.02709494,0.01168783,-0.02621615,-0.11337771,0.03633881,0.03190089,-0.01395626,-0.00753903,0.05252085,0.02701775,-0.01741536,0.14244516,-0.06494736,0.03459585,0.01729747,0.04212417,0.02844401,-0.02908032,-0.04209473,-0.02405341,-0.0346404,-0.04108222,0.04955254,-0.04129577,-0.05767803,-0.06655382,-0.07458182,0.00397434,-0.02497332,0.00256297,0.04389386,0.00492621,0.01896594,0.01638355,0.04758602,-0.03116355,0.01238114,0.05147847,0.04376168,0.0342082,-0.01013925,0.07038268,0.05546531,0.02373474,-0.01935893,-0.05594416,0.00049077,0.04328124,0.03622657,0.02432497,-0.01501818,0.02925788,0.04463218,-0.02352531,0.0036003,-0.05783832,0.03468317,0.02428884,-0.04505897,0.07839756,-0.00427125,-0.01027487,-0.10404381,0.02929917,-0.07222822,0.02727121,-0.04709747,0.06585474,-0.01406727,-0.01697988,-0.05107125,0.01237855,-0.07741614,0.02800671,0.01947681,0.06392977,0.02464827,-0.09153003,-0.0543685,0.07170137,-0.00038072,-0.09992818,-0.02392169,0.05850591,-0.05068531,0.00945382,0.08734029,-0.00125113,-0.06906336,-0.00038022,0.10228316,-0.01813559,0.09789494,-0.02783831,-0.01397709,0.01923964,-0.00892483,-0.09473242,0.02668027,-0.05721035,-0.01753155,-0.06384502,-0.00692845,-0.00538641,-0.0082524,-0.01704751,0.00123658,0.00302209,-0.05903548,-0.0849444,0.0691878,-0.01607031,0.14894906,-0.00379494,0.0128809,0.00842792,-0.04601472,0.0273047,0.04768937,-0.00633328,0.03492944,-0.04759345,-0.11409177,0.01544888,0.08734968,0.07815157,0.00434772,-0.04173094,-0.00037614,0.04862187,0.01379076,0.00487558,0.02530157,-0.00158387,-0.08150831,-0.19743979,0.0647402,0.0119016,-0.01891391,-0.03214197,-0.06953732,0.05277991,-0.02810275,-0.04089362,0.07965454,0.12329279,0.02414296,0.00090581,0.03654789,-0.02657665,-0.00323686,-0.00947414,-0.05519176,-0.04409882,-0.02981269,0.01012822,0.00969913,-0.04097278,-0.11700903,0.08592404,-0.01734072,0.16647911,0.0692886,-0.0015329,-0.03299091,0.05345041,0.00914261,0.02285912,-0.03789897,0.01524141,0.06868258,0.01634399,-0.08530646,0.01596672,-0.01900479,0.02734186,-0.01712879,0.0057497,-0.04611646,0.05185597,-0.05694332,-0.07342926,-0.01912012,0.01785609,0.07261778,0.03167103,-0.01220122,0.05680117,0.10523566,-0.0329714,-0.0035999,-0.00762091,-0.01861314,-0.0330287,0.02784326,-0.00619883,-0.00887395,-0.01411035,-0.08634244,0.01398237,0.05599127,0.00353677,-0.02540827,0.05619692,-0.01783367,-0.0421741,0.11851829,0.02273147,0.01664017,-0.06653472,0.0177675,-0.04040016,0.10903651,-0.00290798,0.0204425,0.05624625,0.03476785,-0.00509022,-0.00758515,0.01334602,-0.02501652,0.03351619,0.00191012,0.03951522,-0.02808971,-0.09972849,0.04905264,-0.05094886,0.05196825,0.0379471,-0.05597301,-0.19066383,0.00089986,0.02122166,-0.00346442,-0.02060133,-0.00463388,0.01798648,-0.09481312,-0.02416693,0.01074387,0.00624157,0.00635653,0.01825152,0.02106979,-0.01813262,0.02308613,0.05987198,0.03637041,0.08358131,-0.05840272,0.03933322,-0.01299534,0.23868145,-0.00111949,0.05336745,0.02133107,-0.04553162,-0.05017732,0.02166012,0.06424346,0.00832172,0.02737478,0.09061572,-0.0518541,-0.05451072,-0.02125556,-0.0260262,0.03294074,0.02688663,-0.00995155,-0.01982214,0.02356034,0.01273908,-0.00200145,0.04909649,-0.14568222,-0.06494217,-0.02268385,-0.05377696,0.01911814,0.01689107,0.04220233,-0.01066703,-0.06060895,-0.02887776,0.00948217,-0.08922806,-0.01046773,-0.03855324,0.03876328,0.01577057,0.00422251,0.03392974,0.0571069,0.03755888],"last_embed":{"hash":"18feybj","tokens":249}}},"text":null,"length":0,"last_read":{"hash":"18feybj","at":1751288824542},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#One-off global selectors","lines":[196,220],"size":724,"outlinks":[{"title":"css-modules","target":"https://github.com/css-modules/css-modules","line":3}],"class_name":"SmartBlock","last_embed":{"hash":"18feybj","at":1751288824542}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#One-off global selectors#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.00636887,-0.00732337,0.05040384,-0.02863434,-0.01006219,0.02868857,-0.02005846,0.01671993,0.0034272,-0.01378671,-0.00074676,0.06054636,0.03150818,0.02909347,-0.01384624,-0.00125496,-0.00969499,0.04676073,-0.06176539,-0.01580874,0.03526384,-0.01330783,-0.01981168,-0.05250065,0.0228572,0.0210357,-0.00481845,-0.04323331,-0.01357384,-0.19847368,-0.03517346,-0.02078503,-0.03239004,-0.03119681,-0.01925332,-0.04030097,-0.00910182,0.02766137,-0.06462286,0.00614554,-0.00789837,0.05599777,-0.0335657,-0.04338472,-0.00985874,-0.02461221,-0.00866367,-0.00868801,-0.0366211,-0.00368148,-0.00898623,0.01314453,0.02948632,-0.04058926,0.01115107,0.12077411,0.06963009,0.07521239,-0.01485989,0.01699295,-0.03646632,0.02151247,-0.13923292,0.08729935,0.03874294,0.04028249,-0.03510339,-0.01691505,0.04228118,0.0420663,0.0137913,0.00517331,0.01286275,0.11883633,0.04577409,-0.04486801,-0.00598078,-0.01385112,0.04433446,-0.01032452,-0.04636639,-0.0499548,-0.03788801,-0.04831823,0.03249583,0.01311849,-0.00724331,-0.02963155,0.02567315,0.01100866,-0.02403365,-0.11503407,0.04089154,0.03110319,-0.01569419,-0.01063233,0.05165323,0.02565738,-0.01789681,0.14328305,-0.06284218,0.03276451,0.01908353,0.04696659,0.02939995,-0.02959375,-0.04242944,-0.02530095,-0.03301821,-0.04334965,0.04762754,-0.03825881,-0.05660359,-0.06927607,-0.07281259,0.00519371,-0.0231464,0.00840567,0.04638977,0.00730869,0.01902322,0.01412917,0.05036978,-0.0325194,0.01068569,0.04960502,0.04420352,0.03500252,-0.00910004,0.06680477,0.0540195,0.02091875,-0.01609997,-0.056106,0.00526814,0.04265749,0.03556864,0.02495366,-0.01280015,0.02583717,0.04520597,-0.02323836,0.00424508,-0.05639805,0.03287587,0.02548662,-0.04460609,0.0782752,-0.00428347,-0.01065688,-0.10641026,0.0270182,-0.06933799,0.02744922,-0.04911597,0.06641976,-0.01728675,-0.0162269,-0.04999117,0.01299717,-0.07800779,0.02724756,0.01783037,0.06724496,0.02379728,-0.09218603,-0.05131916,0.06805772,-0.00345389,-0.09833475,-0.02481783,0.05610014,-0.05358591,0.00609621,0.08927087,-0.00074575,-0.06922295,0.00002171,0.10079836,-0.0164673,0.09598687,-0.02659797,-0.01515157,0.01789975,-0.00917542,-0.09733953,0.0258916,-0.05676842,-0.01917103,-0.06391153,-0.01105679,-0.00822016,-0.00813583,-0.01634177,0.00011261,0.00229751,-0.05651354,-0.08400588,0.07058257,-0.01696374,0.15179633,-0.00341162,0.01639011,0.00700434,-0.04470002,0.02833112,0.05030373,-0.00357316,0.03458926,-0.04540408,-0.11200991,0.01323718,0.08652736,0.07487231,0.00731265,-0.04291436,0.00072752,0.04625241,0.01165792,0.00467941,0.02557738,-0.00281396,-0.08072791,-0.19969065,0.06417055,0.00870927,-0.01648772,-0.03077381,-0.07344663,0.05044024,-0.02855255,-0.0413003,0.07877359,0.12324108,0.02471646,-0.00254315,0.03799319,-0.02794017,-0.00072719,-0.0061998,-0.05570722,-0.04275066,-0.02658523,0.01013243,0.01509246,-0.04154927,-0.11698441,0.0840889,-0.01737509,0.16718927,0.06708328,-0.00180365,-0.03508651,0.05192349,0.00979391,0.02805315,-0.03620727,0.0126642,0.06742176,0.01603659,-0.08600008,0.01821111,-0.01884691,0.02845159,-0.01573604,0.00748762,-0.04620358,0.05699128,-0.05761796,-0.07023001,-0.02150182,0.02149707,0.07339325,0.02900909,-0.01686175,0.05731423,0.10640371,-0.02946007,-0.0049897,-0.01345534,-0.0158366,-0.03247422,0.02762364,-0.00848698,-0.01172878,-0.01498072,-0.08604334,0.01002385,0.05365138,0.00234226,-0.02503963,0.05672208,-0.01782597,-0.0407203,0.11930063,0.02239973,0.01177427,-0.07146329,0.01719511,-0.03737409,0.10889183,-0.00502844,0.02049389,0.05615306,0.03333523,-0.00828533,-0.00524585,0.01396032,-0.02881699,0.03804049,0.00053946,0.04075148,-0.02570393,-0.0997477,0.04996952,-0.04834798,0.05347449,0.03736318,-0.05766278,-0.19139452,0.00185333,0.0242082,-0.00390575,-0.0153804,-0.00142009,0.0159887,-0.09282126,-0.02535861,0.00745077,0.00568978,0.00909673,0.02308124,0.02371558,-0.02002174,0.02178778,0.06292976,0.04034503,0.08663776,-0.05941977,0.04282718,-0.01461223,0.23864132,-0.00073895,0.05315788,0.02004929,-0.04877822,-0.04933215,0.01783919,0.0653249,0.00696399,0.02540365,0.0899033,-0.05362127,-0.05312964,-0.01959175,-0.02672195,0.03380508,0.02601708,-0.0134903,-0.02216455,0.01835825,0.00942798,0.0001533,0.05099982,-0.14335795,-0.06458256,-0.02166623,-0.05564642,0.01913771,0.01777879,0.04092976,-0.01670601,-0.05691939,-0.02995385,0.0097744,-0.08757637,-0.01213395,-0.03686387,0.03620631,0.02111439,0.00553537,0.03363281,0.05631366,0.03812308],"last_embed":{"hash":"1fploil","tokens":247}}},"text":null,"length":0,"last_read":{"hash":"1fploil","at":1751288824613},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#One-off global selectors#{1}","lines":[198,220],"size":694,"outlinks":[{"title":"css-modules","target":"https://github.com/css-modules/css-modules","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1fploil","at":1751288824613}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00784898,-0.00544283,0.02712578,-0.05391575,-0.04192968,0.0557506,-0.05868459,-0.03407243,0.00382238,-0.0352,-0.02005199,-0.01416544,0.04442533,0.05484751,0.01097233,-0.01209032,0.0143037,0.07512224,-0.03810567,0.00080011,0.06007975,0.01902164,0.01383324,-0.05859753,0.00734102,0.0312708,-0.02454435,0.00581503,0.05224364,-0.2048081,0.02006388,-0.04256188,-0.01962976,0.00286669,-0.02165497,-0.05547527,-0.01819346,0.0701942,-0.02876012,0.0671302,-0.00268461,0.08496258,-0.02668839,-0.06296219,-0.03168575,-0.02592174,0.00358995,-0.00125141,0.00919787,0.02597739,-0.02544414,-0.0410118,0.07107811,0.0027175,-0.00888644,0.13050599,0.0573192,0.07624821,0.03954675,0.02887457,-0.01313175,0.05023112,-0.11104664,0.09705615,0.03595014,0.0445782,-0.0473442,-0.01436321,0.05349992,0.01985231,-0.01574871,0.05792503,0.01521823,0.10536654,0.02528179,-0.04612758,-0.04110486,-0.05509656,0.02903274,-0.0458369,-0.08569704,-0.08630729,-0.01499848,0.0017891,0.04670902,0.02473445,0.03472087,-0.05575658,0.04604212,0.06325159,-0.0523062,-0.106198,0.03475636,-0.01522504,-0.02615731,0.00057505,0.0589406,0.07808572,-0.01561559,0.12644565,-0.08683182,0.02118549,0.05339442,-0.0169003,0.01528483,0.00878964,0.01236023,-0.02951816,-0.06584022,0.02005787,0.02033586,-0.02751726,-0.08723386,-0.04563092,-0.06599849,-0.00713556,-0.05057846,-0.00261903,0.03365222,-0.07865312,0.05482282,0.04437243,-0.00987876,-0.01068893,0.0374224,0.02278297,0.02948046,0.04842333,0.0025604,0.09490624,-0.00423158,0.06206676,-0.04554883,-0.05454931,-0.00765052,0.02006895,0.03335872,0.01824355,-0.04135221,0.02467725,0.02001009,0.01264174,-0.00206485,-0.05188901,0.04753153,0.03269586,-0.05150329,0.07705789,0.00135781,0.03699363,-0.04914274,0.06166735,-0.09308523,0.00923891,-0.05145568,0.03538458,-0.00122119,0.02159056,-0.05069389,0.02255931,-0.01213961,0.01566909,0.00636207,-0.00317155,-0.00077821,-0.07142576,-0.05878073,0.04423705,0.0258779,-0.10295475,-0.07236952,0.00009975,-0.02438319,0.03031112,0.06079069,0.03517136,-0.08783352,-0.01728145,0.02718901,0.04945986,0.09155796,-0.03175063,-0.03073223,0.03363134,-0.04479954,-0.10091206,0.01026763,-0.00575447,-0.03516258,-0.01568984,-0.02368096,-0.01196399,-0.03974594,0.03455218,0.00284892,-0.00562487,-0.04379952,-0.02721463,0.06784215,0.01209627,0.13528638,-0.01831806,-0.01517895,0.06226076,-0.05334451,0.0055211,0.00060345,-0.0173011,0.03810197,-0.04093498,-0.09615655,0.02653267,0.06968552,0.08748489,-0.02579526,-0.04156909,0.01858149,0.06672966,0.05292051,0.03041867,0.0158348,-0.0446817,-0.06144489,-0.21793807,0.01513754,0.02018379,-0.01882906,-0.0218978,-0.03515219,0.00899532,-0.0278487,-0.03007654,0.05795334,0.08309972,0.00387309,-0.01476433,-0.01024183,-0.03676666,-0.04906669,-0.0080231,-0.03324596,-0.05739234,-0.04262998,-0.00068532,-0.00577211,-0.04422623,-0.07594014,0.04384182,-0.02143199,0.14908284,-0.00211509,0.02179645,-0.01764003,0.06266335,-0.0379094,-0.02011008,-0.05533249,0.08198045,0.03896264,0.02311105,-0.04481079,-0.00303124,0.00676394,-0.01590235,-0.07443172,0.00914768,-0.0557312,0.08497226,-0.01588096,-0.04282212,-0.00215789,-0.03482644,0.03662467,0.0486736,0.00582767,0.0213148,0.08789479,-0.05242396,-0.01700241,-0.01199262,-0.04654346,0.02851076,0.02654498,-0.01642714,-0.01818428,-0.0078166,-0.07082853,0.01370016,0.07829551,0.03507441,-0.03956603,0.01659163,-0.01837234,-0.04455747,0.11965546,0.01928755,0.02252203,0.06695919,0.00141154,-0.00314757,0.11065391,0.00223044,0.03668294,0.03641792,0.02951251,0.06193344,0.02543209,0.01565343,-0.0571334,-0.02455447,-0.05679472,-0.0064281,-0.05302245,-0.07550543,0.00137327,-0.04460074,0.04863153,-0.00981148,-0.02562026,-0.2459527,0.00985024,0.05109937,0.00421853,-0.04313616,0.01467365,0.0056394,-0.09465009,-0.02089964,-0.0196605,-0.04694032,0.04482064,0.03100126,-0.00006597,0.0239418,0.00785426,0.05676813,-0.01541066,0.08444601,-0.0711231,0.01001598,0.01004761,0.24853927,-0.02648713,0.04053776,0.04314294,-0.03773949,0.00759394,0.07699078,0.04512915,0.05334201,-0.02364902,0.09128372,-0.00190934,-0.02630758,-0.04559436,-0.00800406,-0.00293388,-0.00158703,-0.00611429,0.0204012,0.01994856,0.01564323,0.00357651,0.04358797,-0.091729,-0.01562777,-0.0182176,-0.03561289,0.02781727,0.00092788,0.08902558,0.01944239,-0.03940321,0.02968603,-0.00598695,-0.06102774,0.02316174,-0.02111351,-0.0012777,-0.0117857,-0.01264595,-0.00278395,0.01070251,0.00218856],"last_embed":{"hash":"148q51l","tokens":449}}},"text":null,"length":0,"last_read":{"hash":"148q51l","at":1751288824744},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles","lines":[221,317],"size":2346,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"148q51l","at":1751288824744}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles#Via interpolated dynamic props": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01704214,-0.00178094,0.03415096,-0.05943869,-0.03380674,0.05992573,-0.04714385,-0.02626688,0.00644284,-0.03875315,-0.01396607,-0.01230202,0.04116461,0.05594343,0.00360975,-0.01671739,0.02498089,0.06948874,-0.03766473,-0.00456724,0.055446,0.02458806,0.02107139,-0.05563291,-0.00212206,0.03092966,-0.02276816,-0.00372401,0.0428061,-0.20527083,0.01845871,-0.05319654,-0.0136921,0.00849983,-0.01634754,-0.04333584,-0.01611674,0.0653412,-0.0202445,0.06946602,0.0087938,0.09199253,-0.02717604,-0.05998854,-0.02836667,-0.02067051,0.00232242,-0.00322681,0.00527959,0.03019475,-0.02943647,-0.04468813,0.0857491,0.00659657,-0.01389999,0.12289364,0.05782662,0.08536385,0.04095022,0.0484328,-0.00632287,0.0484464,-0.12287741,0.08667243,0.05776063,0.04366666,-0.06177399,-0.01309876,0.04660173,0.01622058,-0.00800385,0.06553289,0.02184402,0.1058704,0.01863346,-0.04801504,-0.04442833,-0.0548836,0.02765865,-0.04304701,-0.08202126,-0.10203899,-0.02352869,-0.00669824,0.0508627,0.02412798,0.03329755,-0.05228415,0.04611114,0.07264144,-0.03947224,-0.08992455,0.03363098,-0.01646523,-0.02536799,0.00311009,0.0577332,0.08139747,-0.00983187,0.13007781,-0.08099846,0.03357047,0.0389861,-0.0128342,0.00547767,0.00207055,0.01612708,-0.02857934,-0.06501926,0.00980035,0.01711695,-0.04030698,-0.06201576,-0.04029034,-0.06579805,-0.00943915,-0.04194092,0.00344765,0.04161528,-0.07467715,0.05333624,0.03378864,-0.0140358,-0.02047496,0.03653876,0.0205866,0.03536831,0.04638389,0.00398854,0.09251895,-0.00750911,0.05950881,-0.03753311,-0.05917135,-0.01889721,0.01069401,0.02771131,0.01830101,-0.03274797,0.03789953,0.01798064,-0.00268992,0.01153152,-0.05765446,0.05153356,0.03446035,-0.03813917,0.07884763,-0.00332709,0.03902828,-0.04816349,0.06091094,-0.09664012,0.01359741,-0.06290693,0.04112663,-0.00483096,0.03260276,-0.04195824,0.02998396,-0.02080955,0.00149174,-0.01867122,0.00274146,-0.0096883,-0.06456185,-0.06150549,0.03153385,0.02409425,-0.09239552,-0.06966408,-0.01391073,-0.01706692,0.02171125,0.05640238,0.02437135,-0.07293256,-0.01830502,0.024063,0.05753945,0.0979167,-0.03143251,-0.03173396,0.05175295,-0.04403716,-0.09741078,0.00446601,-0.00745563,-0.0275211,-0.00490753,-0.03370774,-0.03053489,-0.03770973,0.04510252,-0.00624825,-0.00639878,-0.04455638,-0.02024397,0.05475439,0.01289267,0.14123529,-0.02293533,-0.0094058,0.07566534,-0.05012,0.01047133,-0.01467369,-0.01762162,0.02694118,-0.0398009,-0.07686643,0.02739655,0.06842401,0.06903515,-0.02309352,-0.04269365,0.0215186,0.06020311,0.05001983,0.04651215,0.0106024,-0.03165651,-0.05204201,-0.23483571,0.02399001,0.01219096,-0.00697043,-0.01044512,-0.03364036,0.01831238,-0.03271937,-0.02922612,0.05855886,0.07045484,0.01716436,-0.00753955,-0.02304118,-0.02961968,-0.04440216,-0.00777046,-0.03174124,-0.05932887,-0.05183087,0.00420374,-0.01986964,-0.05558047,-0.07853801,0.05838365,-0.0066849,0.13959609,0.0112507,0.01495975,-0.02208278,0.0529822,-0.0474228,-0.03058554,-0.05087995,0.08039673,0.05216179,0.04069222,-0.05675416,-0.00064115,0.00392545,-0.02498825,-0.07740784,0.00602781,-0.05046424,0.08242571,-0.0174326,-0.03936304,-0.01497224,-0.03343612,0.04481351,0.05115354,0.00727941,0.01044579,0.0926664,-0.04399351,-0.02926832,-0.0207836,-0.05633285,0.03768646,0.02596593,-0.01722654,-0.01684827,-0.00569589,-0.06797872,0.01899256,0.08436009,0.04114218,-0.0340573,0.00029368,-0.0037489,-0.04529002,0.12423804,0.02545481,0.02426808,0.05150077,0.00505845,-0.00156656,0.10721782,-0.00149786,0.03680854,0.04044177,0.03528402,0.06450678,0.03874108,0.00135292,-0.06148066,-0.01948223,-0.05809989,-0.01454618,-0.06103465,-0.0641759,0.00310191,-0.0531881,0.04866799,-0.01032942,-0.02487748,-0.24937066,0.02089664,0.03681059,0.0022167,-0.04579988,0.01464111,0.01387669,-0.09425051,-0.01476146,-0.0147939,-0.05035447,0.06076035,0.02079474,0.0042311,0.01211942,0.00486669,0.05980695,-0.02652799,0.06423737,-0.08493967,0.01528245,0.02436941,0.23370121,-0.02396766,0.03699643,0.04216601,-0.0267808,0.01558138,0.08811457,0.0397025,0.04704142,-0.02011725,0.07685218,-0.00425894,-0.02160088,-0.04371435,-0.01247263,-0.00065706,-0.00563614,-0.00853293,0.01026309,0.0284592,0.01442856,0.00636508,0.042599,-0.09350283,-0.017076,-0.02499351,-0.03310606,0.03716122,0.0026467,0.0830601,0.0295519,-0.03821081,0.03813841,0.00597227,-0.07956079,0.02532623,-0.03318391,-0.0072219,-0.01587306,-0.00163521,-0.00852021,0.01259641,-0.01176756],"last_embed":{"hash":"146wosa","tokens":350}}},"text":null,"length":0,"last_read":{"hash":"146wosa","at":1751288824991},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles#Via interpolated dynamic props","lines":[225,270],"size":1146,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"146wosa","at":1751288824991}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles#Via interpolated dynamic props#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01575686,-0.00432075,0.03219283,-0.06015543,-0.03345941,0.05957358,-0.04632234,-0.02263771,0.00893056,-0.03710284,-0.01330333,-0.01448037,0.04503938,0.0566779,0.00182643,-0.01200558,0.0246259,0.06963875,-0.03691008,-0.0037329,0.05893046,0.02274746,0.01889333,-0.05783798,-0.00048195,0.0314399,-0.02163682,-0.00597016,0.04380566,-0.20671894,0.01983397,-0.0549747,-0.01380032,0.00763791,-0.01800626,-0.04480379,-0.01700694,0.06285925,-0.01877836,0.07033077,0.01166419,0.09043015,-0.02839109,-0.06145521,-0.0290092,-0.02372514,0.00170519,-0.00433968,0.00586673,0.02977433,-0.02836571,-0.04288714,0.08681326,0.00906246,-0.01573891,0.12054818,0.05920171,0.0856936,0.04346598,0.04774418,-0.00436645,0.04825304,-0.12216111,0.08798704,0.05931539,0.04424396,-0.06240107,-0.01372359,0.04817289,0.01692665,-0.00792734,0.06378806,0.02330881,0.10654098,0.02032563,-0.04927759,-0.04512569,-0.05280646,0.02600488,-0.04361526,-0.07685806,-0.10341363,-0.02637031,-0.00736381,0.05229997,0.02137079,0.03392116,-0.05653263,0.04491636,0.07526428,-0.0390841,-0.08989751,0.02764866,-0.01337056,-0.02571851,0.00373369,0.05847147,0.0806204,-0.00860144,0.12921445,-0.07805739,0.03139858,0.0373611,-0.01382648,0.00086325,0.00357114,0.01752836,-0.02675295,-0.06432797,0.00879278,0.01665178,-0.03774843,-0.06182756,-0.03853687,-0.06623703,-0.00745008,-0.04155669,0.00702324,0.04465424,-0.07590799,0.05278296,0.03379735,-0.01599319,-0.01763291,0.03506203,0.02035447,0.03312144,0.04738994,0.00745484,0.08916893,-0.00762675,0.05991404,-0.03548058,-0.05941711,-0.02036652,0.01048915,0.03060505,0.01908005,-0.02806894,0.03939715,0.01759258,-0.00382307,0.01173905,-0.05644816,0.05331735,0.03096738,-0.0346015,0.07943092,-0.00298239,0.04101077,-0.04927106,0.06411099,-0.09987529,0.01196293,-0.06259225,0.04262095,-0.007512,0.03191286,-0.03909819,0.0284304,-0.02022559,0.00515649,-0.02063042,0.00590509,-0.01224767,-0.06469649,-0.06241062,0.03298358,0.02478744,-0.09215294,-0.07184511,-0.01353513,-0.01881625,0.0179525,0.05804441,0.02430523,-0.07542236,-0.01544246,0.02376605,0.05848941,0.10005521,-0.02901139,-0.03049568,0.0514919,-0.04209321,-0.09810202,0.00397068,-0.00510505,-0.03011815,-0.00308224,-0.03658313,-0.02794446,-0.04426935,0.04442234,-0.00677948,-0.00310597,-0.04570321,-0.02084797,0.05885718,0.01222014,0.13847141,-0.02240003,-0.00826001,0.07268711,-0.05330446,0.01064073,-0.01316703,-0.01675477,0.02445532,-0.03922668,-0.07784275,0.02914302,0.07253824,0.0687329,-0.02221693,-0.04417027,0.02374721,0.0584544,0.04646531,0.04909158,0.01195153,-0.03339515,-0.05022884,-0.23570691,0.02429649,0.01136255,-0.00588333,-0.01253093,-0.03887233,0.02147002,-0.03040823,-0.03142074,0.05804396,0.06743767,0.01637839,-0.00908856,-0.02029633,-0.02968081,-0.04863762,-0.00688375,-0.03246885,-0.05652623,-0.05145552,-0.00192056,-0.01935196,-0.05436134,-0.07536153,0.06043639,-0.00799044,0.1406365,0.01064657,0.01354086,-0.02008582,0.05187518,-0.04487337,-0.03315262,-0.0491244,0.07858837,0.04899143,0.04032087,-0.05795302,0.00294971,0.00297559,-0.02718313,-0.07863049,0.00322772,-0.04874049,0.08684026,-0.01780002,-0.03912212,-0.01409594,-0.03138114,0.04817308,0.05290524,0.0035786,0.01046585,0.09357893,-0.04405282,-0.02799459,-0.02318339,-0.0577716,0.03805523,0.02377318,-0.0165842,-0.01645521,-0.00436959,-0.07021872,0.01595482,0.08430789,0.04186055,-0.03348505,0.00013589,-0.00130152,-0.04421151,0.12613618,0.02377685,0.02065534,0.05296962,0.00818973,0.00015336,0.10643288,-0.00072823,0.03845214,0.03868891,0.03664028,0.06432492,0.0365885,0.00344245,-0.06102378,-0.01793937,-0.05843664,-0.0147957,-0.06139337,-0.06268555,0.00538741,-0.05102193,0.04891942,-0.01280979,-0.02683624,-0.25191528,0.02115426,0.03767956,-0.00052466,-0.04664259,0.01389678,0.01318115,-0.09156426,-0.01456522,-0.01606934,-0.0536603,0.06160181,0.02093394,0.00773754,0.01244357,0.00483971,0.06187911,-0.02930668,0.06362587,-0.08649129,0.01267613,0.02056629,0.23226812,-0.02249036,0.03767513,0.03984399,-0.02692193,0.0152472,0.0842066,0.03894566,0.0428778,-0.02196907,0.07254665,-0.00479994,-0.01939197,-0.03887199,-0.01275896,0.00001582,-0.00608801,-0.00718613,0.0076673,0.02663597,0.01373397,0.00557791,0.03994243,-0.08954476,-0.01477729,-0.02272586,-0.03233788,0.03715433,0.00281859,0.08238075,0.03169873,-0.04303687,0.03586301,0.00532177,-0.07463964,0.02639887,-0.03470224,-0.0046427,-0.01651877,-0.00004437,-0.00904016,0.01239036,-0.01254388],"last_embed":{"hash":"ptoab3","tokens":347}}},"text":null,"length":0,"last_read":{"hash":"ptoab3","at":1751288825180},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles#Via interpolated dynamic props#{1}","lines":[227,270],"size":1109,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"ptoab3","at":1751288825180}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles#Via `className` toggling": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01230944,-0.00841972,0.03748906,-0.04224149,-0.06132162,0.05426246,-0.03074669,-0.03716901,0.00936641,-0.02427194,-0.04507837,-0.00150662,0.03646836,0.01939092,0.04286556,0.01590926,0.025512,0.08814953,-0.04118166,0.01807343,0.08166696,0.03277852,0.0334154,-0.05074839,0.00194901,0.05468475,-0.00018265,0.01127992,0.03890294,-0.19069903,0.00197652,-0.04606234,-0.00640797,-0.00361234,0.01499988,-0.04877244,-0.02221984,0.03642608,-0.03613138,0.05384854,0.00678267,0.07754503,-0.01542041,-0.06198944,-0.00982859,-0.01783729,0.02315917,-0.00793035,0.01246176,0.01045672,-0.03573267,-0.0371211,0.06486533,-0.01558376,0.02324503,0.12935162,0.01280146,0.0691719,0.03694211,0.01284359,-0.01776736,0.06222833,-0.13011582,0.11833844,0.00644657,0.02860635,-0.04092218,-0.01037989,0.04311978,0.04435564,0.01749482,0.07095573,0.0207001,0.12257707,0.01632528,-0.05831015,-0.0299795,-0.0587929,0.02433652,-0.06409844,-0.08233305,-0.08688232,-0.01888255,-0.03543059,0.01982571,0.02171223,0.03430261,-0.04858251,0.01950881,0.03975718,-0.04943495,-0.11205234,0.04409036,0.01030173,-0.01960375,0.02191944,0.01786973,0.07668181,-0.04995114,0.128611,-0.09110993,0.02213431,0.01161046,-0.01374678,0.03910079,-0.00090421,0.02592839,-0.0368445,-0.05301568,0.01111939,0.00313991,-0.03591,-0.09127621,-0.04276789,-0.08308516,0.01318802,-0.03001994,-0.00293376,0.02931647,-0.04968241,0.0705289,0.04154604,-0.0038955,-0.01019274,0.02089701,0.01685119,0.03497323,0.04246266,0.00065718,0.08898009,-0.01538198,0.06114634,-0.06537288,-0.07442326,0.01004739,0.03921813,0.02038487,0.04305404,-0.05310474,-0.02201631,0.00928972,-0.00945303,0.02188436,-0.05854033,0.02424235,0.02175014,-0.07516862,0.0782124,0.00320808,0.01817882,-0.05731686,0.06422809,-0.06334105,-0.00746418,-0.04639187,0.03661649,0.02529134,0.03370912,-0.03765409,-0.00769309,0.02610968,0.03710657,0.02006649,-0.02117871,0.02121991,-0.09050282,-0.03226337,0.03945924,0.03124239,-0.10928052,-0.06546936,0.02227623,-0.03505051,0.03084603,0.07928888,0.04591656,-0.04888993,-0.02916891,0.04000301,0.03261045,0.07316619,-0.04811832,-0.01811034,-0.0108714,-0.02194831,-0.0776687,0.02496562,-0.01290607,-0.03482842,-0.01668112,-0.01396611,0.00371802,-0.01474717,0.01335145,-0.00696866,-0.03153654,-0.04934819,-0.01912379,0.06129107,0.00079969,0.12451568,-0.00154087,-0.01691197,0.06165966,-0.04780733,0.04271339,0.02346051,-0.01828389,0.04766189,-0.05796497,-0.09321806,0.00675515,0.06665483,0.09060156,-0.01790789,-0.02793356,0.01061864,0.09123623,0.02002534,0.01738265,0.03224514,-0.02348635,-0.07816542,-0.22168636,0.02174639,0.01986496,-0.06287038,-0.04204054,-0.01807562,0.03827924,-0.00948069,-0.01750739,0.03084226,0.09228443,0.01468056,-0.02164864,0.00187848,-0.04610172,-0.00455078,0.00663974,-0.03742789,-0.03972992,-0.03811442,0.02564496,-0.0052511,-0.04048427,-0.09317718,0.02046187,-0.03531763,0.14343689,0.00408217,0.0382404,-0.01681655,0.07234212,-0.0127868,-0.00118818,-0.0379032,0.08622082,0.02450163,0.01259242,-0.01054628,-0.00783106,-0.00716419,-0.01409331,-0.055526,0.00559951,-0.05329471,0.06452955,-0.02999072,-0.03639581,-0.02825308,-0.01705489,0.02168993,0.04359438,0.01167171,0.03408068,0.09514967,-0.05583499,-0.03088946,0.01010047,-0.06451065,0.00259204,-0.01505532,-0.01955963,0.00327361,-0.01451542,-0.0876324,0.00712906,0.07100665,0.01205468,-0.02759258,0.01982302,-0.05295129,-0.03727478,0.10598976,0.02102966,0.03512129,0.04149143,-0.00487682,-0.01986879,0.10700459,0.01415063,0.03667669,0.03106102,0.01834043,0.05839104,-0.00707461,0.04794577,-0.04355127,-0.00832817,-0.05242206,0.02317118,-0.0392903,-0.08045537,0.02352771,-0.0349101,0.0722858,-0.01732042,-0.02833718,-0.23560484,0.0095296,0.04236188,0.05751752,-0.05834493,0.02584867,0.00403729,-0.09212325,-0.03761164,-0.01437703,-0.01989199,0.04536722,0.02148242,-0.0197438,0.00056571,0.01756002,0.05592116,-0.00500149,0.09191786,-0.05707925,0.03608539,-0.01779459,0.24555261,-0.04394791,0.06557827,0.03434822,-0.07669054,0.02410089,0.07640551,0.03360265,0.05554217,-0.00850491,0.09367446,-0.03886357,-0.0609882,-0.04669661,0.00363592,-0.01308865,0.03182238,-0.00438331,0.03781109,0.01565166,-0.04462257,0.01636517,0.05900462,-0.10352034,-0.02577538,-0.03328369,-0.02501411,0.04134114,0.00580169,0.06746001,-0.00736057,0.00436196,0.00359956,0.00250267,-0.07611173,-0.00320317,-0.03147916,-0.01250495,-0.01306465,-0.04534675,0.00106527,0.00721308,0.01143056],"last_embed":{"hash":"u8lwey","tokens":179}}},"text":null,"length":0,"last_read":{"hash":"u8lwey","at":1751288825290},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles#Via `className` toggling","lines":[271,294],"size":492,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"u8lwey","at":1751288825290}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles#Via `className` toggling#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00821275,-0.00857853,0.0387091,-0.0433943,-0.06040007,0.05508202,-0.02757634,-0.03625702,0.00953076,-0.02275332,-0.04688574,0.00002921,0.038051,0.01789159,0.0474486,0.01927941,0.02368927,0.09210614,-0.03824942,0.01543627,0.08300807,0.03433543,0.03199279,-0.04819416,0.00352323,0.05499905,0.00291288,0.01417273,0.03401201,-0.18923265,-0.00078594,-0.04686261,-0.00615303,-0.0027418,0.01322129,-0.05150405,-0.02269691,0.03804609,-0.03736191,0.05493255,0.00792231,0.07568655,-0.01553163,-0.06044317,-0.00975385,-0.02045389,0.02575785,-0.00853209,0.01310803,0.01074033,-0.03354809,-0.03548219,0.06458606,-0.01362394,0.02099538,0.12875649,0.01012493,0.06985088,0.03615981,0.00986599,-0.02131088,0.06111209,-0.12723847,0.11934876,0.00227007,0.02896918,-0.03783064,-0.01161008,0.04432569,0.04543464,0.01709354,0.0681176,0.02227378,0.12439983,0.01617019,-0.05784294,-0.02810575,-0.05881289,0.02428878,-0.07208493,-0.08146944,-0.0882296,-0.01754505,-0.03984319,0.02191225,0.02086057,0.03622253,-0.05213124,0.02092488,0.03848214,-0.04896601,-0.11515152,0.04260242,0.0136874,-0.019885,0.02346085,0.01621641,0.07586032,-0.04902864,0.12778641,-0.09433924,0.02056305,0.0116095,-0.01376849,0.03927972,-0.0049891,0.02071957,-0.0361864,-0.05747844,0.01205196,0.0025519,-0.03364459,-0.08941296,-0.0421305,-0.0844228,0.01454133,-0.03003833,-0.00458708,0.02678106,-0.04892609,0.0707843,0.03756133,-0.00101633,-0.0103081,0.02130461,0.01641595,0.03698381,0.04436122,0.00153772,0.08473808,-0.01518548,0.05739649,-0.06474189,-0.07578289,0.0116492,0.03679197,0.02331984,0.04290784,-0.05346066,-0.02419467,0.00831959,-0.00830611,0.02091369,-0.05408851,0.01939973,0.02231813,-0.07539514,0.07854956,0.00068795,0.01257666,-0.05425048,0.06268474,-0.06242149,-0.00870018,-0.04500039,0.03864954,0.02635315,0.03530713,-0.03512604,-0.00794833,0.02902705,0.03886453,0.0235192,-0.02031346,0.02056529,-0.08895585,-0.032793,0.03842687,0.02855755,-0.10945473,-0.06703888,0.02425442,-0.03720682,0.02598499,0.07968206,0.04964954,-0.04771269,-0.02974674,0.03720326,0.02963714,0.07031864,-0.04668324,-0.01683195,-0.01169783,-0.01761208,-0.07606741,0.02565021,-0.01548252,-0.03459135,-0.01806619,-0.01627749,0.0086067,-0.01483649,0.00943346,-0.00520937,-0.02820472,-0.04966503,-0.01790041,0.06419682,-0.00125478,0.12469505,0.00125219,-0.00901435,0.0595441,-0.04586896,0.04521276,0.02883481,-0.01510525,0.04347106,-0.05693971,-0.09262448,0.00481138,0.06612206,0.08675989,-0.01600743,-0.02717906,0.00965815,0.09029961,0.01429583,0.01509538,0.03217527,-0.02340062,-0.07617325,-0.22242169,0.02172674,0.01959219,-0.06488276,-0.04412053,-0.02075233,0.04307971,-0.00636063,-0.01692815,0.03172959,0.0928975,0.01428502,-0.02584031,0.00410663,-0.04482335,-0.00068835,0.00773377,-0.03923403,-0.04151699,-0.03783996,0.02183937,-0.00316326,-0.03653266,-0.09052884,0.02408502,-0.03702576,0.14334777,0.00729197,0.03824875,-0.01745566,0.0731151,-0.00854282,-0.00160529,-0.03748349,0.08778232,0.02026311,0.01454773,-0.0101643,-0.0070192,-0.01000479,-0.01109292,-0.0547413,0.00617628,-0.05594289,0.06526648,-0.02892623,-0.03352067,-0.02935032,-0.01455812,0.02231414,0.04567051,0.01100433,0.03553328,0.09435773,-0.05830178,-0.03331012,0.01214334,-0.06499912,0.00195685,-0.01883243,-0.01754904,0.00415934,-0.01456489,-0.08772595,0.0046002,0.06921101,0.00714444,-0.03007314,0.02052768,-0.05222248,-0.03713581,0.10772458,0.0199433,0.03538144,0.03986039,-0.0074036,-0.01978694,0.10758496,0.01432385,0.03863104,0.03149036,0.01933504,0.05961917,-0.00487851,0.04976747,-0.04420311,-0.00586854,-0.04898688,0.02190993,-0.03881118,-0.08203769,0.02423722,-0.03528508,0.07482558,-0.0204981,-0.02945241,-0.23526984,0.00641578,0.04321854,0.05810186,-0.06031844,0.02255371,0.00454637,-0.09216001,-0.03931933,-0.01686097,-0.01311771,0.04609384,0.02482152,-0.01803041,-0.00198884,0.01861352,0.05732961,-0.00967356,0.09315202,-0.05671784,0.03694369,-0.01987986,0.24513,-0.04370239,0.06886426,0.03440596,-0.07694293,0.0284523,0.07185816,0.03147995,0.05184383,-0.01140059,0.09492285,-0.04043936,-0.06077986,-0.04461855,0.00663847,-0.01550004,0.0333483,-0.0059329,0.03867975,0.01319424,-0.0526525,0.01916311,0.05615963,-0.10323724,-0.02186715,-0.03476233,-0.02708064,0.04280849,0.00823263,0.06667662,-0.0072486,0.00542512,0.00257331,0.00135982,-0.07440334,-0.00619505,-0.03168059,-0.01346499,-0.01206345,-0.04736027,-0.00066748,0.00884233,0.00790937],"last_embed":{"hash":"60zjq9","tokens":176}}},"text":null,"length":0,"last_read":{"hash":"60zjq9","at":1751288825341},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles#Via `className` toggling#{1}","lines":[273,294],"size":461,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"60zjq9","at":1751288825341}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles#Via inline `style`": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00139081,-0.02282201,0.03970707,-0.03875471,-0.04952325,0.06924288,-0.08236441,-0.03797033,0.00176458,-0.04918503,-0.04711309,0.02050753,0.03681873,0.05354916,0.00059386,-0.004422,0.03251793,0.08211524,-0.08725602,0.02329176,0.09144271,0.0310401,0.03736677,-0.08532332,-0.029122,0.02379487,0.00291288,0.01011192,0.04024173,-0.18904524,0.01907218,-0.02969594,0.01408742,-0.02427698,0.0021735,-0.04518788,-0.03170355,0.03822176,-0.02503221,0.03427158,-0.03866162,0.05898507,-0.03424955,-0.05430729,0.01370926,-0.03578382,0.02956148,0.01498984,0.00421344,0.009092,0.01037506,-0.01335554,0.07881542,-0.0188298,0.0315196,0.11573273,0.02952288,0.04486692,0.04060636,0.00134724,-0.01302259,0.06770998,-0.11942367,0.14468107,0.01418559,0.01623767,-0.02873954,0.00346936,0.03722827,0.06176918,-0.01315559,0.0439607,0.00437942,0.09661378,0.02892951,-0.06213204,0.00203029,-0.03208052,0.0248771,-0.02106227,-0.07040303,-0.07426021,0.00568785,-0.01831876,0.04838122,0.03899151,0.03853559,-0.06906395,-0.01227399,0.05423981,-0.06531356,-0.08258717,0.04357907,-0.00095284,-0.01425214,-0.00676673,0.04053309,0.08145463,-0.06712631,0.13155299,-0.06491363,0.0262257,0.03936711,-0.02228455,0.03707926,-0.00700157,0.01950634,-0.03972227,-0.01700557,0.01226006,-0.00138974,-0.04192396,-0.09044987,-0.05196014,-0.05278742,0.00855188,-0.05211715,0.02056864,0.08583866,-0.04716129,0.05581855,0.04371941,-0.02383499,-0.03236973,0.02434415,0.01399738,0.00663155,0.0528095,0.00309324,0.07857929,0.01061785,0.04602684,-0.04386315,-0.04121803,-0.02166376,0.05297003,0.00397914,0.0171966,-0.05681318,-0.03168313,0.01657955,-0.00009407,0.04548332,-0.05419905,0.04037633,0.01056372,-0.07968548,0.08181873,-0.03837875,0.03971449,-0.07713652,0.02165716,-0.08829851,0.0046945,-0.04210062,0.02368859,0.02987225,0.00579941,-0.04315932,0.01388947,-0.00380623,0.00221853,-0.01713363,0.01471492,0.01779602,-0.09280085,-0.02670132,0.08164508,0.03583365,-0.08951958,-0.06079802,-0.01504583,-0.05509253,0.04710977,0.07048635,0.01589091,-0.06924063,-0.01217156,0.02962138,0.07308265,0.10099313,-0.03691306,-0.01729096,0.06264642,-0.01917543,-0.10200677,0.03428082,0.00521669,0.00693101,-0.02240013,0.01429866,-0.01722571,0.00278792,0.01730919,-0.00867,-0.0272225,-0.05010005,-0.02989921,0.02825163,-0.00593572,0.11280122,-0.03607893,-0.00470756,0.04377895,-0.05524507,0.03623577,-0.00019647,-0.01948295,0.06719913,-0.03224746,-0.11651508,0.04544373,0.09578273,0.06699066,-0.03263376,-0.01232376,0.03436513,0.06300465,0.01446103,0.02319478,0.0433564,0.00944361,-0.07857443,-0.2270049,-0.00310063,0.00788177,-0.06071296,-0.02190847,0.00452943,0.02736797,-0.01728771,-0.02420305,0.0437201,0.07921884,0.00164804,-0.01408354,0.01585644,-0.02091745,0.01879336,0.00419558,-0.04975279,-0.02527077,-0.04391311,0.00008129,0.01329786,-0.0410282,-0.07597744,0.04310257,-0.01048033,0.1335033,0.00478359,0.04195791,-0.03247371,0.03253786,-0.03930226,0.02147031,-0.04900705,0.0604853,0.04349121,0.01089102,-0.06018336,-0.00494285,-0.00797627,-0.00576882,-0.03381005,0.00226669,-0.08528356,0.0324952,-0.0511147,-0.06630547,-0.06512414,-0.02800907,0.04216629,0.0372095,-0.0079947,0.031859,0.09600273,-0.0428015,-0.01024216,-0.0126125,-0.06402234,0.01969609,0.01523451,-0.04578599,-0.03735087,-0.00282506,-0.09060695,0.02256693,0.0880279,0.02354433,-0.03379017,0.03959552,-0.05838465,-0.0206104,0.12055407,0.06795733,0.00980082,0.04182758,0.02948956,-0.00653632,0.11491966,-0.0040134,-0.00385108,0.01646735,0.00600791,0.05003977,0.01810528,-0.02759203,-0.03111978,-0.01434831,-0.04576463,0.02991306,-0.06469789,-0.08990517,-0.00812897,-0.03466995,0.08055171,0.02528623,-0.01145836,-0.23087734,0.00804136,0.05276151,0.01324855,-0.01771148,0.01740535,0.0157181,-0.08354489,-0.04039597,0.02638957,-0.04268046,0.03692841,0.004266,-0.00772637,0.0119075,-0.00685798,0.05546713,-0.00716659,0.08870398,-0.09143391,0.02813136,-0.01342857,0.24092233,-0.0419546,0.02703262,0.0533233,-0.04819368,-0.00522945,0.08171976,0.04073422,0.08096768,0.01803141,0.03958799,-0.00314912,-0.03820756,-0.04193441,-0.01755391,0.00148882,-0.01031584,-0.02807337,0.02513368,-0.00991919,0.00397218,0.01573087,0.02517934,-0.07210784,-0.02493765,0.01590321,-0.00689626,0.02677168,-0.00078032,0.05282968,-0.00692665,-0.00481629,0.03359269,0.05582138,-0.07781059,0.00327825,-0.02114045,-0.01120097,0.00290212,-0.05227041,-0.00612213,0.01317806,0.00561307],"last_embed":{"hash":"ybrc4n","tokens":210}}},"text":null,"length":0,"last_read":{"hash":"ybrc4n","at":1751288825387},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles#Via inline `style`","lines":[295,317],"size":580,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"ybrc4n","at":1751288825387}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles#Via inline `style`#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.00103811,-0.02454725,0.03953663,-0.03739422,-0.04763363,0.06828084,-0.0836705,-0.03628305,0.00137307,-0.04905781,-0.04521773,0.02287646,0.03593015,0.0525507,0.00288054,-0.00135823,0.03274668,0.08369655,-0.08720803,0.02351704,0.09337559,0.03288186,0.03838911,-0.08320828,-0.02828576,0.02274353,0.00221514,0.00853358,0.03832814,-0.18754846,0.01874782,-0.03212169,0.01327543,-0.02275881,0.00287232,-0.04635825,-0.03326757,0.03655776,-0.02575738,0.03147706,-0.03794331,0.0599621,-0.03787854,-0.05251369,0.01250247,-0.03499882,0.02996538,0.01336717,0.00057781,0.00943937,0.01090722,-0.01289442,0.08008211,-0.01852584,0.03076253,0.11640259,0.02851537,0.04266808,0.03947344,-0.00070383,-0.01394027,0.06855315,-0.11789788,0.14416981,0.01258263,0.01776926,-0.02991154,0.00260891,0.03769239,0.063517,-0.01274485,0.03926594,0.00386982,0.09707741,0.03072279,-0.06197333,0.0041979,-0.0318641,0.0254725,-0.02010857,-0.06818721,-0.07441541,0.00695264,-0.01995368,0.04690263,0.03663176,0.03980926,-0.07343183,-0.01421082,0.05492128,-0.06216798,-0.08335318,0.04133533,0.00046046,-0.01223539,-0.00945732,0.04070032,0.08050378,-0.06682254,0.12934265,-0.06470378,0.02444593,0.03808573,-0.01934192,0.03788101,-0.00574177,0.0197807,-0.03964077,-0.01558771,0.00824612,-0.00102497,-0.04119062,-0.09017707,-0.05182089,-0.0503285,0.01087724,-0.05152025,0.02214817,0.08764558,-0.04527763,0.05588683,0.04466936,-0.02517432,-0.03110407,0.02300481,0.01494398,0.00611708,0.05335448,0.00489364,0.07974921,0.01535567,0.04383869,-0.04291212,-0.0385494,-0.0233154,0.0535204,0.00623912,0.01494548,-0.05668604,-0.03398737,0.01911032,0.00166988,0.04554165,-0.05402562,0.04021176,0.01015225,-0.07942902,0.08192666,-0.0416698,0.03622145,-0.07524367,0.018965,-0.08947866,0.00298486,-0.03848714,0.02344905,0.03084874,0.00690897,-0.04142954,0.01532971,-0.0037161,0.00365631,-0.02083319,0.0181018,0.01527799,-0.09216571,-0.02824845,0.08268614,0.03449639,-0.08833659,-0.05904306,-0.01656278,-0.05794329,0.04809776,0.06931128,0.01675317,-0.07023755,-0.01276578,0.02863712,0.0751312,0.10261555,-0.03824504,-0.01753549,0.06340485,-0.02013973,-0.10188896,0.03508638,0.00544334,0.00723515,-0.02164061,0.01498658,-0.01484521,0.00054953,0.01725709,-0.00924868,-0.02685842,-0.04916623,-0.03093745,0.027708,-0.00765645,0.11145477,-0.03525901,-0.00574259,0.04097683,-0.05734261,0.03770956,0.00055776,-0.0216329,0.06787207,-0.03293685,-0.1161828,0.04468935,0.09928077,0.06537001,-0.03216739,-0.01135212,0.03517084,0.06284774,0.01206972,0.02560218,0.04388565,0.01246377,-0.0791645,-0.22527248,-0.00598802,0.00591129,-0.06094796,-0.022063,0.00290873,0.02718426,-0.01611075,-0.02615182,0.04466166,0.0787971,0.00146111,-0.0158529,0.01579972,-0.01932857,0.02112369,0.00222565,-0.05253092,-0.02405484,-0.0409116,-0.00021534,0.01724202,-0.04404465,-0.07449449,0.04460235,-0.00997713,0.1323339,0.00495607,0.03898113,-0.03508002,0.03164779,-0.03770196,0.02277813,-0.05115116,0.06052085,0.04301646,0.01291489,-0.06151909,-0.0016055,-0.01071696,-0.00641988,-0.03317619,0.00197047,-0.08641311,0.0323486,-0.05149621,-0.06529434,-0.06749772,-0.02847584,0.04206632,0.03763938,-0.00957573,0.03088844,0.097685,-0.04023092,-0.01105233,-0.01266411,-0.06181208,0.01897436,0.01253285,-0.04515739,-0.0392429,-0.00145219,-0.0926939,0.02061115,0.08823597,0.0244087,-0.03416735,0.03642437,-0.05636775,-0.02131258,0.12060443,0.0664423,0.00915763,0.03951487,0.02861052,-0.00611623,0.11648663,-0.00340902,-0.0046779,0.01842191,0.00855247,0.04820096,0.01849578,-0.02834851,-0.02929441,-0.01465418,-0.04776331,0.03245453,-0.0634226,-0.09099023,-0.00978151,-0.03530355,0.07859609,0.02580196,-0.01104844,-0.23159717,0.00646366,0.0542504,0.01334774,-0.02041115,0.0167945,0.0180948,-0.08326691,-0.04387617,0.02878527,-0.04470645,0.03674702,0.00353116,-0.00762915,0.0129658,-0.00809772,0.05593012,-0.00869847,0.09094072,-0.09077546,0.03067619,-0.01627754,0.24193129,-0.04185957,0.02913601,0.05193052,-0.04432347,-0.00576676,0.08113503,0.04138955,0.08202307,0.01765124,0.03974114,-0.00335901,-0.03704008,-0.04219671,-0.01748884,0.0012578,-0.00925873,-0.02923239,0.0240233,-0.01063704,0.0004066,0.01490374,0.02200046,-0.07141826,-0.02527577,0.01885475,-0.00494662,0.025668,0.00020662,0.05211406,-0.00656758,-0.00519193,0.0364011,0.05747825,-0.07657916,0.00010844,-0.0198758,-0.00861691,0.00501182,-0.05257152,-0.00921275,0.0154229,0.00604741],"last_embed":{"hash":"172eunz","tokens":207}}},"text":null,"length":0,"last_read":{"hash":"172eunz","at":1751288825456},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Dynamic styles#Via inline `style`#{1}","lines":[297,317],"size":555,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"172eunz","at":1751288825456}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Constants": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02104816,-0.01478958,0.01815241,-0.0296365,-0.00791432,0.02479049,-0.04108549,-0.04262514,-0.00261561,-0.02412523,-0.02582225,-0.0099704,0.04352363,0.04111629,-0.00228538,0.03156021,0.00698527,0.06654169,-0.07677934,0.00444713,0.09852146,-0.00139595,0.01323022,-0.0693284,-0.02303318,0.03980101,-0.01298035,-0.03408102,0.05922311,-0.19393888,-0.02346347,-0.04803791,-0.00982564,0.00697254,-0.0077075,-0.04152177,-0.01800235,0.0147345,-0.03451608,0.04515641,-0.00935371,0.04135959,-0.03521715,-0.00133708,-0.05385546,-0.0369917,-0.01711059,0.01519978,0.00120463,-0.01131555,0.00814096,-0.02191997,0.07482564,-0.01978933,0.0272858,0.16666891,0.01109891,0.02848197,0.01213143,0.0261413,0.03041707,0.0578611,-0.15445676,0.09789323,0.05267761,0.03221048,-0.04441371,0.01503989,0.05311189,0.01459955,-0.02806101,0.09490515,-0.01646515,0.11186964,0.02113983,-0.05203204,-0.00570801,-0.06657702,-0.01135466,-0.01492581,-0.0948642,-0.06304798,-0.03273145,-0.00901376,0.05577629,0.04194445,0.0067362,-0.00445651,0.03955356,0.03157627,-0.06581902,-0.0881835,0.02353809,0.02081097,0.01244157,0.00236723,0.03928387,0.06781204,-0.03899924,0.13324934,-0.06588493,0.034762,-0.00504684,0.00993638,0.03075938,0.03043474,-0.02644509,-0.02614907,-0.06511442,0.02951662,-0.03246978,-0.03754146,-0.06766884,-0.07606119,-0.06768335,-0.00271517,-0.044463,-0.00800581,0.05793486,-0.0084386,0.04548573,0.03900677,-0.00794154,0.00226768,0.05499458,0.01519403,-0.01181621,0.02391194,-0.0094037,0.07240775,0.03995755,0.04277546,-0.0626642,-0.03643207,-0.03068744,0.05953507,0.0207676,0.02335844,-0.05595339,0.02706655,0.00773895,-0.02653278,0.0395206,-0.05183093,0.03624283,-0.00084155,-0.07397281,0.05584705,0.01070893,0.01544424,-0.078842,0.07562124,-0.06593572,0.01220127,-0.01684053,0.03867426,0.02196325,-0.00194366,-0.03501723,0.00585244,-0.00796402,-0.0108374,0.00937802,0.03015819,0.01458449,-0.04592121,-0.04486761,0.05679164,0.03194751,-0.08281948,-0.06171516,0.02477596,-0.00125549,0.00191644,0.07296599,-0.00294152,-0.0502568,-0.02533818,0.04414547,0.01587837,0.05645332,-0.0500773,-0.01707735,0.05241669,-0.00654671,-0.06334119,0.04833592,-0.02494598,-0.01466841,-0.0135007,0.00617009,0.03581491,-0.01952142,0.02276465,-0.03560093,0.00723105,-0.05589607,-0.05578097,0.03065639,-0.00091882,0.12274012,-0.01981374,-0.03043615,0.06024102,-0.03651301,0.04649095,0.00139991,-0.01994522,0.05760539,-0.03703533,-0.12883572,-0.00028958,0.05851319,0.08855236,-0.01822194,-0.0279397,0.03027211,0.07062149,0.08116095,0.04086095,-0.01694026,-0.00931348,-0.12570089,-0.21964602,0.029492,0.0339339,-0.05632905,-0.00100486,-0.04026709,0.03875892,0.03430467,-0.04328663,0.00614005,0.05499415,-0.00075904,0.01112409,-0.00896028,-0.04587444,0.00652899,-0.00809386,-0.06426801,-0.03972129,-0.0286085,0.05853941,-0.0258315,-0.06908374,-0.10743526,0.03244362,-0.01115901,0.14586477,0.01514557,0.00908609,-0.05985777,0.07137866,-0.05446428,-0.02112123,-0.06519946,0.03594702,0.05012304,0.01300964,0.00336188,0.02674829,0.0177256,-0.02578064,0.03585592,-0.01278731,-0.01402642,0.03665364,-0.0100574,-0.0256991,-0.05417495,-0.00955353,0.04015455,-0.01086426,-0.01454131,0.06100797,0.08651047,-0.04193563,-0.00188604,0.00962364,-0.04987575,0.00459948,-0.00906394,-0.0356234,-0.01040477,0.02830637,-0.07434528,0.02801184,0.080486,0.057089,-0.04556906,0.04857363,-0.01380692,-0.04087751,0.11124463,0.03204296,-0.00344576,0.01250434,-0.01503509,-0.01987606,0.11577992,0.00637447,0.01886085,-0.00325087,0.00712391,0.06042668,-0.00108501,-0.0135647,-0.03407717,-0.00259288,-0.04546867,0.07146434,-0.04186857,-0.07031485,0.01684992,0.00888685,0.06946394,0.02636461,-0.00909035,-0.20383254,-0.01215628,0.00482955,0.02806111,-0.02534414,0.02641882,0.01542467,-0.09413554,-0.06701045,0.00317031,-0.00577484,0.01654391,0.0032483,-0.02534278,0.01074563,-0.00485156,0.04392293,0.02024062,0.04956432,-0.10733221,0.02227353,-0.01258032,0.27435175,-0.02618274,0.03141681,0.04732213,-0.00577275,0.03582878,0.08704787,0.09434926,0.03831202,0.03660754,0.09498185,-0.02073439,-0.02316481,-0.06983288,-0.04051646,0.05191233,0.04950373,-0.02456238,-0.01222603,0.03459725,-0.01722463,-0.01339826,0.05809503,-0.12490767,-0.0475421,-0.05854389,-0.02944806,-0.02762516,-0.03278125,0.04859431,0.01914624,-0.00809393,0.00166514,0.01123761,-0.07599381,0.01210358,0.00106341,-0.02654831,0.02177903,-0.03673872,0.00374874,0.03083296,0.00899781],"last_embed":{"hash":"1n6dka8","tokens":176}}},"text":null,"length":0,"last_read":{"hash":"1n6dka8","at":1751288825549},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Constants","lines":[318,341],"size":515,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1n6dka8","at":1751288825549}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Constants#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01905738,-0.01264583,0.01862137,-0.0287298,-0.00704826,0.02560962,-0.04225345,-0.04433474,-0.00416667,-0.02598543,-0.02525609,-0.00921341,0.04502654,0.04344304,-0.00376222,0.03031227,0.00743856,0.06490831,-0.07520556,0.00441764,0.09952055,-0.00224644,0.01592134,-0.06859989,-0.02241084,0.03884435,-0.01347858,-0.03485298,0.05763017,-0.19461019,-0.02455931,-0.0500686,-0.0124969,0.00663627,-0.00716971,-0.04250138,-0.01590959,0.01505966,-0.03164137,0.0474012,-0.00997149,0.04415907,-0.03477305,0.00062556,-0.0564175,-0.03722884,-0.01751983,0.01589866,0.00206409,-0.01224732,0.00754618,-0.02238676,0.07666664,-0.0162359,0.02511577,0.16612487,0.00958602,0.0279142,0.01221566,0.0243023,0.03171057,0.05933171,-0.15473765,0.0957904,0.04911771,0.03478104,-0.04632299,0.0157756,0.05169399,0.01064327,-0.02930408,0.09287541,-0.01785995,0.11119243,0.02540282,-0.05167715,-0.00645438,-0.06447055,-0.01423471,-0.01108509,-0.09392881,-0.06063448,-0.03268955,-0.01118764,0.05462579,0.04110857,0.00988296,-0.00664908,0.04238354,0.03175438,-0.06482608,-0.08986212,0.0234504,0.02136209,0.01384277,-0.00133383,0.03967984,0.06845272,-0.03805595,0.132936,-0.06752095,0.03402914,-0.00874095,0.01215443,0.03105365,0.0301885,-0.02655469,-0.02833243,-0.06758638,0.02822898,-0.03284432,-0.03700757,-0.06836594,-0.07535834,-0.06716248,-0.00008755,-0.04219163,-0.00829897,0.05660405,-0.00586942,0.04263864,0.03838921,-0.00616392,0.00299146,0.0542852,0.01332572,-0.00851098,0.02520609,-0.00923199,0.0730002,0.04138774,0.0381807,-0.06437692,-0.03717563,-0.02939398,0.0608513,0.01978811,0.02074897,-0.05747658,0.02440225,0.0070547,-0.02744716,0.04087936,-0.05129218,0.03842003,-0.00174061,-0.07405024,0.05267804,0.0078458,0.01367916,-0.07867162,0.07525997,-0.06459597,0.01242483,-0.01525214,0.03797102,0.02167279,-0.00412573,-0.03509941,0.00340587,-0.00656979,-0.01148991,0.00877951,0.02774256,0.01636759,-0.04582028,-0.04692272,0.055895,0.03046187,-0.07974673,-0.06158299,0.02447484,-0.00187786,0.00417291,0.07240995,-0.00179019,-0.05489565,-0.02856102,0.04088016,0.01522721,0.05502734,-0.05244438,-0.01759354,0.05214814,-0.00785283,-0.06314137,0.04788967,-0.02605227,-0.01470878,-0.01334637,0.0065687,0.03758517,-0.01983747,0.02309899,-0.03559,0.01134188,-0.05596636,-0.05458079,0.03002783,-0.00416657,0.11984771,-0.02025702,-0.03177348,0.05969266,-0.03395434,0.04733711,0.00209589,-0.01861336,0.05857828,-0.03726411,-0.12929878,-0.00133395,0.05780632,0.08926486,-0.01905316,-0.02634432,0.0300404,0.06848866,0.08054017,0.04013146,-0.0171112,-0.0077749,-0.12599769,-0.2199436,0.0290195,0.03494513,-0.05539127,-0.00037464,-0.04067502,0.03885837,0.0361454,-0.0422032,0.00767607,0.05617306,0.00177126,0.01046714,-0.0071354,-0.04535848,0.0099673,-0.01147632,-0.06278287,-0.04137503,-0.02672094,0.06016162,-0.02477588,-0.07163977,-0.10680056,0.0359713,-0.00881479,0.14681974,0.0143503,0.0093524,-0.06012592,0.07208885,-0.05448063,-0.01819864,-0.06435748,0.03185276,0.05059766,0.0165461,0.00608693,0.02565544,0.01753231,-0.02613383,0.0346235,-0.01355257,-0.01498639,0.03887101,-0.010062,-0.02256468,-0.05556137,-0.00694248,0.04207535,-0.01292547,-0.0145164,0.06283401,0.08624069,-0.0401224,-0.0031631,0.01036265,-0.04799959,0.0070569,-0.00939008,-0.03633769,-0.00820487,0.02830502,-0.07676268,0.02790258,0.08179048,0.05718731,-0.04605614,0.04718317,-0.01372071,-0.04250621,0.1117468,0.03173352,-0.005542,0.01034241,-0.01552736,-0.01845835,0.11752559,0.00630419,0.01927602,-0.00114292,0.00467797,0.05963981,0.00329385,-0.0116426,-0.03172847,-0.00431406,-0.04553221,0.06752856,-0.04175837,-0.07163069,0.0161327,0.00771679,0.07042888,0.02528388,-0.01504668,-0.20206688,-0.01328683,0.00381127,0.02760069,-0.02471107,0.02806404,0.01545295,-0.09184459,-0.06798052,0.00247499,-0.00581452,0.01701597,0.00297793,-0.02753103,0.00862505,-0.00515912,0.04337459,0.02388937,0.04886995,-0.10921603,0.0238761,-0.01647956,0.27459133,-0.02560002,0.03263272,0.0475856,-0.00578822,0.03560666,0.08319952,0.09863005,0.04140351,0.03669536,0.09456901,-0.02032769,-0.02263224,-0.06791212,-0.04099772,0.05309666,0.0509354,-0.02575625,-0.00745214,0.03344098,-0.02011831,-0.01173267,0.05825165,-0.12494071,-0.04995168,-0.06074604,-0.02983174,-0.02674844,-0.03305906,0.05151521,0.0191887,-0.00558962,0.00386548,0.00978846,-0.07368655,0.01013326,0.00010085,-0.0263161,0.02560643,-0.03472154,0.00168165,0.029419,0.01016422],"last_embed":{"hash":"1gvrl7z","tokens":174}}},"text":null,"length":0,"last_read":{"hash":"1gvrl7z","at":1751288825613},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#How It Works#Constants#{1}","lines":[320,341],"size":500,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1gvrl7z","at":1751288825613}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01160489,-0.0005819,0.02344635,-0.01370386,0.02035688,0.03173793,-0.05698866,-0.00583741,0.02361235,-0.05366249,-0.04293519,-0.01155716,0.07731509,0.03234143,-0.01650093,0.01646401,-0.02347268,0.08936965,-0.02068498,-0.04208525,0.03018779,-0.01353398,-0.0284005,-0.02448453,-0.02448087,0.03870754,-0.02039322,-0.0232995,-0.01060018,-0.18555915,0.04578935,-0.03720704,-0.03148001,0.03054343,-0.00744457,-0.06332936,-0.06009196,0.07198092,-0.02431592,0.01783113,-0.03956952,0.05233031,-0.04232489,-0.04769712,-0.02818521,-0.04963718,0.01132361,0.01698612,0.01082653,0.03122456,-0.03744956,-0.02104273,0.00331074,0.00314447,0.01930644,0.10372654,0.07576472,0.05472758,-0.02060234,0.07497334,0.01105883,0.00095556,-0.15041424,0.07310649,0.02088671,-0.00858233,-0.07146136,-0.02481297,0.07515223,-0.01337053,-0.04660083,0.06031419,-0.00102941,0.09363602,0.01366823,-0.07256469,0.00719966,-0.04374737,0.02232074,-0.02469472,-0.07138859,-0.04064859,0.00446228,0.00681334,0.05874677,0.01258007,0.03199055,-0.02293145,0.05658726,0.07276788,-0.0210882,-0.0743636,0.08033226,-0.00193691,-0.02460427,-0.0068723,0.03263779,0.04736947,0.0227291,0.12836219,-0.09750087,0.01075069,0.01070743,0.04095846,0.07685658,0.01695348,0.00199297,-0.03165474,-0.04363332,-0.01525529,0.01486304,-0.0231551,-0.0375765,-0.09367581,-0.02598879,0.00777334,-0.03428534,-0.00552905,0.03386975,-0.03409711,0.04339647,0.0139132,0.0392234,-0.01565568,0.03609969,0.07484389,0.00881404,0.025142,-0.00889426,0.08583572,0.01729341,0.05487048,-0.0069446,-0.01284769,0.00793179,0.04568053,0.00009083,-0.02642557,0.01860214,0.02638629,-0.01273118,-0.03670268,-0.01943309,-0.05123078,0.01846983,0.08325893,-0.05035401,0.07840163,-0.05664345,-0.00790346,-0.07187264,0.08478984,-0.08732856,-0.01197494,-0.05106032,-0.00840098,-0.01038172,0.02827126,-0.01902714,0.05459861,-0.04921998,-0.01909001,0.02995513,0.02630248,0.01911275,-0.10333477,-0.04633598,0.13028608,0.01299273,-0.07468736,-0.05969691,0.04814621,-0.00660995,-0.00752212,0.04616006,-0.01960281,-0.01383277,-0.0149933,0.04486953,0.0358574,0.05118779,-0.0814435,-0.04751348,0.0277486,0.00468759,-0.07809867,-0.00115256,-0.06146959,-0.00221621,-0.00790005,-0.04273098,-0.03059734,-0.00140194,-0.00345398,0.02213964,0.02011807,-0.09852113,-0.03092621,0.04028672,0.00291774,0.1147242,-0.01134276,-0.02809397,0.06549271,-0.01182599,0.03284248,0.00367243,-0.04514537,0.04795356,0.00018848,-0.11149415,0.07428116,0.08458324,0.07951,-0.01331153,-0.03668075,0.02324037,0.04960828,0.04688201,0.02930413,0.01489493,-0.00419758,-0.09294543,-0.20521942,0.01062499,0.03551111,-0.00310546,-0.03287338,-0.05189336,0.0592382,0.02090886,-0.03595577,0.06798393,0.11509844,0.05393796,0.00868295,0.0011189,-0.03661482,-0.01197868,0.03450579,-0.03418337,-0.06299324,-0.01343736,-0.02705363,-0.04034221,-0.01765252,-0.10488879,0.11379287,0.02028639,0.20611414,0.00468373,-0.04490352,-0.03947807,0.03540043,-0.01794431,-0.0125971,-0.08573089,0.06390821,0.01607854,-0.00611352,-0.00643723,0.0363366,-0.01193592,-0.0006618,-0.04901971,0.00520136,-0.05545114,0.05531638,-0.04456373,-0.08656067,-0.07953315,-0.02191239,0.05554732,0.04332906,0.01251985,0.03229924,0.1014823,-0.00389938,0.00663923,-0.01554647,-0.04438017,-0.02089625,-0.02367383,-0.02161808,0.00557962,-0.00719461,-0.0822404,0.04071516,0.02736256,0.00538424,-0.00429173,0.00531584,-0.02145951,-0.06198364,0.11363318,-0.01217553,0.00132608,-0.0341929,0.00596143,-0.04481837,0.04541961,0.0377469,0.06345777,0.03850504,0.00925643,0.06158024,0.03573029,-0.02062974,0.01957342,-0.02814777,-0.0305563,0.01293248,0.00314055,-0.05156182,0.01948005,-0.03436174,0.01117937,0.02510267,-0.04082902,-0.2275088,-0.01321478,0.0033038,0.02675482,-0.03333854,-0.02098879,0.05687405,-0.05116421,-0.00895534,0.01402089,-0.01234873,0.0708372,0.0161444,-0.01434151,0.00588166,0.01839344,0.08617653,0.00247416,0.08485968,-0.08448813,-0.0266256,-0.04922548,0.23803374,0.04012821,-0.0075773,0.0300464,-0.00960981,-0.00615646,0.07261561,0.06281286,0.00532469,-0.01637147,0.07336359,-0.01335577,0.00550651,-0.03217897,-0.00932074,-0.01814904,-0.00630665,0.01048348,-0.02815897,0.01635038,0.01220219,0.02050707,0.04996466,-0.12857409,-0.04900151,-0.01340395,-0.04558851,-0.00778359,-0.02393298,0.04249025,-0.03116811,-0.03332867,-0.00211927,0.0001307,-0.0562536,-0.02205072,-0.03174809,0.00563975,0.0298271,-0.01473412,0.04002884,0.07285183,-0.00469624],"last_embed":{"hash":"1yrqwh2","tokens":427}}},"text":null,"length":0,"last_read":{"hash":"1yrqwh2","at":1751288825715},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering","lines":[342,714],"size":9792,"outlinks":[{"title":"CSP","target":"https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP","line":63},{"title":"`resolve`","target":"#the-resolve-tag","line":134},{"title":"`babel-plugin-macros`","target":"https://github.com/kentcdodds/babel-plugin-macros","line":221},{"title":"`create-react-app`","target":"https://create-react-app.dev","line":255},{"title":"Create React App","target":"https://create-react-app.dev","line":257},{"title":"Using `resolve` as a Babel macro","target":"https://github.com/vercel/styled-jsx/blob/main/readme.md#using-resolve-as-a-babel-macro","line":263}],"class_name":"SmartBlock","last_embed":{"hash":"1yrqwh2","at":1751288825715}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01380403,-0.01254649,0.03211932,-0.05005508,0.00654977,0.03274323,-0.07610226,-0.00450137,0.02073339,-0.0514604,-0.03041638,-0.00617553,0.0656461,0.02109605,-0.00360391,0.00083698,-0.0259045,0.07675963,-0.02833392,-0.02175944,0.04737769,0.0076209,-0.02949792,-0.04174593,-0.04944099,0.02341413,-0.02921914,0.00185016,0.01065314,-0.17166784,0.05890894,-0.03103471,-0.02276717,0.02479587,0.00492946,-0.04636161,-0.05647092,0.05151453,-0.0164735,0.02974218,-0.03671045,0.03873798,-0.02957926,-0.04539562,-0.01934905,-0.03785535,0.01056683,0.01298593,0.00345564,0.03770692,-0.04260119,-0.00934565,0.03141294,-0.03288964,0.02866551,0.10748413,0.07667182,0.07348849,0.00727025,0.09566119,0.01193603,0.00561474,-0.15752697,0.06613965,0.02892716,0.00867668,-0.08941467,-0.01935183,0.06472043,-0.00041402,-0.03839201,0.04077057,0.00651338,0.08278751,0.01832538,-0.10043714,-0.00219617,-0.03605298,0.03796942,-0.03938327,-0.07258902,-0.06008295,-0.00851136,-0.01054655,0.05944019,0.01324576,0.01535324,-0.03793589,0.03225326,0.07600959,-0.0305597,-0.07721155,0.07188553,0.00712144,-0.00782509,0.0055427,0.03106588,0.03917834,0.00176302,0.11919027,-0.09009834,-0.00249126,-0.0032917,0.03421151,0.05809911,0.01146921,-0.01304152,-0.03302713,-0.02145424,-0.01313772,0.01566226,-0.03855545,-0.0182967,-0.08726423,-0.03083363,-0.00272445,-0.04289859,0.0029067,0.0476923,-0.02919039,0.04858621,0.00978111,0.0455037,-0.01270001,0.01426579,0.08912137,0.00911125,0.0035107,-0.00649487,0.10050704,-0.00709968,0.06247203,-0.01906425,-0.00405615,0.00041398,0.061563,-0.01529638,-0.00951042,0.00426187,0.0409051,0.01135374,-0.02601677,-0.03416368,-0.04260416,0.02040291,0.08217524,-0.0253114,0.09234634,-0.0564936,-0.00531994,-0.06783012,0.0647076,-0.10227289,-0.01362838,-0.03951703,-0.00815132,-0.00292805,-0.01719013,-0.03025522,0.03175224,-0.02602725,-0.00968812,0.02994822,0.01867234,0.02020804,-0.1037115,-0.02362569,0.12307103,0.01468469,-0.08885223,-0.03758803,0.01336067,0.01244053,0.01725512,0.0648817,-0.01190037,-0.01946267,-0.04148769,0.05536227,0.05161667,0.07587656,-0.07831962,-0.05530164,0.01996732,0.00220141,-0.08446326,0.00881996,-0.03964628,-0.01018703,-0.00977576,-0.02413424,-0.01476989,0.00757856,-0.02982167,0.01337186,0.0297235,-0.08087842,-0.01952781,0.05444207,0.00655459,0.10929432,0.00108766,-0.02402753,0.04045278,-0.03003248,0.05264338,0.00849759,-0.03458419,0.05610265,-0.01411347,-0.10466841,0.08531926,0.08662542,0.08904471,-0.02331768,-0.03421735,0.04990207,0.06157127,0.05036924,0.00384932,0.0063097,-0.00453939,-0.08395377,-0.21604136,0.02255016,0.0278518,-0.01766949,-0.05724118,-0.03510177,0.05359088,0.04160016,-0.05539511,0.04708098,0.13531253,0.03967087,0.03007351,0.03285405,-0.05053025,-0.01675035,0.04496394,-0.03181931,-0.07392934,-0.02252905,-0.01875916,-0.04453321,-0.01868903,-0.10881005,0.09348331,0.01451482,0.19930832,0.00254278,-0.0264692,-0.02623267,0.02288103,-0.02012321,-0.01012289,-0.10096054,0.05249421,0.00179704,0.03725956,-0.02790362,0.03148434,-0.02505305,-0.00262488,-0.06418745,0.01988963,-0.03695001,0.04515671,-0.06190015,-0.07986918,-0.0716779,-0.030903,0.07363275,0.02435055,0.0299617,0.03921751,0.09267924,-0.0249622,-0.01946722,-0.00774553,-0.06516681,-0.00793002,-0.00933754,-0.0193391,-0.02467589,-0.00395728,-0.09702532,0.02949584,0.02070471,0.00473135,-0.01020971,0.01205146,-0.03341018,-0.04310587,0.10861643,0.02462311,0.00997339,-0.01476676,0.00565045,-0.03118016,0.0442396,0.05837979,0.03074411,0.00565691,-0.00338213,0.04722723,0.01107691,-0.0190837,-0.00027109,-0.02090851,-0.03772253,0.01870501,-0.04024993,-0.0587299,0.01821263,-0.00705898,0.01256484,0.02223126,-0.02285689,-0.23487386,-0.00393318,0.01774431,0.02805866,-0.01650176,-0.01577688,0.04928106,-0.04740375,0.00171939,0.00197622,0.0018953,0.05760218,0.0118238,-0.03006673,0.01243107,0.02447361,0.07905868,0.00236699,0.07832561,-0.10494787,-0.01020603,-0.02619796,0.23463547,0.02544951,-0.00546903,0.04982939,-0.01836381,0.02241686,0.06338828,0.05312055,0.02024043,-0.00555956,0.06378731,-0.0205823,-0.01231688,-0.01773186,-0.00026378,-0.0171066,0.00006205,0.00412628,-0.01936146,0.00586436,0.02495289,0.01630244,0.05245157,-0.12327943,-0.06847469,-0.01328465,-0.04349282,-0.00848066,-0.01564837,0.02622167,-0.02577206,-0.01516979,-0.00423053,-0.00146267,-0.04567783,0.01808201,-0.05164699,0.0100989,0.02891931,-0.03368273,0.04528404,0.10829186,0.00137855],"last_embed":{"hash":"1v65w82","tokens":471}}},"text":null,"length":0,"last_read":{"hash":"1v65w82","at":1751288825877},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#{4}","lines":[349,401],"size":1518,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1v65w82","at":1751288825877}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#Content Security Policy": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00791273,-0.01031147,-0.01366007,-0.07665004,-0.01875432,0.00290712,-0.07497983,0.00955888,-0.04219826,-0.00466083,-0.01171497,-0.03560768,0.04260102,0.03445107,0.00469297,0.01383205,-0.00287776,0.06440216,-0.02807509,0.04259218,0.0572731,0.00811076,0.04489148,-0.05512551,-0.04884164,0.03376568,0.04748894,-0.01495537,-0.04198786,-0.18483987,0.0383617,-0.03512536,-0.03936291,-0.00290115,0.03811157,-0.04863528,0.01099818,0.03680566,-0.02405078,0.04840206,0.01003068,0.03649782,-0.04277589,-0.03497553,0.02971549,-0.04842843,0.04693741,-0.02774857,-0.01659043,0.00234001,-0.05641209,-0.00499887,0.05694309,-0.01798558,0.00073051,0.09003574,0.04282359,0.02177007,-0.01594558,0.0578141,-0.00036588,0.04467992,-0.17986056,0.07771154,0.06084279,0.0365346,-0.04391749,-0.01859636,0.03562284,0.01164614,-0.01373937,0.06734664,-0.00371891,0.07101502,0.04499169,-0.00801304,0.03494122,-0.01959139,0.00431472,-0.03692449,-0.09695083,-0.04037952,0.01406783,-0.00519361,0.03922587,0.01803245,0.01413776,-0.02827765,0.05203512,0.05844154,-0.05074188,-0.10759047,0.06968768,-0.01681645,0.00720011,-0.02451398,0.03373168,0.07556874,-0.10285932,0.10401974,-0.01652302,0.01527585,-0.02540603,0.00849603,0.05633778,-0.00061052,0.00272094,-0.06741889,-0.03948667,-0.05475703,-0.00234325,-0.01194832,-0.04903928,-0.06040847,-0.02275098,-0.0162533,0.00532736,-0.02684953,0.04931653,0.01099338,0.01807668,0.04118329,0.05326832,0.00515684,0.02043837,0.00596021,0.00809121,0.06040128,-0.01389677,0.08826228,-0.01248105,0.02262866,-0.03063505,-0.03891057,0.02884236,0.00263619,-0.02312061,0.02192174,-0.01296325,-0.01124557,0.04178036,-0.02764928,-0.03329984,-0.04956119,0.0313247,0.07828528,-0.02315102,0.11270764,-0.06535624,0.00187875,-0.03083893,0.05213866,-0.0607406,-0.01592414,-0.01333793,0.00402842,-0.028318,0.01209858,-0.08563982,0.03121516,-0.00072085,-0.0054015,0.00019224,0.10555875,0.06606143,-0.09429734,-0.00924741,0.0166725,0.0181479,-0.08938833,-0.08537097,0.00578472,-0.02792003,0.01396419,0.04155183,0.01799211,-0.08399293,-0.02897515,0.065528,0.02938221,0.01367905,-0.04262816,-0.06991506,0.03103139,-0.00380004,-0.00167559,0.0681809,-0.054028,-0.03210836,0.01050534,-0.0454856,0.01944247,-0.02048091,0.01213577,-0.03730248,-0.03796585,-0.00700421,-0.02637486,0.03701898,-0.03692189,0.14645836,0.02099796,-0.0427881,0.04439227,-0.04335484,0.04175152,-0.0073334,-0.01545429,0.07590352,0.04486086,-0.07194685,0.00689576,0.0644841,0.0989331,-0.04491213,-0.02027306,0.04581204,0.05825828,0.05851872,0.07723638,-0.0081182,0.0186079,-0.08172468,-0.20811667,0.02963643,0.02651197,-0.06703261,0.00648367,0.00671881,0.0167368,-0.01200211,-0.05226251,0.06386902,0.14010422,0.0375398,-0.01865255,0.03012316,-0.10177796,0.02967149,0.05553313,-0.05424951,0.00657513,0.02500874,-0.016575,-0.02343586,-0.06560005,-0.09114885,0.08579341,-0.0443464,0.15621048,0.03999603,0.01665136,-0.04898773,0.00256893,0.0246828,-0.03958239,-0.14111449,0.01558781,0.01956161,-0.0015382,0.00059106,0.01727639,-0.00972275,-0.00268443,-0.01128328,-0.00235676,-0.04772913,0.04794852,-0.08087105,-0.04870781,-0.00055213,-0.00070696,0.0479254,-0.00920625,0.00125827,-0.0003348,0.09662588,-0.01037426,-0.01602867,0.00032069,-0.03815262,0.03157087,0.03567639,-0.00041763,-0.00257503,-0.00022041,-0.10770645,0.03470806,0.03003962,-0.00750211,0.00234799,0.0662567,-0.05171639,-0.03291535,0.09250876,0.03441138,-0.01995517,0.02874999,0.01178301,-0.05223991,0.05073652,0.0157365,0.00192084,0.08138596,0.02756043,0.08201656,-0.01116341,-0.04944452,0.01199445,-0.00372551,-0.01903504,0.03906601,-0.042878,-0.07590609,0.00754726,-0.07061343,0.05262008,0.04452063,-0.00345863,-0.25031379,-0.04041065,-0.00315725,0.04182281,-0.02891093,-0.01547676,0.06486351,-0.0386046,-0.08510011,0.06777921,-0.06343134,0.0217686,-0.02613527,-0.02532855,0.01512015,-0.0191149,0.04274537,-0.00228213,0.04846004,-0.06021281,0.00938645,0.01959592,0.25572449,-0.00861337,0.00210922,0.0271044,-0.00550708,0.06618138,0.08726107,0.05199347,-0.01336502,0.00629271,0.02864427,-0.02740543,-0.01014989,0.01028884,-0.02643434,-0.03482501,0.00869767,-0.02260708,-0.02979398,-0.02448742,0.02947794,0.00330734,0.04026458,-0.07420775,-0.03753857,0.03364632,-0.00419443,-0.03250905,-0.00382564,0.05807956,-0.00128478,-0.00054921,-0.00724787,0.04495371,-0.0516086,0.00171952,-0.07468955,0.03181066,0.00566089,0.00017229,-0.00308353,0.03579096,-0.02712692],"last_embed":{"hash":"73blp7","tokens":301}}},"text":null,"length":0,"last_read":{"hash":"73blp7","at":1751288826034},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#Content Security Policy","lines":[402,418],"size":682,"outlinks":[{"title":"CSP","target":"https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP","line":3}],"class_name":"SmartBlock","last_embed":{"hash":"73blp7","at":1751288826034}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#Content Security Policy#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00733854,-0.01102776,-0.01443377,-0.07814968,-0.02394138,0.00591725,-0.07292158,0.01016196,-0.0406876,-0.00217065,-0.01378828,-0.03269311,0.04365581,0.0323961,0.00478915,0.01655468,0.00181547,0.06601486,-0.03185455,0.04307484,0.05860114,0.01137253,0.05316297,-0.04967393,-0.0497467,0.02966487,0.05093053,-0.01666751,-0.04266605,-0.18708509,0.03450014,-0.03648011,-0.03823596,-0.00171816,0.04029527,-0.05354448,0.00939648,0.0366399,-0.02354491,0.05125568,0.01231492,0.03663678,-0.04021395,-0.03654225,0.0320175,-0.04626742,0.05064636,-0.02979429,-0.01997227,-0.00211558,-0.05432389,-0.00578106,0.05845127,-0.01580611,-0.00495779,0.08822468,0.04355522,0.02119702,-0.01474544,0.06304268,-0.0045148,0.04296585,-0.17958769,0.07974163,0.05856711,0.03911944,-0.05000214,-0.01820202,0.02992123,0.01036474,-0.01658779,0.06540465,-0.00222327,0.07300506,0.05009192,-0.00150531,0.03905131,-0.02132032,0.00516223,-0.03320182,-0.09428061,-0.04344229,0.01432362,-0.00599259,0.03894491,0.01601592,0.01117823,-0.03021503,0.05143625,0.05755207,-0.04790829,-0.10774641,0.07276498,-0.01645983,0.00401484,-0.03179266,0.03109636,0.07499189,-0.10282698,0.10258856,-0.01347758,0.0184659,-0.02710812,0.01389281,0.05547002,0.00562262,0.00329468,-0.06287191,-0.04062508,-0.05468234,-0.00249285,-0.01132908,-0.05133219,-0.061108,-0.02421122,-0.0143306,0.00556111,-0.02896133,0.04913218,0.01060852,0.018999,0.04118564,0.05812245,0.00938915,0.01825891,0.00494644,0.0050542,0.06153911,-0.01497168,0.08320273,-0.01505388,0.01731217,-0.03232569,-0.03693273,0.03030951,0.0033666,-0.02401489,0.02562653,-0.01341653,-0.01376561,0.04358687,-0.02718565,-0.03127647,-0.05039723,0.02931092,0.07855795,-0.0261167,0.11514273,-0.06368457,0.00250876,-0.03114514,0.04754079,-0.05741458,-0.01816903,-0.0117426,0.00690857,-0.02573002,0.01010003,-0.08948594,0.03000397,-0.00186303,-0.0053778,-0.00241881,0.10649803,0.06103789,-0.09056392,-0.00570026,0.0145954,0.01438348,-0.09035637,-0.08360789,0.00112901,-0.0291131,0.0172277,0.04248494,0.01735666,-0.08629532,-0.02853763,0.06427746,0.03220813,0.00884898,-0.03750979,-0.06834323,0.03376723,-0.00327353,0.00145812,0.06571431,-0.05122127,-0.0261023,0.0083498,-0.04599767,0.01713792,-0.01890746,0.01003867,-0.04044708,-0.0378338,-0.00778932,-0.0231895,0.03598833,-0.0353361,0.14475152,0.01825646,-0.04141878,0.04154844,-0.04397624,0.03765811,-0.00874964,-0.02000498,0.07556137,0.04453653,-0.0685083,0.00843,0.05700947,0.09472018,-0.04602054,-0.01916953,0.04684921,0.05991871,0.05437871,0.07485057,-0.00652524,0.02079212,-0.08048507,-0.20748828,0.02740922,0.02476792,-0.0672005,0.008086,0.0114014,0.01591485,-0.01209064,-0.05419498,0.06136473,0.1359695,0.04307089,-0.02316367,0.03112771,-0.1026691,0.03026559,0.05803618,-0.05365245,0.00908155,0.02479634,-0.01601483,-0.02199634,-0.06892019,-0.09496181,0.08637214,-0.04646109,0.15524361,0.03833,0.01299053,-0.0504566,0.00469335,0.02775579,-0.0394945,-0.14431955,0.01285485,0.02000298,-0.00445305,0.00061899,0.01867423,-0.01645223,-0.00236793,-0.00971246,-0.00673613,-0.04704253,0.04687544,-0.07936827,-0.0455991,0.0022597,-0.00185017,0.04662329,-0.01004393,0.00296879,-0.00304217,0.09726462,-0.01013273,-0.01419291,0.00181875,-0.03324613,0.03172282,0.03272892,-0.00164906,-0.00786021,0.00328849,-0.10364728,0.03258402,0.03054689,-0.00248484,0.00134865,0.06195773,-0.0516805,-0.02742587,0.09116255,0.03030544,-0.02386141,0.03156907,0.00908332,-0.0553087,0.0512357,0.01475707,-0.00085497,0.08527989,0.03071193,0.08617134,-0.00861505,-0.04769462,0.01179455,0.00410235,-0.02237089,0.04106923,-0.04351163,-0.07542223,0.00546521,-0.07046477,0.05367873,0.04503438,-0.00360457,-0.25014567,-0.04512634,-0.00605912,0.03908888,-0.02197985,-0.01622763,0.06543415,-0.03847115,-0.08521276,0.07318964,-0.06420176,0.02099734,-0.02612872,-0.02800176,0.0129753,-0.0178152,0.04139557,0.00120295,0.05409556,-0.06144037,0.01081417,0.02192753,0.25835332,-0.00804126,-0.00067671,0.02595823,-0.00086966,0.07044905,0.08598086,0.0522354,-0.01195936,0.00645091,0.0256504,-0.02513593,-0.01138148,0.01073114,-0.02833803,-0.03503115,0.01129716,-0.02350011,-0.03035422,-0.02553591,0.02790648,0.00306702,0.03634806,-0.07213302,-0.03737124,0.03493889,-0.00385894,-0.03402077,-0.00038913,0.05701215,-0.00318467,0.00042564,-0.00476753,0.04326672,-0.04857679,0.00212883,-0.07561023,0.02784619,0.00366132,0.00558604,-0.00620701,0.04034831,-0.02680366],"last_embed":{"hash":"jj2833","tokens":299}}},"text":null,"length":0,"last_read":{"hash":"jj2833","at":1751288826158},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#Content Security Policy#{1}","lines":[404,418],"size":653,"outlinks":[{"title":"CSP","target":"https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"jj2833","at":1751288826158}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.01273933,-0.02850636,0.04002927,-0.06113109,0.06061356,0.00157934,-0.05095144,-0.03094527,-0.02044467,-0.0293709,-0.0068525,0.007622,0.02134055,-0.00264347,0.01184817,0.03002904,-0.0086317,0.08702116,-0.06594644,-0.02979372,0.04000346,-0.02044826,-0.00578449,-0.04946614,-0.01425821,0.04304887,-0.00845903,-0.00822568,0.02413335,-0.16946262,-0.00709484,-0.05282371,-0.04128741,0.015762,-0.00620954,-0.04041941,-0.01232146,0.02110175,-0.02583826,0.06524764,0.02486035,0.0498987,-0.03503411,-0.0237656,-0.04325029,-0.01764923,0.00274741,0.01891553,-0.03743831,0.00545503,0.00234008,-0.05454898,-0.03589157,-0.02290366,0.00668073,0.1404641,0.06096511,0.03102697,-0.02262473,0.05458495,-0.01925916,0.02040033,-0.15465274,0.08312652,0.02459329,0.02622414,-0.05225677,0.02866063,0.02510528,0.00803088,-0.01688053,0.02369194,0.01478566,0.13578956,-0.00349084,-0.05833,-0.00547445,-0.05250764,-0.00441353,-0.0465485,-0.0682202,-0.01204995,-0.02714734,-0.02385894,0.04462111,0.03680311,0.04768701,-0.04767572,0.02129809,0.0650846,-0.04365264,-0.13374153,0.05791276,-0.01826308,-0.02738559,-0.04491047,0.0226637,0.06972958,-0.01170728,0.14406152,-0.10129915,-0.00028185,0.05644137,0.0047389,0.04136426,0.0288496,0.00316764,-0.04821399,-0.07720969,0.0148238,-0.01695958,-0.03980415,-0.06982978,-0.07327027,-0.07283348,-0.04504858,-0.03512657,-0.00127357,0.05896087,-0.04500643,0.04674425,0.03693137,0.02517367,-0.02892771,0.05219373,0.05507288,0.01794377,0.01932537,0.00087018,0.07162862,0.00814502,0.02708059,-0.02939051,0.02048342,-0.02609295,0.04350995,0.01024598,0.0024523,-0.02972546,0.03568566,0.0272515,-0.02902393,-0.02258558,-0.0481982,0.02580104,0.03132378,-0.03624781,0.06858543,-0.01660275,-0.02035977,-0.07434034,0.06412515,-0.08070757,0.05985163,-0.01892916,0.02777704,-0.00776393,-0.00933012,-0.06381343,0.04138527,-0.01686364,-0.03184174,0.01735341,0.03933654,0.01869128,-0.0595058,-0.04403571,0.05189917,0.01985358,-0.0849221,-0.04891192,0.01008861,-0.0236997,0.00436377,0.06115013,0.02302725,-0.02341622,-0.01964147,0.04969426,0.05603351,0.10619406,-0.05320649,-0.04459893,0.03916762,-0.02299516,-0.07741647,0.00763046,-0.04550627,-0.05333509,-0.02877605,-0.06103665,0.00850574,0.01124953,-0.0298213,0.02823706,-0.0044839,-0.0577011,-0.04320211,0.05361745,0.0140274,0.18254022,-0.00513076,0.00192693,0.07683592,-0.03355983,0.03886879,0.00850925,-0.01247648,-0.00803393,-0.00608058,-0.13408691,0.03689925,0.07942352,0.10647663,0.01301574,-0.04817059,0.01745408,0.07191169,0.01990323,0.04829881,-0.02408885,-0.02546451,-0.08520675,-0.18375707,0.02323435,0.06662183,-0.04358229,0.00601835,-0.03870064,0.04843957,0.01120394,-0.0414886,0.07032079,0.11427691,0.01101056,-0.00363545,-0.01631107,-0.04275325,-0.01279621,0.02588495,-0.03696038,-0.0349957,-0.00424519,0.04095986,-0.0064097,-0.04049723,-0.08403431,0.03214171,0.03398227,0.16229615,0.03207929,0.01888888,-0.05031975,0.03719243,-0.01176045,-0.02825438,-0.05648102,0.05342907,0.01831974,-0.00905288,-0.03494775,0.00741546,0.04236833,0.02712282,-0.00942004,0.02728669,-0.02782165,0.06091927,-0.04318131,-0.0687516,-0.04249651,-0.00190334,0.02598439,0.01284503,0.01597803,0.0438422,0.09391931,-0.0055072,-0.01224688,-0.01344396,-0.05081152,0.00128309,0.00395298,-0.03534137,-0.03173139,0.01140609,-0.09513128,0.04021766,0.05789667,0.01661191,-0.03611433,0.03719928,-0.04498011,-0.04403204,0.11721959,0.04707805,-0.02641457,0.00296984,-0.00852718,-0.03997843,0.13000262,0.01810131,0.05676901,0.04052806,-0.00673231,0.08856856,0.00431496,-0.01699882,-0.01458735,-0.05505943,-0.02777536,0.02376342,-0.01296185,-0.09653237,0.03655137,-0.0332944,0.0148182,0.02654879,-0.01583043,-0.22960457,0.0035101,0.05254021,-0.0371897,-0.06485732,-0.0085294,0.0388751,-0.06095608,-0.07981073,0.00936989,-0.03707546,0.0086704,0.03982122,0.01201774,0.01509774,-0.00756147,0.07335299,0.00333661,0.09352397,-0.03613655,0.00681987,0.01412442,0.23967525,-0.03514163,0.02492814,0.04104514,-0.02053337,-0.00888596,0.04206918,0.09595355,0.0458388,0.01950035,0.09870145,0.00779912,-0.00404325,-0.02719116,-0.02385077,-0.0010993,0.01307051,-0.00033595,0.00917425,-0.00187306,0.00961928,0.01916321,0.03138613,-0.08362123,-0.06556327,-0.00444965,-0.0400711,-0.04904504,-0.01260827,0.06079295,-0.00596842,-0.0334006,0.02109177,-0.01106229,-0.05959676,0.02173418,-0.02324996,0.03921287,0.03423933,-0.02851671,-0.04111384,0.02911585,0.02561772],"last_embed":{"hash":"oav3iu","tokens":461}}},"text":null,"length":0,"last_read":{"hash":"oav3iu","at":1751288826268},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component","lines":[419,714],"size":7175,"outlinks":[{"title":"`resolve`","target":"#the-resolve-tag","line":57},{"title":"`babel-plugin-macros`","target":"https://github.com/kentcdodds/babel-plugin-macros","line":144},{"title":"`create-react-app`","target":"https://create-react-app.dev","line":178},{"title":"Create React App","target":"https://create-react-app.dev","line":180},{"title":"Using `resolve` as a Babel macro","target":"https://github.com/vercel/styled-jsx/blob/main/readme.md#using-resolve-as-a-babel-macro","line":186}],"class_name":"SmartBlock","last_embed":{"hash":"oav3iu","at":1751288826268}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00438907,-0.01302623,0.03654981,-0.05507989,0.05796825,0.00503885,-0.05616871,-0.00933613,-0.02937738,-0.02263642,-0.00174129,0.01292309,0.01426731,-0.01026721,0.01729621,0.00442453,0.02074142,0.08169182,-0.04965381,-0.01713188,0.03572673,0.00750328,-0.02733494,-0.04718572,-0.03476703,0.04512845,-0.04419997,-0.0311865,0.00970313,-0.14628793,0.02880746,-0.04027759,-0.04910364,-0.01011261,0.00653682,-0.03441175,-0.01853305,0.03322303,-0.02780344,0.05209639,-0.00083569,0.06152358,-0.02409486,-0.01457136,-0.03552117,-0.02930185,0.01343389,-0.00408097,-0.04154027,0.00504745,0.00889638,-0.03769551,0.0181948,-0.01554899,0.01242011,0.13850698,0.04916945,0.04453314,-0.01891442,0.03060605,-0.00874398,0.04583275,-0.14680821,0.07327115,0.02848833,0.03159098,-0.06004281,0.01921219,0.02385099,0.01597958,-0.00933561,0.04402086,-0.01059034,0.09707345,0.02658123,-0.05086671,-0.00092777,-0.0519334,-0.02183037,-0.04018424,-0.07984659,-0.04360916,-0.00989844,-0.01971101,0.02439013,0.02433635,0.03077717,-0.03469767,0.02072144,0.07156517,-0.05314976,-0.11546824,0.06556325,-0.00563592,-0.00416838,-0.05308131,0.02961299,0.0793231,0.0164204,0.15979673,-0.10653728,0.01675042,0.05037687,-0.00408015,0.06018168,0.00096343,-0.00766087,-0.05262045,-0.05715294,0.00288202,0.01026732,-0.01472979,-0.09162304,-0.0697061,-0.04467999,-0.07115696,-0.02061191,0.02287116,0.06438781,-0.03384686,0.01515701,0.04287663,0.02150295,-0.02249912,0.04546601,0.04559351,0.03940636,0.03159802,0.00510636,0.06238587,-0.00109725,0.03732662,-0.04127387,0.00811434,-0.02607556,0.03932622,0.00860607,-0.02325574,-0.01587768,0.04069198,0.03061298,-0.01950651,0.00883267,-0.05981889,0.04750087,0.038327,-0.00398221,0.0764196,-0.02828959,-0.00804062,-0.08619107,0.0855453,-0.07130688,0.04604289,-0.03290825,-0.00558955,-0.01298533,-0.03039282,-0.05595954,0.03355279,0.01430077,-0.01833295,0.01409592,0.05130563,0.01972903,-0.07918587,-0.03982738,0.08140245,0.03876497,-0.09689241,-0.05890612,0.00796688,-0.02641113,0.00015615,0.04169144,0.02151654,-0.02472039,-0.01565303,0.03363809,0.03183445,0.11145828,-0.04985807,-0.03877784,0.05553679,-0.03644915,-0.08635512,0.03524418,-0.06328335,-0.03369151,0.0057264,-0.02727994,-0.01622543,0.02706518,0.00335707,0.01728004,-0.01373404,-0.05628816,-0.00708101,0.02766646,-0.00316549,0.15805387,0.01500367,-0.00626471,0.0829606,-0.04657668,0.03312851,0.02111137,-0.00543346,-0.00865762,-0.00158046,-0.15102471,0.02650935,0.09642465,0.10415222,0.00019293,-0.04131646,0.0226119,0.08400534,0.01366806,0.02031508,-0.01248285,-0.02777646,-0.10742514,-0.2013402,0.02365253,0.05318482,-0.05515734,-0.02867181,-0.02295281,0.04127997,0.01573022,-0.0386296,0.09019253,0.11955849,0.00459255,0.01752943,-0.01135443,-0.03252913,-0.00533765,0.01524424,-0.03387267,-0.04386527,-0.01505436,0.02632278,-0.01979264,-0.03894521,-0.07945564,0.06029056,0.03672901,0.1459976,0.02954834,0.0316971,-0.06309778,0.03062092,-0.01496588,-0.02669503,-0.06007589,0.02420833,0.01673372,0.02508562,-0.05836638,0.01645265,0.03157697,-0.00242365,-0.04183887,0.03815845,-0.03621174,0.07158239,-0.05617655,-0.05320992,-0.05793474,-0.00068019,0.04216294,-0.00494532,-0.00421506,0.03179516,0.10642972,0.00955774,0.00448044,-0.02614011,-0.08279116,-0.0029907,0.00104745,-0.02634584,-0.00847498,-0.00388149,-0.09779037,0.03948656,0.0319428,0.01153858,-0.03555844,0.0230033,-0.04639992,-0.06299134,0.12609923,0.06164403,-0.05231221,0.00359897,-0.02543716,-0.03718968,0.11298352,0.0068529,0.05299056,0.04732447,-0.02183637,0.05857422,0.01532062,-0.03278673,-0.01481961,-0.07495829,-0.03180303,0.02776529,-0.02417712,-0.09051568,0.04887737,-0.04736754,0.01258964,0.04967162,-0.00443874,-0.21876481,0.01150391,0.0497774,-0.01086194,-0.05395326,-0.02249317,0.05044009,-0.03810721,-0.06014186,0.0364047,-0.00237558,0.01887088,0.00316348,0.00580989,-0.01841872,0.00540211,0.07839523,0.00061335,0.09434177,-0.03042682,0.00278354,-0.01275597,0.25158128,-0.04565065,0.04256883,0.04741976,-0.04774629,-0.01673727,0.01770733,0.1080934,0.05228562,0.03972316,0.07351184,-0.01272554,-0.00880376,-0.03154232,-0.00263349,-0.00450879,0.02299414,0.00572977,0.01582199,-0.01006208,-0.02716509,0.03095277,0.01713753,-0.06633572,-0.07198194,-0.00867961,-0.02731877,-0.05201854,-0.00525656,0.05472315,-0.02058554,-0.00939558,0.03573366,0.00891461,-0.05396288,0.01131412,-0.04341432,0.01795701,0.0286151,-0.03080493,-0.01648602,0.0470387,0.02749358],"last_embed":{"hash":"wqdzuw","tokens":100}}},"text":null,"length":0,"last_read":{"hash":"wqdzuw","at":1751288826480},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#{1}","lines":[421,422],"size":220,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"wqdzuw","at":1751288826480}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#External styles": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.00190963,-0.03301678,0.050819,-0.07869944,0.05499529,0.00761833,-0.06938783,-0.04896877,-0.01924693,-0.02691819,-0.00626817,-0.01406352,0.0164946,0.01137647,0.00340473,0.03717481,-0.00040874,0.06948004,-0.0647948,-0.03477179,0.01189037,-0.02441195,0.00759663,-0.05763195,-0.01631491,0.04011148,-0.0145019,0.00520635,0.0308886,-0.18505977,-0.01980703,-0.05051318,-0.0425414,0.02626146,0.00900602,-0.0233514,-0.01027124,0.01549417,-0.02176537,0.05024134,0.02910566,0.03895658,-0.03936114,-0.02330865,-0.02971503,-0.03518197,0.01969003,0.02921012,-0.00896169,0.00351886,-0.00634101,-0.05337118,-0.00962623,-0.02531033,0.01473237,0.15062092,0.08592212,0.04826476,-0.00800928,0.05680757,-0.0154483,0.02681484,-0.15235128,0.09453598,0.03683368,0.02468834,-0.04653968,0.02602313,0.03959539,-0.00660395,-0.02229497,0.01817462,0.01642238,0.13770345,0.00885298,-0.06626849,0.01193156,-0.05862109,0.01133967,-0.04707394,-0.0684528,-0.01382879,-0.01032554,-0.01695313,0.0529599,0.03168901,0.03088241,-0.03012169,0.01619452,0.04378517,-0.06139157,-0.13595873,0.06975844,0.00427436,-0.01556308,-0.00633294,0.03064263,0.05848085,-0.01237743,0.12787096,-0.09796465,0.00052612,0.05465038,0.0160352,0.03611845,0.02579539,-0.00331485,-0.03498501,-0.0667484,0.01510947,-0.02463108,-0.05001242,-0.06902317,-0.07250442,-0.07418185,-0.04212689,-0.04520977,-0.01514019,0.0460507,-0.02791951,0.0532003,0.03782807,0.0429525,-0.01465235,0.029798,0.04548634,0.00755108,0.01506194,-0.0027834,0.08390395,0.00798706,0.04068401,-0.03118248,0.00173086,-0.02817608,0.03553287,0.01277374,0.01755452,-0.04817598,0.02251862,0.01784937,-0.04450027,-0.02185509,-0.03884387,0.01705403,0.02004123,-0.04164796,0.06921148,-0.0048814,-0.02309034,-0.07154132,0.06550508,-0.08536231,0.06324804,-0.02573444,0.02836699,0.00470491,-0.00811318,-0.06350996,0.03303512,-0.01020265,-0.02142078,0.02488267,0.03619838,0.02487941,-0.07506622,-0.02972075,0.03844217,0.01919398,-0.08974494,-0.04275163,0.01799322,-0.01388576,0.0109019,0.07017823,0.02862966,-0.02953012,-0.03953245,0.05049491,0.05865893,0.09936955,-0.06945691,-0.03305586,0.02870649,-0.0045126,-0.08189452,0.00680949,-0.02661018,-0.03942214,-0.0309749,-0.03721323,0.02131272,0.00723094,-0.01347617,0.01878846,0.00043805,-0.06761306,-0.03122135,0.05532758,-0.00112794,0.17802311,-0.00366193,-0.00156107,0.05003263,-0.04264183,0.05194591,0.01081692,-0.00269678,0.00716427,-0.03162171,-0.13635178,0.02998124,0.09419928,0.10451779,0.015203,-0.04333,0.02922314,0.06859568,0.04501477,0.0486609,-0.02695037,-0.02191173,-0.09421016,-0.19565712,0.03687093,0.0481289,-0.03925482,-0.01190124,-0.02831446,0.04246343,0.00996686,-0.01192415,0.0790806,0.12314119,0.01284134,0.00496972,0.00480982,-0.04327276,-0.0302432,0.03672846,-0.03788034,-0.03482644,-0.01329324,0.02913735,-0.02497344,-0.06171347,-0.074949,0.0270229,0.00815294,0.15694349,0.02061211,0.01012059,-0.04592688,0.03177664,-0.01292361,-0.02701588,-0.08073918,0.05179646,0.01397913,0.01900123,-0.02342283,0.0042541,0.03563919,0.02289202,-0.00051132,0.00516016,-0.0174349,0.0731125,-0.02936289,-0.06680683,-0.04213779,-0.02149285,0.04029067,0.00469027,0.02365755,0.05342016,0.09729914,-0.03547075,-0.01567658,-0.00568451,-0.03767055,0.0016722,0.01255259,-0.03866651,-0.02756133,0.01585778,-0.09350386,0.04183403,0.06153029,0.02508016,-0.02088733,0.05063528,-0.01833125,-0.04842782,0.10387506,0.0403183,0.01284375,0.00411796,0.01188938,-0.03918416,0.12630497,0.02571824,0.05503799,0.03825546,-0.00037341,0.08239669,-0.00507567,-0.01893305,-0.02743986,-0.03792292,-0.03583028,0.03208375,-0.01534862,-0.08775987,0.00584697,-0.02474592,0.03482175,0.02843248,-0.01157155,-0.21912751,-0.00813449,0.05328035,-0.03198538,-0.06454919,-0.01459001,0.02979423,-0.08014739,-0.07145363,0.00080091,-0.04373198,0.00046069,0.03477557,0.01121234,0.01028332,-0.02170627,0.06720048,0.00497398,0.09409462,-0.05768167,-0.01026644,0.00431959,0.24139974,-0.03066249,0.01764561,0.04833159,-0.01202825,-0.007985,0.04214462,0.0883612,0.05403646,0.0185706,0.08322353,-0.00824493,-0.00723423,-0.04137979,-0.04046803,0.00797772,0.02638845,-0.02006359,-0.00100641,-0.01372037,-0.00343531,0.01963173,0.02750963,-0.1020316,-0.05239333,-0.01557296,-0.04725254,-0.04159205,0.0087319,0.04106569,-0.00187178,-0.04582302,0.01034641,-0.00830062,-0.06800076,0.02488188,-0.02053789,0.0442183,0.04009879,-0.04995543,-0.01624009,0.0209371,0.0188314],"last_embed":{"hash":"fhrpo2","tokens":422}}},"text":null,"length":0,"last_read":{"hash":"fhrpo2","at":1751288826525},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#External styles","lines":[427,485],"size":1215,"outlinks":[{"title":"`resolve`","target":"#the-resolve-tag","line":49}],"class_name":"SmartBlock","last_embed":{"hash":"fhrpo2","at":1751288826525}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#External styles#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.00196363,-0.03293878,0.05193438,-0.07716783,0.05454341,0.01036829,-0.06881605,-0.05065805,-0.01993743,-0.03108709,-0.0075161,-0.0145475,0.01627323,0.01240561,0.00571031,0.03846264,0.00056623,0.07028961,-0.06508409,-0.03252106,0.01196715,-0.02425675,0.00675272,-0.05681688,-0.0169791,0.038343,-0.01073159,0.00482701,0.0312232,-0.18366908,-0.02019182,-0.05296965,-0.04362028,0.02761413,0.00934196,-0.02292984,-0.01236717,0.01614321,-0.02531861,0.05111283,0.03094211,0.03930186,-0.03954159,-0.02401972,-0.03098918,-0.03368263,0.02061151,0.03062855,-0.00898492,0.00299144,-0.00688983,-0.05472732,-0.01004291,-0.02661843,0.01542125,0.14999758,0.08230792,0.04860169,-0.00971004,0.05710781,-0.01520214,0.02729066,-0.15370627,0.09373662,0.03581924,0.02454338,-0.0463553,0.02618932,0.03931723,-0.00789075,-0.02392462,0.01757863,0.01763852,0.13881099,0.00739286,-0.06621984,0.01129013,-0.05943903,0.01305591,-0.04978485,-0.06921426,-0.01248196,-0.01059035,-0.01602601,0.05418607,0.0327083,0.03297305,-0.03202698,0.01729345,0.04487402,-0.06133658,-0.13843702,0.0692751,0.00249765,-0.01769128,-0.00504548,0.02894235,0.05754912,-0.01143037,0.1276613,-0.09669542,0.0021359,0.05540963,0.01400238,0.03435923,0.02591382,-0.00159343,-0.03473384,-0.06620852,0.01470882,-0.02465684,-0.05090551,-0.06638753,-0.07308779,-0.0759828,-0.04006413,-0.04607718,-0.01294752,0.04539683,-0.02928443,0.05587837,0.03808263,0.04409465,-0.01500444,0.02925278,0.04619691,0.00738462,0.01581768,-0.00204335,0.08169144,0.00694311,0.03998591,-0.02972975,0.00156812,-0.02835106,0.03509596,0.01307334,0.01909229,-0.0487142,0.02483642,0.01616612,-0.04359748,-0.02289878,-0.0390147,0.01573539,0.01988236,-0.04146536,0.0714223,-0.0055226,-0.02207291,-0.07220916,0.06526849,-0.08524112,0.06256453,-0.02655468,0.03124519,0.00491116,-0.00705141,-0.06417695,0.03462416,-0.00992387,-0.02371537,0.02398258,0.03779744,0.02563934,-0.07366904,-0.03023159,0.03693133,0.01999593,-0.0872076,-0.04286813,0.01712552,-0.01492375,0.00966825,0.06972614,0.02780782,-0.02772555,-0.04087459,0.05102584,0.0595279,0.10034364,-0.06940158,-0.03278033,0.02768452,-0.00420542,-0.0797151,0.00555507,-0.02533059,-0.04095323,-0.03207182,-0.03999338,0.02066071,0.00792379,-0.01626329,0.01903796,-0.00174072,-0.06614076,-0.03253185,0.05389316,-0.00124673,0.17587826,-0.00500865,-0.00082964,0.05064676,-0.0417641,0.05368059,0.00682702,-0.00221017,0.00781609,-0.03272162,-0.13708457,0.03078668,0.09267591,0.10448501,0.01495347,-0.0427819,0.02643637,0.06793179,0.04413233,0.05030115,-0.02678956,-0.02182668,-0.09099081,-0.19629274,0.03564714,0.04720163,-0.04054504,-0.00665936,-0.02937812,0.04475263,0.01234543,-0.01569316,0.07688009,0.12120962,0.01154734,0.00452156,0.00277565,-0.04306097,-0.03153663,0.03762104,-0.03821084,-0.03530359,-0.01346712,0.03020033,-0.02634092,-0.0619386,-0.07382276,0.02668289,0.01033207,0.15799567,0.02080355,0.00991218,-0.04606399,0.03297182,-0.01490927,-0.02771208,-0.08136519,0.05445957,0.01510548,0.01728677,-0.0232012,0.00330427,0.03415874,0.02412481,0.0018795,0.00586733,-0.0167984,0.07045553,-0.02953973,-0.06689809,-0.03896272,-0.02145822,0.03958806,0.00465988,0.02735557,0.0527301,0.09693228,-0.03322645,-0.01435051,-0.00671886,-0.03636813,0.00306037,0.01254661,-0.03847924,-0.02781142,0.01592858,-0.09328777,0.04496518,0.05967852,0.02284183,-0.02040564,0.0493791,-0.01991263,-0.04867985,0.10373526,0.03824352,0.01290456,0.00612943,0.01000861,-0.04116561,0.12714282,0.02678813,0.05428152,0.03807418,-0.00097225,0.08444443,-0.00715758,-0.01752118,-0.02441455,-0.03657382,-0.03628099,0.0320436,-0.01524778,-0.08853931,0.00384995,-0.02160995,0.03595186,0.02971059,-0.01102061,-0.22081967,-0.00544618,0.05108326,-0.03173576,-0.06794026,-0.01285332,0.02926283,-0.07910074,-0.0722627,0.00084091,-0.04596971,0.00073004,0.03361995,0.01173835,0.01239639,-0.02178869,0.0663438,0.00353194,0.09363431,-0.05864527,-0.01052955,0.00679572,0.24064803,-0.03068037,0.01537828,0.04956135,-0.01160085,-0.0057078,0.04313453,0.08800154,0.05240782,0.01592338,0.08417013,-0.00657343,-0.00666555,-0.04101076,-0.03963592,0.00756552,0.02600007,-0.02078555,0.00035427,-0.01182984,-0.00359485,0.01977597,0.03019633,-0.10236687,-0.05126256,-0.01501172,-0.04619568,-0.04226767,0.00623919,0.04202763,0.00107296,-0.04422199,0.01227369,-0.01114098,-0.06777591,0.02565474,-0.01936709,0.04366962,0.03935038,-0.04797624,-0.01592004,0.01910421,0.01820574],"last_embed":{"hash":"kx6eo0","tokens":419}}},"text":null,"length":0,"last_read":{"hash":"kx6eo0","at":1751288826717},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#External styles#{1}","lines":[429,485],"size":1193,"outlinks":[{"title":"`resolve`","target":"#the-resolve-tag","line":47}],"class_name":"SmartBlock","last_embed":{"hash":"kx6eo0","at":1751288826717}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles outside of components": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00538144,-0.00343821,0.03432022,-0.06150681,0.04161166,0.02079002,-0.05831804,-0.02736204,-0.01922273,-0.03040439,-0.00893218,0.03002845,0.01543587,0.01196064,0.00899512,0.00761092,0.01680643,0.07700787,-0.05448918,-0.01371115,0.01460156,-0.01503299,-0.00533694,-0.05466411,-0.0284642,0.03393612,-0.04769286,-0.00879635,0.02120433,-0.17880702,0.02625958,-0.03605782,-0.0212496,-0.0078541,0.0001378,-0.03781927,-0.00256547,0.02615835,-0.02059243,0.05182961,-0.00109015,0.06366467,-0.01682678,-0.02975333,-0.03677044,-0.0384061,0.0176008,0.00636363,-0.01264627,0.02108403,0.01531119,-0.02765965,0.02864185,-0.01851518,0.00977842,0.15490751,0.05720674,0.04548815,-0.01205682,0.03036732,-0.01124559,0.04010259,-0.13299391,0.09096484,0.03335452,0.0355649,-0.05642223,0.01360066,0.05491185,0.01578186,-0.02548956,0.0484677,-0.00599882,0.12097597,0.02521031,-0.07409127,0.01081072,-0.06396292,-0.00627418,-0.04499416,-0.08157936,-0.0493725,-0.00161128,-0.0100903,0.03182689,0.03435763,0.02775513,-0.04469745,0.00027518,0.05340559,-0.06370439,-0.11908834,0.06985352,-0.01083786,0.01760306,-0.04776716,0.04339559,0.08622262,-0.00929256,0.14254998,-0.10462497,0.01942356,0.04897788,-0.00174023,0.04549547,0.02182865,-0.00476151,-0.02621577,-0.06380398,0.01779179,0.01116648,-0.04270411,-0.10350515,-0.06526623,-0.04516972,-0.06474338,-0.02970874,-0.00723004,0.05997039,-0.04008036,0.02775258,0.04495392,0.01898615,-0.02852471,0.04292907,0.03039077,0.01838239,0.01580475,0.00774996,0.07507743,-0.01172498,0.03536062,-0.03303228,-0.01230749,-0.01265561,0.04470381,0.0255766,0.01390227,-0.03537516,0.01390065,0.0342926,-0.02175376,0.00661325,-0.05708648,0.05844205,0.01356026,-0.02921948,0.07150947,-0.01212685,-0.00413291,-0.07212143,0.07579242,-0.08104733,0.04983688,-0.03692684,0.00396107,-0.01210589,-0.03336952,-0.06169174,0.0230447,-0.00677273,-0.00534974,0.00679509,0.03793088,0.00771495,-0.0648258,-0.04250319,0.0588266,0.04353458,-0.10250025,-0.05206361,0.01243705,-0.00988751,0.00365729,0.06948911,0.02460367,-0.03363237,-0.02920792,0.0359254,0.05842757,0.11413254,-0.05291868,-0.03289512,0.05469625,-0.04240157,-0.09459189,0.02097863,-0.051425,-0.03647006,-0.00681061,-0.02351814,-0.01263212,0.00764304,0.01093573,0.00524537,-0.01145137,-0.05458258,-0.02257152,0.03324055,-0.00402429,0.15510651,-0.01210293,-0.0055833,0.05561395,-0.06011818,0.03030539,0.0207853,-0.01242173,0.02642877,-0.04237992,-0.14175995,0.01901728,0.10877788,0.09442668,-0.00483572,-0.03319754,0.04369566,0.08488312,0.03717757,0.02629077,0.0003577,-0.01866697,-0.09918355,-0.20062234,0.02311087,0.02995952,-0.05423414,-0.0323128,-0.03567844,0.03521757,0.01719068,-0.01784651,0.07150713,0.11434479,0.01026128,0.02321351,-0.00196635,-0.04804846,-0.00606117,0.03480105,-0.04203802,-0.04229189,-0.01496722,0.02194884,-0.00997397,-0.0525578,-0.07949188,0.04961585,0.0258531,0.13944961,0.00771766,0.04051989,-0.06545417,0.02616735,-0.02626903,-0.02480702,-0.06070273,0.04891434,0.02355295,0.02111832,-0.05691819,0.00270395,0.02723164,0.00011898,-0.0371241,0.0148366,-0.01603013,0.05861056,-0.04445804,-0.04626698,-0.0499563,-0.02582142,0.03060047,0.0017521,-0.00301805,0.03494705,0.09971888,-0.01716077,-0.01029222,-0.01395251,-0.06687683,0.00581511,0.00893334,-0.03994098,-0.00928384,0.01174443,-0.09179589,0.04560427,0.05208578,0.02114401,-0.01812887,0.04339977,-0.01839775,-0.06383348,0.13164711,0.03839466,0.0022378,0.01229996,-0.01796131,-0.02443741,0.13115302,0.01684403,0.03802379,0.0506648,-0.01585208,0.07614014,0.01608428,-0.02547545,-0.0334812,-0.04883521,-0.04572618,0.03099505,-0.02943478,-0.08176092,0.02876652,-0.04583,0.04159077,0.01989518,0.00656196,-0.21285991,0.00155754,0.0583205,-0.01138767,-0.05482103,-0.01027023,0.03576154,-0.07569986,-0.0445222,0.02122562,-0.03031534,0.0023989,0.02429713,-0.00130755,-0.00884953,-0.02002715,0.06358216,0.0167265,0.10744969,-0.04967596,0.00329887,-0.00753812,0.24722472,-0.04203633,0.04200816,0.05081711,-0.0237728,-0.0191787,0.03700117,0.09919287,0.0744067,0.03038503,0.06716547,-0.00203112,-0.0197827,-0.05824158,-0.01719579,0.00247199,0.02592817,-0.02524307,-0.00130286,-0.00917183,-0.00605782,0.03207007,0.01183853,-0.09776088,-0.05299991,-0.00429188,-0.02870026,-0.02799509,0.01224374,0.05589144,-0.00554008,-0.02643115,0.02878583,0.01622788,-0.0827176,0.01706958,-0.02563483,0.01663002,0.04099968,-0.04348195,0.00003213,0.01287749,0.0410083],"last_embed":{"hash":"1nljelu","tokens":222}}},"text":null,"length":0,"last_read":{"hash":"1nljelu","at":1751288826891},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles outside of components","lines":[486,508],"size":624,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1nljelu","at":1751288826891}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles outside of components#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00471644,-0.0037471,0.03379462,-0.06047454,0.04077431,0.01974684,-0.05717769,-0.02477242,-0.01991665,-0.03193479,-0.00825399,0.03195829,0.01369586,0.01424742,0.00941974,0.00880761,0.01543047,0.07826868,-0.05207714,-0.01359036,0.01448748,-0.01557655,-0.00139158,-0.05632604,-0.02962313,0.03523545,-0.04601296,-0.00737417,0.02098779,-0.17851029,0.02507173,-0.03550657,-0.02061788,-0.00834461,-0.00054078,-0.03921264,-0.00380128,0.02585471,-0.02125033,0.05082028,0.00029183,0.0629627,-0.01719032,-0.03065165,-0.03617783,-0.03634288,0.01752375,0.00443479,-0.01396973,0.0210829,0.01747298,-0.02895363,0.02930976,-0.01376945,0.01157275,0.154191,0.05839951,0.04582452,-0.00988112,0.03070695,-0.01312219,0.04027922,-0.13178426,0.09101734,0.03398724,0.0363737,-0.05764675,0.01600841,0.0547071,0.01771761,-0.02411959,0.04628477,-0.00775485,0.11828044,0.02848004,-0.07322799,0.00769302,-0.06382955,-0.00773801,-0.04473469,-0.08128674,-0.04914773,-0.00132202,-0.01238256,0.03108122,0.03292849,0.02843633,-0.04718933,-0.00046194,0.05497656,-0.06457661,-0.11943392,0.06761217,-0.01279412,0.01770607,-0.0462696,0.0428971,0.08635098,-0.00945966,0.14137705,-0.10573523,0.02061096,0.05040216,-0.00303766,0.04454701,0.02121616,-0.0031465,-0.02615697,-0.06356002,0.01706462,0.01081235,-0.04034001,-0.10597131,-0.06378908,-0.04771543,-0.06464434,-0.03088562,-0.00582451,0.05924344,-0.04143244,0.02924915,0.04550262,0.01782007,-0.03072932,0.04313952,0.03285502,0.01649964,0.01656772,0.00677197,0.0729724,-0.01190453,0.03165024,-0.03128684,-0.01130451,-0.01486947,0.04329127,0.02765485,0.01387066,-0.03671341,0.01284804,0.03445567,-0.02169996,0.00682235,-0.05919601,0.05815465,0.01237637,-0.02877251,0.07200637,-0.01481387,-0.00336155,-0.07155805,0.07460509,-0.07936418,0.04980599,-0.04045394,0.00422334,-0.01394605,-0.02967011,-0.06002766,0.02028108,-0.00581501,-0.0035789,0.00627837,0.03747819,0.0075188,-0.06595018,-0.04204732,0.05734701,0.04380854,-0.10350248,-0.05300592,0.01282007,-0.01133663,0.00274098,0.0718739,0.02359232,-0.0352708,-0.03038563,0.03482259,0.05859127,0.11516767,-0.05363948,-0.03415703,0.05531584,-0.04101353,-0.09682523,0.0203538,-0.05132847,-0.03715932,-0.00534681,-0.02487527,-0.01249198,0.00799036,0.01130508,0.00464576,-0.0126277,-0.05355281,-0.02500518,0.03502809,-0.00403645,0.15605962,-0.0115473,-0.00550432,0.05425458,-0.06055035,0.02916313,0.02178427,-0.01429479,0.02444035,-0.04114173,-0.14103894,0.01960643,0.10799636,0.0921788,-0.00317775,-0.03452714,0.04079118,0.08506585,0.03817632,0.02629061,-0.00124399,-0.01929454,-0.09961375,-0.20127793,0.02348938,0.02826616,-0.05406222,-0.03322325,-0.0345754,0.03587546,0.0161425,-0.01456081,0.07109389,0.11534067,0.01147684,0.02306946,-0.0038023,-0.04715287,-0.00445469,0.03805126,-0.03915496,-0.04026897,-0.01611394,0.01994832,-0.00951183,-0.0520374,-0.07822856,0.0501316,0.02667397,0.14102536,0.0056999,0.03948282,-0.06752721,0.02379511,-0.02580414,-0.02222002,-0.06060471,0.04879154,0.02306539,0.02204666,-0.05780281,-0.00027409,0.02595929,0.00074568,-0.03831926,0.01360978,-0.01796477,0.06052171,-0.04302742,-0.04567826,-0.05137338,-0.02716379,0.03332766,0.0024671,-0.00272104,0.03429041,0.10042547,-0.01912241,-0.00903369,-0.01488257,-0.06738354,0.00630631,0.00967119,-0.04183363,-0.00766055,0.01013962,-0.09068982,0.04610449,0.05202735,0.02263344,-0.01894741,0.04490935,-0.01834569,-0.06305753,0.13260056,0.03859092,0.00166345,0.01331319,-0.0181047,-0.02546365,0.1337699,0.01808821,0.03992246,0.05065677,-0.01490082,0.07837454,0.01853444,-0.02593396,-0.03483074,-0.04891968,-0.04440449,0.03059916,-0.02906911,-0.08087157,0.02952829,-0.04538658,0.04315944,0.01769708,0.00739964,-0.21218571,0.00407687,0.05956827,-0.01293268,-0.05429087,-0.00958655,0.03677358,-0.07438859,-0.0442777,0.02059123,-0.03129375,0.00310117,0.02339948,0.00085514,-0.00904298,-0.02100759,0.06232381,0.0159137,0.10369552,-0.04923366,0.00529422,-0.00705429,0.24678612,-0.04203227,0.04298598,0.04989165,-0.02262494,-0.01866463,0.03297058,0.09908741,0.07433105,0.03063622,0.0684633,-0.00217075,-0.0196489,-0.05887956,-0.0157978,0.00181409,0.02456204,-0.02769973,0.00025005,-0.00900384,-0.00678159,0.03173907,0.01232458,-0.09842647,-0.05309398,-0.00453525,-0.02722703,-0.02621389,0.01343734,0.0555668,-0.00700568,-0.02629436,0.03054517,0.01687066,-0.08286597,0.01591006,-0.02581519,0.01522589,0.04151414,-0.03964107,-0.00127171,0.01263769,0.04352207],"last_embed":{"hash":"55ft1o","tokens":219}}},"text":null,"length":0,"last_read":{"hash":"55ft1o","at":1751288826980},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles outside of components#{1}","lines":[488,508],"size":589,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"55ft1o","at":1751288826980}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#The `resolve` tag": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00666601,-0.01905898,0.04682367,-0.0581415,0.03089462,0.01151494,-0.06867836,-0.01351025,-0.01880651,-0.04359301,-0.00258176,-0.02466446,0.05804908,0.03993502,0.02513416,0.02685537,-0.00256921,0.10990759,-0.04812291,0.00012071,0.01091483,-0.00862316,0.01522782,-0.05621541,-0.02915277,0.04904588,-0.00467623,-0.02867828,0.03634339,-0.18722522,0.00356615,-0.03073717,-0.00859679,0.03238226,-0.00561004,-0.02693836,0.00792224,0.01056383,-0.00664129,0.04518076,0.02465051,0.04877246,-0.03915783,-0.02157067,-0.04421104,-0.01587774,0.01551742,0.02529493,-0.01857423,-0.00083418,-0.04073763,-0.02544602,-0.0111434,-0.00739452,0.01475583,0.18196939,0.08071406,0.03490881,0.00378448,0.04716331,-0.02727905,0.05270293,-0.15110309,0.07945788,0.04029934,0.04052734,-0.06214736,0.00441982,0.05022175,0.01632009,-0.00261401,0.03851531,0.02805665,0.12701698,-0.00633428,-0.04839874,0.01900411,-0.05105851,0.05965693,-0.06076666,-0.09002275,-0.02525462,-0.02975087,-0.03900094,0.05309879,0.03625051,0.04130236,-0.03634321,0.05450798,0.08539408,-0.04712956,-0.1256981,0.04670678,-0.00083804,-0.03262111,-0.04425013,0.00283675,0.04715307,0.01888104,0.12611428,-0.09678222,0.01615253,0.06473979,-0.00889129,0.02498868,0.0040239,0.03997542,-0.02092428,-0.07726968,-0.01008055,-0.01133488,-0.05645182,-0.04699612,-0.07700745,-0.06358247,-0.04545872,-0.05039181,-0.01495242,0.05695879,-0.04914658,0.03991322,0.03161815,0.02405069,-0.03686619,0.00528711,0.04856506,0.01943206,0.05567633,-0.00566817,0.08599832,-0.01293842,0.00816039,-0.02628633,-0.01902856,-0.04076703,0.04545669,0.01188708,-0.0171897,-0.04697135,0.03414516,0.01033882,-0.07565726,-0.03229472,-0.03041464,-0.02039641,0.01983041,-0.05129742,0.10000113,-0.02386393,-0.00424196,-0.07500718,0.08340599,-0.08870529,0.03953533,-0.05986194,0.04973514,0.00250186,-0.02375701,-0.03993562,0.05653884,-0.02673926,-0.01985089,0.00124368,0.01637064,0.03267393,-0.08570477,-0.05869589,0.03694456,0.01388152,-0.09400534,-0.06467771,0.01886183,-0.00488933,0.00040954,0.05702104,0.03538529,-0.05185831,-0.02575381,0.06640526,0.09079037,0.0887628,-0.07856809,-0.03529648,0.02979498,-0.03597308,-0.0907711,-0.00141653,-0.02615532,-0.02195801,-0.06898098,-0.05075841,-0.02971074,0.00314608,-0.01646073,0.02990349,-0.00952552,-0.04844407,-0.05398905,0.06560911,0.01023564,0.1658832,-0.01040462,-0.0008759,0.07242025,-0.05807289,0.04049493,0.01605819,-0.00336328,-0.01138869,-0.05587182,-0.11562139,0.02996721,0.08097091,0.1030998,0.01157942,-0.06311982,0.01621341,0.03824081,0.02262113,0.07257146,0.00506841,-0.00558012,-0.09292529,-0.19094993,0.03576801,0.04253763,-0.02447307,-0.03093619,-0.0549449,0.02371717,0.00160515,-0.04846099,0.06566285,0.08451372,0.02473834,0.02545666,-0.02692694,-0.03700291,-0.04050489,0.03573984,-0.00738036,-0.03894846,-0.00699095,-0.00452226,-0.04310604,-0.03823759,-0.08132152,0.0580245,0.01439718,0.15262252,0.05234233,-0.01580166,-0.02261094,0.05049047,-0.02498574,-0.03442479,-0.0493212,0.05032594,0.04692473,0.01808483,-0.03986159,-0.00568747,0.0370721,0.01153958,0.00711555,-0.00530068,-0.01455763,0.03072584,-0.04260319,-0.05497157,-0.05682372,0.00353724,0.03798063,-0.01133301,0.03790374,0.01498555,0.09651911,-0.02132984,0.00823073,0.00373335,-0.04220191,-0.00079652,-0.00093315,-0.02846622,-0.03170537,0.00252878,-0.07431465,0.04208235,0.05933298,0.02925661,-0.0384575,0.01815067,-0.01992614,-0.02402674,0.10609934,0.02193941,0.01211347,0.00475019,-0.00164757,-0.01766172,0.10569707,0.03466987,0.04585364,0.02763497,-0.00571068,0.0766823,-0.01293132,-0.02425129,-0.00406984,-0.0033976,-0.05423306,0.02533212,-0.02261224,-0.06191573,0.0298865,-0.02973777,0.01575764,0.01727676,-0.00847358,-0.23135248,0.02057507,0.05246101,-0.03310624,-0.04882662,-0.01419351,0.05097025,-0.06796601,-0.05591375,-0.0154113,-0.02205278,0.01394694,0.01572617,0.02562753,0.00502968,0.00003057,0.06266818,-0.00316754,0.07355139,-0.09432488,0.00448603,0.01856821,0.2289023,-0.03177322,0.01447065,0.03861075,-0.01513315,-0.00335875,0.04302058,0.08285988,0.0228642,0.036993,0.06226423,-0.00463707,0.00852705,-0.03370773,-0.025566,0.01192933,0.02602289,0.01716761,0.01827347,0.00028999,-0.01221768,-0.01168048,0.04450075,-0.07584336,-0.03669205,-0.0138733,-0.01279689,-0.01788188,0.01582314,0.06077597,-0.00029301,-0.03772028,0.02977043,0.01441497,-0.07897184,0.00848668,-0.01910917,0.03948292,0.01639446,-0.00152713,0.00207177,0.04852727,0.01957727],"last_embed":{"hash":"94lkd8","tokens":365}}},"text":null,"length":0,"last_read":{"hash":"94lkd8","at":1751288827069},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#The `resolve` tag","lines":[509,559],"size":1083,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"94lkd8","at":1751288827069}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#The `resolve` tag#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00502924,-0.01878985,0.04598022,-0.05702738,0.03143273,0.01351148,-0.06549089,-0.01259643,-0.01934415,-0.04435122,-0.00271968,-0.02474032,0.05824957,0.04165986,0.02566294,0.02937317,-0.00164841,0.11072869,-0.04938506,-0.00190294,0.00874501,-0.00917291,0.01445096,-0.05492181,-0.02729791,0.04712319,-0.0049294,-0.02859722,0.03495239,-0.18744028,0.00203633,-0.03312222,-0.00753245,0.03389506,-0.00676464,-0.02983974,0.00579123,0.01349453,-0.00944337,0.0455927,0.02827352,0.04783547,-0.03939587,-0.0205055,-0.04475106,-0.01414561,0.01465624,0.02574177,-0.01974381,-0.00148827,-0.03941307,-0.02584586,-0.01279962,-0.00607467,0.01476962,0.18166283,0.08066864,0.03459169,0.00352625,0.04565191,-0.02868224,0.05180023,-0.15034662,0.08162595,0.03767615,0.03721027,-0.06159053,0.00136825,0.04808392,0.01557568,-0.00116031,0.03701516,0.02719524,0.12682511,-0.00824978,-0.04672064,0.02043082,-0.05192735,0.06038791,-0.0593291,-0.0918396,-0.02540499,-0.03036611,-0.04020938,0.05478917,0.0357572,0.0434705,-0.03642387,0.05890786,0.08552227,-0.04462918,-0.12751126,0.04606427,-0.00241646,-0.03527332,-0.04616091,0.00183166,0.05041496,0.01897364,0.12616186,-0.09640781,0.01795111,0.06448515,-0.01016221,0.02547011,0.00326739,0.03889406,-0.02039528,-0.07606587,-0.01067416,-0.0129721,-0.05442872,-0.04373973,-0.07712937,-0.06428158,-0.04435021,-0.05249176,-0.01515227,0.05671939,-0.05115523,0.04112924,0.0307273,0.02450263,-0.03550319,0.00457075,0.04933672,0.02024506,0.05687672,-0.00871699,0.08561698,-0.01326728,0.00778027,-0.02590249,-0.01754888,-0.04283226,0.04746362,0.01135762,-0.01685097,-0.04994502,0.03314626,0.01048806,-0.07614455,-0.03061228,-0.0301096,-0.02181157,0.01773525,-0.05319963,0.09964956,-0.02534607,-0.00416408,-0.07565701,0.08458886,-0.08808078,0.04059874,-0.05956095,0.05337088,-0.0005245,-0.02164876,-0.04010615,0.05752712,-0.02931193,-0.0200339,0.00176909,0.01376861,0.03409217,-0.08454778,-0.0563007,0.0364842,0.0116909,-0.09318963,-0.06426354,0.01876232,-0.00693134,-0.00336666,0.05507297,0.03548205,-0.05051126,-0.02646765,0.06572003,0.09005276,0.08732235,-0.07953516,-0.03594127,0.02980631,-0.03395145,-0.09093826,-0.0006474,-0.02588377,-0.02246337,-0.06973167,-0.05044192,-0.02881994,0.00447438,-0.01864018,0.03063361,-0.00911427,-0.04846084,-0.05554563,0.06679397,0.01053142,0.16486306,-0.0111618,-0.00134752,0.07297648,-0.05659828,0.04098764,0.01597291,-0.00558953,-0.01120693,-0.05518606,-0.11296192,0.02902976,0.07982796,0.10293292,0.01141546,-0.06310467,0.01499979,0.03468131,0.02135494,0.07492948,0.00438972,-0.00533043,-0.09280746,-0.19069962,0.03507295,0.04505282,-0.02330954,-0.02904719,-0.05477215,0.02287911,0.00275256,-0.04931617,0.06417849,0.08318208,0.02469818,0.02442151,-0.02827794,-0.03468659,-0.04042688,0.03574072,-0.00658073,-0.03989879,-0.00689463,-0.00219442,-0.04598793,-0.03709246,-0.08231124,0.05953869,0.01553032,0.15447117,0.05414376,-0.01661872,-0.02137546,0.05294021,-0.02493833,-0.03424781,-0.04811188,0.05223688,0.04613131,0.01918611,-0.03836291,-0.00623837,0.0363734,0.01236393,0.00910332,-0.00787263,-0.01476653,0.03343466,-0.04247133,-0.05402849,-0.05737566,0.00406021,0.03786587,-0.01073676,0.03955874,0.01630075,0.09626921,-0.02100397,0.01028111,0.00562265,-0.0429249,0.00018408,-0.00287332,-0.0263278,-0.03351901,0.0036955,-0.07539938,0.04287873,0.05708594,0.02825317,-0.04056375,0.01598604,-0.01822758,-0.02506673,0.10693123,0.02142419,0.01269807,0.00576214,-0.00015443,-0.01770042,0.10474256,0.03539628,0.04812843,0.02576813,-0.00393545,0.07978693,-0.01418673,-0.02492017,-0.00258908,-0.00431254,-0.05288329,0.02617062,-0.02115312,-0.06228817,0.03175597,-0.03020734,0.01599182,0.01390577,-0.0084966,-0.22980154,0.02102772,0.05110045,-0.03252463,-0.05020656,-0.01223256,0.0494714,-0.06665918,-0.05745342,-0.01437221,-0.02182346,0.01430409,0.01574732,0.02642732,0.00434648,-0.00057489,0.06253011,-0.00255168,0.07072284,-0.09195169,0.00329896,0.01923554,0.23012301,-0.03033229,0.01220574,0.03664419,-0.01239667,-0.00428035,0.04333518,0.08152215,0.02012926,0.03490311,0.0634171,-0.00531117,0.00961921,-0.03454436,-0.02555536,0.01190775,0.02425305,0.01968452,0.0180161,0.00298703,-0.01313818,-0.01274268,0.04454034,-0.07606096,-0.03814271,-0.01363026,-0.01278772,-0.01926602,0.01610375,0.06011857,0.00111167,-0.03821427,0.02969298,0.01222033,-0.0792929,0.00846099,-0.0173496,0.03808187,0.01555287,0.0004377,0.00120552,0.04785822,0.01916601],"last_embed":{"hash":"5rgpqe","tokens":362}}},"text":null,"length":0,"last_read":{"hash":"5rgpqe","at":1751288827206},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#The `resolve` tag#{1}","lines":[511,559],"size":1059,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"5rgpqe","at":1751288827206}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Using `resolve` as a Babel macro": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00196924,-0.02592014,0.06319626,-0.07912175,0.00867422,-0.00929835,-0.07758106,-0.03273479,-0.01229881,-0.0378732,-0.00858007,-0.02287646,0.08974631,0.00756152,0.00955241,0.03601744,-0.02552582,0.09688474,-0.0621969,0.01514237,0.03054047,0.00007776,0.01306505,-0.04054977,-0.06014344,0.04083246,-0.00861201,-0.02359371,0.02991464,-0.19665544,0.02476888,-0.0001291,-0.02843675,0.02159449,0.00640388,-0.01031179,0.00824738,0.01845646,-0.01684261,0.03984138,0.0181438,0.02029888,-0.00595001,-0.04216802,0.01241402,-0.01865246,0.00807759,0.03326548,-0.00957386,-0.00870252,-0.04476646,-0.03372975,-0.00134283,-0.03437486,0.01429112,0.14652088,0.07547613,0.04458956,0.06621514,0.02001129,0.00834329,0.02149853,-0.16942011,0.10504196,0.0101353,0.01796952,-0.02923102,-0.01023354,0.05833576,0.03179543,-0.0234778,0.04327962,0.02357852,0.08899743,0.01365285,-0.07102566,0.03940278,-0.06704817,0.07185435,-0.04446313,-0.06473801,-0.01877514,-0.02213646,-0.04515775,0.03525877,-0.0080737,0.03530465,-0.01307093,0.05949658,0.03527387,-0.04512265,-0.11760022,0.05315599,0.00580253,-0.03166455,-0.08078544,-0.00921108,0.03866324,-0.0005748,0.13091888,-0.0741628,0.02704781,0.05263446,-0.01999302,0.06421019,-0.00845772,0.01087178,-0.01864315,-0.01602471,-0.01753761,-0.01638727,-0.01742716,-0.04883921,-0.05833966,-0.03760108,-0.05049268,-0.02329705,-0.01306596,0.00930467,-0.02043942,0.03383812,0.03156394,0.06163178,-0.00590251,0.0287517,0.0563313,-0.00072361,0.0550301,-0.00604636,0.01035385,-0.01803857,0.00084592,-0.0230056,-0.03029582,-0.01415571,0.05424647,-0.0141499,-0.01495859,-0.05165992,0.0415893,0.03082574,-0.04200125,-0.01178253,-0.01307566,-0.00310063,-0.00485069,-0.01679785,0.05727545,-0.0265551,-0.03404843,-0.08149937,0.08904832,-0.10656801,0.03066139,0.00595357,0.03482382,0.0236325,-0.04384135,-0.04686749,0.04493085,-0.02293358,-0.02193237,-0.00911134,0.06611788,0.03254615,-0.07953149,-0.0510447,0.04721786,0.00151057,-0.10282136,-0.04104793,0.01848431,-0.01252998,-0.01696522,0.04028828,0.05268303,-0.02473045,0.00850797,0.07720296,0.03737391,0.04385426,-0.06216211,-0.04348726,0.02834119,-0.02817656,-0.08882512,0.0075332,0.00364087,-0.02955861,-0.05860959,-0.04717587,0.02011438,0.02068625,-0.03313585,0.02078981,-0.0175293,-0.04753076,-0.02204111,0.05526165,0.01180513,0.14782999,0.00452164,0.0023654,0.03786953,-0.03980251,0.0510217,0.0136511,-0.04271787,0.06782981,-0.07608838,-0.16182488,0.06622083,0.10675152,0.08650152,-0.0288299,0.02155058,-0.00735215,0.02277934,0.01752161,0.03413036,0.02490787,0.03401964,-0.07770534,-0.21979275,0.03858761,0.09825163,-0.05657705,0.00224868,-0.057403,0.0348991,-0.0194447,-0.04057635,0.06865387,0.07675184,-0.00661645,0.04976675,0.00731997,-0.03153842,-0.02120072,0.0383484,-0.01539056,-0.06619491,-0.02116785,-0.02390958,-0.06999078,-0.03526139,-0.07960255,0.06327895,-0.02038145,0.14817935,0.04334794,0.01948213,-0.04663149,0.05801393,-0.02123575,-0.03588397,-0.12390371,0.03370006,0.02807469,0.04185792,-0.00046472,0.00558655,0.04582358,0.03820097,0.0055817,-0.02665721,-0.0646816,0.03571062,-0.04286296,-0.03945873,-0.03954206,-0.02131426,0.05819835,-0.00804424,0.01829539,0.02265208,0.0692837,-0.00940766,-0.0186746,-0.00564425,-0.05611534,0.01045188,-0.02020391,-0.00540882,-0.02842217,0.00331179,-0.06986047,0.02806971,0.03661008,0.05015255,-0.04221013,0.02782182,-0.02798415,-0.05919657,0.06953891,0.03370201,0.04752214,-0.00250693,-0.00096154,-0.09521012,0.08765229,0.02585998,0.02307408,0.0146129,-0.03649603,0.0416606,-0.0017628,-0.04126948,0.02053781,0.01999937,-0.01159528,0.02051352,-0.0149352,-0.08674262,-0.01102479,-0.03100404,-0.01109247,0.03968517,-0.01109885,-0.21162367,0.04437725,0.04907394,-0.03851876,-0.04934926,-0.03465376,0.05451157,-0.05264411,-0.04915583,-0.02510327,-0.0124029,0.04467053,0.02485311,0.03766283,0.02628787,0.01020196,0.04513652,0.02393628,0.06254464,-0.09651425,0.0066003,0.01239524,0.23262778,-0.05034241,-0.00582865,0.0664691,-0.02923512,0.00110206,0.04459308,0.0892399,-0.01279865,0.06622332,0.06179931,-0.01418057,-0.00335967,-0.01519873,-0.0403373,0.00683975,0.06227547,0.05533848,-0.00926918,0.01630964,-0.01760242,-0.01375331,0.03674193,-0.06030194,-0.03708567,0.03396605,-0.01616283,-0.06044885,0.01990996,0.03262324,0.00307341,-0.02741871,0.02619898,0.0073233,-0.06667462,0.0499155,-0.0533927,0.06306701,0.0352198,-0.04947649,0.01268048,0.05821961,0.01912282],"last_embed":{"hash":"12f16s8","tokens":461}}},"text":null,"length":0,"last_read":{"hash":"12f16s8","at":1751288827341},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Using `resolve` as a Babel macro","lines":[560,605],"size":1323,"outlinks":[{"title":"`babel-plugin-macros`","target":"https://github.com/kentcdodds/babel-plugin-macros","line":3},{"title":"`create-react-app`","target":"https://create-react-app.dev","line":37},{"title":"Create React App","target":"https://create-react-app.dev","line":39},{"title":"Using `resolve` as a Babel macro","target":"https://github.com/vercel/styled-jsx/blob/main/readme.md#using-resolve-as-a-babel-macro","line":45}],"class_name":"SmartBlock","last_embed":{"hash":"12f16s8","at":1751288827341}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Using `resolve` as a Babel macro#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0058714,-0.01941594,0.0578165,-0.08604112,0.02549779,-0.0186698,-0.0696561,-0.03319008,-0.00663377,-0.03859147,-0.00091414,-0.01303477,0.06815886,0.00560674,0.01734016,0.04009586,-0.02094736,0.10804757,-0.05839421,0.00902883,0.02955731,-0.00137788,0.00834373,-0.03958675,-0.05001929,0.03938955,-0.00264344,-0.0293065,0.02616542,-0.20400216,0.03351149,-0.00265025,-0.03044028,0.03128543,0.01127718,-0.01587548,0.00894948,0.01440076,-0.0120193,0.03639802,0.03613405,0.01559174,-0.0103072,-0.04362096,0.01550843,-0.01438616,0.01726823,0.0307901,-0.01294198,-0.01708608,-0.04607537,-0.04001199,-0.00194346,-0.02807029,0.00730295,0.14215945,0.07265835,0.03655211,0.06652004,0.01030534,0.00417802,0.02546662,-0.18189724,0.11243196,-0.00447656,0.00971656,-0.02223969,0.00181413,0.03843952,0.05602747,-0.00847109,0.05063787,0.02151695,0.08130924,0.01387461,-0.06318174,0.03324038,-0.07878785,0.06270061,-0.03834775,-0.07428165,-0.00267871,-0.01731324,-0.06383006,0.0253036,-0.00465554,0.03747689,-0.01190271,0.06474489,0.02849069,-0.04822457,-0.12247986,0.0486961,0.00400869,-0.03197942,-0.0759853,-0.01009717,0.03639757,-0.00003179,0.13361076,-0.07528409,0.03391897,0.05922371,-0.02180205,0.06635301,-0.00554508,0.00387992,-0.02106309,-0.01328063,-0.02171541,-0.02545131,-0.01407282,-0.06495017,-0.0495577,-0.03537037,-0.05779908,-0.01942639,-0.00812384,0.0105982,-0.02132011,0.0287597,0.02237634,0.05696106,-0.00492534,0.04021445,0.04966125,0.00816327,0.06889567,0.00695291,0.00796613,-0.02042579,0.00417565,-0.02961483,-0.03354686,-0.01940892,0.04206996,-0.00578369,-0.0188686,-0.05963353,0.01994563,0.03583909,-0.04045604,-0.01619441,-0.00569766,-0.00646073,0.00480216,-0.01404556,0.04779632,-0.01895259,-0.03798562,-0.07714283,0.08633299,-0.09354515,0.03159197,0.0076706,0.04217789,0.02257396,-0.02923744,-0.05278812,0.04079562,-0.01436708,-0.03217626,-0.02898235,0.08522011,0.03913677,-0.07560924,-0.05083802,0.03079569,-0.0048222,-0.10647818,-0.04217999,0.00895456,-0.00898232,-0.02141992,0.03370352,0.05781838,-0.02815363,0.01022858,0.06891052,0.03477944,0.03742249,-0.05812672,-0.03583076,0.04065631,-0.01765456,-0.07814465,0.01295118,0.00031839,-0.03238506,-0.05099149,-0.04999465,0.02360732,0.02662844,-0.04458179,0.0194048,-0.02418113,-0.04279599,-0.0115647,0.05428473,0.00639539,0.15182818,0.01315181,0.00562066,0.04139108,-0.02916454,0.0465204,0.00539902,-0.05271785,0.05800897,-0.0680297,-0.16090083,0.06330466,0.09859469,0.07681997,-0.02708312,0.02714261,-0.00567226,0.02851396,0.00749296,0.030755,0.00906624,0.0316384,-0.08093753,-0.22787108,0.0270372,0.10533763,-0.05980749,0.01369416,-0.04194093,0.03102341,-0.01260899,-0.04056139,0.05733463,0.06887206,-0.00203867,0.0359241,-0.01215152,-0.03244813,0.00501538,0.03545174,-0.0318174,-0.05764526,-0.02494519,0.000733,-0.06466255,-0.04817373,-0.07640043,0.05949273,-0.00903913,0.14229263,0.04040387,0.03817763,-0.04157774,0.0527376,-0.01875635,-0.03087874,-0.11472944,0.04006775,0.03536179,0.04063407,0.00161038,0.00017802,0.04606809,0.05240299,0.01752808,-0.03766382,-0.07386318,0.02543402,-0.03718844,-0.02001535,-0.02668194,-0.02365329,0.05180436,-0.01414151,0.01690425,0.0154571,0.08414958,0.00875095,-0.02304857,-0.01780603,-0.07227851,0.01872323,-0.01596866,0.00736379,-0.03366071,-0.00019004,-0.07482508,0.03920176,0.03922884,0.04637953,-0.04229736,0.04632552,-0.05242838,-0.05795762,0.05419723,0.04236162,0.03313072,0.01043872,0.01308358,-0.10366873,0.0835869,0.01781481,0.02566397,0.02283387,-0.03522068,0.03914333,-0.01160339,-0.03995179,0.02098739,0.00309481,0.00155959,0.0280249,-0.01743141,-0.08703223,-0.01091794,-0.04059509,-0.00368288,0.05840365,-0.01127741,-0.21581146,0.05292444,0.05176117,-0.03084813,-0.05842829,-0.01887439,0.04964829,-0.05781583,-0.05701762,-0.02075501,-0.00710065,0.05070121,0.0160155,0.03507528,0.01187166,0.00869982,0.03736402,0.01135436,0.06265635,-0.07840553,0.01269712,0.02255642,0.23404165,-0.06057923,-0.01187739,0.05712457,-0.03156448,-0.0020954,0.03831582,0.08217291,-0.00472351,0.06633949,0.07784545,-0.01574332,0.00432872,-0.01576045,-0.04247511,-0.0046762,0.06820627,0.05655074,-0.00186486,0.02000105,-0.02977885,-0.02721737,0.04266141,-0.03822593,-0.03121338,0.04475078,0.00311743,-0.06540833,0.01050836,0.0316139,0.00665122,-0.00964584,0.02526316,0.00178945,-0.06792678,0.04613056,-0.05627399,0.0637818,0.03034836,-0.04869205,-0.01214816,0.04533949,0.01994992],"last_embed":{"hash":"1hh7i4i","tokens":337}}},"text":null,"length":0,"last_read":{"hash":"1hh7i4i","at":1751288827537},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Using `resolve` as a Babel macro#{1}","lines":[562,595],"size":766,"outlinks":[{"title":"`babel-plugin-macros`","target":"https://github.com/kentcdodds/babel-plugin-macros","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1hh7i4i","at":1751288827537}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Using `resolve` as a Babel macro#Usage with [`create-react-app`](https://create-react-app.dev)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00252329,-0.03376837,0.07857588,-0.0795931,-0.01056138,-0.00001817,-0.09163771,-0.03351355,-0.02080128,-0.03610025,-0.00883865,-0.0186041,0.08982257,0.01348548,0.00878449,0.03070477,-0.01946601,0.06414422,-0.04030711,0.02392471,0.03614847,0.01092811,0.00574334,-0.03715386,-0.06448084,0.05259871,-0.02374837,-0.01256506,0.03503345,-0.18040089,0.02149575,0.01170978,-0.02494981,0.00329452,0.02706939,-0.01817844,0.00343813,0.03582976,-0.03514367,0.03915069,0.0064602,0.03597249,-0.00562536,-0.02503586,0.01715383,-0.02346707,0.00022724,0.03947173,-0.00331392,-0.00792937,-0.0420188,-0.02759218,0.02010616,-0.04589216,0.01577736,0.14612746,0.08111187,0.05909418,0.04823449,0.01822844,0.01846343,0.01001792,-0.16148461,0.08262859,0.03009222,0.03398072,-0.03324579,-0.02675535,0.06896847,0.00427259,-0.02084723,0.02842801,0.02834683,0.0838537,0.01388969,-0.06561602,0.05175196,-0.06281123,0.06574368,-0.03766308,-0.06965008,-0.02379224,-0.01596192,-0.00301686,0.02249322,-0.00840568,0.03062618,-0.00339763,0.07148578,0.03500018,-0.04357853,-0.10705467,0.04976271,0.02219814,-0.01716738,-0.06646283,-0.0038784,0.03924629,0.01424161,0.13722003,-0.07407118,0.02920307,0.04841392,-0.01361582,0.06241636,-0.00649193,0.00651277,-0.00906734,-0.02411701,-0.01173543,-0.00570928,-0.01484371,-0.04932814,-0.0622118,-0.03269134,-0.0588082,-0.00446401,-0.00610399,0.00253796,0.00452129,0.03602534,0.04828666,0.05938958,-0.00698736,0.01417709,0.03835507,0.00554523,0.04123435,-0.01572952,0.02225236,-0.01235819,0.01639413,-0.02958594,-0.03651368,-0.00557241,0.04082897,-0.02405377,-0.02486023,-0.03081704,0.07144868,0.02134638,-0.03743076,0.00249256,-0.02530172,0.0173711,-0.00929005,-0.01302247,0.07379198,-0.02272325,-0.01225391,-0.0830024,0.09401434,-0.1019449,0.02835561,0.00402938,0.006948,0.01175576,-0.0421789,-0.03167091,0.03997415,-0.01454824,-0.01709422,0.00869357,0.0645704,0.02758202,-0.0808757,-0.04775773,0.07496848,0.01258995,-0.09392769,-0.02625537,0.01866655,-0.02201391,-0.01031853,0.03584393,0.04921651,-0.0309089,0.00027693,0.0765829,0.03866062,0.05611035,-0.07526713,-0.04014092,0.02735902,-0.05077258,-0.10067348,0.01817569,-0.00121905,-0.03339709,-0.04713101,-0.03119006,0.02148792,0.00179787,-0.02289729,0.01256032,-0.01726683,-0.03416934,-0.01933586,0.05068754,0.01375974,0.13521478,0.0069611,-0.0151623,0.04321792,-0.04721283,0.0659482,0.01413647,-0.02280524,0.07581286,-0.06393051,-0.15896349,0.04136831,0.11327337,0.09553777,-0.03771592,0.01134575,-0.00310556,0.0296969,0.02276587,0.02375704,0.02054115,0.01613237,-0.07019578,-0.22344951,0.05298205,0.08670191,-0.06572293,-0.01325426,-0.06282987,0.0384395,-0.02961072,-0.04155329,0.07442585,0.08644204,-0.01129057,0.05269822,0.02110546,-0.03399527,-0.04597193,0.02625819,-0.00986507,-0.09109468,-0.02516451,-0.03899385,-0.07657817,-0.03879064,-0.07237922,0.06516109,-0.02485137,0.14149366,0.05299594,0.01719109,-0.06618091,0.06393008,-0.01554659,-0.03279579,-0.13508986,0.01719188,0.02266622,0.05844868,-0.00443809,0.02046924,0.02956787,0.01303899,-0.01826752,-0.02546517,-0.0540812,0.05661666,-0.03962713,-0.05593818,-0.04850413,-0.01635324,0.06611466,-0.00921336,-0.00055319,0.03575952,0.07084721,-0.02762374,-0.01141499,-0.00823286,-0.04662476,0.00555646,-0.0113631,-0.00032773,-0.00129835,-0.00347325,-0.0653508,0.01713554,0.03419117,0.03722877,-0.04637787,0.01271793,-0.01580473,-0.06853134,0.07162586,0.02031582,0.02058628,-0.00985747,-0.01618953,-0.07410891,0.07396942,0.01959073,0.01486871,0.01237351,-0.05896052,0.03706204,-0.00192066,-0.04261332,0.02297619,0.02136449,-0.03413891,0.0098146,-0.01930748,-0.09163429,-0.01289761,-0.02499138,-0.01193412,0.03831194,-0.01823005,-0.21010409,0.03581072,0.03580257,-0.02748225,-0.04182489,-0.04371822,0.0601393,-0.04366963,-0.04305323,-0.02674022,-0.00325858,0.03572695,0.03616299,0.02558592,0.03489304,0.01186211,0.07306602,0.02822906,0.06441239,-0.10730932,0.00739987,-0.00403197,0.23643993,-0.04015464,-0.00746933,0.08024295,-0.03606492,0.00164796,0.04259112,0.09362625,-0.01384469,0.05727696,0.06483378,-0.00212765,-0.02117181,-0.01148585,-0.02522881,0.0134933,0.06978609,0.05685744,-0.00841279,-0.00542659,-0.01888994,0.01592347,0.02747908,-0.07664651,-0.06445151,0.00663729,-0.02922844,-0.05455429,0.01361291,0.02120903,-0.00366678,-0.03622575,0.0264898,0.01440018,-0.07330502,0.03833312,-0.05525804,0.07205406,0.03892073,-0.04502938,0.03750227,0.05891138,0.01602452],"last_embed":{"hash":"1r06ggr","tokens":237}}},"text":null,"length":0,"last_read":{"hash":"1r06ggr","at":1751288827640},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Using `resolve` as a Babel macro#Usage with [`create-react-app`](https://create-react-app.dev)","lines":[596,605],"size":517,"outlinks":[{"title":"`create-react-app`","target":"https://create-react-app.dev","line":1},{"title":"Create React App","target":"https://create-react-app.dev","line":3},{"title":"Using `resolve` as a Babel macro","target":"https://github.com/vercel/styled-jsx/blob/main/readme.md#using-resolve-as-a-babel-macro","line":9}],"class_name":"SmartBlock","last_embed":{"hash":"1r06ggr","at":1751288827640}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Using `resolve` as a Babel macro#Usage with [`create-react-app`](https://create-react-app.dev)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00220108,-0.03483893,0.07669061,-0.08017192,-0.01099719,0.00166689,-0.08809324,-0.03247654,-0.02533384,-0.03501598,-0.01008645,-0.01650734,0.08948834,0.01615599,0.00574456,0.02796119,-0.02185253,0.05994634,-0.03759883,0.02248617,0.03938303,0.01355108,0.00472992,-0.03706143,-0.06342693,0.05109844,-0.02467306,-0.01158595,0.0333242,-0.17925335,0.02218769,0.01092332,-0.02635408,0.00451688,0.02099552,-0.01788031,0.00295938,0.03455179,-0.0360657,0.04200811,0.00578491,0.03968682,-0.00280725,-0.02209112,0.01332876,-0.02370178,-0.00235722,0.03932632,-0.00612087,-0.00250995,-0.04207601,-0.02867522,0.01911442,-0.04361147,0.01621975,0.14550911,0.07879954,0.0605481,0.04700727,0.02101604,0.02077248,0.00707506,-0.16184488,0.0816241,0.0270505,0.03465071,-0.03286281,-0.02583653,0.06921691,0.00434217,-0.02373564,0.02643891,0.03154404,0.08586629,0.01818772,-0.06365792,0.05022763,-0.06319034,0.0645085,-0.03942977,-0.07016635,-0.02600779,-0.01596573,-0.00297692,0.02298997,-0.00897687,0.03357736,-0.00562917,0.07322524,0.03291174,-0.04539616,-0.10585731,0.0520542,0.02527617,-0.01686771,-0.06436227,-0.00336266,0.04208062,0.01488191,0.13793138,-0.0733906,0.031252,0.04412767,-0.01404212,0.06200304,-0.00563687,0.00660038,-0.00803979,-0.02391279,-0.00804392,-0.00516073,-0.01483626,-0.05232053,-0.06367544,-0.03336709,-0.06005742,-0.00474143,-0.00568941,0.00157124,0.00561973,0.03716063,0.04787661,0.05831884,-0.00412412,0.01160861,0.040681,0.01039644,0.04135725,-0.0176413,0.02171641,-0.01413658,0.01412059,-0.03255612,-0.03320984,-0.00216257,0.04084935,-0.02322782,-0.02434954,-0.03143743,0.0763716,0.02210124,-0.03540301,0.00471852,-0.02850067,0.02199607,-0.00742924,-0.01039779,0.07317176,-0.02187461,-0.0129247,-0.08100273,0.09498521,-0.09944645,0.02721573,0.00563371,0.0035769,0.00895886,-0.04110635,-0.0280215,0.03744552,-0.01443737,-0.01638991,0.00967497,0.06335324,0.02257804,-0.07949159,-0.04695788,0.07784779,0.00966177,-0.08897615,-0.02608929,0.01962092,-0.02147974,-0.01569881,0.03597202,0.04967036,-0.03174302,0.00045775,0.07761795,0.03867896,0.05401627,-0.07242347,-0.04100278,0.02361631,-0.05093617,-0.10005438,0.01947553,0.00037754,-0.03087692,-0.04439115,-0.02993217,0.019743,-0.00045434,-0.02252693,0.0110541,-0.01541361,-0.03581771,-0.02013469,0.04877845,0.01071503,0.13779305,0.01250309,-0.01576694,0.0458264,-0.04856797,0.06636159,0.011932,-0.02366482,0.07233064,-0.0637081,-0.15635793,0.04086684,0.11078565,0.09507214,-0.04208893,0.01233173,-0.0030071,0.02787247,0.02260322,0.02549776,0.02191322,0.01558281,-0.07193942,-0.2248141,0.05205426,0.08897128,-0.06337579,-0.01490205,-0.06557129,0.03525418,-0.02825901,-0.04381206,0.07527198,0.08839551,-0.01267056,0.05251236,0.02231669,-0.03202995,-0.04904935,0.02379818,-0.00807798,-0.09299045,-0.02644805,-0.03846307,-0.07846719,-0.03456391,-0.07508047,0.06760801,-0.02244155,0.14045072,0.0555875,0.01512668,-0.06625121,0.06449198,-0.01750426,-0.03453702,-0.13679942,0.01348689,0.02026451,0.05839428,-0.00877617,0.02431623,0.02751181,0.01079322,-0.01641421,-0.02694302,-0.05476552,0.05775527,-0.0433163,-0.05727722,-0.05223991,-0.01373297,0.06830798,-0.00977091,0.00063456,0.03629505,0.0705129,-0.02782755,-0.01252276,-0.00747842,-0.04519309,0.00416264,-0.01100964,-0.00309684,0.002828,-0.0036189,-0.06565311,0.01628225,0.0323527,0.03514076,-0.04410977,0.01111296,-0.01261615,-0.06811374,0.0729434,0.0163491,0.02341177,-0.00881806,-0.02185942,-0.07177629,0.07203134,0.02012732,0.01485765,0.01206565,-0.05811753,0.03623563,0.00050559,-0.04223332,0.02105286,0.02658193,-0.0369105,0.011191,-0.02439354,-0.09044321,-0.01156618,-0.02349358,-0.0142148,0.03748424,-0.01948291,-0.21088029,0.03659928,0.03463989,-0.02519032,-0.0390688,-0.04494631,0.05953366,-0.03892796,-0.04274501,-0.02549352,-0.0005655,0.03630729,0.03790332,0.02495582,0.03688504,0.01301766,0.0757682,0.02730836,0.06623087,-0.10741033,0.01020101,-0.00416066,0.23634428,-0.04098233,-0.00871128,0.08358451,-0.03690913,0.00379888,0.04207976,0.09575461,-0.01463199,0.056563,0.06321539,-0.00265928,-0.02193482,-0.01063098,-0.02292101,0.00875837,0.06777243,0.06001686,-0.00742314,-0.0044098,-0.02191688,0.01453388,0.02934397,-0.0773637,-0.06797729,0.00274823,-0.02826842,-0.05505287,0.01466313,0.02027024,-0.00720783,-0.03812785,0.02624651,0.01600528,-0.07411925,0.0418414,-0.05401577,0.0672653,0.0398799,-0.04148876,0.03639346,0.05846197,0.01761601],"last_embed":{"hash":"jsi0ws","tokens":233}}},"text":null,"length":0,"last_read":{"hash":"jsi0ws","at":1751288827737},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Using `resolve` as a Babel macro#Usage with [`create-react-app`](https://create-react-app.dev)#{1}","lines":[598,605],"size":448,"outlinks":[{"title":"Create React App","target":"https://create-react-app.dev","line":1},{"title":"Using `resolve` as a Babel macro","target":"https://github.com/vercel/styled-jsx/blob/main/readme.md#using-resolve-as-a-babel-macro","line":7}],"class_name":"SmartBlock","last_embed":{"hash":"jsi0ws","at":1751288827737}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles in regular CSS files": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02180843,-0.03269109,0.03351713,-0.05662528,0.02956644,0.02871822,-0.07619047,-0.0499192,-0.04718975,-0.04216561,-0.01182773,0.00223398,0.02866927,-0.00097536,0.02590338,0.01954274,-0.00100606,0.0590899,-0.05508737,-0.00019044,0.05505216,-0.01550032,0.01443048,-0.03276087,-0.03147927,0.02230531,-0.03608291,-0.00031331,0.00969868,-0.1848821,0.00136222,-0.05665769,-0.03066849,-0.005507,0.01579689,-0.07174754,0.01577117,0.02080488,-0.02522523,0.01703609,-0.00649045,0.05742204,-0.02444757,-0.0350596,-0.05970564,-0.03619662,0.00023091,0.02084812,0.00683965,-0.01109056,-0.01490646,-0.03132265,0.03594512,-0.04754018,-0.0057061,0.11670074,0.08842257,0.05220696,0.00609513,0.04167087,-0.00346512,0.01180641,-0.14089735,0.08368614,0.04380408,0.05063241,-0.10450129,0.01003857,0.04552772,0.01332974,-0.04568266,0.04577884,0.00233129,0.10908889,0.01765421,-0.05599125,0.04746817,-0.02587524,0.01990426,-0.03575342,-0.08655483,-0.03900284,0.00462059,0.01398118,0.04175084,0.00780764,0.02337646,-0.00626506,0.00472502,0.06277183,-0.04369014,-0.10601307,0.07095126,0.03517507,0.00863245,-0.02354739,0.02089768,0.05758183,-0.00775315,0.12367055,-0.10578081,0.00483972,0.05019118,0.01877433,0.0707866,-0.00730136,-0.00752908,-0.02270903,-0.03269459,-0.0108683,0.00652957,-0.02880248,-0.0551642,-0.08701763,-0.05519833,-0.01338239,-0.03861422,-0.00389904,0.02506868,-0.03212191,0.03323351,0.03141374,0.02701213,-0.00647733,0.03057188,0.04626959,0.02475904,0.01156732,-0.00948254,0.08862028,0.00271769,0.07886532,-0.05492548,0.0066184,-0.01173066,0.04855673,-0.01541365,0.00472586,-0.03145448,0.05832031,0.01208584,-0.03910337,-0.02377564,-0.04352577,0.02841054,0.01554823,-0.04190776,0.05924784,0.00595047,-0.03143435,-0.03016269,0.07978159,-0.10081651,0.02419708,-0.01150842,0.02206011,0.000907,-0.02236676,-0.08680055,0.01446612,-0.02420955,-0.00268386,0.00919383,0.01218812,0.02277915,-0.05714883,-0.03455266,0.0500888,0.03734312,-0.09502118,-0.02259207,0.01576148,-0.03013536,0.00336368,0.07455929,0.02395869,-0.0350854,-0.00036058,0.04491029,0.03678073,0.0804001,-0.03012355,-0.03313966,0.03122874,-0.01694173,-0.05582774,0.04211676,-0.0447377,-0.03230642,-0.00227303,-0.03243109,0.00887711,0.00971248,-0.03053857,0.03013491,0.03471331,-0.06509306,-0.006461,0.04290681,-0.02078301,0.17599997,0.03855766,0.00835615,0.04016193,-0.05436931,0.05071004,0.00960691,-0.03152646,0.0496823,-0.05606908,-0.11424358,0.05362661,0.09762217,0.11000701,-0.0239298,-0.03498234,0.070298,0.0345934,0.0411742,0.04661446,0.01448685,-0.04942055,-0.10621826,-0.20453042,0.00076018,0.05622717,-0.02091856,-0.00519187,-0.01835695,0.05744966,0.02084814,-0.03296793,0.06671917,0.11567225,0.02279722,0.00938626,0.01730875,-0.04227301,-0.01121618,0.00952964,-0.06374075,-0.08824065,0.00859608,-0.02619448,-0.02855942,-0.05271922,-0.07147659,0.09343655,-0.00739976,0.1648493,0.02775359,0.01206278,-0.05162591,0.05258879,-0.02931403,-0.01614577,-0.09933995,0.06022511,0.000262,0.02590731,-0.04069434,0.01560765,0.01258738,-0.00585033,-0.02965566,0.00487795,-0.01840175,0.05676637,-0.05039956,-0.03833473,-0.03929451,-0.01483543,0.0345785,0.03385374,0.00408563,0.03467718,0.10637856,-0.04206325,-0.01299729,-0.01015418,-0.0356563,-0.00542181,0.01608753,-0.02364165,-0.00051271,0.00724858,-0.07419328,-0.01213085,0.0609566,0.04754467,-0.03906938,0.02358559,-0.00224384,-0.04435949,0.09103971,0.00544817,0.00784666,0.00557483,0.00920512,-0.07544364,0.10412689,0.03376569,0.05533762,0.03497235,0.02048119,0.04520995,0.02577075,-0.02602258,-0.02461422,-0.04358862,-0.02657387,0.03336853,-0.00646123,-0.09230657,0.02886717,-0.06936437,0.00114613,0.06021231,-0.00769684,-0.2283569,-0.02835184,0.02771414,-0.01757085,-0.01547156,-0.04586136,0.03481269,-0.07569066,-0.02692398,-0.03473259,-0.00151868,0.02600033,0.01396422,-0.00555476,0.03976681,-0.01078334,0.06683831,0.0227212,0.11512417,-0.06827673,-0.02711533,-0.00561967,0.252307,-0.03598533,0.02616542,0.05336215,-0.00892827,-0.00628873,0.02931047,0.06943749,0.04880501,0.02115622,0.10123383,-0.01999496,-0.00349674,-0.00880923,-0.01563615,0.01417417,0.01724911,0.01038204,0.00503009,0.01779092,-0.01105476,0.01007945,0.01908556,-0.12956108,-0.07277224,-0.01518707,-0.0464807,-0.0453067,-0.0042738,0.0502818,-0.01396068,-0.05456419,0.00140769,0.01941341,-0.05389743,0.00697798,-0.03118044,0.03352387,0.02203188,-0.04435626,0.00110879,0.03196894,0.01007626],"last_embed":{"hash":"obos7l","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"obos7l","at":1751288827904},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles in regular CSS files","lines":[606,714],"size":2453,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"obos7l","at":1751288827904}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles in regular CSS files#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02002244,-0.03473153,0.03176533,-0.05658902,0.03074346,0.02981941,-0.07801931,-0.05465842,-0.0462052,-0.04104231,-0.01136461,0.00274364,0.03079937,0.00057846,0.02381715,0.02106698,-0.0034346,0.05621054,-0.05673941,-0.00155173,0.0533828,-0.01381995,0.01405491,-0.03124042,-0.03131224,0.02352486,-0.03458712,0.00224887,0.01052107,-0.18437009,-0.00306025,-0.05760495,-0.03039221,-0.01049737,0.0138131,-0.06693016,0.01620332,0.02423114,-0.02200234,0.01628904,-0.00816726,0.05429092,-0.02187159,-0.03328186,-0.06097585,-0.03739583,-0.00190257,0.02009638,0.00593556,-0.00741275,-0.01314754,-0.03209103,0.03448504,-0.04641953,-0.0051506,0.11574109,0.0881051,0.05215745,0.00943489,0.04046469,-0.00333727,0.01108378,-0.14122996,0.08407072,0.04451307,0.05222617,-0.10628371,0.0140127,0.04561112,0.02010286,-0.04829419,0.04469864,0.00400248,0.10799283,0.02002169,-0.05573362,0.04945702,-0.02640599,0.01977656,-0.03628426,-0.08678827,-0.03991956,0.00231821,0.01526974,0.03924436,0.00526041,0.01957855,-0.0082363,0.00059363,0.06112262,-0.04350011,-0.10353911,0.07076658,0.03417865,0.00333237,-0.02492972,0.02186088,0.05901371,-0.00758124,0.12409875,-0.10476729,0.00442952,0.05297556,0.01935832,0.07325836,-0.00803342,-0.00757238,-0.02385274,-0.0341704,-0.01018212,0.00799179,-0.02669011,-0.05505895,-0.08803438,-0.05287477,-0.01252832,-0.04224707,-0.00619111,0.02133111,-0.02879936,0.0335803,0.02570579,0.02700007,-0.01080625,0.02804223,0.04950796,0.02629183,0.01234119,-0.01010249,0.08570983,0.00218275,0.07903863,-0.05724086,0.00485237,-0.01204191,0.05019207,-0.01440178,0.00360288,-0.02838034,0.058082,0.01514939,-0.03876754,-0.02458506,-0.0410506,0.02911491,0.01600317,-0.04145305,0.05837404,0.0018537,-0.02829401,-0.02733142,0.07881341,-0.09814764,0.02386622,-0.01010661,0.02166451,0.00392338,-0.02119661,-0.08679952,0.01319874,-0.02294627,-0.00394161,0.00918934,0.01338801,0.02045836,-0.05867863,-0.03367848,0.04783291,0.03627036,-0.09344004,-0.01896467,0.01968961,-0.03201139,0.00340646,0.07517675,0.02397591,-0.0364704,0.00221167,0.04835605,0.03443945,0.08066899,-0.02943981,-0.03478499,0.03005867,-0.01584253,-0.05552618,0.04216652,-0.04072491,-0.02858808,-0.00549627,-0.02876842,0.0132886,0.01263593,-0.03138056,0.02908886,0.03762884,-0.063606,-0.00814239,0.04389445,-0.022226,0.17551695,0.03979415,0.00803023,0.03709198,-0.05236922,0.04819718,0.01045564,-0.03409893,0.04903073,-0.05507496,-0.11183278,0.05319155,0.09758814,0.10885983,-0.02632578,-0.03327323,0.07143173,0.03296406,0.04008455,0.04556438,0.01472059,-0.05102706,-0.1076104,-0.20446712,0.00208029,0.05941967,-0.01838658,-0.00648812,-0.01857297,0.05752426,0.02091865,-0.03143019,0.07022852,0.11712886,0.02322633,0.01258944,0.01607893,-0.04007121,-0.01312823,0.01047982,-0.05899688,-0.08755689,0.010454,-0.03139908,-0.0306978,-0.05502818,-0.07205164,0.09923804,-0.01181465,0.16670717,0.02665658,0.01025403,-0.05310128,0.05022918,-0.02846615,-0.01841645,-0.10358545,0.06014989,0.00129788,0.02770731,-0.04041872,0.01736039,0.01138899,-0.00404972,-0.03359982,0.00595792,-0.0144571,0.05553402,-0.04705944,-0.03638228,-0.03975564,-0.01367119,0.03287618,0.03239378,0.00201065,0.03278958,0.10759029,-0.04422024,-0.01265716,-0.0119834,-0.03245786,-0.00606188,0.01591879,-0.02595874,-0.00184948,0.00865217,-0.07385653,-0.01567652,0.06254183,0.05131087,-0.0425535,0.02168286,0.00165478,-0.0438808,0.09082315,0.00583971,0.00986457,0.00460718,0.01163055,-0.07529899,0.10794717,0.03360757,0.0526801,0.03537228,0.01944727,0.04210313,0.02548848,-0.02262662,-0.0251372,-0.03935117,-0.02730457,0.03199745,-0.00471974,-0.09602107,0.02637644,-0.0672856,0.00172438,0.05683305,-0.00846007,-0.22622727,-0.0299146,0.0261494,-0.01449993,-0.01234638,-0.04565817,0.03795735,-0.07424583,-0.02455405,-0.03702606,-0.00168458,0.0228962,0.01346088,-0.00231845,0.04070665,-0.0122155,0.06785727,0.02385669,0.11781626,-0.06643337,-0.02957285,-0.0087406,0.24959113,-0.03796165,0.02843646,0.0543909,-0.00969979,-0.00973819,0.02688934,0.0723118,0.04921473,0.02061052,0.1021378,-0.01955248,-0.00267496,-0.00575816,-0.01793247,0.01274448,0.0176343,0.01103272,0.00613762,0.01506188,-0.01214143,0.01324393,0.01869828,-0.13103001,-0.07306102,-0.01764585,-0.05132176,-0.04614237,-0.00382791,0.05002159,-0.01813375,-0.05691982,-0.00361497,0.01842655,-0.05326363,0.00472195,-0.03182726,0.03288464,0.02373206,-0.04601955,0.00165849,0.03156903,0.01024581],"last_embed":{"hash":"vpjnd1","tokens":473}}},"text":null,"length":0,"last_read":{"hash":"vpjnd1","at":1751288828190},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles in regular CSS files#{1}","lines":[608,689],"size":1993,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"vpjnd1","at":1751288828190}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles in regular CSS files#Next.js": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02149836,-0.02465873,0.06342442,-0.07435395,0.05207629,0.02612682,-0.08324848,-0.04620063,-0.04013743,-0.05019372,0.00549428,-0.01407866,0.02268204,0.00713499,0.04060023,0.04832561,0.00804455,0.07061771,-0.05613996,-0.00999065,0.04389238,0.02047608,-0.00489768,-0.04995908,-0.04815473,0.02954377,-0.0525994,-0.03134876,0.00715398,-0.16140155,0.00423304,-0.06801698,-0.04956256,-0.02654587,0.04304433,-0.03568373,0.00636602,0.02615294,-0.05548321,0.02436507,0.01484465,0.06156556,-0.04351802,-0.01219912,-0.02671539,-0.04513446,0.02002845,0.01506004,-0.01013424,-0.01745988,0.01370208,-0.05754397,0.03604713,-0.03382767,-0.0063236,0.10270017,0.07738551,0.05448262,0.01644855,0.03239665,-0.00204526,0.01403477,-0.14499742,0.07870366,0.05185284,0.0284574,-0.09515753,-0.01220725,0.05329166,0.03909469,-0.01146502,0.0607691,0.01345059,0.10189259,0.02803446,-0.03817819,0.03365669,-0.02895785,0.01829964,-0.04468361,-0.08640497,-0.01995781,0.00760888,0.01019535,0.03787305,0.00570604,0.00443079,-0.02887337,0.02028915,0.0550526,-0.04655479,-0.10376845,0.04372644,0.02719048,-0.02139816,0.00617882,0.03504887,0.05530253,-0.00506159,0.12779927,-0.10135464,0.02540766,0.0518663,-0.00161308,0.06949975,-0.0058628,0.00704274,-0.04945534,-0.03950106,0.00547838,0.01598309,-0.02658382,-0.05392338,-0.07463408,-0.02717438,-0.04503921,-0.06037619,0.00186175,0.01992695,-0.01137424,0.02524793,0.0405609,0.04873296,-0.01045177,0.01757491,0.0419162,0.02021418,0.0050188,0.0035501,0.0755327,0.01223781,0.08969752,-0.01873438,0.00359159,-0.01067436,0.02952936,-0.01303473,0.01307954,-0.02260996,0.06741733,0.02624862,-0.02714511,-0.02767397,-0.06146164,0.03382568,0.01267994,-0.00256975,0.0793245,0.00217356,-0.03771983,-0.04451244,0.05053953,-0.07908531,0.04763003,-0.03200425,0.00859006,-0.01474683,0.00180052,-0.05631399,0.02367964,-0.00082122,-0.01189547,0.00306174,0.03390414,0.02004312,-0.07643025,-0.01386906,0.03647031,0.03907549,-0.08754621,-0.04370902,-0.00466667,-0.0034736,0.01856472,0.06423327,0.02714679,-0.03158915,-0.02090241,0.03009812,0.03193842,0.09361415,-0.03732274,-0.02199628,0.0643801,-0.03449395,-0.07711644,0.0610759,-0.06083399,-0.04746741,-0.00591546,-0.03911386,0.02427741,0.01542527,-0.01946659,0.03708253,-0.02148526,-0.03189687,0.01626166,0.03129338,-0.02308961,0.1628799,0.02068992,0.00888703,0.04480772,-0.06682982,0.05062152,0.00128161,-0.02706132,0.04153072,-0.02760433,-0.12048015,0.02548331,0.11470985,0.10652635,-0.01867211,-0.00692646,0.04524417,0.08295921,0.02301295,0.05784298,-0.00231956,-0.04066885,-0.10486702,-0.21797162,0.00448786,0.03976107,-0.03741236,-0.01026547,-0.00822671,0.04738329,0.04330397,-0.04510052,0.09053706,0.13503955,0.01162013,0.03753883,0.0330303,-0.04048862,-0.03292181,0.00479596,-0.04067809,-0.06305046,-0.01723923,0.0197328,-0.04722783,-0.0549668,-0.08629978,0.06612153,-0.01109342,0.13874486,0.01236125,0.0135557,-0.07539774,0.0338815,0.01122769,-0.00598054,-0.08885707,0.0331381,0.01009036,0.04653215,-0.05288084,0.0336822,0.01964013,-0.01370948,-0.01085056,0.02645582,-0.03398364,0.05994156,-0.03302581,-0.06127012,-0.02650939,-0.02116953,0.03352628,-0.02270958,-0.0086932,0.03244818,0.09733739,-0.020964,-0.00893997,0.00577434,-0.06735329,0.02402711,0.03566413,-0.03052146,0.0123883,0.01557636,-0.09506977,0.02698766,0.05176707,0.04501021,-0.03745178,0.01532601,-0.04006264,-0.05905442,0.08324236,0.02668791,-0.02213772,-0.01899944,0.00306715,-0.05971188,0.09857162,0.03135181,0.05116186,0.05037238,-0.00202705,0.04313373,-0.01596986,-0.01031293,-0.02606824,-0.05510435,-0.00659521,0.03823371,0.01857611,-0.09187337,0.01550378,-0.05348647,0.02045837,0.05537765,-0.01457561,-0.22924951,-0.00354119,0.04728966,-0.02337569,-0.07713499,-0.01259457,0.05003694,-0.06007175,-0.0346984,-0.01093933,-0.02202488,0.02872477,0.01699912,0.00065834,0.00637748,0.01090062,0.05413824,-0.01235742,0.10982812,-0.06947348,-0.03258663,-0.00770571,0.24627371,-0.05454809,0.03913851,0.07896018,-0.03355611,-0.01875883,0.02440949,0.06704754,0.05190581,0.04518287,0.08158451,-0.03891505,-0.02255651,-0.03891919,0.00786961,-0.00361033,0.01451006,-0.01830384,0.02187539,-0.01848733,-0.01445118,0.0294528,0.00459858,-0.1168874,-0.06905445,-0.02328713,-0.03567018,-0.0629917,-0.00681464,0.03236591,0.01070293,-0.02703245,0.01457695,0.02869714,-0.05450166,0.00670388,-0.03283247,0.02595276,0.00181121,-0.05341204,-0.00617476,0.00651748,0.02222063],"last_embed":{"hash":"xcqrgk","tokens":183}}},"text":null,"length":0,"last_read":{"hash":"xcqrgk","at":1751288828610},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles in regular CSS files#Next.js","lines":[690,714],"size":425,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"xcqrgk","at":1751288828610}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles in regular CSS files#Next.js#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01842383,-0.0261072,0.06313542,-0.07663152,0.04982521,0.02675315,-0.08184944,-0.04434178,-0.04052867,-0.05538501,0.00610998,-0.0159716,0.02208857,0.00667644,0.03912254,0.04805548,0.00512417,0.07443754,-0.05519196,-0.01003895,0.04699913,0.02269893,-0.00246524,-0.04924159,-0.04718351,0.02927089,-0.05015383,-0.03251124,0.00804697,-0.16054359,0.00222184,-0.07048639,-0.05424574,-0.02499394,0.04155653,-0.03528881,0.00270101,0.02422142,-0.05577704,0.02277497,0.01503928,0.05990722,-0.04269857,-0.01160023,-0.02717461,-0.04553702,0.02043683,0.01326194,-0.0105036,-0.01795359,0.01454747,-0.05820332,0.03459807,-0.03451264,-0.00464761,0.10396399,0.07710989,0.05256137,0.01645809,0.03064313,0.00023543,0.0150157,-0.14820564,0.07861596,0.04953799,0.0271754,-0.09440786,-0.01111434,0.05301122,0.0403732,-0.01215597,0.0593417,0.01561045,0.10306282,0.03035363,-0.03703531,0.03284702,-0.02863454,0.01999824,-0.0454756,-0.08040265,-0.01812042,0.00866064,0.00944258,0.04055405,0.00265263,0.00465349,-0.03075863,0.02192924,0.05622527,-0.04498924,-0.10635074,0.04192249,0.03054335,-0.0254993,0.00783804,0.03607774,0.05424031,-0.00828903,0.12610263,-0.10181894,0.02553665,0.05341513,-0.00122323,0.07025385,-0.00488354,0.00564763,-0.04908848,-0.03961735,0.00273518,0.01269928,-0.02637524,-0.05283189,-0.07493587,-0.02718759,-0.04478561,-0.06020914,0.00436343,0.019527,-0.01149551,0.02962578,0.03940661,0.05073736,-0.00745439,0.0183695,0.04507028,0.01836187,0.00660032,0.00111673,0.0745541,0.01308272,0.08722028,-0.01994736,0.00273024,-0.01227991,0.02762783,-0.01198361,0.01886361,-0.02501254,0.06721348,0.02766324,-0.02436521,-0.0287096,-0.06137648,0.03344206,0.01433788,-0.00510015,0.07912155,-0.00060708,-0.03786497,-0.04353668,0.05063072,-0.07529139,0.04828983,-0.03211952,0.00704965,-0.01565609,0.00226591,-0.0535423,0.02358203,-0.00079425,-0.01066525,0.00157089,0.03328969,0.01906059,-0.07672148,-0.01472538,0.0328288,0.03651009,-0.086403,-0.04271057,-0.00590852,-0.00698008,0.01990642,0.06553895,0.02853511,-0.03371013,-0.02130771,0.02878726,0.03490279,0.09361041,-0.03712194,-0.02201216,0.06401625,-0.03307983,-0.07614861,0.06147844,-0.06015047,-0.04691206,-0.0058398,-0.04052955,0.02831672,0.01364462,-0.0209937,0.03574623,-0.02175762,-0.03335318,0.01649372,0.03060984,-0.02225619,0.1596099,0.02229786,0.00876493,0.04066869,-0.06688593,0.05212272,0.00112165,-0.02854674,0.04211076,-0.02684005,-0.11936259,0.02666933,0.11214393,0.10703555,-0.01875266,-0.0045075,0.04339775,0.07942355,0.02070736,0.05662157,-0.00251388,-0.03985722,-0.10683551,-0.21778996,0.00337267,0.03897834,-0.04000833,-0.01231234,-0.00753179,0.04705879,0.04792104,-0.04642687,0.08993051,0.13193759,0.01095891,0.03925245,0.03468292,-0.03936529,-0.03232691,0.00381253,-0.04010352,-0.06303427,-0.0162641,0.02231004,-0.04843442,-0.05618716,-0.08670083,0.0627062,-0.01029394,0.13812849,0.01297918,0.01043976,-0.07572356,0.03348448,0.00955128,-0.00538696,-0.0885755,0.03494889,0.00900924,0.04678188,-0.04988262,0.03331089,0.0217906,-0.01237368,-0.01015386,0.025466,-0.03185895,0.06320458,-0.0326161,-0.0629954,-0.02314904,-0.02107959,0.0365619,-0.02659303,-0.00502695,0.03134226,0.09505472,-0.02078947,-0.00773177,0.00554669,-0.06662117,0.02396845,0.03298793,-0.03059662,0.01317003,0.01620569,-0.09677785,0.02920413,0.05198846,0.04680754,-0.0375171,0.01567229,-0.03892436,-0.06199626,0.08202373,0.02748121,-0.0222846,-0.01687181,0.00247267,-0.06144939,0.09701911,0.03124004,0.0491632,0.05055605,-0.00152924,0.04109236,-0.01512585,-0.00805493,-0.02403505,-0.05246961,-0.00447258,0.04259738,0.01793348,-0.09213342,0.01553734,-0.04991114,0.02238936,0.05583123,-0.01542132,-0.23137629,-0.00200479,0.05055243,-0.02165786,-0.0776909,-0.01121175,0.04956421,-0.06034265,-0.03499508,-0.01181441,-0.02273863,0.02904815,0.01682483,0.0028144,0.0051481,0.01497523,0.05552829,-0.01382281,0.11013845,-0.07111031,-0.03116144,-0.00611572,0.24612524,-0.05298553,0.04094763,0.08153414,-0.03304896,-0.01709327,0.02303511,0.0668473,0.05146625,0.04376335,0.08331097,-0.03932673,-0.02213138,-0.04164468,0.00684824,-0.00511561,0.0151231,-0.01927065,0.0206414,-0.01703915,-0.01695949,0.02889877,0.00756423,-0.12109721,-0.0699625,-0.02501038,-0.03619016,-0.06601718,-0.00824904,0.03132257,0.0104042,-0.02563337,0.01634297,0.02906234,-0.05279962,0.00637599,-0.03139879,0.02562757,-0.00032318,-0.05411463,-0.00497552,0.0045309,0.02052166],"last_embed":{"hash":"1wqcfqe","tokens":179}}},"text":null,"length":0,"last_read":{"hash":"1wqcfqe","at":1751288828768},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Server-Side Rendering#External CSS and styles outside of the component#Styles in regular CSS files#Next.js#{1}","lines":[692,714],"size":410,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1wqcfqe","at":1751288828768}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#CSS Preprocessing via Plugins": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03128011,-0.04694027,0.05078515,-0.07798232,0.02368938,0.01056313,-0.08553275,-0.05630906,0.03509598,-0.04228171,-0.05751422,0.0365488,0.03957867,-0.0074325,0.03985612,0.00551316,0.01976693,0.06715477,-0.09743228,-0.00297433,0.05523947,0.04788619,0.0162434,-0.06251664,0.01147636,0.00445876,0.00696644,0.00096334,0.01783861,-0.17893441,0.06103683,-0.03684328,-0.05301278,0.02871831,0.02093129,-0.06314773,-0.03291585,0.01402427,-0.04664531,0.02796244,0.02299584,0.03279259,-0.02379153,-0.04574841,-0.00877834,-0.03700493,0.03480882,-0.01863922,-0.0030969,-0.00918693,0.00184455,-0.06127044,0.05305022,-0.05842279,0.01706835,0.0825997,0.07541502,0.04046346,0.02147071,0.0517687,0.00557805,0.01343363,-0.14380382,0.12769055,-0.01540485,0.03108727,-0.04730689,-0.04602151,0.03717135,-0.01904785,-0.01848761,0.02993966,-0.01089456,0.08926888,0.01692513,-0.04761479,0.02192757,-0.05153548,0.04052928,-0.00821343,-0.01412947,-0.02379238,0.05248256,-0.02251941,0.04605817,0.00478233,0.03960217,-0.03694504,0.02236675,0.00768401,-0.02371389,-0.15240097,0.04470045,-0.00176152,0.00785435,-0.0079888,0.02210819,0.01355295,-0.04471976,0.07972103,-0.09112162,0.06324433,0.04047375,0.02612467,0.04922149,-0.04879885,-0.05680573,-0.02674716,-0.01534753,-0.01852198,0.00058579,-0.04642927,-0.04048077,-0.06999502,-0.03618777,-0.03178999,-0.01241833,0.01635962,0.02951048,-0.02556202,0.03113073,0.06470051,0.05098826,0.03867455,0.02230912,0.04461327,-0.00267605,0.0472919,-0.00236679,0.09453349,0.05462546,0.08627002,-0.03810535,-0.00521678,-0.00366609,0.01811057,-0.04748359,-0.01029076,-0.04038125,0.02943047,0.02677631,-0.0124908,0.01004559,-0.01292794,0.01236506,0.04499611,-0.03128443,0.06184971,-0.01243328,-0.03533282,-0.07220462,0.05433467,-0.07694719,0.00500846,-0.02030092,0.01964648,0.00069045,0.01060392,-0.05239053,0.05408478,-0.05889739,-0.00901609,-0.0033083,0.04831915,0.01808644,-0.09232933,-0.02737593,0.02255324,0.03790492,-0.08760125,-0.06626108,-0.00596719,-0.02488466,0.02132569,0.05086678,0.05607707,-0.04910337,-0.04928172,0.0456606,0.02769666,0.07207858,0.00356581,0.00522661,-0.00252918,0.00864297,-0.07786844,0.02669072,-0.05695902,-0.02274557,0.04144025,-0.00792414,0.01004878,0.01080701,-0.0341562,-0.00153824,0.00788912,-0.05799973,-0.01014608,0.04542774,0.00205035,0.08596443,-0.0011617,-0.01060672,0.04996871,-0.03748534,0.04703571,0.02550127,-0.04954409,0.05308158,-0.00824651,-0.1145127,0.04463271,0.12842666,0.0717605,-0.00267516,0.01693226,-0.02545202,0.05589781,0.02040256,0.01541122,-0.01860688,-0.02196826,-0.10755168,-0.19820032,0.02649071,0.08540657,-0.04010462,0.02304007,-0.02111903,0.04050742,0.00011825,-0.03192592,0.07647774,0.10900256,-0.0058264,0.04150767,0.04209743,-0.04374886,0.03357306,0.01079494,-0.07024438,-0.02303471,0.00559216,0.00404948,-0.00778071,-0.03463104,-0.09345013,0.05971404,-0.01388218,0.13902755,0.01868282,0.02697749,-0.0792093,0.05051203,-0.01337714,-0.00554694,-0.08451802,0.09737793,0.02182782,0.02993768,0.00224818,0.0281777,-0.00979471,-0.00266642,-0.00704556,-0.01215581,-0.05184872,0.02652875,-0.0671502,-0.08521181,-0.04312438,-0.00345869,0.07657135,-0.0091524,0.01625664,0.040841,0.10534771,-0.00351186,-0.03786677,-0.03572939,-0.07835291,-0.03434156,-0.00465368,-0.01809408,-0.05383984,-0.02237301,-0.09086829,0.01734056,0.04957282,0.03081266,-0.02371984,0.01553855,-0.06290196,-0.08907121,0.07770177,0.03556883,0.02496481,-0.00923455,-0.00478246,-0.06069718,0.07425096,0.01220246,0.03847544,0.01720958,0.02530627,0.03149016,0.0180299,-0.01762081,0.00350601,-0.00072983,-0.0033561,0.05152489,0.00157417,-0.09283241,0.00933579,-0.00921888,0.03137928,0.05332486,-0.01068261,-0.23498708,0.01335273,0.05746048,-0.00807719,-0.03476175,-0.03379367,0.04939744,-0.1204334,-0.03922607,0.03911981,-0.04934671,0.04473583,0.03137375,0.01548038,0.00771153,-0.00347756,0.03802203,0.01614861,0.06943382,-0.09795121,-0.00802197,-0.04510649,0.26423562,-0.04334167,-0.00096589,0.06515319,-0.01668479,-0.00710759,0.07745852,0.05584303,0.05454667,0.05349363,0.06599528,-0.00312811,-0.00933283,-0.00564655,-0.04688212,0.00376776,0.01305412,0.00321291,-0.02167094,-0.01545562,-0.02774908,-0.01752168,0.02039419,-0.09087319,-0.05185688,0.02314877,-0.02361045,-0.04374029,0.00385767,0.06686833,-0.01737814,-0.03682246,0.02258154,0.01456396,-0.0077237,0.04438714,-0.04212763,0.02976821,0.00591026,-0.0915537,0.01920288,0.08046795,0.00252309],"last_embed":{"hash":"37wtu8","tokens":431}}},"text":null,"length":0,"last_read":{"hash":"37wtu8","at":1751288828858},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#CSS Preprocessing via Plugins","lines":[715,857],"size":3671,"outlinks":[{"title":"require.resolve","target":"https://nodejs.org/api/globals.html#globals_require_resolve","line":59},{"title":"styled-jsx-plugin-sass","target":"https://github.com/giuseppeg/styled-jsx-plugin-sass","line":138},{"title":"styled-jsx-plugin-postcss","target":"https://github.com/giuseppeg/styled-jsx-plugin-postcss","line":139},{"title":"styled-jsx-plugin-stylelint","target":"https://github.com/giuseppeg/styled-jsx-plugin-stylelint","line":140},{"title":"styled-jsx-plugin-less","target":"https://github.com/erasmo-marin/styled-jsx-plugin-less","line":141},{"title":"styled-jsx-plugin-stylus","target":"https://github.com/omardelarosa/styled-jsx-plugin-stylus","line":142}],"class_name":"SmartBlock","last_embed":{"hash":"37wtu8","at":1751288828858}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#CSS Preprocessing via Plugins#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02653943,-0.05022677,0.04809814,-0.07590885,0.03062085,0.00758818,-0.08549039,-0.05719884,0.03444909,-0.04742748,-0.06088975,0.0314317,0.03395747,-0.01100921,0.04059044,0.01080309,0.01595986,0.0745813,-0.09556125,-0.00536587,0.05427548,0.04005205,0.02158056,-0.05695125,0.01732651,0.00082607,0.01173516,0.00517406,0.02706284,-0.17822303,0.06001674,-0.04256974,-0.05850212,0.03771499,0.02032172,-0.06403373,-0.0326667,0.00390062,-0.0433207,0.03221674,0.02799955,0.02925268,-0.02174745,-0.04451863,-0.00901807,-0.02709112,0.0301301,-0.01954267,-0.0043755,-0.01052772,0.00939584,-0.06694702,0.04661839,-0.06076139,0.01674921,0.08170515,0.07492401,0.04881052,0.0264094,0.05946825,0.00931752,0.00728395,-0.14104362,0.12193953,-0.01521961,0.03368961,-0.0463084,-0.04605107,0.03142554,-0.02013373,-0.01813373,0.03177058,-0.01016148,0.08794032,0.01214208,-0.05200396,0.02000151,-0.04623782,0.04552438,-0.01110862,-0.00937816,-0.01385237,0.04603583,-0.02477346,0.04286408,0.01093398,0.03778392,-0.03674321,0.02176335,0.01238433,-0.02303161,-0.15461349,0.04044033,0.001542,0.00461102,-0.00923287,0.02693742,0.01390597,-0.04180063,0.0808586,-0.09030031,0.06639158,0.04372672,0.02934694,0.04524506,-0.04612174,-0.05179079,-0.03572785,-0.01289679,-0.00984006,0.00040757,-0.04970912,-0.04068082,-0.07264441,-0.03464532,-0.02741555,-0.01366988,0.02395967,0.02331103,-0.02128173,0.03207418,0.06330021,0.04956184,0.03059676,0.02126009,0.05060189,-0.00513069,0.04548603,0.00096186,0.09614644,0.04949609,0.08308938,-0.04133951,0.00395053,-0.00843254,0.0191369,-0.04341967,-0.01588895,-0.04373666,0.03683369,0.03102284,-0.01238589,0.00553866,-0.01423767,0.01360962,0.04573239,-0.03254145,0.06770997,-0.00739197,-0.04566895,-0.0700865,0.05399908,-0.09371386,0.00159933,-0.02743361,0.02104678,-0.00238454,0.01207879,-0.05115484,0.05435361,-0.06032757,-0.00810512,0.00317777,0.05021084,0.01189409,-0.084697,-0.02261174,0.01720256,0.03893084,-0.08279048,-0.06810373,-0.01181058,-0.02565308,0.01517635,0.05055539,0.05662046,-0.05313268,-0.05220845,0.04122627,0.03480684,0.06486442,0.00191597,0.00720234,-0.00877894,0.01627757,-0.07455724,0.0287465,-0.05154314,-0.02182877,0.03785051,-0.01293691,0.00953736,0.00899483,-0.03805284,-0.00334274,0.00929805,-0.05973273,-0.00702829,0.05231287,-0.00077293,0.09161695,-0.00328264,-0.01176961,0.0472565,-0.04287708,0.04496945,0.02249489,-0.05180177,0.04810053,-0.00867782,-0.10996757,0.05115661,0.12688594,0.06699435,0.00215988,0.00533441,-0.0236165,0.05821083,0.02536676,0.02431203,-0.01852715,-0.01733929,-0.10613741,-0.19754167,0.02146897,0.0812107,-0.03374607,0.02665699,-0.028278,0.03428967,0.00438318,-0.03775418,0.07328002,0.11317442,-0.00630096,0.04009509,0.04911504,-0.04812274,0.03979056,0.01445951,-0.06770965,-0.02453667,0.00987369,0.01157314,-0.01195602,-0.03651888,-0.09867293,0.05157503,-0.01181225,0.13782318,0.01089438,0.02452716,-0.08185274,0.05348598,-0.01482876,-0.00696615,-0.08491878,0.09696729,0.01965419,0.02988404,0.00726438,0.02577996,-0.00661912,-0.00770686,-0.00507843,-0.01632308,-0.0512311,0.0242246,-0.06674861,-0.09020264,-0.03758772,-0.00335389,0.07398298,-0.01564594,0.02100319,0.04514868,0.09993567,-0.00603373,-0.04655669,-0.03801538,-0.08005613,-0.03149748,-0.00159617,-0.02199256,-0.06329539,-0.02676208,-0.09554721,0.02621814,0.05151348,0.03501622,-0.02113575,0.02621229,-0.05629569,-0.08588662,0.07260427,0.03778963,0.02555508,-0.00862291,-0.00512468,-0.05846462,0.07454409,0.00990788,0.03401728,0.02011031,0.02405922,0.0311656,0.01701518,-0.01967985,-0.00355057,0.00174854,-0.00169599,0.04848728,0.00536595,-0.09208453,0.00333425,-0.01361634,0.01965243,0.05526796,-0.00796876,-0.23235489,0.01789364,0.05493319,-0.01552711,-0.02756228,-0.02979828,0.05346038,-0.11295196,-0.03833692,0.03479497,-0.04734961,0.04503373,0.03902092,0.00790294,0.00772016,-0.00680248,0.03178317,0.01864688,0.06578957,-0.10461688,0.00498946,-0.04327454,0.26729864,-0.03565067,-0.00140072,0.06358954,-0.01382609,-0.00480302,0.08142834,0.0501675,0.05022404,0.05837589,0.06516916,-0.00178525,-0.0033409,-0.00328892,-0.04955699,0.01003571,0.01684884,0.00141859,-0.02193664,-0.01943544,-0.02615252,-0.02002547,0.02325739,-0.09014867,-0.05467227,0.02252499,-0.01944599,-0.03642525,0.00510256,0.06603609,-0.02028745,-0.04529219,0.02390005,0.01906204,-0.00668619,0.04305561,-0.04689636,0.02731625,0.01098442,-0.09132729,0.01789101,0.07765574,-0.00259862],"last_embed":{"hash":"1gyu6k3","tokens":448}}},"text":null,"length":0,"last_read":{"hash":"1gyu6k3","at":1751288828993},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#CSS Preprocessing via Plugins#{1}","lines":[717,790],"size":2050,"outlinks":[{"title":"require.resolve","target":"https://nodejs.org/api/globals.html#globals_require_resolve","line":57}],"class_name":"SmartBlock","last_embed":{"hash":"1gyu6k3","at":1751288828993}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#CSS Preprocessing via Plugins##Plugin options": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04098111,-0.05145388,0.04887028,-0.05579916,0.00109225,0.02230323,-0.02308916,-0.02776393,0.03441279,-0.02829451,-0.03835769,0.02447734,-0.00613334,0.0087483,0.0229319,0.02649684,-0.03670333,0.06603627,-0.06597519,0.00537637,0.06766634,-0.01021063,0.01806372,-0.10918921,-0.02965201,0.02559154,0.00142576,0.00485236,0.00985068,-0.19867547,0.06654665,-0.00579059,-0.03908106,-0.00494463,0.02604777,-0.05284566,-0.01230609,0.03071927,-0.0352033,0.01766927,0.03634101,0.0371347,-0.02649215,-0.06877057,0.00199051,-0.01626622,0.03948859,-0.00191396,-0.02387169,-0.00581801,-0.00979318,-0.02259528,0.06607172,-0.05341019,-0.00487596,0.12017758,0.08274311,0.0799256,-0.00853336,0.04336992,-0.03188308,0.00490951,-0.15646695,0.10218838,0.02613771,0.00755157,-0.05818376,-0.04742648,0.0436492,-0.03004762,0.00067343,0.05806399,-0.01633775,0.08457712,0.03137165,-0.08167475,0.02638364,-0.02535387,0.02929667,-0.01568097,-0.06773128,-0.01262155,0.02072168,0.00865892,0.03448184,0.011078,0.04089901,-0.03939068,-0.0024131,0.02243378,-0.01630617,-0.13828464,0.03296886,0.02590787,0.01982804,-0.01758467,-0.00225298,0.03196301,-0.04001885,0.10266294,-0.08548765,0.01684909,0.01554696,0.00858809,0.0487879,-0.04017297,-0.04333558,-0.03109545,0.01219549,-0.00727228,0.01300132,-0.00270556,-0.05594299,-0.06516229,-0.04415216,-0.00615904,-0.01767176,-0.01964713,-0.01301463,-0.01955119,0.01758195,0.0643119,0.07957939,0.06015956,0.00982829,0.02256793,0.02497756,0.04889711,0.02399131,0.05317181,0.05662841,0.07490402,-0.04692495,-0.04354741,0.0260775,0.02150861,-0.02594604,0.02246994,-0.03240098,0.04135159,0.04919072,-0.03337649,0.02830471,-0.02079158,0.04194644,0.05521296,-0.03545663,0.10605496,0.01170236,-0.03397095,-0.05581838,0.05950994,-0.10398935,0.00354596,-0.03688666,0.03849611,-0.0048112,0.02181458,-0.07429688,0.03474773,-0.0310998,-0.00611107,-0.00184814,0.05380507,0.0244546,-0.09952839,-0.04260691,0.02412207,0.04007724,-0.09139134,-0.06401636,0.03150336,-0.04298604,0.02035762,0.05272165,0.05799582,-0.05538169,-0.02251941,0.02241581,0.02146346,0.03684066,-0.00614211,-0.01893522,-0.00488888,0.00355406,-0.02914912,0.05013543,-0.06715506,-0.02059777,0.04249639,-0.01129179,0.01878861,0.0224083,-0.021511,-0.01902301,-0.0214447,-0.06620988,-0.01399105,0.04086334,-0.005556,0.09700095,0.00935204,-0.01022677,0.06425741,-0.02669691,0.05691784,0.0086232,-0.03098195,0.06156131,-0.04467644,-0.12557569,0.03070879,0.09622195,0.06322213,-0.01933383,-0.01703085,-0.00143235,0.03369997,0.03859946,0.01598322,-0.00793512,-0.0356429,-0.09054444,-0.23048791,0.02536726,0.05733856,-0.05615394,-0.03107046,-0.01459829,0.03669982,0.01597547,-0.04573091,0.0883784,0.14880727,-0.02999284,0.02266124,0.00789271,-0.02384313,-0.00254653,-0.02773042,-0.07109553,-0.06919345,0.00606313,0.0055146,-0.03955633,-0.04665666,-0.09331042,0.04465674,-0.00817375,0.14411943,0.0592928,0.01531496,-0.04497311,0.05214756,-0.00336861,-0.03100227,-0.09459458,0.05444827,0.03688739,0.02021534,-0.01140492,0.01763155,0.01344635,-0.00241382,-0.02274494,-0.01445392,-0.05447308,0.06669658,-0.08045407,-0.02040956,-0.02041102,-0.01471915,0.04880587,0.00324819,0.01617535,0.06607516,0.09962899,-0.00713695,-0.02796465,0.00873157,-0.09552325,-0.01638365,0.02700938,-0.00275565,-0.02120466,-0.00376947,-0.09113097,0.01351534,0.02370506,0.01521271,-0.02439632,0.01792674,-0.05523588,-0.05422008,0.08849782,0.03970142,0.02122372,0.0149419,-0.01429638,-0.05062823,0.04854423,0.02676846,0.04101688,0.01710679,-0.00038185,0.0069557,0.0158728,0.00053352,0.00698416,-0.00022423,0.00399904,0.03803788,-0.01132914,-0.07866213,-0.0154393,-0.05398217,-0.02747617,0.04108822,-0.02209242,-0.24823451,0.01063622,0.04077565,-0.0165183,-0.01859613,-0.00472399,0.05425965,-0.06620403,-0.0309972,0.00614804,-0.02505912,0.07231601,0.01940035,-0.03054857,0.01503071,0.02847409,0.03241528,0.00551907,0.09261571,-0.06251623,0.0264364,0.00711257,0.26264608,-0.03888055,0.03012745,0.03224773,-0.01850428,0.01064016,0.04818473,0.06951518,0.02844441,0.0581195,0.0803588,-0.01407709,-0.00373472,-0.01435507,0.00301333,-0.03417985,0.06025835,0.01497759,-0.02777018,-0.01767038,-0.04313964,-0.02566777,0.03856636,-0.10218261,-0.05332708,0.03545103,0.00552608,-0.01561947,-0.02218485,0.05699696,-0.00533789,0.00333927,0.01255928,0.00920326,-0.04735628,0.01377311,-0.064573,-0.00477078,0.03670958,-0.08067314,0.03764846,0.0614774,0.0236797],"last_embed":{"hash":"17abp8x","tokens":333}}},"text":null,"length":0,"last_read":{"hash":"17abp8x","at":1751288829098},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#CSS Preprocessing via Plugins##Plugin options","lines":[791,847],"size":1084,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"17abp8x","at":1751288829098}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#CSS Preprocessing via Plugins##Plugin options#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03998928,-0.05119649,0.05095445,-0.05821724,0.00328407,0.0208535,-0.02154628,-0.02726209,0.03427981,-0.02875643,-0.03874712,0.02901001,-0.00897845,0.01043201,0.0248366,0.02657116,-0.03917667,0.06753753,-0.0668864,0.00605863,0.06800083,-0.00928854,0.01733945,-0.10924085,-0.02927325,0.02444303,0.00352419,0.0047957,0.01018167,-0.197548,0.06614834,-0.0088002,-0.04245735,-0.00254134,0.02673522,-0.05378084,-0.01613446,0.02906499,-0.03648086,0.02048383,0.03649496,0.03813219,-0.02753265,-0.06906612,-0.00258118,-0.01741896,0.03830624,-0.00235033,-0.02580728,-0.0054088,-0.00797665,-0.02220837,0.06603789,-0.05239668,-0.00786065,0.11835948,0.08426076,0.08115642,-0.00898901,0.03987876,-0.03404026,0.00510235,-0.15534249,0.09978732,0.02392372,0.00984768,-0.05785207,-0.04736444,0.04681734,-0.02950973,-0.00228365,0.05835,-0.01579584,0.0839605,0.02945349,-0.07871469,0.02738241,-0.025763,0.03059824,-0.01502476,-0.06523961,-0.01276715,0.02306685,0.00902995,0.03477044,0.00839481,0.04210994,-0.03885868,-0.00140033,0.02413087,-0.01512763,-0.1394932,0.03356403,0.02752312,0.01781564,-0.01836213,-0.00347597,0.03084574,-0.0398468,0.10193676,-0.08607389,0.01764206,0.01158665,0.00747962,0.04833718,-0.03940124,-0.0446202,-0.02833537,0.01327187,-0.00823479,0.012887,-0.00183643,-0.05653409,-0.06528598,-0.04194192,-0.01078655,-0.01309627,-0.02031996,-0.01200984,-0.02151716,0.01686853,0.06656756,0.08400705,0.06302485,0.00958165,0.02315082,0.0228739,0.0473039,0.02523017,0.05107247,0.05766428,0.0720511,-0.04579156,-0.04137615,0.02297373,0.01991003,-0.02420898,0.02489775,-0.03255341,0.03999236,0.04947697,-0.03146844,0.02858181,-0.02006938,0.04018479,0.05625506,-0.03596866,0.10706808,0.01015629,-0.03373051,-0.05333862,0.0592697,-0.10137532,0.00310568,-0.03233597,0.04139498,-0.00546839,0.02239823,-0.07381541,0.03572861,-0.03291382,-0.00445979,-0.00271631,0.05440465,0.02438493,-0.09995025,-0.04102623,0.02344789,0.03986519,-0.09342043,-0.06518354,0.03221709,-0.04362429,0.0202524,0.05439091,0.05841181,-0.05728158,-0.0195929,0.01987505,0.02178393,0.03523416,-0.00358437,-0.02013554,-0.00348973,0.00158117,-0.02893489,0.05477946,-0.06767875,-0.01822681,0.04178519,-0.01197983,0.02041316,0.02207978,-0.02343572,-0.01775657,-0.01842036,-0.06597171,-0.01595917,0.04037591,-0.00718757,0.09517017,0.0129652,-0.01043065,0.06551655,-0.02651335,0.05887781,0.00690633,-0.03389765,0.06034556,-0.04864347,-0.12606531,0.0292187,0.0953847,0.06114636,-0.01878604,-0.01860612,-0.0014381,0.03189777,0.03746616,0.01896171,-0.0059597,-0.03475599,-0.08972438,-0.22975904,0.02155807,0.05605249,-0.05788238,-0.03169604,-0.01205448,0.03427403,0.01575514,-0.04721664,0.09037873,0.14754857,-0.02875514,0.02184563,0.01084922,-0.02266865,-0.00321639,-0.03023174,-0.07086941,-0.07022787,0.0079455,0.00609183,-0.04083819,-0.04974264,-0.08948583,0.04835391,-0.01005824,0.14123486,0.06105673,0.01285505,-0.0485857,0.05235159,-0.0049281,-0.03034315,-0.09612822,0.05279996,0.03714917,0.02006112,-0.01190664,0.01889093,0.01659115,-0.00223593,-0.02290312,-0.01581008,-0.05502579,0.0655377,-0.07856586,-0.01789392,-0.02110933,-0.01582796,0.04569297,0.00210717,0.01385488,0.06831744,0.10079675,-0.00487445,-0.02813934,0.0081869,-0.09863519,-0.0168363,0.02520242,-0.0039126,-0.02169778,-0.00374706,-0.08729541,0.0142452,0.02256916,0.01851362,-0.02465618,0.01726875,-0.05216656,-0.05666484,0.08931875,0.04046756,0.02291145,0.01248515,-0.0157036,-0.0526295,0.04308045,0.02624422,0.04152219,0.01585862,0.00518625,0.00609301,0.01735551,0.00095087,0.00628719,0.00357874,0.00320054,0.03719002,-0.01394909,-0.07967084,-0.01820109,-0.05492241,-0.02833208,0.04204074,-0.02159771,-0.2484508,0.01212882,0.04369627,-0.01657908,-0.01819089,-0.00556816,0.05168763,-0.06457379,-0.0310776,0.00636606,-0.02327601,0.07249832,0.01970526,-0.0304876,0.01604461,0.02870201,0.03330441,0.00764054,0.09600458,-0.06385779,0.02797735,0.00774877,0.26103586,-0.03874798,0.03133643,0.03695359,-0.01809208,0.01074872,0.04355659,0.0711275,0.0292978,0.05919919,0.08197524,-0.01304546,-0.00071469,-0.01138763,0.0029626,-0.03476058,0.06056786,0.01549847,-0.02783449,-0.01506438,-0.04552304,-0.02562729,0.03868349,-0.1034824,-0.05285542,0.03603042,0.00377314,-0.01784306,-0.02096136,0.05620944,-0.00096001,0.00135294,0.01182104,0.00667092,-0.04352754,0.01269343,-0.06417638,-0.00809117,0.03838492,-0.08120196,0.03459774,0.05872174,0.02375255],"last_embed":{"hash":"yqts4k","tokens":330}}},"text":null,"length":0,"last_read":{"hash":"yqts4k","at":1751288829195},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#CSS Preprocessing via Plugins##Plugin options#{1}","lines":[793,847],"size":1063,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"yqts4k","at":1751288829195}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#CSS Preprocessing via Plugins##Example plugins": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04572683,0.00086788,0.04781519,-0.06952947,0.00947482,-0.01767419,-0.09213896,-0.00091856,0.00635904,-0.03052696,-0.0302768,0.03283362,0.03208346,0.00451866,0.05809519,-0.02766786,0.02202068,0.08453328,-0.04485535,0.00080118,0.06100101,0.01975277,0.01542684,-0.02060315,0.02335942,0.0222443,0.01399731,-0.01733408,0.04074563,-0.19615443,0.01824163,-0.0276916,-0.03401118,0.00775912,0.07904729,-0.03794369,-0.04132302,0.00440228,-0.10476954,0.04599288,0.04447005,0.02101487,-0.03285283,0.00011404,-0.00599847,-0.03320748,0.03433653,-0.04218132,-0.01891808,-0.02279285,0.01583532,-0.03014115,0.06303056,-0.03533766,0.01471362,0.09416893,0.09409101,0.05977454,0.01623337,0.03645578,0.02098946,0.05034975,-0.17788039,0.09400459,0.01718224,0.05298452,-0.05368878,-0.05037783,0.03636272,-0.00991188,0.00741364,0.04498603,0.00145022,0.11367454,-0.01169675,-0.0185634,0.0093647,-0.03231649,0.00135968,-0.03305274,-0.01962357,0.00430575,0.05586258,-0.0007258,0.01981777,0.01557937,0.03619017,-0.01434634,0.05693513,-0.0006884,-0.03469007,-0.12217356,0.03903831,-0.01317967,0.00801334,-0.0146884,0.02514828,0.02315498,-0.03541959,0.0921331,-0.08093325,0.06743533,0.03683623,0.00918192,0.03988793,-0.05504619,-0.02205337,-0.05303405,0.00259541,-0.00169309,0.00516141,-0.05383852,-0.03609703,-0.07536342,-0.01901771,-0.06615631,0.03970362,0.01875978,0.02478559,-0.02205616,0.02233741,0.07797897,0.04240331,-0.00039129,0.00800066,0.02115291,-0.01238645,0.04419774,0.00749177,0.09589942,0.06045882,0.07537013,-0.03746733,-0.02292745,-0.00419102,0.02949631,-0.00665839,0.0191001,-0.05566006,0.00585201,0.03515188,-0.01938495,0.03062512,-0.03380797,-0.00999539,0.04144999,-0.02812709,0.07058782,-0.01214531,-0.02383176,-0.0600538,0.06423819,-0.08189273,0.01529579,-0.03905534,0.0289718,0.02609699,0.0420063,-0.04379059,0.0773805,-0.0376708,-0.04329296,-0.0142871,0.08888213,0.05153379,-0.08973635,-0.01783837,0.05281583,0.00615204,-0.04843744,-0.03139981,0.00796381,-0.06567194,0.01015346,0.03633929,0.0527931,-0.03564496,-0.04704669,0.03958671,0.03521762,0.07712349,0.00205658,-0.00407794,0.04907869,0.00380294,-0.08392105,0.03248531,-0.04921435,-0.02904902,0.05481153,-0.00400218,-0.02707732,0.02929748,0.01428061,-0.04186171,0.00560684,-0.05347472,-0.03336031,-0.00605271,-0.03414574,0.07743011,-0.01669938,-0.02167665,0.04475296,-0.05872716,0.06261606,0.01381875,-0.03866385,0.05188636,-0.02045918,-0.09881444,0.03531739,0.11050442,0.03867774,0.00774377,-0.00414089,-0.02735927,0.06485499,0.01857088,0.04549043,0.0116288,-0.01632436,-0.13575192,-0.22305198,0.00959768,0.04799209,-0.05288005,0.02184423,-0.02461158,0.04778879,-0.017524,-0.01978773,0.07105932,0.08224238,-0.0109799,0.04417753,0.01150783,-0.02291784,-0.00193529,-0.01771792,-0.09269604,-0.03415877,-0.01128483,0.01324419,0.00551799,-0.02913157,-0.07962365,0.05480653,-0.01035891,0.1313121,0.04861467,0.01772221,-0.05096827,0.051795,-0.00876846,-0.00303975,-0.08088224,0.06039918,0.03547058,0.05387771,-0.00853816,0.02973156,-0.00362436,-0.02343919,0.02678649,-0.00371988,-0.06415959,0.01883426,-0.04863336,-0.08675451,-0.05681416,-0.00406214,0.0968238,-0.01995456,0.03502294,0.03660604,0.07832798,-0.00650104,-0.02047312,-0.07000504,-0.08680734,-0.04692741,0.00520512,-0.00894924,-0.05377706,-0.00181769,-0.08902992,0.04890244,0.06044255,-0.01566345,-0.07156337,0.00054998,-0.02549087,-0.09341706,0.1117157,0.05299385,-0.00846619,-0.02299519,-0.00708878,-0.03123993,0.0354182,-0.01160705,0.02931677,-0.01279141,0.02002736,0.0305062,0.00765222,-0.00666691,0.04438713,-0.0006165,-0.02953376,0.06374399,-0.02887012,-0.08546798,-0.00304118,-0.02231862,0.02560467,0.0489361,0.00993227,-0.23206612,0.02005665,0.06432497,0.00586481,-0.0460818,-0.03394059,0.01157265,-0.1177564,-0.00937142,0.03678678,-0.02090307,0.05014658,0.02282308,-0.0068792,-0.01205574,0.01224775,0.02171755,-0.0087014,0.08438819,-0.08017456,-0.02076929,-0.02424723,0.24554525,-0.0571968,0.00540819,0.02953838,-0.02069277,-0.00616274,0.03915191,0.05627576,0.03348442,0.04417247,0.04638885,0.01164477,-0.02640757,0.02405155,-0.03156275,0.01507985,0.0380384,-0.02075639,-0.03930693,-0.0166798,-0.06027859,-0.00824163,0.0598113,-0.12280905,-0.04402531,0.00284855,-0.0217565,-0.03587659,-0.00179233,0.03567759,0.01501531,-0.01397948,0.06509256,0.00238806,-0.06122993,0.02356996,-0.02985179,0.01432628,0.00955864,-0.07533854,0.04955825,0.0633886,0.0034857],"last_embed":{"hash":"9naabg","tokens":260}}},"text":null,"length":0,"last_read":{"hash":"9naabg","at":1751288829290},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#CSS Preprocessing via Plugins##Example plugins","lines":[848,857],"size":501,"outlinks":[{"title":"styled-jsx-plugin-sass","target":"https://github.com/giuseppeg/styled-jsx-plugin-sass","line":5},{"title":"styled-jsx-plugin-postcss","target":"https://github.com/giuseppeg/styled-jsx-plugin-postcss","line":6},{"title":"styled-jsx-plugin-stylelint","target":"https://github.com/giuseppeg/styled-jsx-plugin-stylelint","line":7},{"title":"styled-jsx-plugin-less","target":"https://github.com/erasmo-marin/styled-jsx-plugin-less","line":8},{"title":"styled-jsx-plugin-stylus","target":"https://github.com/omardelarosa/styled-jsx-plugin-stylus","line":9}],"class_name":"SmartBlock","last_embed":{"hash":"9naabg","at":1751288829290}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Rendering in tests": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00679464,0.01876902,0.03096231,0.0075543,0.04071995,-0.01828635,-0.07601452,-0.00769994,0.00430119,-0.04008323,-0.02281632,-0.01963811,0.06427915,0.04086131,0.01659247,0.03750771,0.03961023,0.06474571,-0.03682281,-0.01158538,0.05004151,0.01416396,0.03069767,-0.05120388,-0.03160279,0.03286045,0.01572633,-0.05186017,-0.00731808,-0.21634902,0.01571553,-0.02404794,-0.05378203,-0.01099759,0.06246262,-0.0315477,-0.02323668,0.028368,-0.02362903,0.01577125,0.00048722,0.04252338,-0.02815827,-0.05791084,-0.03148768,-0.02058118,-0.00555394,-0.02425814,-0.03164227,0.04336876,-0.05826054,-0.0734439,0.04600021,-0.04331094,0.00846026,0.10271456,0.06692132,0.06726062,0.01800791,0.03196677,-0.03003995,0.02966094,-0.15255329,0.09685881,0.04063438,0.00815818,-0.06171796,-0.03035646,0.03337794,0.00135599,0.03043534,0.04965131,-0.01449971,0.12275241,0.02327542,-0.04019684,0.00561517,-0.05274443,0.07582694,-0.03712204,-0.07616099,-0.05947739,0.00081111,-0.0101022,0.07388366,-0.01304989,0.05973598,-0.01523963,0.03140518,0.02705172,-0.01846882,-0.11980746,0.05401729,-0.00697232,-0.00864693,0.00203713,0.02758404,0.03950005,-0.0242788,0.12355673,-0.07529785,0.05194536,0.01649541,0.00318275,0.05332888,-0.04545969,-0.02499533,-0.04120692,-0.02906596,-0.05216095,0.02769062,-0.01907348,-0.0203766,-0.06748148,-0.06401281,-0.03026318,0.02594849,-0.03052113,-0.00609349,-0.02327648,0.03288496,0.01958356,0.04325623,-0.00291047,0.04891121,0.03942037,0.00835444,0.06242906,0.01660674,0.05517417,-0.01198505,0.04544964,-0.0278085,-0.04398884,-0.01366191,0.04772137,-0.01532901,0.026082,-0.0034136,0.06555201,0.03852778,-0.05070386,-0.00442372,0.00876155,0.01180881,-0.00472386,-0.06120966,0.08645272,-0.01454838,-0.01727135,-0.03959462,0.07567184,-0.0891678,0.03484091,-0.00919918,-0.00144611,-0.01551,0.02725781,-0.04337112,0.05598973,-0.00765011,-0.01845121,0.00614731,0.02597842,0.01261514,-0.03686128,-0.08732066,0.09265584,0.04102224,-0.10888515,-0.05580139,-0.00811227,-0.04081465,-0.01216828,-0.01353016,0.00053916,-0.01023733,0.05427898,0.00323554,0.02478495,0.05474596,-0.02556997,-0.03948783,0.04369885,0.00743553,-0.06266531,0.00331419,-0.09677519,0.01291789,0.00820569,-0.02782808,-0.01598482,-0.00556199,0.00892564,0.02168619,-0.01221433,-0.02894012,-0.00242381,0.03953847,-0.02474008,0.16905372,0.0145027,0.02634942,0.04696065,-0.0740077,0.0745127,0.03107257,-0.03078555,0.05764315,0.01426404,-0.09945477,0.00473426,0.07440492,0.06689228,0.01548749,0.01726927,0.00979179,0.04773466,0.05403713,0.07447714,-0.02327969,0.00461929,-0.11249445,-0.24351212,0.011774,0.04908033,-0.0105387,-0.01402471,-0.03327975,0.03061499,0.00074858,-0.02022828,0.07817235,0.07219175,0.02236171,0.01365622,-0.04559627,-0.03449464,-0.02674445,-0.01268108,-0.05988571,-0.03439922,-0.01751472,-0.0171691,-0.05707435,-0.02267685,-0.12570506,0.07607746,-0.00330138,0.15029728,0.03340608,-0.0128304,-0.03923571,0.01899646,0.00419866,0.0236513,-0.07650403,0.07942038,0.03644968,0.01137467,0.00342163,0.02180277,-0.01991425,-0.01125784,-0.04002443,0.0017703,-0.04656268,0.08437423,-0.08528606,-0.04869732,-0.03090023,-0.02383987,0.07172177,0.02153062,0.01338609,0.04791677,0.09558409,0.00212467,-0.01278969,-0.03790088,-0.07043595,0.02440544,-0.00489723,0.00838108,0.00401125,0.01394326,-0.08593491,0.01387119,0.04938059,0.01579149,-0.0553405,0.01546843,0.0014372,-0.05250065,0.13469741,0.04241943,-0.0042257,0.0120164,0.00050426,-0.07349852,0.03977621,0.02637334,0.03985734,0.02424383,0.03887464,0.03390829,0.01891644,-0.0317305,-0.00719417,-0.01710634,-0.00908276,0.06031952,-0.02702206,-0.08064374,0.02800375,-0.05584563,0.00368459,0.06322047,0.00337013,-0.21355224,-0.03915936,0.0283371,-0.01126858,0.00277846,-0.02308709,0.05019498,-0.06097837,-0.04112307,-0.00306737,-0.01911373,0.052237,0.02985938,-0.01824936,0.00668625,0.0326881,0.03676213,-0.0130994,0.08342227,-0.07269783,-0.01507283,-0.01279983,0.24602552,-0.04088889,0.0009732,0.04160094,-0.04259155,0.00880928,0.03877685,0.07760201,0.00859635,0.04233929,0.07472558,-0.00929877,-0.06201353,-0.01182672,-0.01096673,-0.04443581,0.0238005,0.009737,0.01016546,-0.00121746,-0.02255227,0.01038792,0.03825715,-0.11472355,-0.04741242,0.02000144,-0.00426543,-0.04593624,-0.02985103,0.04423057,-0.01221635,-0.00719008,0.01194474,0.02582676,-0.03661426,-0.01246793,-0.06750726,-0.0308918,0.00344348,-0.0132423,0.02651135,0.0883846,-0.02052445],"last_embed":{"hash":"1yu3ey7","tokens":507}}},"text":null,"length":0,"last_read":{"hash":"1yu3ey7","at":1751288829367},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Rendering in tests","lines":[858,898],"size":1516,"outlinks":[{"title":"Config Merging options","target":"https://babeljs.io/docs/en/options#config-merging-options","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"1yu3ey7","at":1751288829367}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Rendering in tests#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01279576,0.02566645,0.04298332,0.01064135,0.0394676,-0.00461129,-0.06700279,-0.0104974,0.00031636,-0.04788816,-0.01938591,-0.01039915,0.06205892,0.0409571,0.01973528,0.02008338,0.03298737,0.05092439,-0.03108714,-0.02303155,0.04297066,0.01988482,0.02134908,-0.05176768,-0.03220691,0.04177752,0.00782045,-0.04972855,-0.00536489,-0.21667875,0.02035346,-0.0115301,-0.0470066,-0.01317781,0.07197989,-0.03005033,-0.0188962,0.04159092,-0.0416155,0.01507878,0.00490577,0.04217614,-0.02219751,-0.05886064,-0.03021568,-0.03363996,-0.00258536,-0.03314491,-0.01582162,0.03709866,-0.05048845,-0.07940754,0.05099167,-0.04605448,0.01041133,0.09255726,0.06314176,0.08190076,0.01386705,0.03230716,-0.02654251,0.02170973,-0.15295304,0.08454729,0.04741526,0.00730566,-0.05758577,-0.02652097,0.03982634,0.00328185,0.02877844,0.05786985,-0.02013315,0.11712204,0.01151171,-0.03776947,0.00379777,-0.04888603,0.07922482,-0.03890396,-0.08492299,-0.06148645,-0.00067743,-0.00168013,0.07758839,-0.02385256,0.06863602,-0.01582259,0.03604136,0.01466074,-0.03551803,-0.12344843,0.03572314,0.00668353,0.00305635,0.01467465,0.03372614,0.03102246,-0.01695045,0.13298434,-0.07235462,0.05139354,0.01371734,-0.0005376,0.04829278,-0.05146618,-0.02983379,-0.02957369,-0.02343804,-0.04717797,0.03014904,-0.0125075,-0.03016037,-0.05801569,-0.06781138,-0.02424726,0.01683928,-0.01894908,-0.01493669,-0.03118748,0.04142568,0.00894985,0.05363209,0.01139577,0.05909295,0.02886922,0.02890724,0.05964875,0.00314553,0.0587053,-0.01903224,0.04685187,-0.03424086,-0.04773959,-0.01969092,0.04231639,-0.01148801,0.03426498,0.00084359,0.07515015,0.03232517,-0.05038467,0.00665371,0.00431921,0.00655878,-0.00522261,-0.06506194,0.08969627,-0.00472256,-0.01362071,-0.05027086,0.07677604,-0.08557019,0.02643079,-0.00994945,-0.00908105,-0.03064167,0.01862976,-0.04314144,0.0541984,-0.00480406,-0.00423091,0.01513842,0.02843693,0.00959213,-0.03309486,-0.0930218,0.09497169,0.042323,-0.10672183,-0.06213435,0.00289725,-0.03823523,-0.02730421,-0.00975196,0.00001429,-0.01112225,0.06536728,0.0028385,0.01714258,0.05313165,-0.03307142,-0.0425851,0.02819538,0.02093909,-0.06581904,-0.00004932,-0.0830912,0.00856485,0.00475098,-0.02708833,-0.01416567,-0.018591,0.0169792,0.02588045,-0.01921298,-0.02748857,0.00590952,0.03170522,-0.0247488,0.15759243,0.01231884,0.02691292,0.05371265,-0.06422413,0.09172089,0.01744294,-0.02824997,0.07459224,-0.01285041,-0.10222366,-0.00153854,0.08758428,0.07137808,0.01055419,0.02115989,0.01502743,0.05426297,0.04773552,0.07435291,-0.0072721,-0.00263831,-0.10399897,-0.24070893,0.02102687,0.03917462,-0.00550018,-0.01194404,-0.03567443,0.03286695,-0.00265644,-0.01955181,0.07507311,0.06401427,0.01851944,0.02162691,-0.03562817,-0.03820919,-0.02486019,-0.00431219,-0.06428568,-0.04381703,-0.01006481,-0.01285175,-0.05812941,-0.01721784,-0.11850846,0.07809822,-0.01024476,0.15706266,0.01884448,-0.00295533,-0.03491517,0.02008105,0.01092056,0.01006367,-0.07713883,0.06108013,0.03684697,0.01860226,0.01765518,0.01370147,-0.01166238,-0.00834933,-0.05111519,0.00380706,-0.05706425,0.07917176,-0.09386447,-0.05668503,-0.02205763,-0.0315958,0.06186449,0.02078903,0.01855467,0.06741258,0.10172284,-0.00912658,-0.01658084,-0.03677527,-0.06207291,0.02216624,-0.00342189,0.00890796,0.01912281,-0.00805074,-0.09649062,0.01441614,0.04538668,0.00304369,-0.05456971,0.02486465,0.0090656,-0.04996586,0.13089591,0.03526467,-0.00793022,0.01308304,0.00001332,-0.05417333,0.03447291,0.02447567,0.04058632,0.01527499,0.01853379,0.03314897,0.01498801,-0.02626473,-0.00708833,-0.00903435,-0.00544478,0.05723473,-0.01818206,-0.0776837,0.0175371,-0.04497216,-0.00893542,0.04729712,0.01700613,-0.21248063,-0.03813537,0.01679889,-0.00816323,-0.00541988,-0.02474678,0.03267134,-0.05271719,-0.03846093,-0.0089598,-0.01186464,0.06960159,0.03861139,-0.0243865,0.00809041,0.04183206,0.0477883,-0.01727993,0.0911314,-0.07392071,-0.00367782,-0.01973497,0.24868345,-0.02527289,-0.00025754,0.04528601,-0.05643814,-0.0034214,0.01489731,0.06901661,0.01203532,0.0365649,0.08364878,-0.00652647,-0.06551106,0.0018853,0.00996342,-0.04248663,0.02364174,0.0097072,0.01740182,-0.0136951,-0.03431596,0.02110847,0.03398909,-0.11287338,-0.0591715,0.01759806,-0.01790827,-0.03609851,-0.02931858,0.03234033,-0.00532094,-0.01732104,-0.00182406,0.02921978,-0.04817041,0.00277985,-0.0641635,-0.03375607,0.01075125,-0.01862415,0.03784534,0.09033711,-0.03002132],"last_embed":{"hash":"if06ze","tokens":368}}},"text":null,"length":0,"last_read":{"hash":"if06ze","at":1751288829534},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Rendering in tests#{1}","lines":[860,888],"size":1089,"outlinks":[{"title":"Config Merging options","target":"https://babeljs.io/docs/en/options#config-merging-options","line":3}],"class_name":"SmartBlock","last_embed":{"hash":"if06ze","at":1751288829534}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Rendering in tests##styled-jsx/css in tests": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01255841,0.00732117,0.03807377,-0.01693136,0.03776266,-0.02105193,-0.09399051,0.0005047,-0.00512863,-0.03211118,-0.00700556,-0.03894021,0.05690958,0.02155365,0.01958436,0.04272458,0.02673611,0.0748562,-0.02977246,0.03388815,0.0695115,0.01109114,-0.00077587,-0.03998266,-0.00948086,0.03352356,0.00047014,-0.05193609,0.00784115,-0.1938723,0.01808601,-0.04676703,-0.04420095,-0.00976666,0.05815487,-0.02226865,-0.04058946,0.01344977,0.00040495,0.02236043,-0.00652586,0.05396179,-0.04082608,-0.03115274,-0.00771955,0.00332925,-0.00582719,0.01291928,-0.03796116,0.02557111,-0.07499409,-0.05080998,0.06311677,-0.05319345,0.00697147,0.11950073,0.0639619,0.03221345,0.02154918,0.02509823,-0.01935207,0.05386512,-0.16119613,0.09785643,0.02452479,0.02734614,-0.06876636,-0.02989208,0.00807454,0.01593807,0.03151164,0.02976156,-0.00598018,0.10504179,0.04379697,-0.03971382,0.01005555,-0.05455051,0.05537966,-0.03622212,-0.07726888,-0.06290261,-0.00317863,-0.01895054,0.03842716,0.00871632,0.01476855,-0.00512214,0.02063523,0.05650593,0.00164566,-0.0866896,0.07393849,0.00196525,-0.04390527,-0.02301285,0.01464452,0.05687951,-0.01460911,0.13356225,-0.079731,0.04801461,0.02560414,0.02059637,0.05203653,-0.02575191,-0.00766107,-0.06781533,-0.05386589,-0.0340752,0.02803052,-0.02667317,-0.00833028,-0.06386883,-0.03795157,-0.04494431,0.04298394,-0.01465336,0.00937967,-0.00173033,0.0135578,0.05953991,0.02984964,-0.03790208,0.02533572,0.02499072,-0.01411827,0.04629784,0.02127275,0.04340621,-0.01781027,0.05455438,-0.017895,-0.04044665,-0.01395038,0.06558553,-0.00726603,-0.00378501,-0.0124344,0.04199724,0.02937299,-0.05294956,-0.01682913,-0.01085245,0.04708771,-0.01057344,-0.05272661,0.08126386,-0.01782062,-0.00545728,-0.04665518,0.05041679,-0.0920862,0.03016524,-0.01569331,-0.00828245,0.01179034,0.04436718,-0.04546386,0.04618255,-0.00082045,-0.0416881,-0.02873669,0.03912319,0.00886451,-0.04086336,-0.05839936,0.08464985,0.04303911,-0.10619445,-0.02188993,-0.00500258,-0.05204001,0.01185516,-0.00374338,0.00032725,-0.02198649,0.01262606,0.02298619,0.02496267,0.08035494,-0.02350237,-0.01695562,0.0750167,-0.02632757,-0.06316605,0.01954216,-0.10315318,0.00738006,-0.00301782,-0.00889078,-0.01262369,-0.00293082,-0.00384232,0.00312616,-0.00394405,-0.02679466,-0.0165681,0.04841653,-0.00902956,0.15876254,0.01504729,0.01681577,0.05011955,-0.08794279,0.03483099,0.04711705,-0.03989984,0.03278955,0.06099673,-0.10410475,0.01521003,0.06958859,0.06636851,0.00449883,-0.00437072,-0.00105325,0.04499121,0.04544678,0.04748501,-0.03532657,0.01830632,-0.10064971,-0.24249685,-0.00903684,0.06340362,-0.03699633,-0.00265967,-0.02999179,0.0268092,0.02827209,-0.02806338,0.07489604,0.08246883,0.02933673,0.00798533,-0.06235409,-0.01760225,-0.03081319,-0.04075404,-0.0532947,-0.03313939,-0.04712832,-0.01700174,-0.05325682,-0.06768327,-0.13386168,0.07251379,0.0116955,0.14277034,0.07968904,-0.00844896,-0.0512207,0.02490156,-0.01457861,0.0406894,-0.09184016,0.0944476,0.03830108,0.01533524,-0.03403921,0.0348984,-0.04464656,-0.01807894,-0.01128103,-0.00641231,-0.02273291,0.08198376,-0.04264428,-0.03530896,-0.04198487,0.00705497,0.09458032,0.00125711,0.00213062,0.00822235,0.08179554,0.03170928,-0.00730209,-0.04801288,-0.08015687,0.01942538,0.00494442,0.01223492,0.00471031,0.04052234,-0.07120328,0.03343631,0.04299391,0.03739966,-0.05629162,-0.0036819,-0.03142935,-0.05567224,0.09952042,0.05235229,-0.02026661,0.01042621,0.01575188,-0.08186736,0.05264641,0.03402396,0.0146702,0.04444783,0.03619985,0.03462278,0.00697031,-0.03909307,0.00482081,-0.05595133,-0.03130503,0.06695098,-0.04974994,-0.08267125,0.04361411,-0.06676593,0.03109011,0.09731547,-0.03283591,-0.20811808,-0.01689669,0.04048922,-0.00047703,-0.01747779,-0.00539763,0.07484936,-0.07627948,-0.05785345,0.00979344,-0.01141199,0.00667902,0.01053745,-0.00731445,0.01107486,0.01923509,0.03536891,0.00241307,0.07400506,-0.06163441,-0.01901479,0.00905227,0.24659394,-0.07133338,0.01084117,0.03365152,-0.01481034,0.01085385,0.089371,0.08173214,0.00972057,0.04947738,0.05439613,-0.01003939,-0.04373147,-0.04174782,-0.04149153,-0.01886695,0.03703885,0.006386,-0.00788558,0.00532146,-0.01159715,-0.01112147,0.05684441,-0.07860512,-0.04307492,0.0065301,-0.00455754,-0.04582135,-0.03326907,0.04311958,-0.01877379,0.02253046,0.03617307,0.02478389,-0.037834,-0.04220776,-0.06133602,-0.01488977,-0.00620465,0.00616564,0.00830687,0.05519424,-0.00258028],"last_embed":{"hash":"spvk3l","tokens":168}}},"text":null,"length":0,"last_read":{"hash":"spvk3l","at":1751288829636},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Rendering in tests##styled-jsx/css in tests","lines":[889,898],"size":403,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"spvk3l","at":1751288829636}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Rendering in tests##styled-jsx/css in tests#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01196078,0.01166619,0.03637541,-0.01457197,0.03963045,-0.0152421,-0.09388485,-0.00142896,-0.00679346,-0.03425194,-0.00698594,-0.0355591,0.05473699,0.02476086,0.02021875,0.04300287,0.02990253,0.07021289,-0.02773023,0.03501262,0.06491575,0.01430033,-0.00150787,-0.04024387,-0.01422114,0.02988424,0.00138073,-0.05369546,0.00506773,-0.19737007,0.01836727,-0.0521693,-0.04749794,-0.01008001,0.05974018,-0.02576755,-0.036968,0.01326838,-0.00428722,0.02445792,-0.00389587,0.05376,-0.04283221,-0.03045384,-0.00965872,0.00196273,-0.00422665,0.00888256,-0.03749202,0.02368246,-0.07213601,-0.05364996,0.06238713,-0.05531326,0.00502408,0.11834772,0.06205865,0.03483869,0.02161955,0.026751,-0.02031086,0.05188223,-0.16511968,0.0966531,0.02657151,0.026828,-0.06680773,-0.02875611,0.01080211,0.01823148,0.02994916,0.02918547,-0.0056938,0.1093473,0.04171639,-0.03580934,0.01202914,-0.05570244,0.05580693,-0.03837405,-0.07805791,-0.06560714,-0.00175685,-0.02162212,0.03927749,0.00692967,0.01867212,-0.00382597,0.02151982,0.0547286,0.00373664,-0.08849185,0.07434198,0.00378763,-0.04389375,-0.02533023,0.01462811,0.05868765,-0.01188921,0.13399918,-0.08144052,0.05042272,0.02724426,0.02136772,0.0500547,-0.02687497,-0.00794734,-0.06856697,-0.05439632,-0.03428054,0.02792501,-0.02841051,-0.00555491,-0.0579356,-0.03949321,-0.04408017,0.04405899,-0.01210183,0.00992608,-0.00281029,0.01839758,0.05725994,0.03310975,-0.0381707,0.02795311,0.02579626,-0.01260463,0.04895826,0.01675449,0.03937675,-0.01601639,0.04949032,-0.02015936,-0.04022375,-0.01218728,0.06208783,-0.00741518,-0.0038765,-0.01433136,0.03993947,0.03084718,-0.05443,-0.01188241,-0.00973204,0.04624601,-0.01144193,-0.05514491,0.08366313,-0.01753316,-0.00716372,-0.04630493,0.04875043,-0.08936001,0.02966369,-0.01552687,-0.00729099,0.01058751,0.04435597,-0.04636123,0.04744716,-0.00015356,-0.03888136,-0.02901476,0.04017871,0.01238175,-0.03830965,-0.06122148,0.08261466,0.04346386,-0.10380796,-0.02519054,-0.00500711,-0.05307875,0.00988546,-0.00357666,0.00214786,-0.02359222,0.01508063,0.02238376,0.02264193,0.08483588,-0.0229789,-0.01799534,0.07633681,-0.02616703,-0.06362478,0.02002555,-0.10396917,0.00809588,-0.00624602,-0.01211589,-0.01419257,-0.00218259,-0.00448487,0.00261651,-0.00354564,-0.02855191,-0.01636802,0.04750968,-0.01046374,0.15583934,0.01381663,0.01843934,0.04866694,-0.0881528,0.03887688,0.04575025,-0.04078234,0.03612971,0.05708964,-0.10475533,0.01791806,0.07405113,0.0657244,0.0091261,-0.0042529,-0.00279687,0.04314329,0.0418461,0.04642563,-0.02974079,0.0196026,-0.10100333,-0.24462789,-0.01121584,0.06399097,-0.03427709,-0.00114954,-0.02389098,0.0289086,0.0300302,-0.02575264,0.07389044,0.07921501,0.03010131,0.01127714,-0.06193477,-0.01351659,-0.03338002,-0.04336833,-0.05363111,-0.031936,-0.04790172,-0.01754486,-0.05380045,-0.06162538,-0.13174731,0.07539442,0.01247899,0.14049987,0.07897144,-0.0081653,-0.0514817,0.02379346,-0.01435925,0.04266458,-0.08968817,0.09326431,0.04059279,0.01534283,-0.036509,0.03315905,-0.04375294,-0.01807033,-0.0142644,-0.00622849,-0.02543512,0.08366664,-0.0445703,-0.03409992,-0.03875418,0.00292874,0.09363333,0.00031551,0.00224036,0.00780198,0.08335014,0.03295342,-0.00819693,-0.04933884,-0.08204032,0.01917637,0.00433816,0.01094331,0.00599162,0.03946177,-0.07642493,0.03184818,0.04164579,0.03707027,-0.05888351,-0.00229858,-0.02821852,-0.0569277,0.10422716,0.05372299,-0.01732592,0.00991855,0.01556084,-0.08160211,0.05080434,0.03406421,0.01385245,0.04310275,0.03498549,0.03014375,0.00945752,-0.03763245,0.00637741,-0.05410281,-0.03370294,0.06680112,-0.05036379,-0.08498807,0.04399222,-0.06643556,0.03214782,0.09867167,-0.03141959,-0.20693934,-0.01787711,0.04233674,0.00307145,-0.02211502,-0.00866588,0.07371331,-0.07408439,-0.05625961,0.0129379,-0.00850046,0.00973183,0.01294633,-0.00571075,0.01027806,0.0162317,0.03673,0.00140456,0.07796119,-0.06141954,-0.02211,0.00825294,0.24395706,-0.07022625,0.00687517,0.03502802,-0.01113683,0.00759457,0.08550834,0.08374333,0.01489156,0.04797966,0.05151934,-0.01190114,-0.04467387,-0.03574146,-0.04046411,-0.01893412,0.03329396,0.00747775,-0.00643103,0.00215817,-0.01310656,-0.01021371,0.05833876,-0.07989497,-0.04269096,0.00660248,-0.00440923,-0.0463341,-0.03112086,0.04002212,-0.01746771,0.01976968,0.0362375,0.02729113,-0.03697206,-0.04471388,-0.06037106,-0.01652906,-0.00270803,0.00376103,0.00970567,0.0525114,-0.00097526],"last_embed":{"hash":"n3szjd","tokens":165}}},"text":null,"length":0,"last_read":{"hash":"n3szjd","at":1751288829715},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Rendering in tests##styled-jsx/css in tests#{1}","lines":[891,898],"size":373,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"n3szjd","at":1751288829715}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.02198394,-0.03999265,0.03991234,-0.03938266,0.02478319,0.02420132,-0.1057583,0.01693098,-0.00886757,-0.03630097,0.00211193,0.00726934,0.06947197,0.00941014,0.01259422,0.02270169,0.02189721,0.08854689,-0.04605876,0.01828885,0.04505667,0.00600237,0.00084805,-0.05619473,0.0096284,0.04767622,-0.01349491,-0.03957438,0.00251001,-0.2113784,0.04498639,-0.0128855,-0.02360261,0.01885561,0.03004258,-0.02494637,-0.01186544,0.03423268,-0.02636995,0.03781104,-0.03231405,0.07197215,-0.02072545,-0.038728,-0.01641483,-0.01597587,-0.00346741,-0.00370047,-0.02617954,0.04117833,-0.04681484,0.0059543,0.05457286,0.02587846,0.04153373,0.14813103,0.0350089,0.03548869,0.02472875,0.04725343,-0.01568128,0.04039709,-0.13814256,0.09812238,0.03130861,0.06131312,-0.04666123,-0.04506341,0.02844115,0.00210152,0.00900102,0.02407588,0.04102213,0.11967652,0.01096562,-0.05398674,0.03615091,-0.0406613,0.05173949,-0.02202296,-0.08719984,-0.05825341,-0.02008974,-0.05226299,0.05518091,-0.04013175,0.05068555,-0.03227416,0.01233145,0.04079079,-0.05365087,-0.10260376,0.06041932,0.02558103,-0.04168535,-0.00198302,0.00476188,0.03812165,-0.01009175,0.13013364,-0.08218156,0.0463689,0.04512537,0.005123,0.0534041,-0.01594698,0.01017699,-0.02778278,-0.05102842,-0.01293351,-0.01139435,-0.02812484,-0.01399118,-0.09344623,-0.05122272,-0.0531851,-0.03160969,-0.00353893,0.02900394,-0.05614162,0.0450349,0.05819293,0.00630491,-0.00187582,0.00546893,0.05031407,-0.00249849,0.03488286,-0.01169637,0.09748797,-0.0025696,0.01216502,-0.03222529,-0.02903323,-0.00598843,0.04261885,0.03062362,-0.0122258,0.00563208,0.06029882,0.01252222,-0.06421421,-0.00137664,-0.0043292,0.02234376,0.01372665,-0.04647802,0.06308062,-0.01270099,0.03443395,-0.11023743,0.08364796,-0.10206125,0.02156422,-0.03820298,0.02271575,-0.01474852,-0.03756154,-0.06752609,0.03600203,-0.00007675,0.0322504,0.02756927,-0.00549099,0.00524873,-0.08351744,-0.04364032,0.07693715,0.04233992,-0.10230064,-0.05010121,0.04920282,0.01134371,-0.0116584,0.06107047,0.03034551,-0.0328673,0.01506803,0.04995545,0.06049841,0.06792904,-0.05299679,-0.0203436,0.00910397,0.00968797,-0.08233008,0.0126221,-0.03071031,0.00865243,-0.02907943,-0.02096537,-0.02250285,-0.02539824,0.00103293,0.02437007,0.00727102,-0.04428376,-0.03765055,0.06639398,-0.00582653,0.1559972,0.02180675,0.00032898,0.04617647,-0.05785217,0.04267276,0.06327069,-0.03530262,0.01447967,-0.0322374,-0.12133394,0.02243296,0.08804484,0.08300714,-0.0329444,-0.01307283,0.00656527,0.04798387,0.00679133,0.04361273,0.04455834,-0.00765064,-0.08013606,-0.19083048,0.03335885,0.03894606,-0.04887841,-0.08483235,-0.05765549,0.0038398,-0.00981171,-0.04768827,0.05837587,0.0825622,0.03081652,0.01471066,0.00534236,-0.05622974,-0.04255083,0.00245086,-0.02873259,-0.05271124,-0.02316755,-0.01714928,-0.03666428,-0.00141102,-0.1143491,0.0662113,-0.00949613,0.16935398,0.06767781,-0.03118527,-0.00019317,0.0561443,-0.04324455,-0.02844289,-0.06651918,0.05280636,0.01637487,0.00178142,-0.05048379,0.00209608,-0.01214877,-0.00694269,-0.03979523,0.00877752,-0.04282664,0.04123055,-0.04049674,-0.06909322,-0.02742585,-0.00541883,0.06696033,0.02325572,0.03222509,0.0202068,0.1253943,-0.0156199,-0.00481548,-0.00571336,-0.01696113,0.01244402,0.00417052,-0.03718352,0.01370428,-0.01760942,-0.11424324,0.00518748,0.03452065,0.00340291,-0.05676597,-0.01689364,-0.03229841,-0.05004302,0.11093104,0.02355918,0.00142162,0.0201272,0.01820807,-0.05348832,0.09594176,0.02401653,0.05267073,0.01148514,0.00390059,0.02564767,0.00227822,-0.00053515,-0.01255431,-0.00781107,-0.05163458,0.00763582,-0.01998723,-0.05189053,0.02533217,-0.05965861,0.01833928,0.01836894,-0.00527854,-0.21345906,-0.02431693,0.02795859,0.00257488,-0.02028072,-0.02652421,0.05793113,-0.08519935,-0.07086764,-0.01340907,-0.04690664,0.02023051,0.01441903,-0.01596637,0.00270748,0.02826005,0.06405462,0.03516585,0.078019,-0.06867716,0.00140843,0.00404196,0.2406404,-0.01864825,0.01779618,0.02081244,-0.03866233,-0.03208997,0.05141382,0.07248381,0.02793136,0.02210234,0.08402985,0.00155827,-0.00858061,-0.05404325,-0.0334984,-0.03178705,-0.00236838,0.04862545,0.00792907,0.00014034,-0.02740982,0.01377975,0.07237527,-0.11551695,-0.03014687,-0.01166674,-0.02103671,-0.02425322,0.00198469,0.058506,-0.01149835,-0.03518301,-0.00787779,-0.00617467,-0.04854931,-0.00852903,-0.05598528,0.0363482,0.04884269,0.01995188,-0.00057574,0.07536922,0.00073802],"last_embed":{"hash":"7bjbnp","tokens":459}}},"text":null,"length":0,"last_read":{"hash":"7bjbnp","at":1751288829779},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ","lines":[899,951],"size":1765,"outlinks":[{"title":"the `resolve` tag from `styled-jsx/css`","target":"#the-resolve-tag","line":28},{"title":"article","target":"https://medium.com/@tomaszmularczyk89/guide-to-building-a-react-components-library-with-rollup-and-styled-jsx-694ec66bd2","line":52}],"class_name":"SmartBlock","last_embed":{"hash":"7bjbnp","at":1751288829779}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Warning: unknown `jsx` prop on &lt;style&gt; tag": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0217651,-0.03197,0.05156117,-0.02548009,0.05234592,0.01893674,-0.08533784,-0.00197372,-0.01773184,-0.04886167,-0.00962293,0.00491346,0.03334272,-0.01954642,0.01358747,-0.01510875,0.04700197,0.03407246,-0.02617204,-0.00754902,0.10763281,0.03712419,-0.00452964,-0.04032223,0.00845968,0.04899818,-0.01348159,-0.04115092,0.01695342,-0.17054942,0.02604936,-0.04166443,-0.04663164,-0.01091708,0.08824007,-0.03338924,-0.02498226,0.05072992,-0.00495178,0.01895929,-0.02354432,0.066066,-0.02133183,-0.03666676,0.00434277,-0.02781011,0.02078358,-0.01675011,-0.02064771,-0.01746738,-0.03063367,-0.02014141,0.07192933,-0.0105548,0.05205571,0.11570665,0.05342456,0.0690463,0.0245072,0.06183409,0.0076046,0.04191573,-0.1620284,0.07707947,0.02539921,0.0241049,-0.03590071,-0.06702949,0.01299998,-0.00100915,0.04478394,0.03973862,-0.00503556,0.06425714,-0.00287637,-0.02602941,0.03301533,-0.03724544,0.00107782,0.01325809,-0.06419393,-0.05650718,-0.04891073,-0.03533834,0.0217799,-0.0492488,0.03646765,0.01228959,0.01615295,0.048853,-0.03375765,-0.07854209,0.06789487,0.03842756,-0.02745793,0.00216686,0.0072124,0.06536426,0.00000212,0.14734113,-0.08086755,0.06258653,0.03658592,0.04882054,0.08266645,-0.05027586,-0.00692694,-0.02882955,-0.0318591,-0.01600994,0.00478267,-0.01534916,-0.03138089,-0.06982867,-0.02235963,-0.03003222,0.01623773,0.04314376,0.02156465,-0.04344961,0.02248369,0.05989667,0.03933394,0.00202853,0.02432822,0.01808346,0.01116008,0.04018988,-0.00715267,0.05414439,-0.01011453,0.05367631,-0.04464916,-0.02601692,0.00091961,0.04249267,0.01954594,-0.0078499,0.00652176,0.01992644,0.02425658,-0.04775701,0.01101341,-0.00535883,0.02635179,-0.00530425,-0.01517245,0.04057902,-0.0007691,0.04074463,-0.10695643,0.06786983,-0.12019802,0.02274677,-0.01770769,-0.01977308,0.00362917,0.0128472,-0.10631157,0.02583332,0.01441403,-0.00879717,-0.01443737,0.02498333,0.01039293,-0.06508536,-0.06649474,0.07380529,0.03969294,-0.11755841,-0.0383279,0.01961992,-0.03733834,0.01234185,0.01548107,0.02165094,-0.02125244,-0.02933071,0.02281289,0.0109807,0.07252982,-0.02835598,0.01123282,0.01207106,-0.01500086,-0.08153988,0.01168303,-0.03203496,0.01209862,0.02439351,0.01041122,-0.01569229,0.00829639,0.00582195,0.00047651,0.01680439,-0.03946901,0.01367478,0.06325775,-0.04522465,0.12704024,0.01385635,-0.00129213,0.08145354,-0.08133328,0.03000147,0.02381003,-0.05394613,0.05022732,0.02294484,-0.11447524,0.01176763,0.06755041,0.05182052,-0.04510699,0.00313457,-0.00549535,0.06974099,-0.00249204,0.00094887,-0.00752763,-0.00018271,-0.07776716,-0.23666768,-0.01145547,0.04770167,-0.03219949,-0.0290197,-0.01435256,0.0144196,-0.01001661,-0.05051701,0.08868968,0.10127182,0.03329944,-0.0055071,-0.03999782,-0.04412294,0.0193846,-0.01830323,-0.08753303,-0.07021189,-0.03198521,-0.00533212,-0.03433418,-0.06318673,-0.11597718,0.03592943,0.04628114,0.15229934,0.07009434,0.04398946,-0.03613894,0.0384188,-0.04551634,-0.0218548,-0.1051269,0.07594781,0.03248188,0.02633945,-0.03928196,0.01077916,0.01136709,-0.03541547,-0.07739072,-0.02827311,-0.05253508,0.05637363,-0.03181105,-0.05036657,0.01303326,-0.02459042,0.10107219,0.0154594,0.04896056,0.04339197,0.11940898,-0.01351328,0.00299696,-0.04607856,-0.05127471,0.01779612,0.02157752,-0.01286692,0.01637736,0.00448792,-0.12630828,-0.00112371,0.01232874,-0.008261,-0.05296676,0.01234278,-0.02624814,-0.05184356,0.09436528,0.04945116,-0.02640378,0.03183952,-0.00361353,-0.06951153,0.06310482,0.00158624,0.0242441,0.00349315,-0.01880034,0.0298726,0.01933916,-0.02622031,0.01776322,-0.0643905,-0.04143164,0.0386847,-0.0184382,-0.04126498,-0.00516547,-0.0163063,-0.02223278,0.08641822,-0.00957051,-0.22055051,-0.01928142,0.03945808,0.04621249,-0.05138425,-0.03861952,0.07623821,-0.07080121,-0.04856921,0.03719962,-0.01808157,0.00555727,-0.00894265,-0.03407222,-0.0042104,0.00452535,0.06309348,0.00540492,0.06059926,-0.030356,0.01012879,-0.00317251,0.26314688,0.00109384,0.02235986,0.02752324,-0.04296132,-0.0037365,0.04746797,0.101583,0.03799637,0.05027977,0.05658725,-0.01584175,-0.03213497,-0.03584064,-0.02209828,-0.01649878,0.01547092,0.02691993,-0.0052124,-0.00676465,-0.04997584,0.03030986,0.06564803,-0.12522241,-0.04751649,0.0111968,-0.02582179,-0.01709734,-0.03160046,0.049338,-0.00186411,0.01655068,0.00799235,0.00314076,-0.04475402,-0.01535221,-0.04819366,0.01855484,0.02745664,0.01738119,0.01699787,0.06715696,-0.01224169],"last_embed":{"hash":"1dps39k","tokens":98}}},"text":null,"length":0,"last_read":{"hash":"1dps39k","at":1751288829927},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Warning: unknown `jsx` prop on &lt;style&gt; tag","lines":[901,906],"size":268,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1dps39k","at":1751288829927}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Warning: unknown `jsx` prop on &lt;style&gt; tag#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01857416,-0.0317173,0.04900892,-0.02475992,0.05836683,0.01881205,-0.08203863,-0.00026935,-0.01680595,-0.05182965,-0.00792823,0.0090858,0.03148533,-0.01755124,0.00874474,-0.01798528,0.04447575,0.03709541,-0.02382642,-0.00991575,0.11026534,0.04381632,-0.00470786,-0.04172885,0.0121321,0.05042722,-0.0145457,-0.03997444,0.01453976,-0.17130455,0.0268513,-0.0450172,-0.05238348,-0.01171252,0.08969973,-0.03504417,-0.02326121,0.05292508,0.00035379,0.01844979,-0.02720217,0.0671599,-0.02011531,-0.03688201,0.00270242,-0.02839366,0.02586316,-0.0219435,-0.01885222,-0.01914705,-0.02789816,-0.01889769,0.06924391,-0.00678365,0.05132627,0.11275022,0.05267473,0.06804949,0.02449126,0.06735739,0.00994767,0.04015127,-0.16295759,0.0734205,0.0252108,0.02450543,-0.03704382,-0.06324715,0.01307964,-0.00064363,0.04405696,0.03798719,-0.0054505,0.06776614,-0.00200371,-0.0214937,0.03306991,-0.03819194,-0.00193708,0.01618289,-0.06166304,-0.05793978,-0.05355145,-0.03381519,0.020967,-0.0522533,0.04140813,0.01494835,0.01926178,0.04440138,-0.03117536,-0.07782356,0.06640972,0.03760648,-0.02906707,0.00364398,0.00601241,0.06567189,-0.00383052,0.15209171,-0.07965011,0.06103646,0.03200388,0.05665989,0.08382603,-0.05311349,-0.0067727,-0.02435216,-0.03651561,-0.0209563,0.00568666,-0.0161342,-0.02943486,-0.06884398,-0.02205573,-0.02716961,0.01662467,0.04566176,0.01997121,-0.04159221,0.02497609,0.05324437,0.04303424,0.00029515,0.02642267,0.01879924,0.01025235,0.04156981,-0.00819334,0.05397299,-0.00807159,0.04741039,-0.04229518,-0.02645195,0.00550599,0.03917934,0.0184965,-0.00449439,0.01132575,0.01172336,0.02328896,-0.04734626,0.01085968,-0.00108351,0.02101283,-0.00588621,-0.01612473,0.03809624,-0.00168396,0.04348575,-0.10656355,0.0678374,-0.1218385,0.02195089,-0.01563791,-0.02472929,0.00549714,0.01284547,-0.1033501,0.02625781,0.01479662,-0.00606667,-0.01587928,0.02412426,0.00760392,-0.05860166,-0.06725124,0.07041801,0.0365524,-0.1149465,-0.03904983,0.01595947,-0.03824212,0.01027307,0.01099025,0.02076136,-0.0251767,-0.02883316,0.0187585,0.00964879,0.06714004,-0.02889686,0.01189376,0.01249646,-0.01036702,-0.08247373,0.00842213,-0.03305817,0.01928282,0.02578646,0.01071971,-0.01473067,0.0035706,0.00265482,0.00012869,0.02182739,-0.03871704,0.01399659,0.06650848,-0.04724937,0.13047537,0.01814127,-0.00197771,0.0815623,-0.08054573,0.02956358,0.02493696,-0.05608833,0.04373679,0.02260032,-0.11226738,0.0073456,0.06554338,0.04633223,-0.04618054,0.00622336,-0.00749331,0.06834156,-0.00660253,-0.00168623,-0.00570207,0.0021277,-0.07759544,-0.23833553,-0.01150165,0.04749427,-0.03222199,-0.03630611,-0.01122919,0.01466195,-0.00845428,-0.04897504,0.09073353,0.09792467,0.03895022,-0.00810022,-0.04391783,-0.04026571,0.0203881,-0.01492693,-0.08999778,-0.06786274,-0.02991631,-0.01034656,-0.03375142,-0.06439123,-0.11082657,0.03251365,0.04674887,0.1531506,0.07370099,0.04406224,-0.03953144,0.034563,-0.04632436,-0.01881394,-0.10562208,0.07921708,0.0308049,0.02378687,-0.04091635,0.0109578,0.01393545,-0.03469042,-0.08076701,-0.03145055,-0.04850361,0.05737392,-0.03163994,-0.04458061,0.01482587,-0.02542954,0.10120551,0.01998036,0.04875375,0.04510657,0.11981147,-0.01111377,0.00059874,-0.04839529,-0.04814716,0.01878785,0.01949929,-0.01293644,0.0124731,0.00498757,-0.12751333,-0.00276867,0.00994909,-0.01016923,-0.05875119,0.01290154,-0.02372054,-0.04895762,0.09686695,0.04902243,-0.02611265,0.03057348,-0.00760071,-0.06870401,0.05356535,0.00089851,0.02794516,0.00561505,-0.01550452,0.03416109,0.02283891,-0.02511436,0.01729676,-0.06770156,-0.0353826,0.0412542,-0.01770179,-0.03906982,-0.00618161,-0.01277357,-0.02288239,0.08937283,-0.01010869,-0.2227388,-0.02148152,0.04071747,0.04254252,-0.051795,-0.03862002,0.07758838,-0.071402,-0.04542558,0.03804568,-0.01805955,0.0047045,-0.01325771,-0.0359973,-0.01108282,0.00663739,0.06513802,0.00797268,0.05967098,-0.02807706,0.01065293,-0.00232723,0.2603313,0.007718,0.02297914,0.02380454,-0.04103197,-0.00224017,0.04498705,0.10471687,0.03628089,0.04786076,0.05287815,-0.02079552,-0.02774924,-0.03077065,-0.02039796,-0.01735984,0.01528673,0.02812052,-0.00435394,-0.00947326,-0.04970489,0.02930097,0.06493235,-0.12633041,-0.04883923,0.00933289,-0.0230714,-0.01860533,-0.03267604,0.0469279,-0.00375949,0.02225569,0.00918114,0.00347283,-0.04341619,-0.0162334,-0.04810235,0.01541081,0.02952239,0.01980681,0.01847476,0.06626826,-0.01294728],"last_embed":{"hash":"1h5jr2t","tokens":96}}},"text":null,"length":0,"last_read":{"hash":"1h5jr2t","at":1751288829960},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Warning: unknown `jsx` prop on &lt;style&gt; tag#{1}","lines":[903,906],"size":214,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1h5jr2t","at":1751288829960}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Can I return an array of components when using React 16?": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0057028,-0.00708035,0.05237775,-0.01428688,-0.00038373,0.0282935,-0.08736654,0.03945203,-0.0030025,-0.05085132,0.02287986,-0.03406399,0.03814936,0.02116603,0.02552134,0.03576865,0.04453661,0.05396796,-0.02442368,0.03277355,0.04656801,-0.00592453,-0.02621654,-0.05947588,0.00176897,0.05440264,-0.01089024,-0.05794356,-0.00892725,-0.20033859,0.0367787,-0.00336893,-0.02420847,0.02906306,0.06199759,-0.00795684,-0.00226484,0.00626401,-0.04972128,0.04963482,-0.0282585,0.08906721,-0.044009,-0.03993077,0.01139086,-0.02514068,-0.01138574,-0.0260766,0.00847853,0.03302253,-0.03986509,0.03848025,0.06264168,0.00811451,0.03907641,0.12283484,0.00052169,0.04487874,0.00392134,0.01648494,0.02684483,0.03236737,-0.15252948,0.06374837,0.04115776,0.05128529,-0.03857462,-0.05805866,0.02167272,-0.0101603,0.000861,0.04216449,0.03133734,0.12003647,0.04005784,-0.0656247,0.01880058,-0.0310412,0.04424687,-0.03781324,-0.10016709,-0.05928526,-0.00277717,-0.01577504,0.0611934,-0.04762346,0.02937178,-0.00448725,0.02622979,0.02007228,-0.03645644,-0.07164565,0.04119125,0.04787141,-0.04404623,-0.00307113,0.05625636,0.03902074,0.03366578,0.15555944,-0.08162623,0.05548121,0.0548777,0.00475498,0.03219622,-0.01503721,-0.00205056,-0.03994435,-0.03327752,0.00007881,-0.02083697,-0.0346777,0.00233283,-0.07711046,-0.03710556,-0.05206004,-0.03676599,-0.01368642,0.00300391,-0.01931652,0.0364616,0.07016012,0.02647725,-0.01203056,-0.01783858,0.04197321,-0.00302394,0.01414332,-0.00980328,0.09145203,0.02177321,0.01479013,-0.01118961,-0.04534038,-0.00167609,0.04249687,0.04121516,-0.04540827,-0.00273629,0.06808762,0.01691937,-0.09113972,0.0296912,-0.03197134,0.0390325,0.03510815,-0.04690436,0.06419747,-0.03261095,0.02412664,-0.07180355,0.06489646,-0.09082322,0.02444428,-0.04789639,-0.01654108,-0.01792193,-0.0327,-0.04961542,0.04648182,-0.0092192,0.0210922,0.0277941,0.00580717,0.02845893,-0.08537958,-0.03257927,0.07889628,0.0190199,-0.073286,-0.02939369,0.0267885,-0.00173285,-0.04499207,0.08394184,0.03910693,-0.03655777,-0.01319794,0.0443981,0.04026272,0.04483393,-0.08687319,-0.04214874,0.02325957,0.01212944,-0.05658693,0.02970019,-0.05111898,0.01443584,-0.00501399,-0.00389142,-0.02455981,-0.01434169,0.03820077,-0.00622517,-0.03274245,-0.05865133,-0.01372878,0.02222445,0.00496938,0.15628827,0.02101481,-0.01132637,0.04818627,-0.03777597,0.04744071,0.0336363,-0.00541943,0.02156928,-0.03514099,-0.11770729,-0.01122187,0.10532343,0.09241062,-0.03296687,-0.04092615,0.00758272,0.05616835,0.01752382,0.04227206,0.05940138,-0.04023572,-0.1055682,-0.19608888,0.07355519,0.0201805,-0.06494151,-0.08095837,-0.04158205,0.0231223,-0.0183662,-0.01170515,0.04640951,0.09923892,0.02061048,0.03477482,-0.00643074,-0.05495751,-0.02878863,-0.0156857,-0.00294699,-0.07443313,-0.02184443,0.00045818,-0.06579611,-0.03675145,-0.09960418,0.0769498,-0.049867,0.17893595,0.07740634,-0.04228672,-0.02822188,0.04469427,-0.03867486,-0.02405941,-0.05812473,0.0336034,0.00678327,0.01390705,-0.02605947,0.00858966,-0.02306303,-0.04793167,-0.03085551,0.013043,-0.01672953,0.04145213,-0.02474199,-0.05107516,-0.0227964,0.01128392,0.07548259,-0.00199157,0.03122925,0.01596409,0.14022641,-0.02773439,0.01469691,-0.02687065,-0.02189041,0.00925716,0.03357527,-0.00801177,0.02484287,-0.04511095,-0.08789983,0.03464071,0.05252612,0.00526201,-0.03649843,-0.03789024,-0.02534805,-0.05193148,0.09431692,-0.005921,0.02106122,0.03411881,-0.01105206,-0.00624249,0.06969087,0.03642473,0.04640061,0.01917371,0.01101001,0.02895324,-0.00070544,0.00537358,-0.01235669,0.00548387,-0.05073175,0.00891726,-0.01050323,-0.04072592,0.01074673,-0.07273397,-0.0211473,0.00804299,0.00951525,-0.21611905,0.00440092,0.02772309,-0.00759123,-0.01683947,-0.02488901,0.07000422,-0.0836747,-0.06278327,-0.00272692,-0.02045166,0.01780053,-0.01955783,-0.03411816,0.00531651,0.0363125,0.06164485,0.03971544,0.10185003,-0.0639503,-0.01960503,-0.00107105,0.2362422,-0.05640237,0.00457903,0.01131693,-0.02370086,-0.06870839,0.06076467,0.04243226,0.01108332,0.01493471,0.081044,0.00080839,-0.03829526,-0.0316954,-0.00719712,-0.00090526,0.01294821,0.04571653,0.02579845,-0.01145994,-0.05490407,0.05134944,0.07762583,-0.11555778,-0.04846147,-0.04840867,-0.02492439,-0.02003756,-0.03118505,0.05178228,0.00745744,-0.02838862,0.0022139,0.00211265,-0.0520182,-0.01931642,-0.07139803,0.00120335,0.03801797,0.02784556,0.0333399,0.06204791,-0.00711458],"last_embed":{"hash":"e437ve","tokens":155}}},"text":null,"length":0,"last_read":{"hash":"e437ve","at":1751288829988},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Can I return an array of components when using React 16?","lines":[907,923],"size":390,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"e437ve","at":1751288829988}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Can I return an array of components when using React 16?#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00670185,-0.00614666,0.05302675,-0.01606831,0.00154906,0.02607955,-0.08239464,0.03837262,-0.00194932,-0.05037887,0.02412533,-0.03307388,0.04018976,0.02217315,0.02581224,0.03633957,0.04376315,0.05537392,-0.02353792,0.03165215,0.04662642,-0.00842789,-0.02601866,-0.0581558,0.0025744,0.0517154,-0.01406379,-0.05566007,-0.0089245,-0.19966638,0.0373458,-0.00385132,-0.02539898,0.02974498,0.06230591,-0.01070436,-0.00490698,0.00602697,-0.04734915,0.05131841,-0.02810969,0.09186572,-0.04417676,-0.04038355,0.01153477,-0.02291017,-0.0118871,-0.02443774,0.00995407,0.03512013,-0.03887743,0.0403781,0.06359159,0.0099525,0.04016727,0.12438522,-0.00034349,0.04567861,0.00259176,0.01446999,0.02593082,0.03227827,-0.1490107,0.06197583,0.04127264,0.05081032,-0.0369801,-0.05798621,0.0243706,-0.01002188,0.00238692,0.04283026,0.03104497,0.11973465,0.04153341,-0.06627877,0.01632007,-0.03330154,0.04657528,-0.03754277,-0.09928283,-0.05985,-0.00067813,-0.0150645,0.0621755,-0.04872017,0.02986043,-0.00485545,0.0274589,0.0215447,-0.03782853,-0.07304403,0.04061338,0.04569476,-0.04664046,-0.00413004,0.05595724,0.04287858,0.03395621,0.1556343,-0.0812615,0.05546182,0.05364626,0.00562568,0.03020019,-0.01362331,-0.00358308,-0.03764663,-0.03470389,-0.00008739,-0.01848645,-0.03349371,0.00074898,-0.07854689,-0.03513693,-0.05025246,-0.03871809,-0.0132952,0.00311788,-0.01954178,0.03596345,0.06814765,0.02827115,-0.01089199,-0.01799118,0.04171401,-0.00277509,0.01457612,-0.00951629,0.09258119,0.02257996,0.01486744,-0.01173557,-0.04569172,0.00070993,0.04289545,0.04336651,-0.04473778,-0.00425828,0.06982233,0.01655492,-0.09222855,0.03132642,-0.03045267,0.03957009,0.03471654,-0.04744435,0.06317502,-0.03205978,0.0214628,-0.07392051,0.06408367,-0.08877215,0.02257164,-0.05035179,-0.01707163,-0.01899637,-0.03370668,-0.0495051,0.0459088,-0.01031028,0.02375466,0.02796402,0.00781216,0.02792487,-0.08308633,-0.0326166,0.07894714,0.01753697,-0.07442895,-0.03024557,0.02560228,-0.002212,-0.04671865,0.0845846,0.04023905,-0.04038097,-0.01555422,0.04507807,0.04220561,0.0444984,-0.08595444,-0.04153548,0.0241618,0.01395504,-0.05722713,0.02865808,-0.05064245,0.01019295,-0.00777517,-0.00667941,-0.0246009,-0.01575769,0.03633613,-0.00585677,-0.03242135,-0.06050264,-0.01334556,0.02712296,0.00684465,0.155855,0.02065783,-0.00988275,0.04760559,-0.03638069,0.04876515,0.03369268,-0.00492866,0.01901768,-0.03658426,-0.11553238,-0.01058312,0.10703555,0.09430449,-0.033885,-0.04102969,0.01003126,0.05776496,0.01796653,0.04451575,0.05851928,-0.04212836,-0.10570846,-0.19571124,0.07318316,0.01871018,-0.06462743,-0.08303444,-0.0423997,0.02406363,-0.01786587,-0.01251183,0.04745815,0.0981572,0.02018006,0.03511794,-0.00517257,-0.05324367,-0.02976887,-0.01708446,-0.00182919,-0.07524264,-0.0213724,0.00143244,-0.06896716,-0.03746692,-0.09754464,0.07623238,-0.04960157,0.17910603,0.07700758,-0.04276246,-0.02669099,0.04455595,-0.03992863,-0.02525801,-0.05537213,0.03393604,0.00675822,0.01159956,-0.02593165,0.00917451,-0.02091835,-0.04582311,-0.0320175,0.012497,-0.01676207,0.04240797,-0.02684723,-0.05062552,-0.02249658,0.0109564,0.07661262,-0.00262902,0.02933377,0.01562211,0.1370786,-0.02486066,0.0139862,-0.0253667,-0.02299304,0.00742641,0.03034401,-0.00730183,0.02655973,-0.04329095,-0.0876551,0.0337504,0.05124017,0.00768727,-0.03442524,-0.03919073,-0.02286845,-0.05302974,0.09348761,-0.00526772,0.02271472,0.03398159,-0.01237214,-0.00452057,0.07114972,0.03735349,0.04700119,0.01740452,0.00945402,0.03180742,-0.00045686,0.00723844,-0.01155497,0.00557037,-0.05042233,0.00673709,-0.01080537,-0.04045841,0.01065218,-0.07369146,-0.02021401,0.00689269,0.00889604,-0.21527228,0.00418213,0.02802872,-0.00686877,-0.01774772,-0.02579358,0.06906582,-0.0852239,-0.06368376,-0.00140477,-0.01969774,0.01702988,-0.02032952,-0.0335056,0.00497771,0.03704637,0.06139486,0.03814223,0.10084144,-0.06491306,-0.0212408,-0.00066515,0.23979506,-0.0532833,0.00358597,0.01060305,-0.02540655,-0.06932003,0.05935428,0.03975643,0.00891991,0.01254119,0.08173712,-0.00149662,-0.03786238,-0.02807064,-0.00650116,-0.00130328,0.01394935,0.04376662,0.02324878,-0.01274815,-0.05678715,0.04758396,0.07677322,-0.11488358,-0.05327505,-0.05032326,-0.02489055,-0.01827008,-0.03045744,0.05250806,0.00973024,-0.03008482,0.00116996,0.00291937,-0.05441698,-0.01846796,-0.07030059,0.00040024,0.0379144,0.02855824,0.03219136,0.06002257,-0.0085369],"last_embed":{"hash":"1gqjl5n","tokens":153}}},"text":null,"length":0,"last_read":{"hash":"1gqjl5n","at":1751288830031},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Can I return an array of components when using React 16?#{1}","lines":[909,923],"size":328,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1gqjl5n","at":1751288830031}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Styling third parties / child components from the parent": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.01651688,-0.03134776,0.04198243,-0.08087578,0.03539894,0.02172456,-0.09028591,-0.01367776,-0.02454996,-0.01316176,0.01015196,0.01529528,0.08038592,0.01701062,0.04302317,0.01400176,0.01160969,0.11649662,-0.04725472,0.0171179,0.0241118,0.00733626,0.01613702,-0.03678141,0.00575414,0.04848383,-0.02968854,-0.02102252,0.02116856,-0.20670073,0.03061178,0.00687177,-0.00746422,0.02115724,0.00017612,-0.04168583,-0.01869709,0.04052566,-0.02827368,0.01605506,0.00158752,0.04208729,-0.02477631,-0.02218455,-0.02938326,-0.00555836,0.02093957,0.01792483,-0.02920628,0.00471147,-0.02102538,-0.04082739,0.03773491,0.01216159,0.01425571,0.16698086,0.05808804,0.00212103,0.01982962,0.02926538,-0.05714286,0.06294753,-0.13259147,0.13562782,0.02443429,0.05319422,-0.05035618,0.00496737,0.01046726,0.03505025,0.02263548,0.00347022,0.01246532,0.13351052,-0.00531612,-0.03353583,0.02545408,-0.06286054,0.05148919,-0.04912639,-0.09615371,-0.04586523,-0.00091926,-0.06413542,0.01327698,0.004471,0.04427902,-0.05151312,0.02026066,0.04504424,-0.07035065,-0.14604838,0.05647132,0.00710532,-0.03070515,-0.00910706,-0.0221179,0.02448688,-0.04967836,0.13215886,-0.08244479,0.03033379,0.02967128,-0.00323731,0.0439478,0.01058863,0.00043975,-0.03484736,-0.08502948,-0.0101264,-0.00290401,-0.02193229,-0.04593025,-0.09338833,-0.07794821,-0.04608293,-0.03816714,-0.01065826,0.05510673,-0.0572201,0.04657801,0.03206874,0.0076942,-0.01168099,0.03319784,0.03283127,0.02455256,0.05098784,-0.03297478,0.10524897,-0.01280522,0.0204142,-0.05129497,-0.04449478,-0.01033276,0.03589106,0.00138838,0.01105299,-0.03201027,0.01089899,0.01458661,-0.05491374,-0.03317869,-0.02079113,0.00130231,0.01989908,-0.0797163,0.09370874,0.00468217,0.00377437,-0.11267925,0.0586543,-0.07992283,0.0166142,-0.05650996,0.03917933,-0.00080382,-0.02497642,-0.05243622,0.01678557,0.01924026,0.0141754,0.0231087,0.03384498,-0.00186596,-0.08347686,-0.05325738,0.05323713,0.04582235,-0.10721134,-0.0477189,0.08611978,0.00711123,0.01924873,0.05324151,0.03703697,-0.05196519,0.00406811,0.05669075,0.04531217,0.07076705,-0.05090628,-0.0037484,0.00218578,0.01644109,-0.07815827,0.01661609,-0.04541161,-0.01929849,-0.05775182,-0.04169194,0.00416604,-0.03672031,-0.03898521,0.02439829,0.00420853,-0.04898855,-0.0519208,0.07745779,-0.02596935,0.13082029,0.01677138,0.00837548,0.05941756,-0.0475221,0.05309649,0.06177979,-0.038066,0.01264789,-0.02983177,-0.13432208,0.03368943,0.0746132,0.08476317,-0.00650996,-0.01075616,0.008032,0.02918752,0.01016398,0.05434754,0.01465985,0.0162522,-0.0643019,-0.19016054,0.0388822,0.0384624,-0.03236906,-0.07737312,-0.05829248,0.03401908,0.01738155,-0.04899684,0.04123485,0.06077216,0.03247379,-0.00106823,0.02675034,-0.04240852,-0.02694917,0.01868699,-0.02875566,-0.03817496,-0.00762586,-0.00618151,0.01230255,-0.00600527,-0.08612778,0.05295785,0.00533744,0.15754348,0.05508451,-0.00200647,0.0303532,0.07241222,-0.01870579,-0.02855848,-0.06029142,0.04074251,0.02722222,0.00336284,-0.05049832,-0.00829706,-0.02117242,0.0246886,0.01057477,0.0137681,-0.07034833,0.04017288,-0.05764838,-0.08231243,-0.02442881,0.0050696,0.06243778,-0.00057445,0.03405356,0.0203875,0.09854843,-0.01976925,0.00453701,0.00459039,-0.01639941,-0.00978125,-0.00410319,-0.03996785,-0.01907826,0.00333128,-0.11016095,0.00468514,0.03160511,0.01404144,-0.03729233,0.01892546,-0.03485399,-0.03002651,0.11199834,0.04927347,-0.01829212,0.00400993,0.02441478,-0.05113439,0.09669276,0.01356286,0.03508048,0.02653475,-0.02030712,0.03860249,-0.00623233,-0.00458716,-0.00315822,-0.02410274,-0.02507694,0.0040498,-0.04976407,-0.08341847,0.04369707,-0.04060565,0.06754963,0.02925112,-0.04415859,-0.21303424,-0.00888204,0.03656237,0.01810768,-0.04342278,0.01094619,0.01862842,-0.05756494,-0.06867272,-0.01049215,-0.01942227,0.04327148,0.0419692,0.00096022,-0.0120185,0.01848441,0.05182415,-0.00461433,0.05914469,-0.06930107,0.02252632,0.03093413,0.24953879,-0.00052595,0.04640287,0.01662024,-0.05258559,0.01102959,0.01095116,0.06942831,0.02402751,0.01978105,0.09342273,0.00356817,0.0225734,-0.040097,-0.0171156,-0.02993593,0.00333363,0.03554019,-0.02236538,0.01101351,-0.03417816,-0.00929862,0.05075973,-0.07076656,-0.02809639,-0.01146397,-0.00822539,-0.00385264,0.02578292,0.0393705,-0.01724316,-0.0292068,0.00364268,-0.00108119,-0.05121107,0.00574409,-0.0256563,0.05581777,0.02044457,0.00562342,-0.01833707,0.06970324,0.01423984],"last_embed":{"hash":"1h5sq1a","tokens":265}}},"text":null,"length":0,"last_read":{"hash":"1h5sq1a","at":1751288830073},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Styling third parties / child components from the parent","lines":[924,947],"size":810,"outlinks":[{"title":"the `resolve` tag from `styled-jsx/css`","target":"#the-resolve-tag","line":3}],"class_name":"SmartBlock","last_embed":{"hash":"1h5sq1a","at":1751288830073}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Styling third parties / child components from the parent#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.01786338,-0.0307829,0.04128683,-0.08323072,0.03369489,0.02084266,-0.09027625,-0.01263722,-0.02309456,-0.01319049,0.01255128,0.01538679,0.0810724,0.02013923,0.04405008,0.01159337,0.00854169,0.11518657,-0.04561269,0.01602833,0.0241245,0.00727763,0.01334151,-0.03763237,0.00888995,0.04680815,-0.03065646,-0.01929039,0.02023036,-0.20659719,0.03102595,0.00712781,-0.00516414,0.0221125,-0.00108918,-0.04365304,-0.01762904,0.04247702,-0.02581382,0.01442384,0.00159954,0.04425903,-0.02334078,-0.02430996,-0.03381814,-0.00586751,0.02175282,0.0172282,-0.02820958,0.0053045,-0.02076513,-0.04176015,0.03858903,0.01624694,0.01561109,0.16638483,0.05700102,0.00376328,0.02059076,0.02679593,-0.05760653,0.06217037,-0.13231868,0.13611819,0.02323594,0.05363251,-0.0486218,0.00664532,0.01135898,0.03461162,0.02498368,0.00515799,0.01421541,0.13556124,-0.00469663,-0.03334182,0.02576015,-0.06367667,0.05470379,-0.05008215,-0.0973861,-0.04289169,-0.0023166,-0.06321751,0.01294382,0.00482165,0.04347539,-0.05322844,0.02167767,0.04483435,-0.07297333,-0.14762796,0.05322105,0.00759505,-0.03073172,-0.00806177,-0.02612556,0.02525469,-0.04851824,0.1318984,-0.08721251,0.03007777,0.02804462,-0.00436292,0.045538,0.01422306,-0.00181035,-0.03540215,-0.08740301,-0.01244791,-0.00115063,-0.01938518,-0.04790466,-0.09488124,-0.07704417,-0.04831703,-0.03889653,-0.00744813,0.05703048,-0.05738116,0.04836224,0.03333573,0.00591988,-0.01179238,0.03339851,0.03246761,0.02985747,0.0520858,-0.03300564,0.10656167,-0.01343839,0.02152488,-0.05352983,-0.04419521,-0.00924709,0.03459538,0.00220184,0.00855944,-0.03237046,0.00958913,0.01308834,-0.05235391,-0.03203368,-0.02062263,0.00043342,0.01986678,-0.07871648,0.09089687,0.00521344,0.00671984,-0.11118388,0.06157401,-0.07589998,0.01592291,-0.05473507,0.03734977,-0.00299234,-0.02595836,-0.04929239,0.01396326,0.0195592,0.01825124,0.02350414,0.03145421,-0.00303318,-0.08327681,-0.05261923,0.0535133,0.04790645,-0.10506931,-0.04838497,0.08630641,0.00713682,0.01940137,0.05290215,0.03562839,-0.05071306,0.0054526,0.05488116,0.04871682,0.0713376,-0.04905417,-0.00316804,0.0026712,0.01863632,-0.07797443,0.01516745,-0.0461273,-0.01851752,-0.05849903,-0.03919383,0.0053071,-0.0360866,-0.0376812,0.02625563,0.00747808,-0.05003066,-0.05404011,0.07831527,-0.0271755,0.13216278,0.01608885,0.009994,0.05597913,-0.04782318,0.05453953,0.06224355,-0.03771715,0.01138587,-0.03189686,-0.13329604,0.03078604,0.07328621,0.08228841,-0.00739467,-0.01344691,0.00387982,0.03046856,0.00804409,0.05538071,0.01501632,0.01557978,-0.06394158,-0.18900259,0.04019971,0.03844175,-0.03214706,-0.07958729,-0.0573429,0.03448205,0.01809149,-0.0474094,0.03989318,0.06188591,0.03473995,0.00125334,0.02553936,-0.04302105,-0.02770998,0.02134881,-0.02974634,-0.03916325,-0.00824764,-0.00810641,0.01273472,-0.00543668,-0.08431788,0.05320535,0.00573069,0.15766311,0.05629778,-0.00109199,0.0293854,0.07435625,-0.01769179,-0.02561338,-0.05898446,0.0394989,0.02410007,0.00159139,-0.05224141,-0.00861858,-0.02323256,0.02447766,0.0123852,0.01537762,-0.07104649,0.04123383,-0.05698731,-0.08192683,-0.02511753,0.00509174,0.06186936,-0.00193563,0.03262392,0.01936953,0.10003614,-0.01876138,0.00251952,0.00691351,-0.01576912,-0.01137056,-0.00488267,-0.03976523,-0.01581362,0.00243212,-0.11162999,0.00385265,0.02902283,0.01421204,-0.03774608,0.01769405,-0.03145631,-0.03233829,0.11300764,0.04599763,-0.01715004,0.00347312,0.02405262,-0.04743584,0.09595805,0.01029696,0.03588523,0.02545035,-0.02123978,0.03649855,-0.00426539,-0.00344756,-0.00604271,-0.02295858,-0.02552491,0.00412205,-0.05057801,-0.0827658,0.04740231,-0.04148393,0.06950301,0.02766358,-0.04376614,-0.21159224,-0.00681408,0.035701,0.01742985,-0.04276145,0.01038021,0.01828812,-0.05728761,-0.06415189,-0.0115329,-0.01919541,0.04134575,0.04287556,-0.0005825,-0.01630533,0.02094814,0.05329832,-0.00332974,0.06116579,-0.0679632,0.02337122,0.03228671,0.24872072,0.00001453,0.04625573,0.0151864,-0.05510261,0.01210137,0.01011349,0.06743525,0.02365071,0.01778995,0.09266541,0.00267424,0.02303539,-0.04108232,-0.01897466,-0.03377014,0.00145929,0.03553284,-0.02492249,0.01150904,-0.03841368,-0.00844663,0.05047993,-0.07132907,-0.0298907,-0.01587737,-0.00548581,-0.0061548,0.02602145,0.03870128,-0.01558082,-0.02967419,0.00408889,-0.00158053,-0.04831427,0.00662538,-0.02330647,0.0521744,0.01912344,0.00492964,-0.01852656,0.06806275,0.01516951],"last_embed":{"hash":"1eeghsi","tokens":263}}},"text":null,"length":0,"last_read":{"hash":"1eeghsi","at":1751288830148},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Styling third parties / child components from the parent#{1}","lines":[926,947],"size":748,"outlinks":[{"title":"the `resolve` tag from `styled-jsx/css`","target":"#the-resolve-tag","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1eeghsi","at":1751288830148}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Build a component library with styled-jsx": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05289621,-0.00040356,0.06076866,-0.00962996,0.02980227,0.02151758,-0.10499819,-0.00210073,-0.031479,-0.04302778,0.00239778,-0.0073774,0.04710981,-0.00525319,0.06272618,0.01944871,0.01032559,0.05407242,-0.05264133,0.00041367,0.02057751,0.0207497,-0.03090467,-0.02005843,0.00013082,0.01886689,-0.01961039,-0.04620775,-0.00153089,-0.17779136,-0.03961201,0.01855365,-0.01999829,-0.00710498,0.04770909,-0.03670434,0.02312638,0.040857,-0.0487287,0.03395512,-0.0229573,0.06204681,-0.03974328,-0.04491119,0.02096446,-0.03229713,-0.00667312,0.02823976,0.00636947,0.00853494,-0.02046864,-0.00093861,0.05702119,-0.02700394,0.01254102,0.16629548,0.0482481,0.04656674,-0.0173035,0.0021459,-0.01205439,0.04198895,-0.13049193,0.10692034,0.05387988,0.02846268,-0.04208623,-0.00411872,0.07558245,0.04304836,-0.01896954,0.04582579,0.00103794,0.08273077,0.05359035,-0.05049313,0.04014385,-0.06213343,0.05996909,-0.06520057,-0.09860648,-0.00098012,-0.01408681,0.01674689,0.04728956,-0.00009249,0.0675544,-0.03722366,0.01934628,0.05269411,-0.05296605,-0.10136967,0.04962822,0.01992756,-0.03160714,0.00463626,-0.00429925,0.04844815,0.0002748,0.13827921,-0.09179566,0.0269749,0.03053624,0.0258596,0.02191299,-0.04122306,0.01704993,-0.02170014,-0.06541338,-0.0258275,0.03924402,-0.01463084,-0.0674741,-0.08742685,-0.02012418,-0.03970791,-0.06034008,0.00702758,0.02467981,-0.01087304,0.00673507,0.04144228,0.03766362,-0.0055106,0.01785147,0.01658412,0.01081311,0.01714815,-0.00023666,0.06476989,0.028897,0.07634496,-0.01073612,-0.03669235,-0.01014444,0.07984494,0.01811543,0.00210396,-0.01711774,0.09047484,-0.01145191,-0.03049234,0.02943505,-0.06670375,0.00342473,0.00207123,-0.07018192,0.09387022,-0.00846372,0.06448778,-0.06650084,0.0433,-0.06455617,-0.00439627,-0.01216434,-0.03562667,0.00459626,0.0033938,-0.07046346,0.00920858,-0.00497198,-0.0202324,-0.00448027,0.03697298,-0.00764328,-0.10875921,-0.06472258,0.09534343,0.05756585,-0.09238504,-0.03313129,0.04237584,-0.03083341,0.01265485,0.08310445,-0.00908707,-0.00535531,-0.0085305,0.03649127,0.02548339,0.09069669,-0.04087529,-0.02831418,0.01396709,-0.03076149,-0.09513123,0.03463149,-0.06860477,-0.01587223,-0.01269887,-0.06480651,0.01925548,0.00276826,0.00544254,0.00368043,-0.02970384,-0.06419627,-0.00658931,0.02793822,-0.00696399,0.1092779,0.01103381,-0.03857472,0.07117198,-0.05539898,0.05183738,0.00211078,-0.00681495,0.03580558,-0.04291506,-0.09768534,0.02642385,0.10194857,0.07847562,-0.01397444,-0.00713611,0.03447428,0.04786968,0.01439042,0.06074323,0.00780703,-0.04427102,-0.04401972,-0.2074094,0.06224524,0.01574534,-0.0334648,-0.06025868,-0.03991777,0.06779423,-0.01086055,-0.05740872,0.06274877,0.0922721,-0.00485586,0.07133691,-0.00125261,-0.05878073,0.00585916,0.00905741,-0.04598599,-0.09321382,-0.0395624,-0.04247956,-0.03278185,-0.03157348,-0.09784433,0.07355113,-0.01707021,0.15506682,0.03205157,-0.02483098,-0.05752934,0.07696523,0.03125553,-0.00939982,-0.06582973,0.01555739,0.01425456,0.00509114,-0.02087025,0.01841532,-0.01412225,-0.03507787,-0.006152,-0.00165879,-0.0646525,0.03908486,-0.07405859,-0.0742005,-0.0370444,0.01499515,0.08414537,0.00963248,0.01013622,0.03547763,0.08568057,-0.05850394,-0.00212582,-0.00456594,-0.02926559,-0.02330416,0.02969196,-0.00752877,0.03665128,0.0168872,-0.10534666,-0.0047986,0.03542155,0.00862409,-0.03800403,-0.02151999,-0.02476751,-0.01592142,0.07916883,0.00831079,-0.00591659,-0.01585176,0.0012911,-0.03709768,0.06623905,0.03210105,0.05670603,0.01660157,-0.02140803,0.05964422,-0.02299605,-0.03413786,-0.01360617,-0.01305431,-0.05950882,0.02059632,0.01567497,-0.0556937,0.02853923,-0.03469981,0.03904747,0.07565432,0.00603225,-0.24019328,0.01394229,0.03725361,-0.02562422,-0.06575438,-0.01972544,0.03202295,-0.06296401,0.00525291,0.01445204,0.01591218,0.0374655,-0.0051867,0.0152219,0.0659981,0.01193506,0.09607868,0.02747268,0.069238,-0.08288776,-0.01523767,0.00369855,0.26377693,-0.00850805,0.0261925,0.02902962,-0.03728377,0.00625192,0.01422576,0.0483383,-0.00416477,0.03554519,0.03394917,-0.00391184,-0.06077266,0.00124201,-0.03241661,0.02474606,0.00003472,0.04876078,0.003551,0.02745377,-0.02370224,0.03190371,0.05247343,-0.09695457,-0.04869395,-0.03330491,-0.06352674,0.00566373,-0.01845475,0.01928106,0.01402656,-0.02489516,0.0139606,0.01432262,-0.03675061,-0.02299842,-0.04756681,0.0087675,-0.00214221,-0.03077242,0.0505019,0.04175565,0.01458692],"last_embed":{"hash":"1sdz8zk","tokens":122}}},"text":null,"length":0,"last_read":{"hash":"1sdz8zk","at":1751288830225},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Build a component library with styled-jsx","lines":[948,951],"size":286,"outlinks":[{"title":"article","target":"https://medium.com/@tomaszmularczyk89/guide-to-building-a-react-components-library-with-rollup-and-styled-jsx-694ec66bd2","line":3}],"class_name":"SmartBlock","last_embed":{"hash":"1sdz8zk","at":1751288830225}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Build a component library with styled-jsx#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05444655,0.00073198,0.06011519,-0.00661027,0.02996197,0.02367983,-0.10477123,-0.00400184,-0.02986415,-0.04301366,0.00262275,-0.00684449,0.04938285,-0.0066721,0.06126958,0.01948849,0.01100225,0.05621945,-0.05431315,0.00141921,0.0199091,0.02309369,-0.02842994,-0.019089,-0.0018183,0.01756441,-0.02016649,-0.04388857,-0.00084462,-0.17587228,-0.04087837,0.01781102,-0.01693857,-0.00726475,0.04556921,-0.03565583,0.02236377,0.04288745,-0.04965116,0.03026032,-0.02154004,0.06344476,-0.0376225,-0.04357458,0.02052896,-0.03354383,-0.00901307,0.02576804,0.0037678,0.005255,-0.01859966,0.00140842,0.06004883,-0.02473864,0.01269408,0.16488706,0.04522419,0.04975699,-0.01766583,0.00227714,-0.0093596,0.04080725,-0.13365662,0.10624395,0.05031748,0.02861978,-0.042727,-0.00270229,0.07703479,0.04133982,-0.01747215,0.04625266,0.0000623,0.08077385,0.05333361,-0.05036679,0.03783121,-0.06600869,0.0603786,-0.06686987,-0.09645315,-0.00312013,-0.0098857,0.01719902,0.0489096,0.00013666,0.06902323,-0.03696753,0.01922078,0.05201712,-0.05448948,-0.10260713,0.0493435,0.01944305,-0.03060747,0.00306954,-0.00489238,0.0492736,0.00169766,0.13931061,-0.0937789,0.02929252,0.03216977,0.02430446,0.02550472,-0.04334689,0.02016777,-0.02404058,-0.06265526,-0.02508048,0.03987178,-0.01357447,-0.06929144,-0.08711562,-0.01961913,-0.03884793,-0.05960913,0.00623184,0.02356388,-0.00940394,0.00800225,0.04104395,0.0340171,-0.00617978,0.01708969,0.01706042,0.01097766,0.01599652,-0.001895,0.0605206,0.0254875,0.07562236,-0.01488383,-0.03925057,-0.00901073,0.08043771,0.01765816,0.00063596,-0.02039344,0.08819379,-0.01183346,-0.02869252,0.02954941,-0.06827499,0.00532719,0.00468335,-0.06824961,0.09530927,-0.00666252,0.06492493,-0.06570295,0.0437872,-0.06327206,-0.00428568,-0.01104717,-0.03756881,0.00311604,0.00499142,-0.06808604,0.00891141,-0.00551619,-0.01832118,-0.00366243,0.03394461,-0.00791707,-0.10632536,-0.06464684,0.0963761,0.05442253,-0.09061958,-0.03294836,0.04159136,-0.0302048,0.01547353,0.08335292,-0.0084966,-0.00914882,-0.00752257,0.03523289,0.02615471,0.08909322,-0.04250983,-0.02906166,0.01653986,-0.03041955,-0.09064782,0.03280137,-0.06948628,-0.01492163,-0.01063812,-0.06295256,0.01959577,0.00522214,0.00468704,0.00573133,-0.0295407,-0.06596322,-0.00927259,0.02736587,-0.0069201,0.10806975,0.01094371,-0.03837891,0.07026837,-0.05586758,0.05082382,0.00286822,-0.00877183,0.03328226,-0.04511936,-0.10131766,0.02638712,0.10026357,0.07970294,-0.0155867,-0.00629227,0.03390892,0.04759773,0.01215184,0.06043801,0.00827162,-0.04433354,-0.04729896,-0.21175548,0.06183635,0.01583609,-0.03728045,-0.06255926,-0.03982041,0.06631967,-0.01125879,-0.05963377,0.06442169,0.09078856,-0.00424582,0.0703556,0.00017521,-0.05591653,0.00667219,0.01155266,-0.04633223,-0.09299923,-0.03983,-0.04201458,-0.03159053,-0.03552045,-0.09787207,0.07495437,-0.01536046,0.15451944,0.03318994,-0.02320988,-0.05356087,0.07775866,0.03053304,-0.01093268,-0.06636531,0.01716031,0.01320397,0.00707614,-0.02101325,0.02035895,-0.01375777,-0.03664897,-0.00551652,-0.00125506,-0.06424928,0.03981986,-0.07382595,-0.07228276,-0.0365007,0.01633531,0.08223538,0.00965063,0.01134393,0.03691094,0.08606472,-0.05677215,-0.0035654,-0.00345791,-0.03104434,-0.02324952,0.02669355,-0.00735009,0.03640278,0.01821782,-0.104092,-0.00485111,0.03094811,0.00788186,-0.03784439,-0.01899722,-0.02277957,-0.01948088,0.07999901,0.00817638,-0.00739851,-0.01159935,0.00103053,-0.03686873,0.06591636,0.0302162,0.05840891,0.01312652,-0.02172165,0.05899596,-0.0198112,-0.03304158,-0.01376693,-0.01437364,-0.06018174,0.0208252,0.01294558,-0.05825387,0.02851539,-0.0340074,0.03940034,0.07602888,0.00684434,-0.23956752,0.01427132,0.03611108,-0.02248997,-0.0684355,-0.01965028,0.03286391,-0.06281242,0.00724932,0.01672074,0.01610343,0.03774422,-0.00752533,0.01610907,0.06355398,0.01099943,0.09911271,0.027431,0.07217788,-0.08024931,-0.01881845,0.00352212,0.26648653,-0.00701518,0.02279565,0.0280262,-0.03544537,0.00609684,0.01373464,0.05091888,-0.00280913,0.03367689,0.03700467,-0.00366197,-0.06097754,0.00343104,-0.03274518,0.02324188,-0.00149165,0.04724305,0.00525698,0.0287199,-0.02673017,0.02979248,0.05265163,-0.09488174,-0.05010566,-0.03309272,-0.06472768,0.00505238,-0.01739562,0.02063831,0.01373191,-0.02411294,0.01441456,0.01557204,-0.03650517,-0.02617276,-0.04807007,0.00801777,-0.00141351,-0.03055688,0.05264239,0.04332295,0.01713884],"last_embed":{"hash":"1undoc0","tokens":120}}},"text":null,"length":0,"last_read":{"hash":"1undoc0","at":1751288830259},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#FAQ#Build a component library with styled-jsx#{1}","lines":[950,951],"size":239,"outlinks":[{"title":"article","target":"https://medium.com/@tomaszmularczyk89/guide-to-building-a-react-components-library-with-rollup-and-styled-jsx-694ec66bd2","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1undoc0","at":1751288830259}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02177436,-0.009123,0.0034984,-0.05595471,0.03466542,-0.0093213,-0.08086684,-0.02331989,-0.00232168,-0.03877701,-0.0471056,0.00461803,0.03302678,0.02216388,0.00897094,0.04417155,-0.00188067,0.07872609,-0.10168703,-0.0082833,0.05563172,-0.00497575,0.00155266,-0.03400411,-0.01162208,0.05656336,0.00325489,-0.05433277,0.00611549,-0.19977564,0.00837572,0.0417072,0.00837416,0.01026271,0.0034869,-0.01106408,-0.07183462,0.03737776,0.0029193,0.007591,0.05034281,0.01504526,-0.02750858,-0.00000542,-0.0335602,-0.0542747,-0.01038085,-0.01717569,-0.04906916,-0.02809331,-0.02002743,-0.05890894,0.0808284,-0.04218135,0.03005514,0.09873667,0.05439008,0.05499781,0.00844359,-0.00085612,-0.00825407,0.06703983,-0.16974501,0.10885804,0.01908267,0.00996442,-0.06074194,-0.05285185,0.01985068,-0.01986484,0.01192539,0.02811445,0.01588255,0.08212417,0.00879568,-0.07603762,0.05527753,-0.06208445,0.07022469,-0.03987355,-0.03485059,-0.00446171,-0.01672569,-0.00781094,0.0170698,0.01750205,0.02084826,-0.00168312,0.02864761,0.01382705,-0.01605047,-0.12618682,0.06144635,0.01399221,-0.01898167,0.008902,0.03307726,0.02519409,-0.05837872,0.13516614,-0.05572136,0.06820022,0.00440758,-0.01635901,0.08133353,-0.03306917,-0.01452453,-0.04667129,-0.06222148,0.00248352,-0.03121317,0.00331374,-0.03980186,-0.0453462,0.00404421,-0.00808534,0.03927733,-0.0023298,0.03698755,0.01113123,0.02296553,0.04045339,0.02070368,-0.02028839,-0.00048125,0.04664981,0.01188636,0.03967147,0.03416913,0.0808998,0.05075514,0.07842298,-0.0229357,-0.0309459,-0.01914956,0.04681762,-0.01365783,-0.01847895,-0.02294811,-0.01206642,0.01295928,-0.02573799,-0.02788651,-0.02111209,0.04943923,0.06941852,-0.05728776,0.08282715,-0.0238361,-0.01996551,-0.05734827,0.06878693,-0.07759613,0.04488915,-0.0312215,0.03425023,0.01662795,0.02243017,-0.02880469,0.04436967,-0.01259951,-0.03898638,-0.04222697,0.04723008,0.00260937,-0.04479078,-0.07762866,0.05728631,0.02093864,-0.07932457,-0.05527861,0.03976507,-0.01248813,0.02208448,0.01514915,-0.00170741,-0.03929355,-0.02077056,0.04704686,0.00682256,0.09159224,-0.0344578,-0.01363154,0.07947603,-0.0059122,-0.06071709,0.01554751,-0.06123085,-0.00928756,0.00294688,-0.02795338,0.03234445,0.02795224,0.02157336,-0.02221015,-0.00822582,-0.04782285,-0.04929203,0.02468517,-0.05273215,0.13478231,0.01838777,0.00431437,0.00332148,-0.01488624,0.01344587,0.02626504,-0.03317827,0.05516871,0.01239554,-0.13765225,0.03222251,0.10514652,0.07856788,-0.05088845,-0.02822512,-0.0112783,0.06811324,0.02488368,0.05251781,-0.03388016,-0.00365385,-0.09829328,-0.21497959,0.01624827,0.05984719,-0.08594096,-0.00566783,-0.04893044,0.03539877,0.01462536,-0.05932065,0.0836928,0.06319465,0.03528742,-0.02932152,-0.06676772,-0.03714789,0.01705735,-0.00095259,-0.05717663,-0.03994451,-0.01191653,0.03632694,-0.03057063,-0.07351533,-0.1067391,0.025733,-0.0284412,0.15895353,0.05511485,0.03632485,-0.07974952,0.00543089,-0.03786587,0.03298007,-0.09433404,0.05661306,0.030243,-0.00433899,-0.00808663,0.03922031,-0.02985607,0.0135961,-0.02796482,-0.0451102,-0.03425666,0.07141897,-0.0467702,-0.04424454,-0.04805607,0.01590155,0.07652856,0.02453942,0.02512886,0.00725283,0.09681219,-0.01551569,-0.00335427,-0.0559683,-0.04519549,0.01452373,0.03238199,-0.00689155,0.0082059,0.02422139,-0.08160598,0.04597526,0.05285958,0.01908826,-0.02433413,0.0303835,-0.03878286,-0.07448028,0.11835827,0.02228437,-0.01166348,0.03799469,0.02777452,-0.03681004,0.06818966,0.03437477,0.02060114,-0.02043503,0.02782491,0.04623457,-0.00669206,-0.01445445,0.01975661,-0.05353444,-0.03934376,0.05584905,0.01090263,-0.07832505,0.01788041,-0.01515911,0.04954045,0.02013638,-0.01976324,-0.21122459,-0.00166406,0.03191434,-0.01855655,-0.01037932,0.01515059,0.04032702,-0.10330349,-0.04285156,-0.00020497,0.0111143,0.02068422,-0.03571655,-0.02018582,-0.0319105,-0.01519415,0.06867435,0.00700318,0.05552318,-0.04958516,0.01458584,0.00330419,0.24896619,-0.02948779,0.02627688,0.01454746,-0.02708506,-0.02286644,0.07405958,0.07455865,-0.00360455,0.04056851,0.10914312,0.01390141,-0.03838275,-0.01574826,-0.04816673,0.01625411,0.03807933,0.02160429,-0.01147476,0.06064186,-0.03649872,-0.0043498,-0.0139646,-0.08219821,-0.02548936,0.00081791,0.02346575,-0.02222114,-0.02992625,0.08573121,-0.01710626,0.05971145,0.01058228,-0.0283701,-0.034172,-0.00939981,-0.05574637,0.03375055,-0.02650701,-0.04459555,0.02784679,0.08407845,0.01282368],"last_embed":{"hash":"bvyzja","tokens":468}}},"text":null,"length":0,"last_read":{"hash":"bvyzja","at":1751288830291},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting","lines":[952,1037],"size":3160,"outlinks":[{"title":"open a PR","target":"https://github.com/vercel/styled-jsx/pull/new/main","line":5},{"title":"Atom editor","target":"https://atom.io/","line":9},{"title":"`language-babel`","target":"https://github.com/gandm/language-babel","line":9},{"title":"extend the grammar for JavaScript tagged template literals","target":"https://github.com/gandm/language-babel#javascript-tagged-template-literal-grammar-extensions","line":9},{"title":"source","target":"https://github.com/gandm/language-babel/issues/324","line":11},{"title":"installing the package","target":"https://github.com/gandm/language-babel#installation","line":11},{"title":"babel-language settings entry","target":"https://cloud.githubusercontent.com/assets/2313237/22627258/6c97cb68-ebb7-11e6-82e1-60205f8b31e7.png","line":17},{"title":"Visual Studio Code Extension","target":"https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-syntax","line":61},{"title":"vscode-styled-jsx-stylus","target":"https://marketplace.visualstudio.com/items?itemName=samuelroy.vscode-styled-jsx-stylus","line":69},{"title":"Visual Studio Code Extension","target":"https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-languageserver","line":75},{"title":"vim-styled-jsx","target":"https://github.com/alampros/vim-styled-jsx","line":85}],"class_name":"SmartBlock","last_embed":{"hash":"bvyzja","at":1751288830291}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01568058,-0.00434508,0.00608189,-0.03356883,0.03544136,-0.013962,-0.07354328,-0.01393207,0.00490206,-0.03118665,-0.01918678,0.01545069,0.02100547,0.02669052,0.02093461,0.02542744,0.00761709,0.06789267,-0.07913927,-0.00714448,0.04090606,0.01115943,-0.03130417,-0.03598519,-0.00912313,0.05528713,0.0065172,-0.05092744,-0.01474567,-0.20177008,-0.0174847,0.01770957,0.01643492,0.01974569,0.02080016,-0.00229545,-0.06604201,0.04595759,-0.01307108,0.02671174,0.04756648,0.04402772,-0.03737163,-0.00132434,-0.04871422,-0.04011493,0.00771833,-0.01956424,-0.03716189,-0.03170794,-0.01245124,-0.04265561,0.07843748,-0.04249416,0.04529553,0.10882358,0.03548894,0.04729128,-0.00248702,-0.00801508,0.00007994,0.06235842,-0.17947194,0.0735142,0.01720456,-0.00118108,-0.05891051,-0.05249302,0.00144844,-0.00107555,0.02657867,0.01921133,-0.01076414,0.08558585,0.01369288,-0.07353956,0.04246546,-0.06626146,0.03528296,-0.03052119,-0.03985383,-0.00622436,-0.00204058,-0.02447363,0.01607212,0.03112244,0.02296619,-0.00310405,0.05294001,0.00872116,-0.0174922,-0.119126,0.07247753,0.02256315,-0.0318272,0.00082991,0.0413242,0.03318881,-0.03663111,0.14871179,-0.06290178,0.06946172,0.01688692,0.0135423,0.07552746,-0.03822703,0.01588622,-0.04636943,-0.06270028,0.00438999,-0.02283056,-0.00049406,-0.03740702,-0.04028643,-0.02908969,-0.01953334,0.01767981,0.0105872,0.0360164,0.02654586,0.02229963,0.05699719,0.02493619,-0.02543333,0.00009396,0.04313755,0.01487021,0.03276203,0.0263267,0.08051257,0.04796562,0.07673909,-0.02021749,-0.04400475,-0.02556904,0.04006366,0.00299395,-0.0015628,-0.01248137,-0.00451533,0.01581847,-0.02027549,-0.01705989,-0.04371013,0.0512025,0.06153779,-0.05560368,0.09101244,-0.02850422,-0.01181842,-0.09054168,0.04483801,-0.06644107,0.04465177,-0.03883069,0.02237607,0.00102363,0.00081069,-0.03619122,0.05403143,-0.00038117,-0.02570234,-0.05172162,0.04783522,-0.0096842,-0.04616663,-0.06181404,0.06195042,0.00752348,-0.08657711,-0.04831545,0.03926787,-0.02169212,0.00706688,0.03779597,-0.00844379,-0.05502221,-0.03605159,0.04041182,-0.00759337,0.11095125,-0.03890287,-0.01866409,0.08873072,-0.01458511,-0.05655416,0.02292434,-0.04925899,-0.01056887,0.02181897,-0.00220862,-0.01385564,0.01732097,0.02508691,-0.04056466,0.00112916,-0.0446278,-0.04162376,0.01050232,-0.06389661,0.10890216,0.01781642,0.01824301,0.02195844,-0.03305544,0.01796348,0.02803799,-0.0335075,0.05851424,0.02606816,-0.14076807,0.03201892,0.10843606,0.07466179,-0.03215831,-0.05709304,-0.00600047,0.07585985,0.0159621,0.04627534,-0.02310105,0.01692371,-0.10346051,-0.20647676,0.00280643,0.04784774,-0.05974991,-0.01814122,-0.0429799,0.04017811,0.03880824,-0.07038049,0.08846215,0.05141656,0.05799926,-0.03340296,-0.07147773,-0.02675144,0.01297054,-0.0107929,-0.07343693,-0.04514137,-0.01642446,0.02435338,-0.03540632,-0.08723146,-0.10899045,0.03883462,-0.00936841,0.14179942,0.06199221,0.02812853,-0.07828557,0.01505841,-0.03052438,0.01665447,-0.10326818,0.05014826,0.03863651,-0.00644607,-0.03274599,0.02633419,-0.02460824,0.00135849,-0.03486631,-0.0460524,-0.02509915,0.06374754,-0.05943868,-0.02833187,-0.03768309,0.01552786,0.07078432,0.02183732,0.01744825,0.01714685,0.09943106,-0.01780069,0.00223232,-0.06222061,-0.05190865,-0.01261512,0.02988228,-0.00060426,0.01201536,0.0261731,-0.06819752,0.0787151,0.04776742,0.01943418,-0.03482247,0.01384033,-0.03813179,-0.06408668,0.1166352,0.0333727,-0.0339756,0.01734539,0.02711187,-0.01248788,0.06294979,0.034762,0.02502261,0.02272799,0.03064316,0.0520735,-0.01848442,0.00449418,0.02093165,-0.06003729,-0.04898885,0.04654939,-0.01751775,-0.08003291,0.01583831,-0.01448536,0.05558292,0.02709762,-0.00513009,-0.19767281,0.00306722,0.04292759,-0.00886736,-0.00913835,0.03353566,0.03558713,-0.10495989,-0.04273434,0.02193442,0.04594773,0.01653715,-0.04595934,-0.02441462,-0.04506835,-0.0170928,0.07369071,0.02206722,0.07476304,-0.04320326,0.0028149,-0.01297305,0.25462887,-0.02754755,0.0228218,0.01107823,-0.0319367,-0.00908215,0.09170003,0.0719625,0.00237839,0.05209052,0.11124571,0.01218427,-0.04351721,-0.01355733,-0.03747369,0.02644845,0.03613609,0.01620923,-0.00821575,0.0457532,-0.06207683,-0.02179279,0.0026421,-0.10114815,-0.0233984,-0.00632092,0.00446499,-0.01316478,-0.03683025,0.07185024,-0.01802951,0.06966943,0.01779599,-0.02679858,-0.05677441,-0.01725198,-0.04259342,0.03367076,-0.02626286,-0.05146003,0.04170958,0.06637672,0.01130601],"last_embed":{"hash":"mgu9cv","tokens":122}}},"text":null,"length":0,"last_read":{"hash":"mgu9cv","at":1751288830405},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#{1}","lines":[954,957],"size":332,"outlinks":[{"title":"open a PR","target":"https://github.com/vercel/styled-jsx/pull/new/main","line":3}],"class_name":"SmartBlock","last_embed":{"hash":"mgu9cv","at":1751288830405}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Atom": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04008285,-0.02543008,0.00752008,-0.068294,0.03475162,-0.01789331,-0.09903821,-0.0349196,-0.00548596,-0.04920023,-0.07272006,-0.00383229,0.0244412,0.01261375,0.03226597,0.04193262,0.00147921,0.08290759,-0.100069,-0.02267752,0.05898394,-0.0001386,0.0228421,-0.03894122,-0.01032266,0.06957309,-0.01209871,-0.05576417,0.02736371,-0.18293709,0.00216428,0.04951226,-0.01062363,0.01711383,0.01050694,-0.00300938,-0.06858385,0.02433488,0.00838629,-0.00625845,0.04496055,-0.00692052,-0.02534058,0.01294041,-0.01950915,-0.04403855,-0.02375656,-0.00461961,-0.06211141,-0.03508258,-0.03171046,-0.06086667,0.07550272,-0.05458827,0.02703013,0.08504835,0.06701151,0.04436713,-0.00751128,0.00057584,-0.00728981,0.06622654,-0.17420347,0.12981701,0.01804972,0.02852934,-0.06624992,-0.04160833,0.02351942,0.00512826,0.0005056,0.03606833,0.02521667,0.05323074,0.02277015,-0.05669457,0.03540122,-0.0882887,0.08281742,-0.044166,-0.02444512,-0.001948,-0.04745794,0.00651314,0.01129909,-0.01778028,0.00038132,-0.00025739,0.00841925,0.02293698,-0.01562856,-0.126929,0.04046703,0.01321536,-0.02131625,-0.01858064,0.01207569,0.01560131,-0.04058184,0.14686032,-0.0553203,0.06484189,0.0094912,-0.03804757,0.0674391,-0.02786963,-0.01547816,-0.03036685,-0.05685702,-0.01746764,-0.02302576,0.0200976,-0.04866543,-0.07295973,0.02651463,-0.02107175,0.04162431,-0.01679413,0.03225755,0.01000145,0.02089054,0.03673179,0.02817736,-0.02563888,0.00544517,0.04341943,0.00096199,0.05157594,0.04987715,0.06221227,0.04564497,0.07842679,-0.01762531,-0.0218081,-0.0272368,0.05037736,-0.03556307,-0.01996849,-0.01841014,-0.01178087,-0.00454431,-0.03543352,-0.03345827,-0.02010007,0.02682813,0.06921975,-0.06430265,0.08095511,-0.02178583,-0.01914012,-0.04034329,0.07000989,-0.0621968,0.02069572,-0.03308875,0.02882911,0.03503162,0.03298092,-0.03487547,0.05158315,-0.01996826,-0.05888225,-0.02726546,0.05881349,0.0160764,-0.05911176,-0.06391129,0.03881519,0.03910585,-0.0630633,-0.04218176,0.04623742,-0.0163421,0.03454569,0.00426499,0.00712626,0.00118143,-0.00823515,0.03847059,-0.00440382,0.06299485,-0.04520205,-0.03765802,0.0650766,0.01273367,-0.06683384,0.00148311,-0.07312957,-0.00566088,-0.02374289,-0.03182456,0.06642378,0.03120801,0.02647626,-0.01085852,-0.03017082,-0.03097047,-0.03666342,0.03067024,-0.01698329,0.13157389,0.0232481,-0.01663805,0.01422062,-0.0016867,0.02165311,0.03719823,-0.02662564,0.05563571,0.00639415,-0.12369816,0.02627303,0.10013573,0.09000202,-0.06053887,-0.00852957,-0.01910441,0.07744048,0.01721605,0.03871517,-0.03959116,-0.00948262,-0.11054949,-0.22852612,0.04791195,0.06504793,-0.08737379,0.01558444,-0.04630656,0.03589624,-0.00898218,-0.0387586,0.06822386,0.08330909,0.00709346,-0.00649147,-0.05735466,-0.03291389,0.01432141,0.02375256,-0.03645837,-0.02442285,-0.00503451,0.03551335,-0.02934607,-0.07571033,-0.1174933,0.03417347,-0.05307999,0.15550755,0.0539576,0.03737457,-0.07473499,0.02321926,-0.04255439,0.03523818,-0.10387166,0.0808766,0.03220829,-0.00495764,-0.0003269,0.03386925,-0.03830078,0.04376944,-0.00573536,-0.05025134,-0.04966755,0.0750282,-0.0126734,-0.04147789,-0.05369129,-0.00743698,0.06864074,0.01830177,0.03135541,0.01123013,0.08751261,-0.0195854,-0.02140492,-0.06302402,-0.0415297,0.01959765,0.02201828,-0.02855426,0.00331805,0.02969906,-0.07118776,0.01332509,0.06985704,0.01130484,-0.00646861,0.01647101,-0.04386856,-0.0383045,0.07013793,0.01284981,0.00863645,0.046807,0.02741327,-0.06428286,0.08583596,0.02518865,0.01686847,-0.02395723,0.02633641,0.02743637,0.00257225,-0.03821587,0.02243111,-0.04680282,-0.02941984,0.06358707,0.04076304,-0.07578123,0.00301136,-0.01370026,0.02830515,0.03609716,-0.01972403,-0.219166,0.0172777,0.03847203,-0.03362508,-0.03194347,0.01132948,0.06079104,-0.06263026,-0.04855828,-0.01495632,-0.01131891,0.03739691,-0.00927017,-0.02039304,-0.01520001,0.00372886,0.07334203,0.00350659,0.05237303,-0.04621457,0.03840983,0.00299171,0.24609183,-0.03968571,0.02392379,0.01454749,-0.05073293,-0.03358091,0.05918125,0.08843742,-0.01612952,0.05562174,0.09586978,0.01043608,-0.03612451,-0.00494902,-0.02701885,0.00641237,0.05169053,0.01630525,-0.00222504,0.0466186,-0.04418179,0.0310478,-0.00434633,-0.06437422,-0.04584748,0.02342288,0.03552337,-0.02969362,-0.02965287,0.07297184,-0.02036076,0.04903541,0.01490364,-0.0236347,-0.01711273,0.00082185,-0.03742804,0.02799241,-0.01861853,-0.00870633,0.01394603,0.08722609,0.01395944],"last_embed":{"hash":"ja574e","tokens":325}}},"text":null,"length":0,"last_read":{"hash":"ja574e","at":1751288830439},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Atom","lines":[958,969],"size":792,"outlinks":[{"title":"Atom editor","target":"https://atom.io/","line":3},{"title":"`language-babel`","target":"https://github.com/gandm/language-babel","line":3},{"title":"extend the grammar for JavaScript tagged template literals","target":"https://github.com/gandm/language-babel#javascript-tagged-template-literal-grammar-extensions","line":3},{"title":"source","target":"https://github.com/gandm/language-babel/issues/324","line":5},{"title":"installing the package","target":"https://github.com/gandm/language-babel#installation","line":5},{"title":"babel-language settings entry","target":"https://cloud.githubusercontent.com/assets/2313237/22627258/6c97cb68-ebb7-11e6-82e1-60205f8b31e7.png","line":11}],"class_name":"SmartBlock","last_embed":{"hash":"ja574e","at":1751288830439}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Atom#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03905688,-0.02848023,0.0074569,-0.06751659,0.03191442,-0.0194347,-0.09844388,-0.03566819,-0.00391175,-0.05375452,-0.0697614,-0.00112429,0.02620316,0.01084296,0.03079744,0.04538919,-0.00011481,0.08104832,-0.10563169,-0.02347992,0.0556257,-0.00200423,0.02552686,-0.04025297,-0.00909668,0.06737306,-0.01319906,-0.05646749,0.02613458,-0.18372448,0.00473011,0.05014816,-0.01563827,0.01608766,0.0097208,-0.00199476,-0.07010309,0.02644673,0.00919746,-0.00965708,0.04565458,-0.00675786,-0.02168095,0.01271802,-0.01846569,-0.04207948,-0.02549414,-0.00495173,-0.06153725,-0.0328912,-0.03046823,-0.05913997,0.07371488,-0.0498812,0.02468897,0.08308961,0.06714772,0.04590027,-0.00546627,0.00066766,-0.01256886,0.06220953,-0.17274036,0.13224192,0.0177994,0.03018289,-0.06627116,-0.039007,0.02394746,0.00614023,-0.00164685,0.03255882,0.02758298,0.05409891,0.02669057,-0.05835048,0.03280815,-0.09271808,0.08296154,-0.04326145,-0.02176886,-0.0045803,-0.04911027,0.00600477,0.01059842,-0.0236002,-0.00027498,-0.00020756,0.00918626,0.02333084,-0.01535877,-0.12735192,0.04206375,0.01344364,-0.02273378,-0.01674584,0.01399791,0.01862636,-0.04061585,0.14650299,-0.05226447,0.0676354,0.0090469,-0.04051152,0.06826188,-0.02816415,-0.01652219,-0.02913927,-0.0544009,-0.01827342,-0.02183588,0.01951469,-0.04848917,-0.07573134,0.03040642,-0.02156432,0.04506297,-0.01979343,0.03061685,0.00684751,0.02263968,0.03215204,0.02716354,-0.02241287,0.00789413,0.04121156,-0.00258526,0.05117785,0.04834592,0.06128113,0.04617435,0.07630932,-0.01980702,-0.02512358,-0.02657586,0.05177746,-0.03643253,-0.0223861,-0.01426086,-0.01047338,-0.00719893,-0.03778645,-0.03565539,-0.02015854,0.02741356,0.06907362,-0.06095026,0.0816517,-0.02395754,-0.01988287,-0.03944955,0.07388519,-0.06072976,0.01988663,-0.02776557,0.02810082,0.03455953,0.03424119,-0.03348146,0.05142527,-0.02172249,-0.05987938,-0.02917234,0.05928712,0.01587247,-0.05635415,-0.06626202,0.04005183,0.04109297,-0.0620087,-0.04333667,0.04231275,-0.01364653,0.03339898,0.0027765,0.01062131,0.00244599,-0.00533921,0.03982916,-0.00234964,0.06132425,-0.04284996,-0.03758617,0.06493312,0.01508263,-0.06500497,0.00012529,-0.07221977,-0.00335499,-0.02940436,-0.0315426,0.06985056,0.0303261,0.02603875,-0.01555177,-0.03339662,-0.02788389,-0.03549424,0.03101695,-0.01262393,0.13233879,0.02263532,-0.01564029,0.01782654,-0.00494255,0.01970119,0.03719276,-0.02615822,0.05840052,0.00283921,-0.12228958,0.02641104,0.09570201,0.08586086,-0.06282566,-0.00731181,-0.01846918,0.0748899,0.01798385,0.03621341,-0.03814809,-0.01049976,-0.10911682,-0.23199224,0.04783501,0.06385094,-0.0895341,0.01666745,-0.04807677,0.03477482,-0.01188731,-0.04011093,0.06508645,0.08147395,0.00591137,-0.00307538,-0.05799398,-0.03458413,0.016376,0.02316787,-0.03414039,-0.02413646,0.00169964,0.03413054,-0.02846054,-0.07935721,-0.11451552,0.03735438,-0.05511745,0.15212123,0.05306742,0.03690324,-0.0742982,0.020953,-0.03998694,0.03767499,-0.10239366,0.08401909,0.02988729,-0.00584559,-0.00316378,0.03283361,-0.04018069,0.04592475,-0.00772625,-0.04804725,-0.0504997,0.07540628,-0.00939086,-0.04050099,-0.05368323,-0.0102715,0.06589396,0.01796585,0.03217694,0.0086878,0.08697609,-0.01758819,-0.02354946,-0.0633324,-0.0418708,0.02325364,0.0226925,-0.02731952,0.00558831,0.02897269,-0.06723842,0.01028472,0.06907358,0.01120244,-0.00560549,0.01681287,-0.04704702,-0.03720915,0.0711366,0.01293924,0.00971271,0.04881031,0.02724662,-0.06644989,0.08815545,0.02786029,0.01803691,-0.02448542,0.02597109,0.02807797,0.00535415,-0.04014486,0.0233926,-0.04445481,-0.02910183,0.0608639,0.04031866,-0.07494272,0.002529,-0.01539385,0.0288513,0.03557551,-0.02125162,-0.22019698,0.01730799,0.03938878,-0.0329101,-0.03001342,0.01117903,0.06180458,-0.06135599,-0.05006346,-0.01761143,-0.01131299,0.04074461,-0.00589403,-0.01717792,-0.0164247,0.00359695,0.07563156,0.00120247,0.05475163,-0.04483708,0.04195828,0.00590519,0.24714732,-0.04099044,0.02055097,0.01305783,-0.04958775,-0.03339843,0.05771112,0.0880742,-0.01519863,0.05533508,0.09483572,0.00940952,-0.03542412,-0.00532663,-0.02851694,0.00671447,0.05354418,0.01357693,-0.00231713,0.0470767,-0.04326009,0.03142262,-0.00451483,-0.06182521,-0.04897705,0.0231578,0.0384324,-0.03095446,-0.02527196,0.07291602,-0.02084697,0.04895416,0.01304365,-0.02433183,-0.01473319,0.00260161,-0.03576532,0.02653257,-0.01637179,-0.00418934,0.01390204,0.08683455,0.01751959],"last_embed":{"hash":"1kf8nxj","tokens":323}}},"text":null,"length":0,"last_read":{"hash":"1kf8nxj","at":1751288830532},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Atom#{1}","lines":[960,969],"size":782,"outlinks":[{"title":"Atom editor","target":"https://atom.io/","line":1},{"title":"`language-babel`","target":"https://github.com/gandm/language-babel","line":1},{"title":"extend the grammar for JavaScript tagged template literals","target":"https://github.com/gandm/language-babel#javascript-tagged-template-literal-grammar-extensions","line":1},{"title":"source","target":"https://github.com/gandm/language-babel/issues/324","line":3},{"title":"installing the package","target":"https://github.com/gandm/language-babel#installation","line":3},{"title":"babel-language settings entry","target":"https://cloud.githubusercontent.com/assets/2313237/22627258/6c97cb68-ebb7-11e6-82e1-60205f8b31e7.png","line":9}],"class_name":"SmartBlock","last_embed":{"hash":"1kf8nxj","at":1751288830532}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Webstorm/Idea": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01658067,0.01205518,-0.00572001,-0.07621014,-0.00271041,0.06193351,-0.03190469,-0.02062415,-0.01094043,-0.03884155,-0.03320179,-0.00524838,0.02563815,0.0428343,-0.01027165,0.05489469,0.00321074,0.04915922,-0.10968943,0.01779331,0.06071926,-0.00195402,0.01086848,-0.07703525,-0.02926549,0.04170498,-0.01672799,-0.03950481,0.00742884,-0.17782727,0.04782085,-0.0078954,0.02248432,-0.06470492,-0.02303244,-0.01043315,-0.05857886,0.00941172,-0.01511894,0.01778493,0.02831482,0.00520934,-0.03167648,-0.01210105,-0.01412604,-0.09366579,0.02869194,-0.01569376,-0.02288409,-0.02026277,0.00902195,-0.06686608,0.07528888,-0.03122962,0.00090585,0.09942218,0.0187782,0.08249301,0.02750455,0.03338866,0.01302746,0.0757444,-0.15351029,0.1105789,0.01201248,0.04437643,-0.0530108,-0.01267968,0.01634457,-0.01335271,0.00449812,0.02056612,0.00313919,0.09187131,0.00368052,-0.05134575,0.04663254,-0.02314731,0.07998396,-0.01156017,-0.04937988,-0.05842032,-0.00990822,-0.03960695,0.04566182,0.04651328,0.01610686,-0.01282893,0.00627362,0.02697656,-0.02599346,-0.12429876,0.04740841,0.00928826,0.00052392,0.05321278,-0.01173769,0.01293083,-0.07756702,0.14249979,-0.07320976,0.00951825,-0.01182323,-0.0332464,0.05137007,0.01168467,-0.01535463,-0.06272451,-0.05601395,-0.03173076,-0.06398103,-0.0320983,-0.02348127,-0.0207682,0.00334368,0.02355708,0.02179439,0.03688365,0.05952619,-0.00852779,0.06325858,0.00580192,0.01761257,0.00152287,-0.00569614,0.02101802,0.02363112,0.05767628,0.02193692,0.08619685,0.04172617,0.05866275,-0.0381014,0.00157102,-0.01281962,0.02203042,0.00186622,-0.03097943,-0.02971694,-0.02236798,0.02423611,-0.03137803,-0.01246354,-0.0412446,0.05667602,0.08032146,-0.08394165,0.02714882,0.01234333,-0.02378764,-0.052309,0.08090549,-0.0721752,0.03024039,-0.05179889,0.08237926,0.00880376,0.02187369,-0.03036214,0.00733103,-0.01898821,0.00842285,-0.02188953,0.06134626,0.00937574,-0.05774555,-0.08516869,0.0678191,0.03165468,-0.05244658,-0.07199182,0.03681827,-0.00429477,0.00621041,0.03376518,-0.02752118,-0.07540942,-0.01279885,0.07063132,0.04603162,0.10056347,-0.03771807,0.02824133,0.04943643,-0.01347837,-0.08287802,0.03622819,-0.03823262,-0.00062561,0.00863842,-0.0282621,0.04111351,0.02115009,0.00070236,-0.0261091,0.00852736,-0.06456088,-0.04122406,0.03068637,-0.06801766,0.14919843,0.02103789,-0.04072342,0.03006627,-0.00278487,0.03768793,0.01178748,-0.02995431,0.03096477,-0.03407586,-0.11718486,0.02707795,0.11822701,0.06644628,0.00101052,-0.01915019,-0.02265232,0.04311379,0.05311413,0.05329092,-0.0403433,-0.01331043,-0.07361966,-0.22079745,0.0281512,0.04438154,-0.08911986,-0.02425024,-0.05807918,0.03549484,-0.02390867,0.01976858,0.07794406,0.08857381,0.03662727,-0.00939441,-0.03028685,-0.02115009,0.00952748,0.00432354,-0.03928323,-0.02759278,-0.01296207,0.05287988,-0.00951689,-0.00883088,-0.06878214,0.00890749,-0.00695075,0.18035655,0.0390641,0.03513367,-0.03403069,0.00237416,-0.01442311,-0.00567607,-0.09666066,0.02187144,0.01770042,0.00724389,0.02200647,0.03683934,-0.00248277,0.00496517,-0.01768105,0.00038722,-0.05622853,0.06571479,-0.07842682,-0.02711532,-0.09008346,0.02440994,0.04571009,0.0277998,0.0228266,0.00919296,0.13514683,-0.03386124,-0.00333274,-0.03069045,-0.02645604,0.00101351,0.03147767,-0.00287113,-0.0227212,0.00144593,-0.09815069,0.01778908,0.0663349,0.01685779,-0.04897542,0.07996786,-0.00489547,-0.08636928,0.14763483,0.02749132,-0.01146578,0.03902198,0.03425843,-0.03344825,0.06405292,0.01476282,0.01268294,0.0019125,0.00762982,0.02455468,-0.00591739,-0.02405085,0.02020776,-0.0296827,-0.05396478,0.08232006,-0.03180828,-0.04755491,0.0364077,-0.0347828,0.05495159,0.01429575,-0.0556748,-0.22583765,0.01807295,0.00096615,0.0302544,-0.00753896,0.00650056,0.02574156,-0.11328535,-0.05367918,0.00302432,-0.02673752,0.01970725,-0.03712422,-0.01927536,-0.04587531,-0.03169173,0.0490188,-0.01847159,0.06167515,-0.07215241,-0.00621387,0.00111058,0.23054241,0.05312373,0.04508278,0.03322799,-0.00429953,0.01092463,0.05150402,0.07945985,0.02500327,0.00129573,0.06184965,0.00408456,0.00035994,-0.04640828,-0.02013276,0.01721522,0.01793099,0.01346201,-0.0397247,0.02753125,-0.00248814,-0.0139479,-0.04209043,-0.06813613,-0.01847702,-0.03918663,-0.0299944,-0.0139684,-0.00391014,0.06166964,-0.0008984,0.00510472,0.0200456,0.02447214,-0.03982491,0.01230528,-0.04544568,-0.00270957,-0.00461738,-0.06264589,0.01917588,0.05834094,-0.01550031],"last_embed":{"hash":"16daeom","tokens":249}}},"text":null,"length":0,"last_read":{"hash":"16daeom","at":1751288830628},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Webstorm/Idea","lines":[970,997],"size":824,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"16daeom","at":1751288830628}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Webstorm/Idea#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01676767,0.0121669,-0.00683277,-0.07386127,-0.00141303,0.0637237,-0.03090119,-0.01974086,-0.01248852,-0.04128955,-0.03120017,-0.00327181,0.02751902,0.04463026,-0.01245106,0.0574147,0.00401061,0.04850955,-0.10728168,0.02093275,0.0612703,-0.00141151,0.01287847,-0.07586675,-0.0270107,0.04262397,-0.01783703,-0.03849421,0.00766311,-0.17605607,0.04917316,-0.00994558,0.019393,-0.06522153,-0.02344476,-0.01031618,-0.05822716,0.01063989,-0.01388052,0.02023186,0.02700457,0.00670817,-0.03142163,-0.0141511,-0.01221055,-0.0933257,0.02831989,-0.01507513,-0.0203259,-0.02174325,0.00834236,-0.0692909,0.07358278,-0.0305803,-0.0032795,0.09437965,0.01444995,0.0856799,0.02740214,0.03596991,0.01011878,0.07555975,-0.15256821,0.10982024,0.00931845,0.04478114,-0.05212105,-0.00906092,0.02186736,-0.01271709,0.00366502,0.02095045,0.00319642,0.09293955,0.00505512,-0.05262665,0.04639863,-0.01831054,0.07936361,-0.01266167,-0.04832046,-0.06120989,-0.01093659,-0.04307557,0.04269356,0.04673965,0.01805805,-0.01082169,0.00612992,0.02814776,-0.02783838,-0.1244271,0.04799325,0.0090721,0.00174686,0.05508267,-0.01181074,0.0122277,-0.07635317,0.14352252,-0.0736391,0.00582246,-0.0128539,-0.03260104,0.05044079,0.01364455,-0.01673176,-0.0628702,-0.05485481,-0.03292502,-0.06340629,-0.03512666,-0.02462287,-0.02052796,0.0050198,0.02051828,0.02505488,0.03800965,0.05691575,-0.00846866,0.06473079,0.00590449,0.02136278,0.00522692,-0.00507831,0.01872819,0.02512521,0.05910159,0.01981454,0.08356818,0.04102674,0.05403583,-0.03757744,0.00015595,-0.01311688,0.02260857,0.0054098,-0.03084324,-0.02806904,-0.02267158,0.02567896,-0.03192494,-0.01180419,-0.04365247,0.05498235,0.07851529,-0.08420224,0.02894549,0.01104972,-0.02689422,-0.04689629,0.08327082,-0.07167425,0.02782647,-0.0509715,0.08225714,0.0092252,0.02103882,-0.03139388,0.00356921,-0.01645367,0.01390684,-0.0214097,0.06266089,0.00956394,-0.05527158,-0.08568034,0.06799078,0.02947918,-0.05358243,-0.07333233,0.03539068,-0.00504054,0.00455672,0.03213183,-0.02791342,-0.08195981,-0.01124752,0.07016232,0.0474619,0.09948146,-0.03729225,0.02995782,0.05021618,-0.01321936,-0.08028231,0.0346703,-0.04079032,0.00036053,0.00561002,-0.0310101,0.04363921,0.02082442,-0.00300987,-0.02252587,0.01036526,-0.06489201,-0.04095692,0.03194525,-0.06926602,0.14829846,0.02281732,-0.0398038,0.02821257,-0.00459935,0.03999348,0.00669335,-0.03246596,0.02653352,-0.03346377,-0.11479511,0.02570225,0.11400048,0.06516259,-0.00278247,-0.01809946,-0.0237431,0.04392124,0.052395,0.0525388,-0.03963733,-0.01119651,-0.07389912,-0.22448133,0.02676007,0.04461162,-0.09047493,-0.02345544,-0.05515848,0.03570729,-0.02549576,0.01936268,0.07640278,0.088682,0.03453396,-0.0094426,-0.02781433,-0.02022183,0.01142927,0.00142595,-0.03665629,-0.02854514,-0.01076987,0.05580575,-0.00858128,-0.01053589,-0.06799211,0.01146756,-0.00432143,0.1797892,0.03756585,0.03529415,-0.03495323,0.00352799,-0.01307259,-0.00375827,-0.09697073,0.01911634,0.01852716,0.00982722,0.02415994,0.03826801,-0.00247173,0.00511794,-0.02024157,0.00058825,-0.05722099,0.06721042,-0.07984703,-0.02660428,-0.09038574,0.02549639,0.04725794,0.02510344,0.02555814,0.00872972,0.13704364,-0.03300909,-0.00207552,-0.03188668,-0.02733038,0.00278916,0.03163636,-0.00229153,-0.01769486,-0.00073979,-0.09889353,0.01806813,0.06569466,0.01805754,-0.04943918,0.08347027,-0.00608905,-0.08596969,0.14797533,0.02836548,-0.00853544,0.03935614,0.03341389,-0.03428409,0.06106241,0.01203463,0.00847513,0.00205122,0.00322681,0.02606731,-0.00498294,-0.02585283,0.01909784,-0.02728486,-0.05186072,0.07935861,-0.03024183,-0.04825096,0.03542139,-0.03798288,0.05547841,0.01366842,-0.05623863,-0.22848427,0.01773536,0.00242987,0.0342767,-0.00842872,0.00369589,0.02731698,-0.11123566,-0.05275821,0.00002545,-0.02800515,0.01980713,-0.03962869,-0.0203417,-0.04621855,-0.02752302,0.05135232,-0.01911996,0.06414779,-0.06941929,-0.0074534,0.00058136,0.23266649,0.05287135,0.04750247,0.03526963,-0.0029965,0.01090106,0.04835173,0.07957017,0.02563449,-0.00163272,0.05827201,0.00224226,-0.00161088,-0.04581542,-0.01794332,0.01543838,0.01865072,0.01140247,-0.03863292,0.02826742,-0.00349229,-0.01332288,-0.04021913,-0.06411757,-0.01806284,-0.04520336,-0.0302313,-0.01377224,-0.00612724,0.0604526,0.00145996,0.00457476,0.01979828,0.0254568,-0.04156673,0.01078035,-0.04499754,-0.00194578,-0.00138055,-0.06121772,0.01789475,0.05923451,-0.01588282],"last_embed":{"hash":"10w615k","tokens":247}}},"text":null,"length":0,"last_read":{"hash":"10w615k","at":1751288830717},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Webstorm/Idea#{1}","lines":[972,997],"size":805,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"10w615k","at":1751288830717}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Emmet": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04324006,0.00414564,0.07389075,-0.04490557,0.01415732,0.0303786,-0.09179068,-0.01846502,-0.01919439,0.01294778,-0.03919335,-0.01659959,0.0121302,0.00287177,0.07777677,0.02316093,0.00942567,0.09768775,-0.11438619,-0.01232316,0.04338789,0.03647568,-0.02058338,-0.04192538,0.00241529,0.08081026,-0.03629616,-0.03448157,-0.02107711,-0.17702001,0.02596098,-0.00498661,0.01061005,0.01672481,0.00314608,-0.020939,-0.05580847,0.04856193,-0.0040843,0.07519618,0.02595761,0.03843033,-0.04614083,-0.04213634,-0.049223,-0.07195457,0.00246806,0.0217785,-0.04846679,-0.02941802,-0.04541525,-0.08510307,0.06402338,-0.04918137,0.04575579,0.09279583,0.01487341,0.07053322,-0.02852655,0.01284649,-0.00694904,0.08560106,-0.14048009,0.09015024,0.01946535,-0.01280762,-0.05968824,-0.00372534,0.01937228,0.02084038,0.02905368,0.0240007,-0.01429934,0.09230824,-0.00722147,-0.02427524,0.0178143,-0.04406802,0.04748803,0.00525897,-0.08548662,-0.03446772,-0.00563883,-0.06631207,0.03716653,-0.01284647,0.01657461,0.016621,0.03113172,0.02234924,-0.01108743,-0.11321764,0.00569939,-0.0107416,-0.02662972,-0.0547962,-0.01606598,0.0320592,-0.00457638,0.1459271,-0.0751593,0.04262183,0.01312391,-0.00357049,0.06731579,-0.02478437,0.02031904,-0.01529325,-0.01054682,-0.03622344,-0.02058506,-0.00420991,-0.05597088,-0.05248543,-0.01179644,0.00673586,-0.01961438,-0.00734816,0.00638939,-0.01838914,0.03083771,0.02385634,0.02546695,-0.01638473,-0.01803754,0.01418983,0.02523466,0.06113327,0.0270943,0.08296343,0.01632468,0.04397808,-0.03163894,0.00175244,-0.06035927,0.04494744,-0.03226514,0.02096233,-0.02353731,0.00530278,0.00178859,-0.03621884,-0.0393695,-0.08853451,0.02538154,0.08027224,-0.02790301,0.04661265,0.00200997,-0.03965308,-0.07959835,0.00813429,-0.06100786,0.02483207,-0.03947968,0.03912371,0.03504164,0.01329221,-0.03942506,0.05393674,-0.02391912,0.00289528,-0.04237135,0.0691531,0.00090951,-0.07662099,-0.0502554,0.03805608,0.030888,-0.10648108,-0.05746904,0.05604431,-0.02623027,0.04625037,0.05790152,0.00244996,-0.01652208,-0.0567338,0.05189788,-0.01787699,0.08491485,-0.0633312,-0.04796207,0.02921742,-0.01205292,-0.06727689,0.03719434,-0.04426827,-0.04232846,-0.01356113,-0.02369759,0.04362471,0.01087394,-0.00506621,-0.00528749,-0.00971973,-0.00717763,0.01064518,0.02485891,-0.04443988,0.14835617,0.02294895,0.00009206,0.09834206,0.0007059,0.03700407,0.03546638,-0.01412061,0.06457342,-0.00563298,-0.13751137,0.01392408,0.10247565,0.10919407,-0.00474689,0.0046734,-0.05189838,0.05458412,0.03828914,0.02430807,-0.04189578,-0.00554967,-0.10242637,-0.19183496,0.01473229,0.06869236,-0.04662139,-0.06462342,-0.07378493,0.03431728,0.01058676,-0.04444428,0.0684282,0.07685404,-0.01172247,0.00390006,-0.04028841,-0.00417412,0.00698167,0.01755599,-0.05234654,-0.01991306,0.02290238,0.02363517,0.00301038,-0.03787835,-0.11305359,0.04440872,-0.03031519,0.14039139,0.0477014,0.01363204,-0.06699947,0.05071433,-0.01041352,-0.01093227,-0.08920429,0.03126333,0.05182777,0.04312616,0.01284071,0.01281183,-0.00113698,-0.00773882,0.00163307,0.00450219,-0.06575787,0.03874992,-0.06339794,-0.03742106,-0.02662117,0.01777268,0.02368492,0.01490071,0.04515827,0.04286136,0.08369689,-0.04138011,-0.01932419,-0.0407887,-0.0373656,-0.01079267,0.02377746,-0.02468619,0.0275788,0.00071971,-0.06488913,0.04332689,0.09127316,0.02616927,-0.00628871,-0.03515469,-0.03082421,-0.0639257,0.10361583,0.0394916,0.01645346,0.01182032,0.03511818,-0.06054219,0.08852002,0.00041123,0.03709422,-0.0062146,0.00608377,-0.02540216,0.01253585,-0.01953196,0.01844729,-0.05406164,-0.03745557,0.0281897,-0.03596582,-0.1035569,-0.00888094,-0.04443808,0.05192007,0.01071704,0.02500179,-0.2331688,0.04053039,0.03936795,-0.01115523,-0.02945459,0.03492735,0.029291,-0.06336536,-0.0825963,0.03647851,0.00729653,0.04637456,0.01983193,0.022858,-0.0208924,0.01982172,0.03939026,0.04152227,0.06375768,-0.04451913,0.01393099,-0.00145904,0.27235353,-0.01371516,0.02006748,0.03833585,-0.02098162,-0.03605972,0.06674692,0.06916128,0.0443327,0.06879519,0.07118233,0.01682364,-0.02471697,-0.06238249,0.00893221,-0.00137597,0.0158183,0.012163,0.01925739,0.00511084,-0.06533314,-0.00272953,0.02593814,-0.09900388,-0.02269735,0.00902875,-0.00057156,-0.01478568,-0.01993534,0.01013447,0.00957043,0.06176287,0.02295638,-0.00756242,-0.04668556,0.04188958,-0.02207002,0.01861707,-0.0336155,-0.03729012,-0.00057559,0.04696468,0.02173002],"last_embed":{"hash":"1tchdkb","tokens":146}}},"text":null,"length":0,"last_read":{"hash":"1tchdkb","at":1751288830807},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Emmet","lines":[998,1011],"size":279,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1tchdkb","at":1751288830807}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Emmet#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03721365,-0.00081103,0.07491397,-0.04587672,0.01024254,0.03180435,-0.09332198,-0.01817959,-0.02412366,0.01482088,-0.03634961,-0.01827838,0.01774354,-0.00004533,0.07868424,0.02849085,0.01049149,0.09584463,-0.11219234,-0.01023304,0.04011711,0.03114395,-0.01952036,-0.03842453,0.00381322,0.08354118,-0.03412887,-0.03675034,-0.03058612,-0.18085025,0.03026017,-0.00666071,0.00764212,0.02216266,0.00128027,-0.02352571,-0.05927075,0.05760859,-0.00186495,0.07663615,0.01692352,0.04312715,-0.04393711,-0.04296935,-0.048916,-0.07498845,0.00541788,0.0225914,-0.04938903,-0.02782744,-0.04489082,-0.08625589,0.05732369,-0.04422632,0.04170442,0.09101201,0.01344562,0.07159188,-0.03068375,0.01592409,-0.01087742,0.08872076,-0.13612193,0.08782183,0.01531659,-0.01470122,-0.05797744,-0.00459527,0.02221929,0.01855122,0.02709995,0.01912263,-0.0111503,0.09090665,-0.00473371,-0.02133086,0.0135936,-0.04255008,0.04279145,0.01213428,-0.08367679,-0.03034907,-0.00445008,-0.06336426,0.03614945,-0.01643239,0.01511389,0.01911126,0.035685,0.02286065,-0.01245712,-0.1108624,-0.00509125,-0.01183198,-0.02758271,-0.06035297,-0.01611971,0.03814709,-0.00333166,0.14462547,-0.07732579,0.0425835,0.00999171,-0.00512781,0.06690414,-0.02540653,0.02324727,-0.01353956,-0.01079375,-0.0360773,-0.0208432,-0.00258775,-0.06032801,-0.05274548,-0.007134,0.00985893,-0.02206666,-0.00767086,0.00242183,-0.02386985,0.03169816,0.0220962,0.02789266,-0.01436872,-0.0202289,0.01298184,0.02385719,0.06615068,0.02538113,0.08568974,0.01668465,0.0340993,-0.03628772,0.00074077,-0.06431127,0.04748528,-0.03099848,0.02143685,-0.02364957,0.00664741,-0.00342876,-0.03625344,-0.03765221,-0.09168569,0.02470471,0.08343041,-0.02182319,0.03535824,-0.0041428,-0.03794986,-0.07653291,0.00639,-0.05946068,0.02838788,-0.03791103,0.03784256,0.03206663,0.01286184,-0.0374468,0.05240237,-0.02818513,0.00295608,-0.04433767,0.08069922,-0.00779919,-0.07497343,-0.05545636,0.04060555,0.02768779,-0.10082375,-0.05481761,0.05233478,-0.02614002,0.04590073,0.05754826,0.00510624,-0.01474154,-0.05737366,0.04456045,-0.01663002,0.08544612,-0.05700188,-0.04700089,0.03480067,-0.01185218,-0.06605434,0.03570332,-0.04681478,-0.03666351,-0.01798812,-0.02750954,0.05274341,0.0104395,-0.00656248,-0.007447,-0.00900959,-0.00562097,0.01408598,0.03056475,-0.04223923,0.14352441,0.03084251,0.00433913,0.10386918,0.00002086,0.04091793,0.03813848,-0.01607146,0.06276008,-0.00205936,-0.13557179,0.01203463,0.10080048,0.11021682,0.00124388,0.0119001,-0.05095997,0.04537346,0.041382,0.02000709,-0.04771105,-0.00966607,-0.10356186,-0.19328418,0.01159788,0.06900492,-0.04303925,-0.06412207,-0.07943235,0.03022767,0.006915,-0.04430342,0.06935138,0.07438985,-0.01046031,0.00605366,-0.04306865,0.00047824,0.01070604,0.01519071,-0.05359561,-0.01850054,0.02462123,0.01740013,0.003058,-0.03488314,-0.11320959,0.04557599,-0.02943185,0.13840808,0.04650311,0.01110621,-0.06947439,0.04362115,-0.00723046,-0.00543296,-0.08165804,0.03074453,0.05019022,0.04353289,0.00688882,0.01349734,0.00044804,-0.01210829,0.00257843,0.00785304,-0.07035989,0.03427349,-0.06543517,-0.03328096,-0.02835563,0.0161775,0.02548288,0.01680409,0.04356027,0.04054499,0.08054916,-0.0404107,-0.02091885,-0.0388522,-0.03071042,-0.01175288,0.02514055,-0.01984223,0.03444336,0.00296252,-0.06339289,0.04020703,0.09377003,0.02562422,-0.00909222,-0.03801128,-0.03189759,-0.06550275,0.10744151,0.03600003,0.01325091,0.00653162,0.03867813,-0.05983523,0.09133393,0.00114298,0.03729961,-0.00728515,0.01067671,-0.02768786,0.01593045,-0.01960565,0.01747848,-0.05440836,-0.0328754,0.02474253,-0.0355536,-0.10536301,-0.00401748,-0.04100959,0.05050641,0.01101807,0.02774132,-0.23445813,0.04094136,0.04146104,-0.00839716,-0.03411221,0.03143849,0.03036267,-0.06222339,-0.08678383,0.0341572,0.01401752,0.04592451,0.02463908,0.03358098,-0.0220064,0.02260112,0.03942347,0.04300426,0.05748079,-0.04658833,0.01314333,0.00051066,0.275038,-0.01621312,0.01906573,0.03510903,-0.01899126,-0.03953153,0.06217341,0.06676868,0.04834579,0.06749675,0.06466636,0.0209999,-0.02022322,-0.06805705,0.0057169,-0.0022717,0.01283979,0.01506599,0.02084849,0.00650087,-0.06506128,-0.00667909,0.02571462,-0.09645375,-0.01604097,0.01260829,0.00230688,-0.01370322,-0.02092957,0.00531028,0.00780473,0.06461945,0.02248988,-0.01084897,-0.04207817,0.04250646,-0.02422723,0.01533308,-0.03474322,-0.03062368,-0.0031562,0.04583714,0.02885434],"last_embed":{"hash":"1h667ev","tokens":144}}},"text":null,"length":0,"last_read":{"hash":"1h667ev","at":1751288830850},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Emmet#{1}","lines":[1000,1011],"size":268,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1h667ev","at":1751288830850}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Syntax Highlighting [Visual Studio Code Extension](https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-syntax)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03452719,0.01584194,0.04779413,-0.07043575,0.03270113,-0.01027505,-0.09390516,-0.03263827,-0.01310735,-0.00778542,-0.02318745,0.02354607,0.03549825,0.02634605,0.0361766,0.00789031,0.02627771,0.05073343,-0.04782775,0.0023611,0.01548483,0.0319664,-0.04532392,-0.0630087,-0.02680235,0.07628956,-0.01244489,-0.08155001,0.00590054,-0.16462925,-0.02354194,0.03566764,0.02377641,-0.01137774,0.02050106,-0.03376701,-0.08536538,0.03071292,-0.0565328,-0.00339286,0.00664627,0.03580537,-0.07164413,0.00837048,-0.02394903,-0.09000725,0.02389325,-0.01404043,-0.02133356,-0.01608651,0.02503946,-0.04629061,0.02725818,-0.07506267,0.00515952,0.08264214,0.02971388,0.04097712,-0.03114148,-0.03075978,0.01836916,0.04527565,-0.16436511,0.12264355,0.02213168,0.00493404,-0.03061449,-0.02355198,0.02274986,-0.01780584,0.02128437,0.02649293,-0.00338981,0.04874357,0.00155786,-0.0431182,0.01144501,-0.074935,0.06862653,-0.01209733,-0.04450059,-0.0004205,0.00359539,-0.00465734,-0.00251631,0.0093782,0.00268562,0.01940591,0.09598298,-0.02934149,-0.05411664,-0.14045231,0.05351638,0.01051704,-0.0367224,-0.02136519,-0.00071577,-0.00787353,-0.04240889,0.1283934,-0.04756555,0.04234013,-0.02298217,-0.03662734,0.08398358,-0.03521191,-0.01758554,-0.05275121,-0.04040453,0.01449841,-0.01476635,0.01467846,-0.01317779,-0.08578826,-0.0112164,-0.0067559,-0.04122485,0.00612218,0.02685198,0.00475583,0.02971168,0.02579725,0.01507918,-0.03128186,-0.02677138,0.02059254,0.02573633,0.07331032,0.00342478,0.04040756,0.04285842,0.06712975,-0.03578443,-0.00425956,-0.04318419,0.06572443,-0.01258408,0.01656666,-0.03080858,-0.06481144,0.01132375,-0.00812541,0.01868327,-0.02313362,0.02478794,0.02921185,-0.05104579,0.06559292,-0.0148495,-0.01490196,-0.02651653,0.02229148,-0.03168099,0.01821752,-0.0421256,0.02899523,0.05225649,0.01747068,-0.05523681,-0.0059872,-0.00359926,-0.04690159,-0.01458083,0.04524195,0.03298846,-0.07292973,-0.04039141,0.036824,0.00813099,-0.06832436,-0.01952351,0.02928984,-0.05037429,0.02308916,0.03132324,-0.0078973,-0.02317717,-0.0327718,0.04822721,0.01819933,0.08121683,-0.05183094,-0.05715182,0.02091011,-0.0029835,-0.06923227,0.03798648,-0.02307821,-0.02598582,0.02197825,0.00891705,-0.01709353,0.02006469,-0.00807095,0.00102039,0.03320757,-0.05948593,-0.03695141,0.00433285,-0.05683691,0.15343134,-0.00327877,-0.02490216,0.06344073,-0.0107963,0.03146978,-0.01228586,-0.06007542,0.06868889,-0.01569813,-0.13376369,0.03533246,0.1391456,0.0767457,-0.0087839,-0.00196553,-0.0256153,0.0955559,0.009278,0.08043264,-0.04149672,0.00352697,-0.08272397,-0.19208716,0.03006533,0.04745732,-0.02005105,-0.02764685,-0.0254194,0.01397831,0.01050045,-0.01789861,0.02318081,0.09635318,-0.01873805,0.03251359,-0.05221868,0.01430211,0.00019019,0.04340206,-0.0482977,0.01862213,-0.01342052,0.01885751,-0.01072745,-0.05065719,-0.06706021,0.05710907,-0.03674421,0.15622707,0.04908818,0.04890173,-0.01790063,0.0522793,-0.03405578,0.01068513,-0.12602608,0.04335869,0.074903,0.0526225,0.00808643,0.01745371,0.00439994,-0.00231685,-0.01434286,-0.00615199,-0.04729842,0.08548384,-0.07431401,-0.03476474,-0.02858576,-0.01988992,0.09203281,0.01247866,0.03960635,0.05456273,0.10404176,-0.03102243,0.0140379,-0.057586,-0.01149086,-0.00429961,0.02813084,0.00526887,-0.00099175,-0.01250245,-0.09627111,0.01202167,0.06437222,0.02839709,-0.04406626,0.08121829,-0.03542498,-0.07394985,0.07750718,0.01713066,0.0131724,0.00352324,0.00151518,0.00373218,0.08706373,0.01446356,0.00693508,-0.00609436,0.03717116,-0.0058339,-0.03751209,-0.00970926,0.00688743,0.00386466,-0.0751376,0.07289612,-0.0001049,-0.07736091,0.00694305,0.02399253,0.0608172,0.00231481,-0.00053669,-0.23093434,-0.00109769,0.05445286,0.00268714,-0.05383454,0.00218193,0.06025262,-0.11463679,-0.05974768,0.01877288,-0.01962298,0.03648459,-0.00610554,-0.01661835,-0.04846567,-0.01558509,0.10654296,0.00615125,0.11014609,-0.04398154,0.00497535,-0.0326149,0.24660082,-0.00582781,-0.00553216,0.0341431,-0.04411149,-0.02428558,0.10141019,0.04589618,0.03363571,0.06064331,0.07082251,-0.00428637,-0.02478808,-0.01115756,-0.00813311,0.01969105,0.05779282,0.05226717,-0.00246524,-0.01757583,-0.06718738,0.03151488,-0.00576276,-0.11161254,-0.01813231,-0.02187715,-0.02027336,-0.02253246,-0.0120325,0.02918283,-0.04435587,0.040027,0.05110555,-0.02106524,-0.04288084,0.00796534,0.00459696,0.05103666,0.02088774,-0.08133342,0.03925394,0.06138901,0.02168382],"last_embed":{"hash":"a6a4fc","tokens":199}}},"text":null,"length":0,"last_read":{"hash":"a6a4fc","at":1751288830888},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Syntax Highlighting [Visual Studio Code Extension](https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-syntax)","lines":[1012,1025],"size":509,"outlinks":[{"title":"Visual Studio Code Extension","target":"https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-syntax","line":1},{"title":"vscode-styled-jsx-stylus","target":"https://marketplace.visualstudio.com/items?itemName=samuelroy.vscode-styled-jsx-stylus","line":9}],"class_name":"SmartBlock","last_embed":{"hash":"a6a4fc","at":1751288830888}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Syntax Highlighting [Visual Studio Code Extension](https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-syntax)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03004895,0.0148905,0.05068472,-0.06947673,0.03116446,-0.0066309,-0.09520856,-0.0341404,-0.01342025,-0.01170498,-0.01989799,0.02586846,0.03896819,0.02415779,0.0391378,0.00670792,0.02629469,0.04892359,-0.04532184,0.0036012,0.01373082,0.03244613,-0.04411446,-0.06444617,-0.02606408,0.07676845,-0.01181948,-0.08182282,0.00463126,-0.16364914,-0.02256792,0.03385311,0.02266951,-0.01133699,0.02063603,-0.0382889,-0.09016126,0.03425643,-0.0621891,-0.00329497,0.00366873,0.03483662,-0.0718291,0.00604863,-0.02355446,-0.09532314,0.0264703,-0.01346473,-0.02024604,-0.00939517,0.02852921,-0.04598378,0.02101677,-0.06915087,0.00128059,0.07974444,0.02749824,0.04129193,-0.03102261,-0.02909268,0.01826371,0.04211605,-0.1598853,0.12500782,0.01671552,0.00641507,-0.0315709,-0.01800546,0.02485702,-0.01877132,0.01939627,0.02051482,-0.00319546,0.04931084,0.00384707,-0.04480658,0.0080476,-0.07427442,0.06907128,-0.01087961,-0.04281005,-0.00211155,0.00650303,-0.00638462,-0.00437856,0.00574204,0.00423053,0.02040617,0.09911116,-0.02813839,-0.0547589,-0.14285894,0.0558338,0.01039509,-0.03508552,-0.02346054,-0.00344615,-0.0077714,-0.04368388,0.12628485,-0.0488381,0.04483635,-0.02322985,-0.03955135,0.08305442,-0.03246408,-0.02151501,-0.05225881,-0.03822517,0.01624863,-0.01613539,0.01715012,-0.01311183,-0.08410056,-0.01169388,-0.00910622,-0.04368667,0.00299711,0.02531101,0.00261741,0.02911243,0.02093895,0.01550441,-0.02788839,-0.02543814,0.02297221,0.0257974,0.07553156,0.00405313,0.03783456,0.0380524,0.06346379,-0.04118298,-0.00539534,-0.04515489,0.06670499,-0.0119883,0.01920143,-0.03014508,-0.06794921,0.01074023,-0.0048689,0.02057857,-0.02125036,0.02393113,0.02619726,-0.04931638,0.06234799,-0.01756552,-0.01753014,-0.02688965,0.02340554,-0.03264669,0.01774202,-0.03926807,0.0301352,0.04966281,0.01578453,-0.05625159,-0.01348115,-0.00408069,-0.0488176,-0.01372194,0.04584257,0.03239848,-0.07478312,-0.04557926,0.03787084,0.00892933,-0.06676686,-0.01542111,0.02378815,-0.05127382,0.02252962,0.03062329,-0.00859262,-0.02647991,-0.02859071,0.04482521,0.0226862,0.07799859,-0.04946551,-0.05765239,0.01821065,-0.00324416,-0.06907207,0.03739492,-0.02382764,-0.0286263,0.02176861,0.00932922,-0.01829007,0.01746253,-0.01113981,0.0062848,0.03595645,-0.06042892,-0.03737042,0.00509251,-0.05723915,0.15411586,-0.00517952,-0.02453223,0.05941384,-0.01485989,0.032331,-0.01670541,-0.06262493,0.06928843,-0.01702813,-0.13099666,0.03693192,0.13819452,0.07467306,-0.0076699,0.00170789,-0.02500412,0.09394623,0.01233967,0.08130078,-0.04357905,0.00616908,-0.07732181,-0.1930209,0.02903911,0.04867732,-0.01718388,-0.02969586,-0.02627207,0.01319686,0.00908198,-0.01864782,0.02099399,0.09528923,-0.0169092,0.03730159,-0.05039113,0.01367128,0.00378575,0.04596813,-0.04528512,0.01979939,-0.01210917,0.0143559,-0.00666454,-0.049807,-0.06340984,0.06402484,-0.03416411,0.15497202,0.04678075,0.05022697,-0.01352714,0.05665128,-0.02904558,0.01014061,-0.12717146,0.04446312,0.07360342,0.05491931,0.00740765,0.01710613,0.00468363,-0.00214261,-0.01478553,-0.00461645,-0.04708945,0.08802236,-0.0738564,-0.03282694,-0.0277912,-0.02200765,0.09394964,0.01083549,0.04070107,0.05628888,0.10433647,-0.02731504,0.01444766,-0.05614936,-0.00862707,-0.00270208,0.02719525,0.00523636,-0.0001576,-0.01540377,-0.09327602,0.00793973,0.06160293,0.03427672,-0.04219002,0.08699759,-0.03495744,-0.07563495,0.07732837,0.0164254,0.01686051,0.00107991,-0.00014517,0.00096531,0.09005862,0.01807534,0.00560113,-0.00829103,0.03993772,-0.00450459,-0.03873188,-0.00798917,0.00707165,0.00775384,-0.072561,0.0717143,-0.00119041,-0.07731859,0.00468846,0.02330104,0.06277603,0.00147114,-0.0025255,-0.23134564,-0.00446723,0.05616534,0.00624952,-0.05558564,-0.00239275,0.05908634,-0.11323632,-0.05984732,0.01628274,-0.02089678,0.03852427,-0.00456565,-0.02075394,-0.04917167,-0.01325883,0.11020502,0.00512626,0.11268525,-0.04260708,0.00423964,-0.03171431,0.24581274,-0.00509315,-0.00817007,0.03581046,-0.0426481,-0.026732,0.09399974,0.04763189,0.03502012,0.06053827,0.07470869,-0.00411785,-0.02272684,-0.00833467,-0.00887947,0.01932621,0.05767895,0.05300606,-0.00356225,-0.02130503,-0.06931098,0.03352236,-0.005742,-0.11197807,-0.0136849,-0.02197677,-0.0215492,-0.02517667,-0.01144662,0.0301762,-0.04629514,0.03858367,0.04944518,-0.02153583,-0.04451494,0.00705575,0.004892,0.05271669,0.02560859,-0.07714052,0.03583481,0.0597619,0.01967046],"last_embed":{"hash":"nongb0","tokens":197}}},"text":null,"length":0,"last_read":{"hash":"nongb0","at":1751288830951},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Syntax Highlighting [Visual Studio Code Extension](https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-syntax)#{1}","lines":[1014,1025],"size":369,"outlinks":[{"title":"vscode-styled-jsx-stylus","target":"https://marketplace.visualstudio.com/items?itemName=samuelroy.vscode-styled-jsx-stylus","line":7}],"class_name":"SmartBlock","last_embed":{"hash":"nongb0","at":1751288830951}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Autocomplete [Visual Studio Code Extension](https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-languageserver)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04576525,0.01656192,0.05184774,-0.06298865,0.02508526,0.00804046,-0.09785936,-0.03145017,-0.03108808,-0.04280223,-0.01601597,0.00890665,0.02044695,0.02270302,0.06823471,0.02324655,0.0049285,0.04511936,-0.04208654,-0.00803331,0.04146963,0.01975495,-0.03701513,-0.05495563,-0.04088618,0.05422317,-0.01150176,-0.05803858,0.01063036,-0.15113984,-0.00963373,0.03035353,0.04487652,-0.01754114,0.0152758,-0.02139604,-0.10452374,0.02249505,-0.06477909,-0.01639812,0.01725927,0.03701005,-0.0445161,-0.00898205,-0.02491889,-0.08600454,0.02871994,-0.00240204,-0.029063,-0.04431718,0.01560576,-0.0318253,0.02290643,-0.05032336,-0.00179903,0.08066966,0.02288995,0.03775639,-0.02241617,0.00138441,0.04390022,0.02484994,-0.15456063,0.10829531,0.02354855,0.02456095,-0.04754426,-0.01474197,0.01600251,-0.02097277,0.0217528,0.03484062,0.01588868,0.0579805,-0.00580367,-0.05693906,-0.00030727,-0.06065609,0.07471787,-0.00870678,-0.04084634,-0.04283624,-0.00488208,-0.0304318,-0.01397638,0.01086472,0.01517797,0.03823423,0.08651941,-0.00679939,-0.07328598,-0.14991248,0.08112253,0.03563686,-0.02740244,0.01147943,-0.00017352,-0.00011305,-0.02841605,0.13500272,-0.0659532,0.04254784,-0.00615885,-0.03117627,0.07372151,-0.01588046,0.00663392,-0.01493608,-0.03894734,0.02425995,-0.01457678,0.0101321,-0.00534898,-0.0864052,-0.01167387,-0.00114355,-0.02233772,-0.00188691,0.02125719,0.01596281,0.02203587,0.01411226,0.02337827,-0.03313652,-0.01841809,0.04472692,0.05000872,0.07377604,0.01238383,0.05734802,0.04288749,0.08203928,-0.02486825,-0.02734056,-0.04686325,0.04313993,-0.00724794,0.00512947,-0.05063592,-0.05331055,-0.00927817,-0.01488737,-0.00386586,-0.04802608,0.01177673,0.02244522,-0.07720245,0.0584037,-0.00328196,-0.02926199,-0.02305489,0.03120524,-0.04518044,0.01457222,-0.03136047,0.02257008,0.03022604,0.00378358,-0.05616928,-0.0265335,-0.01018197,-0.03092005,-0.00068434,0.06104403,0.02840874,-0.07133867,-0.07020468,0.06589156,0.01224895,-0.05658052,-0.01316083,0.02880948,-0.04450965,0.00696786,0.03436343,-0.01583297,-0.029481,-0.02657601,0.04119619,0.0215006,0.07630166,-0.0685005,-0.03982468,0.04783235,0.0132055,-0.05634737,0.04765599,-0.03419447,-0.04411808,0.00710032,-0.00704469,-0.02151603,0.02475986,-0.02540692,0.01413182,0.01619678,-0.05979624,-0.03782948,0.02108369,-0.04945029,0.15877937,0.01520339,-0.00702696,0.0658532,-0.01510429,0.02751945,-0.01517396,-0.05628848,0.06381475,-0.01888592,-0.12484436,0.02460119,0.12716195,0.08486176,-0.01086652,-0.01459173,-0.02888983,0.08970883,0.00160451,0.07199503,-0.0309456,-0.00413909,-0.07889969,-0.191084,0.0224629,0.0463568,-0.05147067,-0.04152366,-0.03180853,0.00669181,0.01443754,-0.00489541,0.01461427,0.10462832,-0.02192151,0.0165586,-0.01801645,-0.00332458,0.00556801,0.0263112,-0.03319223,-0.03078856,-0.01812828,0.0423897,-0.03295171,-0.02957632,-0.06840557,0.04419612,-0.025786,0.14363475,0.02284976,0.08718992,-0.0007118,0.05892856,-0.02634634,0.0019729,-0.13276608,0.01883797,0.0613352,0.06604275,0.01604371,0.02590012,0.02519018,-0.00691315,-0.02190446,0.00808338,-0.06568118,0.09515837,-0.09198353,-0.03299292,-0.0323792,-0.04375272,0.0706893,0.01365754,0.01980083,0.05397318,0.13069445,-0.03202815,0.03365799,-0.03266948,-0.02151121,-0.00041489,0.02349289,0.01149201,-0.00547085,-0.01909748,-0.08785282,0.02728103,0.05340337,0.02990056,-0.02685775,0.08602326,-0.050158,-0.08707453,0.07483255,0.03177354,-0.00512307,0.01064934,-0.00043888,-0.05139279,0.07835091,0.01847939,0.0305406,0.03021194,0.02320554,-0.00783675,-0.05013595,-0.00853468,0.00512017,-0.0336594,-0.03210627,0.08288252,-0.00338331,-0.07254848,0.01389558,0.01544386,0.03438601,0.02372414,-0.01661114,-0.23208763,0.00467125,0.04769348,-0.01829404,-0.04280161,0.0122469,0.05948432,-0.1097092,-0.06103316,-0.00144637,-0.00264811,0.02114145,-0.02284305,-0.03664517,-0.03562851,0.00717009,0.10367057,0.00658477,0.11667565,-0.02760641,-0.01402413,-0.02959646,0.24364832,-0.00272807,0.02427494,0.04471863,-0.03398903,-0.03180627,0.0875797,0.04796082,0.04417397,0.0771156,0.09633903,-0.02136418,-0.03454658,-0.02895004,0.0213585,0.04253948,0.06179676,0.03924919,-0.01616168,-0.02742527,-0.05529192,0.03502346,-0.02036744,-0.09666499,-0.01699032,-0.02939092,-0.02879262,-0.02747248,-0.02061183,0.0228763,-0.02875937,0.03439975,0.04915,-0.01501756,-0.03317335,0.01965237,-0.01603792,0.03180142,0.02586759,-0.07209744,0.03984693,0.05000789,0.01994558],"last_embed":{"hash":"1xq1b9y","tokens":121}}},"text":null,"length":0,"last_read":{"hash":"1xq1b9y","at":1751288831011},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Syntax Highlighting#Autocomplete [Visual Studio Code Extension](https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-languageserver)","lines":[1026,1033],"size":280,"outlinks":[{"title":"Visual Studio Code Extension","target":"https://marketplace.visualstudio.com/items?itemName=Divlo.vscode-styled-jsx-languageserver","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1xq1b9y","at":1751288831011}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#ESLint": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03815762,-0.03302449,0.08580355,-0.06111334,0.01655999,0.00835541,-0.08969487,-0.02886692,-0.03860374,-0.00967077,-0.0068205,-0.02839173,0.03361111,-0.00216418,0.03084445,0.00431521,-0.02482526,0.03096537,-0.0796133,0.00702691,0.03677391,0.00738315,0.02310839,-0.05290336,-0.03169698,0.05592439,0.00373348,-0.02860709,0.00546252,-0.18633451,-0.00492022,-0.01829341,-0.06811313,0.01875585,0.02452763,-0.0167313,-0.01853237,0.00239244,-0.03011772,0.02346631,0.02707065,0.02623592,-0.05557713,-0.00666605,-0.00393745,-0.03131764,0.02765009,0.02964718,-0.00253726,-0.01352147,-0.03104319,-0.02746065,0.08274271,-0.03257789,0.01239396,0.14475898,0.06337084,0.04154526,0.02179297,0.05577999,0.01518307,0.05896377,-0.17002922,0.10261157,0.02464688,0.01755819,-0.05430049,-0.02464511,0.02504422,-0.03321202,-0.01200407,0.04317218,-0.01546492,0.11098219,0.02983873,-0.02417757,0.01484636,-0.04274673,0.02447944,0.02260047,-0.05073089,-0.04110239,0.00204461,-0.00102213,0.05710031,-0.01379482,0.02697157,0.01016877,0.02278941,0.03565814,-0.02821708,-0.10855656,0.03648739,0.03004108,-0.0041538,-0.00449378,0.02694542,0.03639713,-0.04003143,0.13697451,-0.07668442,0.03396438,0.01999228,0.0526255,0.03152413,-0.02686805,-0.04821497,-0.04289788,-0.02621636,-0.03053291,-0.00199383,-0.00634297,-0.04279647,-0.06726466,-0.01280595,-0.01625316,-0.00503672,-0.02328402,-0.01683244,0.01335771,0.02596751,0.04743187,0.04505273,0.01131557,-0.01037721,0.02319434,-0.0066802,0.02060716,-0.01322442,0.07634439,-0.03353064,0.0800618,-0.04306772,-0.03989406,-0.01098552,0.04953221,-0.02311618,0.04169222,-0.01800437,0.02615029,0.0209423,-0.06387492,-0.01215916,-0.04683071,0.06642941,0.0325731,-0.03159296,0.07484724,-0.02024577,-0.03332625,-0.07358705,0.02070095,-0.08241838,0.02799126,-0.02411674,0.02832633,0.04424968,-0.01539618,-0.05450546,0.00360557,-0.00282625,-0.03073986,-0.00980935,0.01783277,0.03736966,-0.05913916,-0.05070559,0.04951115,0.02382463,-0.08049215,-0.04986852,0.03305813,-0.02958984,0.04923721,0.04269717,0.00471154,-0.03171415,-0.03628199,0.0241523,0.0307724,0.07674519,-0.02969897,-0.01071642,0.03406438,-0.00772027,-0.0688766,0.03846189,-0.05324325,-0.01802369,-0.02000264,-0.01366842,0.02527808,0.0327911,0.00231168,-0.01596966,-0.004965,-0.06555232,0.00606007,0.05395189,-0.04174621,0.14459451,-0.00440023,0.01641667,0.06234749,-0.07703188,0.0666359,-0.01674158,-0.03887272,0.04557065,-0.03056438,-0.13349688,0.04582724,0.11615862,0.07730301,-0.00652574,0.00427146,0.01889925,0.07781141,0.06904246,0.02576083,-0.00890736,0.01466649,-0.1142166,-0.24193171,0.04634806,0.05061572,-0.02916555,-0.05706482,-0.03494648,0.03633022,0.00529313,-0.0231321,0.09633961,0.07404304,0.00767737,0.0376911,-0.00757989,-0.02826066,-0.00933921,-0.02417789,-0.07478678,-0.07162745,0.00080294,-0.0036482,-0.00969479,-0.07059805,-0.09763621,0.04950939,-0.02299841,0.1375486,0.04091344,0.01732516,-0.07103684,0.06213663,0.01162048,0.00003382,-0.0986554,0.0559863,0.04008272,0.06966193,0.00613113,0.0115686,-0.0046495,-0.01872928,-0.00141285,-0.00805765,-0.03395214,0.08961017,-0.04520764,-0.05814704,-0.0551966,-0.00790324,0.05700599,0.00731496,0.02410696,0.06231925,0.1010343,-0.05046951,0.02617173,-0.05081721,-0.03143827,0.02583191,0.00129717,-0.0146756,0.02398388,0.0321379,-0.10834934,0.01546389,0.03777451,0.0439722,-0.06187996,0.03829241,-0.00093239,-0.06142278,0.09016962,0.04080278,0.01572637,-0.01768359,0.01703554,-0.06730165,0.08827452,0.00353914,0.05352997,0.01000676,0.01428053,0.03554581,-0.0182619,-0.04545538,-0.00894297,-0.04716542,-0.02443908,0.04517356,-0.04408833,-0.0650477,0.00530586,-0.00591465,0.06593769,0.08348632,0.0329588,-0.19751814,0.01459239,0.04216688,0.00656258,-0.03801158,-0.01521779,0.02146761,-0.07606377,-0.01278417,-0.01495179,-0.00768294,0.00696744,0.02025406,-0.01562404,-0.00415623,-0.01534858,0.06361013,-0.02734908,0.09421006,-0.05861823,0.00110389,0.00304501,0.26400676,-0.01096022,0.00030895,0.04797148,-0.01735993,0.01475916,0.03045624,0.07253884,0.02669706,0.05179978,0.02345611,0.01999126,-0.0166905,-0.0365448,-0.01251392,-0.00317574,0.04112157,-0.01408972,-0.02302406,0.00466996,-0.02325737,0.01211458,0.03082025,-0.13155086,-0.05363597,-0.01639957,-0.01075808,-0.02303266,-0.01401074,0.04689924,0.01594362,-0.01566121,0.00911465,0.00484596,-0.05026901,0.00883229,-0.03605014,0.01297872,0.02391303,-0.10669886,0.01522766,0.04426924,-0.02402689],"last_embed":{"hash":"csnizj","tokens":124}}},"text":null,"length":0,"last_read":{"hash":"csnizj","at":1751288831044},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#ESLint","lines":[1038,1045],"size":295,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"csnizj","at":1751288831044}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#ESLint#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03880386,-0.03700252,0.08322629,-0.06234099,0.0145708,0.00425532,-0.0871081,-0.0298568,-0.03969393,-0.00784437,-0.00481912,-0.02738918,0.03133959,-0.00095234,0.03106918,0.00828936,-0.02932809,0.03136018,-0.08083601,0.00911051,0.03491954,0.00617682,0.02584003,-0.05389711,-0.03259923,0.05608239,0.00615804,-0.02871714,0.00524916,-0.18668516,-0.00759463,-0.02065311,-0.07158324,0.02054265,0.02393404,-0.01660364,-0.02109484,0.00102064,-0.02967713,0.02358607,0.02804619,0.02412181,-0.05651719,-0.00682129,-0.00375061,-0.0304506,0.0268307,0.03255025,-0.00297494,-0.01183471,-0.02641187,-0.02675049,0.08129573,-0.03306118,0.00952937,0.14539452,0.06518387,0.03886456,0.02429634,0.05900381,0.01491238,0.05830806,-0.17051226,0.10202997,0.02088893,0.01633888,-0.05298424,-0.02304635,0.02641803,-0.03218308,-0.01295013,0.0431933,-0.01323994,0.11036067,0.03305119,-0.02232542,0.01545221,-0.04050304,0.02218008,0.02244649,-0.0450098,-0.03846038,0.00117694,0.00078732,0.05855573,-0.01416778,0.0237458,0.00867362,0.02367855,0.03587847,-0.02848522,-0.10801899,0.0330886,0.03049488,-0.00447346,-0.00270752,0.02879097,0.03712133,-0.04408136,0.1374712,-0.07818884,0.0324793,0.01909478,0.05308332,0.02755051,-0.02619377,-0.047839,-0.04074954,-0.02596655,-0.02830652,-0.00599382,-0.00439871,-0.04468886,-0.06816815,-0.01534101,-0.01357461,-0.00551422,-0.02263629,-0.02074815,0.01364255,0.02716027,0.04706627,0.04823777,0.01082695,-0.01297049,0.02109662,-0.01000737,0.01845811,-0.01038,0.07266193,-0.03509839,0.07797412,-0.04505503,-0.03813044,-0.01079002,0.0485503,-0.02017195,0.04703527,-0.01483176,0.02726996,0.02001098,-0.0660186,-0.01103957,-0.04871243,0.0652709,0.03454306,-0.02956335,0.07426476,-0.02086743,-0.03853537,-0.0726454,0.01979978,-0.08167405,0.02728453,-0.02461735,0.02891159,0.04415672,-0.01537803,-0.05433512,-0.00101759,-0.00463654,-0.02993177,-0.01000362,0.01676067,0.03469546,-0.05768726,-0.05030205,0.04784718,0.02497162,-0.07986864,-0.04948483,0.03179015,-0.0290616,0.04893589,0.04447882,0.00649263,-0.03269841,-0.03544246,0.01927218,0.03394535,0.07443308,-0.02714145,-0.01094281,0.03512394,-0.00453487,-0.06959496,0.04021515,-0.05390478,-0.0182447,-0.02110701,-0.01603321,0.02736106,0.0327666,0.00072078,-0.0176464,-0.00107158,-0.06597446,0.0075794,0.05523995,-0.04163294,0.14451814,-0.00216295,0.01731031,0.05893961,-0.07514729,0.06912427,-0.01815394,-0.0408678,0.04549333,-0.02887703,-0.13366187,0.04802473,0.11540759,0.07427422,-0.00171983,0.00345821,0.01865547,0.08116411,0.07216765,0.02737,-0.00900099,0.01347191,-0.11216196,-0.24303252,0.04410799,0.05093297,-0.02732975,-0.05652485,-0.0372625,0.03472776,0.00769765,-0.02259243,0.09520237,0.074204,0.00895329,0.03614461,-0.00565581,-0.02801321,-0.01243221,-0.02232583,-0.07183626,-0.07222587,0.00372683,-0.00578232,-0.00667054,-0.07227689,-0.09500191,0.05037916,-0.02342828,0.13700521,0.0376109,0.01428064,-0.07435449,0.06096615,0.0152332,0.00298145,-0.09969931,0.05576961,0.03519244,0.06967924,0.00535226,0.01226774,-0.00636437,-0.01819018,-0.00066941,-0.01040807,-0.0321047,0.0912461,-0.04374418,-0.05574249,-0.05661571,-0.01268362,0.05844355,0.00670842,0.02307361,0.06423345,0.10105669,-0.05103112,0.02574945,-0.0540384,-0.03000559,0.02693771,-0.00068431,-0.01375976,0.0264221,0.0328494,-0.1109358,0.01419249,0.03757813,0.04475238,-0.06154954,0.03813519,0.00199391,-0.05911615,0.08889215,0.03913943,0.02011202,-0.02044041,0.01828925,-0.06549007,0.09012486,0.00504692,0.0538881,0.01047404,0.01492147,0.03536516,-0.02004714,-0.04570004,-0.01086911,-0.04636667,-0.02150483,0.04665859,-0.04086447,-0.06241366,0.00345421,-0.00272019,0.06755865,0.08536436,0.03416124,-0.19559132,0.01535489,0.04419553,0.0057621,-0.03656919,-0.01704919,0.01701571,-0.07519557,-0.01124785,-0.01598904,-0.00705364,0.00607252,0.02396287,-0.01489806,-0.005244,-0.01636853,0.06348602,-0.02953756,0.09514417,-0.06330754,0.00419247,-0.00144212,0.26363215,-0.00957238,0.00042958,0.04807555,-0.01156642,0.01814143,0.02577178,0.07175193,0.0236121,0.04903437,0.02327899,0.02193021,-0.01511817,-0.03245523,-0.0134932,-0.0046576,0.04072955,-0.01637431,-0.02193348,0.00255568,-0.02153669,0.00926798,0.03244984,-0.13261986,-0.05272559,-0.01804473,-0.00875462,-0.02637301,-0.0089092,0.04696351,0.01639938,-0.01903547,0.00910306,0.00604588,-0.04978938,0.01096201,-0.03651037,0.01033614,0.02586505,-0.11093449,0.01147882,0.0468791,-0.02426256],"last_embed":{"hash":"1qzlx4n","tokens":123}}},"text":null,"length":0,"last_read":{"hash":"1qzlx4n","at":1751288831076},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#ESLint#{1}","lines":[1040,1045],"size":284,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1qzlx4n","at":1751288831076}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#TypeScript": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04276275,-0.02087213,0.03503403,-0.06048703,0.02138244,0.02302078,-0.05709074,-0.03523902,-0.04659872,-0.04812679,-0.03667885,0.01178551,0.06231404,-0.00144337,0.03170456,0.01893733,0.00953952,0.05788045,-0.08915275,-0.00447254,0.08811828,0.00795242,-0.01731461,-0.01106594,-0.0186387,0.0363913,-0.01040089,-0.02614637,0.03149628,-0.16115776,0.01602075,-0.03655401,-0.06909624,0.02750784,0.02966432,-0.03541815,-0.05018535,0.02245581,-0.01026613,-0.00072054,0.009203,0.04144737,0.00602884,-0.05054354,-0.0198034,-0.03572473,-0.0364601,0.01799065,-0.0049578,-0.0034237,-0.04608905,-0.04762498,0.03952769,-0.01894522,0.02268229,0.12869267,0.05569309,0.05592981,0.0227818,0.00422231,0.02061128,0.03476787,-0.14155529,0.1203533,0.0197626,0.0699939,-0.06391244,0.00276625,0.04165282,-0.03018182,-0.01776313,0.03888394,-0.00707107,0.08419719,0.02470978,-0.03616882,0.03473494,-0.06198053,0.04663388,-0.00687812,-0.08418582,-0.04588722,0.00911703,-0.01517021,-0.00482324,-0.01765122,0.07226066,0.00696087,0.00934943,0.02932299,-0.01175205,-0.10953056,0.03987586,0.03313547,-0.01501421,0.00035591,-0.00227127,0.07903237,0.00349712,0.11504426,-0.09971933,0.03428309,0.02270958,0.0013123,0.0699511,-0.04173415,-0.03999843,-0.05005005,-0.03559856,-0.02319399,0.00319736,-0.01018417,-0.05705665,-0.06218335,-0.044524,-0.01899297,-0.01410109,0.01400664,0.00687661,-0.03090215,0.01991133,0.06312419,0.03075531,0.01500547,0.01161771,0.04992913,0.02873258,0.02285955,0.01588938,0.06634595,0.00618101,0.09656695,-0.05425213,-0.01110302,0.00902925,0.06077322,-0.0509698,-0.05482895,-0.0511905,-0.00107352,0.04521578,-0.04042733,0.00684998,-0.03011237,0.02973078,0.04519,-0.01784539,0.03563425,0.02291266,-0.01254766,-0.08126388,0.05814931,-0.09993688,0.02382396,0.00899102,-0.00041805,0.00860533,0.01081131,-0.05336967,0.02876007,0.00987287,-0.00118775,-0.00170555,0.04403065,0.0003888,-0.07071514,-0.05751973,0.0646949,0.01450768,-0.09748761,-0.05678172,0.01366043,-0.00748871,-0.00540477,0.02132548,0.03518567,-0.0267901,-0.05909573,0.05109782,0.00184596,0.05688767,-0.04012264,-0.00234435,0.01228671,0.00211242,-0.0541971,0.04499147,-0.07033724,-0.01167664,0.03415307,0.01773485,0.03574035,0.06582044,-0.02315528,0.02250448,-0.00503026,-0.04412651,-0.01057691,0.04111389,-0.01528665,0.12051567,-0.00116121,-0.01167908,0.09920003,-0.0632128,0.04821708,0.01546655,-0.04269796,0.08395967,-0.00999227,-0.09884401,0.02120633,0.08519553,0.0931737,-0.03845049,0.02003345,0.03323806,0.04522164,0.0227121,0.06136359,0.00407105,-0.03410405,-0.10594374,-0.22069506,-0.01322618,0.04361328,-0.06268241,-0.02014303,-0.1045793,0.02753096,-0.02263703,-0.06121151,0.08940234,0.11963011,0.009367,0.01829531,-0.04555723,-0.02964021,0.01035547,0.01110227,-0.05371419,-0.02695207,-0.01731981,0.0325162,-0.04381301,-0.08524194,-0.11093185,0.05251188,-0.00837285,0.13912439,0.0150777,0.06660884,-0.04651495,0.07923836,-0.04358469,-0.00256186,-0.1072754,0.0514986,0.01085864,0.02249777,0.02726379,0.03517449,-0.01824597,-0.00215022,-0.04517177,-0.00071479,-0.05265598,0.05701825,-0.05804294,-0.05860535,-0.03766145,-0.01765669,0.07992837,-0.01741789,0.02561734,0.04399905,0.10096975,-0.0413749,-0.01300879,-0.01903233,-0.06146479,0.01194498,-0.01036955,-0.01863417,0.02250637,-0.00525227,-0.06396219,0.02300384,0.02293229,0.02355909,-0.01478288,-0.00092945,-0.04413968,-0.06207506,0.07180072,0.04195118,-0.00107367,-0.00938473,-0.0071837,-0.10711358,0.08233085,-0.00822765,0.01657102,0.00715082,-0.01399095,0.0339601,0.04840277,-0.02653375,0.01470257,-0.01263841,-0.02878744,0.03374753,0.00296839,-0.04703208,-0.00882926,-0.02420164,0.0124864,0.04590997,-0.00980983,-0.22413628,0.01187274,0.03424927,-0.0176232,-0.05562121,-0.0086408,0.07103723,-0.06367897,-0.03737878,0.00676275,-0.01693306,0.02458085,0.03778108,-0.00043837,0.0097623,-0.00318644,0.05660269,0.00782597,0.09689653,-0.05582634,0.00012542,0.00038615,0.2701599,-0.02989703,0.04368499,0.04050819,-0.05953643,0.00389859,0.05550173,0.08463743,0.05232106,0.04934955,0.07791506,0.00336134,-0.02025053,-0.03439096,-0.01642855,0.02894433,0.03231041,0.03510044,-0.01357058,0.03369784,-0.05743328,-0.01757599,0.04601093,-0.06989358,-0.07409959,0.01481534,-0.04695123,-0.03270712,-0.06127716,0.04650869,-0.01531795,-0.02375401,0.02744537,-0.00064988,0.00304164,0.02977319,-0.04134623,0.0268794,0.01755256,-0.04609868,0.00993438,0.0641788,0.01959093],"last_embed":{"hash":"2zcok","tokens":192}}},"text":null,"length":0,"last_read":{"hash":"2zcok","at":1751288831112},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#TypeScript","lines":[1046,1055],"size":562,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"2zcok","at":1751288831112}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#TypeScript#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04233889,-0.02230098,0.03112142,-0.06015715,0.02201208,0.02337806,-0.05754232,-0.03414899,-0.04925142,-0.04945903,-0.03573549,0.01344492,0.0606089,-0.00112772,0.0338812,0.02079568,0.00688995,0.05555479,-0.09201045,-0.00382651,0.0887583,0.00802814,-0.01717126,-0.00958957,-0.01813033,0.0346509,-0.01071636,-0.02647495,0.02946688,-0.16036621,0.01447624,-0.03919251,-0.07112215,0.02929259,0.02868528,-0.03514691,-0.05098591,0.02336156,-0.00755689,0.0002,0.0078334,0.04248643,0.00814605,-0.05300147,-0.02165146,-0.03582594,-0.03874028,0.01661539,-0.00518668,-0.00349591,-0.0463721,-0.04851154,0.03859651,-0.01865338,0.02136958,0.12780079,0.05490587,0.05781542,0.0238687,0.00477387,0.01947418,0.03392187,-0.14196591,0.12150884,0.0179876,0.07292148,-0.06547789,0.00369574,0.04253113,-0.03058156,-0.01843679,0.03860529,-0.00381852,0.08353728,0.02541298,-0.03635577,0.03504829,-0.06151025,0.04773897,-0.00619744,-0.0830421,-0.04664007,0.00900105,-0.01445713,-0.00587879,-0.0203873,0.07299891,0.0072629,0.00623337,0.02860829,-0.009133,-0.10776977,0.03606133,0.03342082,-0.01733428,0.00123409,-0.00443713,0.08019909,0.001618,0.11457981,-0.10048459,0.03317576,0.02336174,-0.00074683,0.06956843,-0.04043448,-0.04206531,-0.04991721,-0.03351864,-0.02290488,0.00082395,-0.00855226,-0.06084828,-0.06184929,-0.04598928,-0.01806341,-0.01495915,0.01459506,0.00387307,-0.03275651,0.02037231,0.06468824,0.03292352,0.01545288,0.01092453,0.04930352,0.02954255,0.0229148,0.0160281,0.06558581,0.00409358,0.09532402,-0.05382823,-0.01144412,0.01068375,0.06026798,-0.05228811,-0.05565216,-0.0487261,-0.00016522,0.04653135,-0.03920106,0.00855393,-0.03009486,0.03098715,0.04623219,-0.01694529,0.03586352,0.02172655,-0.01457531,-0.07937406,0.06053428,-0.10170801,0.02170967,0.01125748,0.00056767,0.00697399,0.00859026,-0.05242363,0.02653133,0.00955159,-0.00027635,0.00048108,0.04384085,-0.00063418,-0.0690018,-0.05726193,0.06421011,0.01440691,-0.09726981,-0.05926785,0.01341139,-0.00547504,-0.0072443,0.02189968,0.03616522,-0.02842227,-0.05756053,0.04879893,0.0032385,0.05626388,-0.0380818,-0.00091747,0.01241473,0.00420206,-0.05323607,0.04630161,-0.0697618,-0.01130596,0.03464259,0.01868972,0.03777651,0.06477928,-0.02618912,0.02442829,-0.00567184,-0.0425742,-0.01194062,0.03931895,-0.01437064,0.11918824,-0.0007553,-0.01033973,0.09675475,-0.06302152,0.04855714,0.01717515,-0.04283771,0.08438341,-0.01035117,-0.09827536,0.02184127,0.08407342,0.09135601,-0.03785989,0.02382158,0.03406089,0.0458791,0.02364518,0.06398998,0.00403352,-0.0350838,-0.10236973,-0.22050288,-0.01634278,0.04373593,-0.06307864,-0.0180659,-0.10918193,0.0268615,-0.02465149,-0.06237438,0.08793923,0.11967414,0.01083833,0.01880864,-0.0468984,-0.02823869,0.01154996,0.01389401,-0.05082211,-0.02537591,-0.01474959,0.03081229,-0.04439541,-0.08213565,-0.11375428,0.0526334,-0.00973133,0.139717,0.01287104,0.0671259,-0.04883028,0.0784352,-0.04405209,-0.00379252,-0.11002338,0.05283015,0.00861229,0.02044891,0.0262929,0.03679724,-0.01840249,-0.00295921,-0.04656226,-0.00220393,-0.05301426,0.05460359,-0.05737963,-0.05628652,-0.03788602,-0.01554053,0.08065522,-0.01933132,0.0255885,0.04420627,0.10156558,-0.04234039,-0.0129525,-0.01990883,-0.06020828,0.01108284,-0.00902959,-0.01742787,0.02404598,-0.00387059,-0.06351384,0.02500646,0.02239043,0.02259494,-0.01348384,-0.00250697,-0.04304123,-0.06126249,0.06990588,0.03941055,0.00033425,-0.00958573,-0.00537901,-0.10823338,0.08202129,-0.00779859,0.01506645,0.00705095,-0.01331161,0.03276071,0.05129492,-0.02523705,0.01521016,-0.00973576,-0.02835472,0.03212863,0.005361,-0.04479935,-0.01016796,-0.02290063,0.01233428,0.04516828,-0.01059096,-0.22328988,0.01117139,0.03314665,-0.0196387,-0.05732641,-0.0078403,0.07081173,-0.05989983,-0.03619139,0.00475434,-0.01649239,0.0247027,0.04100599,0.00202837,0.01084251,-0.00595132,0.05581639,0.00980104,0.09915057,-0.05842838,0.0023045,0.00099826,0.2698411,-0.03012945,0.04323883,0.04044126,-0.0591018,0.00338167,0.05353026,0.08456972,0.05022513,0.04961872,0.07720727,0.00416112,-0.01964935,-0.0314346,-0.01706441,0.02889794,0.03008781,0.03508291,-0.01350297,0.03300696,-0.0578405,-0.01972801,0.04770545,-0.06773441,-0.07333526,0.01743357,-0.04631319,-0.03459385,-0.06180408,0.04787023,-0.01589474,-0.02679607,0.02781346,0.00019156,0.00618487,0.03084023,-0.04047225,0.02698211,0.01961604,-0.04470076,0.00779718,0.06470291,0.0208605],"last_embed":{"hash":"1yqkahr","tokens":191}}},"text":null,"length":0,"last_read":{"hash":"1yqkahr","at":1751288831160},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#TypeScript#{1}","lines":[1048,1055],"size":547,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1yqkahr","at":1751288831160}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Credits": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.040971,0.00954813,0.01035906,-0.04688439,0.0159335,0.00339527,-0.07609202,0.02073009,0.00187877,-0.00742509,-0.04720544,0.00664538,0.06106859,-0.00976016,0.04426321,0.03505348,0.04120541,0.08762898,-0.06064697,-0.03114062,0.04172144,-0.03904856,-0.01324561,-0.06422633,-0.01052251,0.05447,-0.00490374,-0.04566951,0.00673367,-0.21843451,0.04016443,0.0120177,0.02542745,0.01365761,-0.01122574,-0.0293838,0.00409214,0.06006881,-0.02839638,0.02147941,-0.01570483,0.01831786,-0.04916637,-0.02109971,0.02148965,-0.05151088,0.00224964,0.01591795,-0.08014049,0.00947451,-0.05985262,-0.05175653,0.03885272,-0.0392943,0.02352066,0.10985513,0.05694625,0.07705886,-0.00362113,0.03541263,-0.00964862,0.0528992,-0.15069516,0.08157756,0.01678952,0.02988124,-0.03023073,-0.08698642,0.0004689,0.03396767,0.04929469,0.06108323,0.00038717,0.10552826,0.01199852,-0.02662998,0.03700486,-0.04871118,0.02754212,-0.00333089,-0.05259853,-0.02276533,-0.0104966,0.0137846,0.00916081,0.01376722,0.02820273,-0.01084297,0.02154849,0.02623424,-0.03467265,-0.10952586,0.04458186,0.00496683,0.01778919,-0.02649187,0.03525698,0.00596083,-0.02288307,0.10299769,-0.05773783,0.05690786,0.02337405,-0.04454515,0.08543696,-0.04399121,-0.01003023,-0.06319573,-0.02675166,-0.03286203,0.00458703,0.01431894,-0.05667958,-0.04893546,0.00968171,-0.01248311,-0.00869785,-0.00215732,0.0279742,0.01470026,-0.00833987,0.02256898,0.05238731,-0.00624251,-0.01759276,0.01417439,0.01477793,0.04868061,0.02443717,0.11087289,0.02951261,0.08845235,-0.04069629,-0.03875564,-0.0050536,0.05071712,-0.00218127,0.01725372,-0.02661354,0.0073693,-0.0055864,-0.01820773,0.01325256,-0.02782631,0.0285966,0.05650101,-0.05734275,0.04205733,0.00303431,-0.00644034,-0.0362437,0.05181786,-0.10083495,-0.03995007,-0.03880601,0.00402345,-0.00942952,0.03750635,-0.01525481,0.02803118,-0.03438228,-0.04020828,-0.01962249,0.04729749,-0.00411489,-0.12180308,-0.05600871,0.06180554,0.01335651,-0.08334822,-0.05111924,0.03539889,-0.02503728,0.02646137,0.01396688,0.0332201,-0.05849589,-0.03176469,0.068774,0.05337796,0.05825915,-0.01806505,-0.01727823,0.0341205,0.01004354,-0.08555108,0.02371438,-0.0831394,-0.02519355,-0.00993604,-0.02820999,0.02420447,0.01635413,0.02271412,0.00207383,-0.03033493,-0.021218,0.01220586,0.01346323,-0.01785551,0.09983689,-0.00320887,-0.00351065,0.02358051,-0.04574461,0.04248514,0.01651964,-0.04076049,0.05244383,0.01243854,-0.09887785,0.00940773,0.09440169,0.06328917,0.00043337,-0.00929603,0.02589793,0.04883003,0.02089101,0.04827185,-0.02208526,-0.03570698,-0.12687711,-0.21798231,0.01633333,0.0371809,-0.05200174,0.00767276,-0.06385101,0.01596456,-0.01889144,-0.01109191,0.01844965,0.07820521,0.0031149,0.01930584,-0.02095385,-0.06469926,0.03547506,0.00508538,-0.08436472,-0.06760812,-0.02733892,0.01481121,0.03034593,-0.04169769,-0.09920449,0.08498558,-0.00326674,0.15355556,0.06761295,-0.01163394,-0.02031729,0.03559704,0.01234015,0.00214921,-0.10477954,0.05123667,0.0475399,0.04417498,-0.031314,0.01859195,0.00521504,0.00469382,-0.01468159,-0.00798232,-0.08513477,0.02672547,-0.02647517,-0.04752868,-0.04313733,0.01048657,0.06341877,0.0154017,0.02135955,0.07319619,0.06478014,-0.00627935,-0.02737141,-0.03242115,-0.04802969,0.01315922,0.02805299,-0.01644178,-0.03341476,0.00263538,-0.09427694,-0.00043595,0.02714922,0.00624265,-0.05489384,0.02282356,-0.04918116,-0.08427972,0.09113478,0.00164963,0.00669119,0.01964061,0.00284164,-0.05258955,0.09795468,0.00706357,0.02055384,0.02150372,0.01123661,-0.00886682,-0.02694786,-0.03220968,-0.00641438,0.02334822,0.00775486,0.05006925,-0.02629718,-0.11441822,0.0272069,-0.04148103,0.00313345,0.07368309,-0.00793556,-0.2367634,-0.00943387,0.00894341,-0.02730651,-0.02107799,0.03529673,0.060106,-0.08535153,-0.020176,-0.02482289,0.01426009,0.05429924,0.04056222,0.02856044,-0.00132165,0.0000784,0.06809183,-0.01722569,0.06167679,-0.0718937,0.01023378,0.00520568,0.28836364,-0.03800024,0.02130019,0.01623422,-0.02237555,-0.00673311,0.02162054,0.06458383,0.03745595,0.04843797,0.1065419,0.02141371,-0.06249855,-0.00940836,-0.04831565,-0.01821625,0.03797894,0.00232693,0.00373346,0.00598478,-0.04170615,0.02594128,0.0691105,-0.08867933,-0.03155875,0.00239335,0.02141631,0.02215265,0.00429269,0.0502101,-0.06801986,0.00037873,0.02164337,0.02481439,-0.08917472,0.00215925,-0.05754153,0.02446515,-0.00927009,0.0279579,0.02847092,0.08999852,0.05281202],"last_embed":{"hash":"spktqa","tokens":415}}},"text":null,"length":0,"last_read":{"hash":"spktqa","at":1751288831234},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Credits","lines":[1056,1065],"size":1101,"outlinks":[{"title":"rijs","target":"https://github.com/rijs/fullstack","line":3},{"title":"glamor","target":"https://github.com/threepointone/glamor","line":4},{"title":"stylis.js","target":"https://github.com/thysultan","line":5},{"title":"styled-components","target":"https://github.com/styled-components","line":6},{"title":"stylis","target":"https://github.com/thysultan/stylis.js","line":6},{"title":"ember","target":"https://github.com/emberjs","line":7},{"title":"vuejs","target":"https://github.com/vuejs","line":8},{"title":"babel","target":"https://github.com/babel","line":9}],"class_name":"SmartBlock","last_embed":{"hash":"spktqa","at":1751288831234}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Credits#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03504239,0.00093125,0.02460234,-0.05573991,0.04007849,0.00509563,-0.08072474,0.00445352,0.01209884,-0.01316008,-0.03018789,0.01262042,0.04677726,-0.00434799,0.02067019,0.0023965,0.03385443,0.09299558,-0.07774949,-0.01855067,0.00928131,-0.00932062,-0.0283931,-0.06242127,0.0205826,0.05699381,-0.05708108,-0.05765683,0.04639785,-0.19132029,0.03244905,-0.01109594,0.04328513,0.0152735,0.01752792,-0.03602917,-0.02459802,0.03138473,-0.03324998,0.02954667,-0.03303757,0.07105013,-0.05062781,0.0039059,-0.01383303,-0.03303131,0.00024609,-0.00600771,-0.05819355,0.01681842,-0.03396524,-0.02135358,0.04734466,-0.01393582,0.03578393,0.13102645,0.02826497,0.04296622,-0.02409917,0.00529246,-0.01047473,0.0494433,-0.14990106,0.06262718,0.05155336,0.02444627,-0.0197482,-0.0744627,-0.00802302,0.02176724,0.02533483,0.06085283,0.01588735,0.10908845,0.02649307,-0.00485585,0.04034473,-0.08474907,0.02240102,0.01799145,-0.05470005,-0.0424559,-0.01486399,0.01094691,-0.00117287,-0.00691684,0.04523584,-0.00660988,0.05718921,0.02230465,-0.03033124,-0.14689751,0.05477287,0.01459375,0.01126415,-0.00579284,0.02822584,0.03137936,-0.01861639,0.12503634,-0.07181294,0.0343857,-0.00063062,-0.00637116,0.07570603,-0.00070674,-0.00390237,-0.03965481,-0.04345549,-0.04675288,0.01530229,-0.02195382,-0.05226369,-0.06009744,-0.00441915,-0.01553135,0.00482254,-0.00185492,0.02564724,0.01308495,0.01289385,0.05897029,0.04135914,0.00881135,-0.01819329,0.01669763,0.01668025,0.04154491,-0.00190308,0.10243618,0.02780512,0.07932442,-0.01768443,-0.0295137,-0.0059949,0.04030117,0.01324494,0.0059379,-0.03224529,0.00068047,-0.00779113,-0.02653411,0.01099084,-0.05564226,0.01744933,0.07534564,-0.0405102,0.08616956,-0.01990271,-0.02489935,-0.05277281,0.02466436,-0.04214071,-0.02902102,-0.0766403,0.03928814,0.01180986,0.02038211,-0.05575065,0.02335445,-0.01614883,-0.00208769,0.00223605,0.06803818,0.02057019,-0.10642597,-0.01651484,0.05489975,0.00991065,-0.06684615,-0.0374009,0.00643482,-0.02225825,0.01709772,0.01591172,0.00625162,-0.04812432,-0.01832544,0.08384901,0.051623,0.09763756,-0.03667725,-0.06265531,0.03419453,-0.00607976,-0.09130872,0.04535675,-0.06299541,-0.04969803,-0.00292294,0.00425322,0.02122661,0.04503568,0.02216873,-0.03013461,-0.0064402,-0.04952393,-0.00367912,-0.00623224,-0.01592622,0.11677505,-0.00575806,-0.04395083,0.03288359,-0.03354057,0.01881209,0.01450901,-0.04620548,0.07491972,0.01939647,-0.08653571,0.01393089,0.09897257,0.07369452,0.03152249,-0.04148515,0.02284197,0.06736621,0.01690745,0.03097383,0.02493918,-0.01518854,-0.10552329,-0.22186826,0.00506432,0.04164323,-0.06701049,0.01655784,-0.0749749,0.02486887,-0.00661493,-0.04989504,0.02228579,0.07608625,0.02421281,0.01271366,-0.02615147,-0.02006939,0.00717766,0.0103177,-0.07261986,-0.07228104,-0.00414577,0.01509506,-0.01821432,-0.05320584,-0.0785639,0.05564279,-0.00895783,0.14173602,0.0570282,0.00920507,-0.00310718,0.03955868,-0.02256475,0.01270833,-0.10404364,0.03845506,0.06543767,0.05134727,-0.05952293,0.05225477,-0.02022242,-0.00058583,-0.03310691,0.01119565,-0.05927183,0.04612161,-0.04896612,-0.0537146,-0.04949316,-0.00970475,0.07587346,-0.03763472,0.00814845,0.04922322,0.10234609,0.01412375,-0.00644468,-0.01983837,-0.05614853,0.00672208,0.02177155,-0.00978436,-0.03961809,0.01135721,-0.0798872,0.04879524,0.04576617,0.03100634,-0.05640094,0.0131998,-0.06333151,-0.07851855,0.07894629,0.00890278,-0.03302271,-0.01624107,-0.00433508,-0.01921965,0.12200382,-0.00387691,0.0265569,0.02497547,-0.01650755,0.01732503,-0.00678777,-0.00933759,0.02801098,0.01380119,-0.02084156,0.03375395,-0.00204705,-0.07935642,0.03067013,-0.02864081,0.0094121,0.04086106,-0.00446007,-0.25084499,-0.00076874,0.0003003,-0.03387556,-0.06477993,0.03224365,0.05261035,-0.08548831,-0.04101337,0.02294153,0.00001144,0.06106295,0.00929702,-0.00244513,-0.01701188,-0.00792861,0.07830504,-0.00105251,0.10703775,-0.04851755,-0.00914977,0.01802123,0.26403269,-0.04472322,0.01724482,0.00441584,-0.05553814,-0.00638865,0.06326576,0.04193231,0.03473376,0.03366705,0.07162815,0.02245217,-0.05480186,0.04180777,-0.02108294,0.00180711,0.01483417,0.02426757,-0.02088604,-0.02600084,-0.03436562,0.00596322,0.06875198,-0.10183356,-0.03344451,-0.02281588,0.01804523,-0.02531602,-0.01214851,0.06528644,-0.06146041,-0.01640417,0.0240433,-0.00085062,-0.09210669,0.00866242,-0.01606208,0.02984313,-0.03779118,-0.00756774,0.02588538,0.09137402,0.02049603],"last_embed":{"hash":"gxcrer","tokens":125}}},"text":null,"length":0,"last_read":{"hash":"gxcrer","at":1751288831367},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Credits#{4}","lines":[1061,1061],"size":315,"outlinks":[{"title":"styled-components","target":"https://github.com/styled-components","line":1},{"title":"stylis","target":"https://github.com/thysultan/stylis.js","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"gxcrer","at":1751288831367}},
"smart_blocks:Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Authors": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03897638,0.01929642,0.04689472,-0.00071917,0.01520774,-0.02133874,-0.11440176,0.00412881,-0.01861786,-0.04081477,-0.01749831,-0.02229896,0.05389801,0.00926588,0.05025297,-0.01255506,0.0365171,0.03495954,-0.02386678,-0.01043106,0.07419089,-0.01939485,0.00386921,-0.06130068,0.00103249,0.03872371,-0.02250599,-0.03505289,0.0183777,-0.18460727,0.00892396,0.00743108,0.00954841,-0.00870985,0.0782608,0.02434059,-0.02428631,0.04586425,-0.04315926,0.02836671,-0.00339834,0.04929484,-0.02971254,-0.00495409,0.06766393,-0.04019695,0.02487055,0.00700493,-0.05743337,-0.02289095,-0.05045588,-0.03262963,0.04716832,-0.01349297,0.03433934,0.1342545,0.05307825,0.05392829,0.04409135,0.03791154,0.05473749,0.07337622,-0.21048065,0.05019053,0.04827392,0.00035527,-0.05909178,-0.01560891,0.00733006,0.00835082,0.05261268,0.03741705,0.00345012,0.07277876,-0.02230427,-0.02155249,0.02424296,-0.05157062,-0.01239309,-0.00803676,-0.05680287,-0.02874244,0.00835272,-0.00334196,0.00249221,0.00041376,0.01527075,0.00428829,0.00655845,0.05295143,-0.04509404,-0.08953596,0.09424347,0.01894157,-0.04308154,-0.01052708,0.01815622,0.03290527,0.01684335,0.14644291,-0.05763447,0.0377926,0.06467906,0.03808629,0.02928883,-0.01287713,0.0051464,-0.08873466,-0.03237011,0.022933,-0.00836654,-0.04015404,-0.02872856,-0.05896246,0.02847243,-0.00352508,0.03102287,0.02721704,-0.0105834,0.01463397,0.02547383,0.08076227,0.00810925,-0.01535635,0.01584129,0.01651314,0.01700429,0.02007314,0.01450269,0.12162595,-0.0191823,0.11596189,-0.07034591,-0.01867562,0.00075704,0.04474623,-0.0117669,-0.00985011,-0.04781777,-0.00563477,0.02175529,-0.04481091,-0.00404036,-0.05081522,0.00841822,0.01512159,-0.04298523,0.08326299,0.0060074,0.01540427,-0.05232379,0.04415338,-0.08270814,-0.00321714,-0.04165069,-0.00239248,0.00424043,0.03055239,-0.06929557,0.03353247,0.0214578,-0.01145187,0.02326387,0.05124801,0.04119037,-0.09749688,-0.0294356,0.07892486,0.02091093,-0.09277496,-0.02579298,0.02457677,-0.01202702,0.02146044,0.04857565,0.02368838,-0.01570815,-0.06313463,0.05060991,0.02680246,0.06285337,-0.05367417,-0.02797751,0.04316692,-0.0357068,-0.0386745,0.0512156,-0.04868293,-0.00180967,0.01947904,-0.00924557,-0.00088204,0.0098349,0.0340629,-0.02106143,-0.05525709,-0.04942674,-0.01850947,0.0260234,-0.04136384,0.06880029,-0.01947179,-0.04706993,0.03578424,-0.00917584,0.04524678,0.00596023,-0.0215491,0.04639352,0.01338271,-0.08626097,0.00824341,0.08987219,0.0988049,-0.02907334,-0.02469194,0.02383008,0.05984685,0.00727072,0.02462532,-0.01701587,-0.02663972,-0.12157518,-0.21777387,0.01752521,0.01969467,-0.06534529,-0.02792432,-0.02312865,0.02767416,-0.02415162,-0.01184481,0.08318219,0.07837989,-0.01294553,-0.00157375,-0.03430691,-0.02601412,-0.00324304,-0.00144163,-0.07691187,-0.05812437,-0.07543265,0.02445298,-0.03316165,-0.069612,-0.08023177,0.02872309,-0.00811568,0.14138284,0.10096051,-0.0065595,-0.05141519,0.05215986,-0.01420568,0.01643433,-0.1077444,0.03395461,0.05810662,0.07878925,-0.01457528,0.00106379,-0.02217279,-0.02863766,0.02495803,0.01970439,-0.06701973,0.00827928,-0.03728765,-0.07172417,-0.03142861,0.0233568,0.07099231,-0.02987358,0.06409261,0.0477146,0.07269511,-0.06433018,0.0294292,-0.07013105,-0.04686239,0.01468722,0.04757987,0.01555754,0.00603345,-0.02017205,-0.07642215,0.05955777,0.03733607,-0.02602241,-0.01004931,0.04261455,-0.02456805,-0.03712342,0.07988908,0.04146505,-0.01476051,0.05944201,-0.00260212,-0.02212806,0.03286011,-0.01176381,0.02275675,0.03367029,-0.02137299,0.01663683,-0.00263499,-0.02628155,-0.01774348,-0.03335212,-0.07932585,0.03513655,-0.02561643,-0.07069578,0.02882776,-0.02145522,-0.00090327,0.05269573,-0.00186921,-0.24591199,-0.0099687,-0.00518315,0.02862815,-0.04847373,0.00038928,0.0445082,-0.07779571,-0.01473321,-0.02945559,0.00585384,0.06811422,0.00049998,-0.03182719,-0.00797746,-0.00430012,0.06080705,-0.01629764,0.05835443,-0.05738609,0.02120246,0.02811745,0.2642304,-0.01811547,0.00061057,0.03297272,-0.08995803,-0.03108584,0.0320691,0.08219704,-0.00097364,0.05257948,0.07162274,0.03935654,-0.04912336,-0.02276626,-0.03956989,0.0188507,0.0304838,-0.01247466,-0.01562625,0.02501942,-0.02125378,0.03096971,0.07161225,-0.08559557,-0.09146021,-0.04540204,-0.01591109,0.01214103,-0.03839321,0.03693888,-0.02638195,0.04216186,0.01820096,0.01165491,-0.04977099,-0.02839616,-0.05266031,-0.00719556,-0.00205227,-0.03334445,0.02573586,0.04258151,0.01952752],"last_embed":{"hash":"153vbcx","tokens":136}}},"text":null,"length":0,"last_read":{"hash":"153vbcx","at":1751288831401},"key":"Projects/Piecework/node_modules/styled-jsx/readme.md#styled-jsx#Authors","lines":[1066,1071],"size":270,"outlinks":[{"title":"@rauchg","target":"https://twitter.com/rauchg","line":3},{"title":"▲Vercel","target":"https://vercel.com","line":3},{"title":"@nkzawa","target":"https://twitter.com/nkzawa","line":4},{"title":"▲Vercel","target":"https://vercel.com","line":4},{"title":"@giuseppegurgone","target":"https://twitter.com/giuseppegurgone","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"153vbcx","at":1751288831401}},
