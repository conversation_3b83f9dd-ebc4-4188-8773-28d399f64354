
"smart_sources:Projects/Freelance/Carmel Bo<PERSON>/__home.md": {"path":"Projects/Freelance/Carmel Boutique/__home.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01874928,-0.02313127,0.02884403,-0.05041911,-0.02777691,0.01893943,-0.06102551,0.03144199,-0.04834684,0.02127637,0.03252212,-0.03256759,0.00314198,0.05236604,0.02336058,-0.00193342,0.08591221,-0.02932571,-0.04928768,0.02461998,0.01631459,-0.03361018,-0.05986621,-0.00829818,-0.00871081,0.00057971,0.00864799,-0.01787934,-0.06778848,-0.17444122,-0.07201085,0.02301375,0.06313743,-0.01375616,0.09814616,0.07138991,0.00131608,0.0324782,-0.03468746,-0.00041324,-0.00527416,0.00478018,-0.01540815,-0.00503895,0.05049457,-0.0542895,0.03053445,0.00795642,-0.05211254,-0.01252845,0.0133818,-0.06274669,0.00022599,0.01467135,0.03727875,0.09838511,0.03436491,0.03499401,-0.01115598,0.04766016,0.05878489,-0.03551098,-0.2090259,0.03586961,-0.02249433,0.04700281,-0.00468071,-0.00564537,0.01326759,0.06388284,-0.01431496,-0.02602005,0.04229371,0.0964654,0.02720817,-0.00767013,0.06362048,-0.07127723,0.02624399,0.03206788,0.00847743,0.02937218,-0.05451367,-0.01124886,-0.03007206,-0.01090279,0.00779096,-0.02897562,0.03747209,-0.02434378,-0.02188821,-0.11649383,-0.00748343,-0.07366424,-0.03377551,-0.00861479,0.00403327,-0.05214998,-0.02765351,0.13071021,0.06545608,0.05602786,0.05503258,-0.0766083,0.05020281,-0.00794749,0.02233967,-0.02411691,-0.00702475,-0.0046501,-0.05446668,-0.04949718,0.10779405,-0.11525118,-0.03795158,0.05456635,-0.07665849,0.03217426,0.03131547,0.00506341,0.04926831,0.00450399,0.02653589,-0.04254292,-0.05959976,-0.00733447,0.0235388,0.00085239,0.03816853,0.0828962,0.02310633,0.04947469,-0.02729402,0.01430121,0.03963191,-0.01035325,-0.03891232,0.03071231,0.01525664,-0.03475786,-0.05991897,-0.01621655,-0.02806656,-0.10526834,-0.03992547,0.10759433,-0.00031533,0.01924859,0.00765082,-0.02164307,-0.06739245,0.08261952,-0.00822084,-0.04753304,-0.0115074,0.03575512,0.10158886,0.05384506,-0.01403345,-0.00075566,0.05830454,-0.01709649,0.01131617,0.1071739,-0.03296696,-0.15181603,-0.02395569,0.0178791,0.02241847,-0.01252963,0.02191517,0.02602409,-0.02217133,0.03775765,0.08667418,-0.03158651,-0.00432843,0.08186814,0.06282771,0.03901235,-0.03995705,0.0182736,-0.04081923,0.0269908,-0.01666331,-0.13837971,0.03307806,0.04162705,0.03328169,0.03533954,-0.01958806,-0.06060056,-0.01033915,0.05598242,0.02264619,-0.00163628,-0.02216639,0.0293875,0.02556178,-0.03361967,0.06547503,0.03168342,-0.03362712,0.0640973,0.01726919,0.07348812,0.01827443,-0.02336939,0.06713117,-0.07728191,-0.02929225,0.06146023,0.06871369,-0.01459398,0.04639898,0.05742314,-0.00369166,0.11376204,0.08266168,0.06758616,-0.00678056,0.03251236,-0.0290467,-0.20444475,0.0480942,-0.02103403,0.01364212,-0.04438311,-0.06377722,0.05932704,-0.03535137,-0.02966651,0.05143002,0.13049772,-0.06033229,-0.00040704,0.00921401,-0.01630878,0.04126873,0.03638492,0.012534,-0.01327474,-0.01586818,-0.02577832,0.05108685,-0.0649194,-0.06968085,-0.00125403,0.00097604,0.12138966,0.05343156,0.01049842,-0.0193423,0.07378479,0.01963003,-0.06322972,-0.18684693,0.05912346,-0.0074334,-0.04344411,-0.07519878,-0.03223615,0.03086366,-0.03645398,0.03574165,0.00300189,-0.05885089,-0.01142435,-0.03793276,-0.0829337,0.03983779,-0.09770592,0.04632808,-0.01976477,-0.0632002,-0.00079318,0.04609195,0.05754026,-0.00982491,-0.04342773,-0.02758296,-0.01154616,-0.00250696,0.00852597,-0.00748164,0.02712564,0.02218354,0.02005502,-0.00141469,0.03518663,-0.02026992,0.01552221,-0.08929244,-0.04307479,0.04954507,0.03399209,-0.00497109,0.03285557,-0.03114462,-0.05903485,-0.00016583,-0.00875715,-0.02396626,0.05402115,-0.03034836,0.03154438,0.05874008,-0.01338953,0.05907774,0.06530602,-0.04014988,0.05526438,-0.01218008,-0.07145055,0.00968598,-0.0189113,0.01503436,0.05789017,-0.00629433,-0.23131938,-0.00241222,-0.01782019,0.01655224,-0.00765345,0.00749375,0.02345105,-0.05298044,-0.02870644,-0.05470703,-0.00046987,-0.01326165,0.02019363,-0.01077689,-0.00433042,-0.00196451,0.00080347,-0.04004494,0.02106375,-0.07305418,-0.00125784,-0.01590612,0.16654775,0.00544622,-0.00417449,-0.02061779,-0.01604448,0.00940737,0.03272581,0.03371403,0.00434816,0.03817805,0.13545643,-0.01089168,-0.02132196,-0.00778171,-0.02400923,0.0403005,0.01053566,-0.04126661,-0.00733212,-0.02656076,-0.05319288,0.00636348,0.05615254,-0.05190684,-0.01043731,-0.01441032,-0.03944861,0.06752518,-0.02858208,0.00817136,0.02659055,-0.00761021,-0.01788596,-0.00194107,-0.05444457,-0.00875838,-0.05106372,-0.03644142,0.02061153,0.02650094,0.03050323,0.05688431,-0.02855477],"last_embed":{"hash":"1m0777r","tokens":315}}},"last_read":{"hash":"1m0777r","at":1750999889787},"class_name":"SmartSource","last_import":{"mtime":1750949073777,"size":1242,"at":1750999881897,"hash":"1m0777r"},"blocks":{"#Carmel Boutique - WordPress Store":[1,43],"#Carmel Boutique - WordPress Store#Project Overview":[3,5],"#Carmel Boutique - WordPress Store#Project Overview#{1}":[4,5],"#Carmel Boutique - WordPress Store#Status":[6,11],"#Carmel Boutique - WordPress Store#Status#{1}":[7,7],"#Carmel Boutique - WordPress Store#Status#{2}":[8,8],"#Carmel Boutique - WordPress Store#Status#{3}":[9,9],"#Carmel Boutique - WordPress Store#Status#{4}":[10,11],"#Carmel Boutique - WordPress Store#Project Requirements":[12,18],"#Carmel Boutique - WordPress Store#Project Requirements#{1}":[13,13],"#Carmel Boutique - WordPress Store#Project Requirements#{2}":[14,14],"#Carmel Boutique - WordPress Store#Project Requirements#{3}":[15,15],"#Carmel Boutique - WordPress Store#Project Requirements#{4}":[16,16],"#Carmel Boutique - WordPress Store#Project Requirements#{5}":[17,18],"#Carmel Boutique - WordPress Store#Technical Specifications":[19,24],"#Carmel Boutique - WordPress Store#Technical Specifications#{1}":[20,20],"#Carmel Boutique - WordPress Store#Technical Specifications#{2}":[21,21],"#Carmel Boutique - WordPress Store#Technical Specifications#{3}":[22,22],"#Carmel Boutique - WordPress Store#Technical Specifications#{4}":[23,24],"#Carmel Boutique - WordPress Store#Deliverables":[25,32],"#Carmel Boutique - WordPress Store#Deliverables#{1}":[26,26],"#Carmel Boutique - WordPress Store#Deliverables#{2}":[27,27],"#Carmel Boutique - WordPress Store#Deliverables#{3}":[28,28],"#Carmel Boutique - WordPress Store#Deliverables#{4}":[29,29],"#Carmel Boutique - WordPress Store#Deliverables#{5}":[30,30],"#Carmel Boutique - WordPress Store#Deliverables#{6}":[31,32],"#Carmel Boutique - WordPress Store#Client Information":[33,37],"#Carmel Boutique - WordPress Store#Client Information#{1}":[34,34],"#Carmel Boutique - WordPress Store#Client Information#{2}":[35,35],"#Carmel Boutique - WordPress Store#Client Information#{3}":[36,37],"#Carmel Boutique - WordPress Store#Immediate Actions":[38,43],"#Carmel Boutique - WordPress Store#Immediate Actions#{1}":[39,39],"#Carmel Boutique - WordPress Store#Immediate Actions#{2}":[40,40],"#Carmel Boutique - WordPress Store#Immediate Actions#{3}":[41,41],"#Carmel Boutique - WordPress Store#Immediate Actions#{4}":[42,43],"#carmelboutique #wordpress #ecommerce #overdue #halfpaid #urgent":[44,45]},"outlinks":[],"last_embed":{"hash":"1m0777r","at":1750999889605}},"smart_blocks:Projects/Freelance/Carmel Boutique/__home.md#Carmel Boutique - WordPress Store": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01737659,-0.02107941,0.02734645,-0.05090498,-0.02851203,0.0189923,-0.06300747,0.02976941,-0.04957006,0.02231418,0.03518679,-0.03034847,0.00554973,0.04985873,0.02116769,-0.00178344,0.08342642,-0.03104522,-0.05010404,0.02527081,0.01746337,-0.03555016,-0.05968631,-0.0073675,-0.01141274,0.0009297,0.01242679,-0.01820242,-0.06169316,-0.17302445,-0.07290472,0.02115446,0.06540968,-0.01130606,0.09706953,0.06948912,-0.00373443,0.03132398,-0.03700057,0.00336317,-0.00315537,0.00639634,-0.01398484,-0.00248096,0.0529956,-0.05092286,0.03072296,0.00467712,-0.05589521,-0.01062249,0.01450684,-0.06014279,0.00128699,0.01460969,0.03238793,0.10360828,0.03456622,0.03414831,-0.0118041,0.04934683,0.05502772,-0.03549721,-0.21296676,0.03703363,-0.02107159,0.04619182,-0.00504773,-0.0046648,0.01402542,0.05992405,-0.01400228,-0.01984022,0.04186762,0.09624809,0.02724217,-0.00478671,0.0667049,-0.07111889,0.02596336,0.02884733,0.0104438,0.03347951,-0.05569823,-0.01323828,-0.03280463,-0.00937516,0.00541997,-0.02742673,0.03665629,-0.02238571,-0.0265943,-0.11557121,-0.01449141,-0.07436525,-0.03536035,-0.00821374,0.00507881,-0.0536297,-0.02497388,0.13522875,0.06218346,0.05491565,0.05005448,-0.07641803,0.05072255,-0.01033129,0.01828807,-0.01733051,-0.00894694,-0.00198779,-0.05121375,-0.04616981,0.10157565,-0.11783097,-0.04147964,0.05195983,-0.07909273,0.03043471,0.03392098,0.0062753,0.05247448,0.00562923,0.02766604,-0.04056653,-0.05628429,-0.00859032,0.02523254,-0.00164346,0.03662012,0.08244894,0.02226843,0.04974603,-0.03063029,0.01696197,0.0369474,-0.01059399,-0.03735673,0.03339703,0.01442541,-0.03149528,-0.06131316,-0.01456562,-0.02422093,-0.10743248,-0.04274032,0.10753427,-0.00220098,0.02079056,0.00408416,-0.01610402,-0.06927712,0.08417331,-0.00851407,-0.0472903,-0.01142931,0.04054561,0.09882846,0.04875032,-0.01496273,0.00472428,0.06323872,-0.01835379,0.01349592,0.1047356,-0.03635821,-0.1568768,-0.02206942,0.01837804,0.02591283,-0.01353118,0.02032246,0.02746496,-0.01902686,0.03696965,0.0870107,-0.02776814,-0.00115212,0.08558361,0.06495728,0.03860741,-0.03803147,0.01773331,-0.03541644,0.02346502,-0.01558684,-0.13533263,0.03202248,0.04121996,0.02884689,0.02907148,-0.01871428,-0.0604798,-0.00665856,0.05448694,0.02432717,-0.00014466,-0.02279645,0.0296306,0.02265941,-0.03039664,0.06425268,0.02921174,-0.03642443,0.06315812,0.01912711,0.0717212,0.01505412,-0.02487003,0.06483615,-0.07534945,-0.03125238,0.05955845,0.06974882,-0.01671671,0.04097761,0.05906291,-0.00441139,0.11336054,0.08230104,0.06199943,-0.00710842,0.03422614,-0.03080227,-0.20647523,0.05010878,-0.01810292,0.01496779,-0.05132466,-0.06513339,0.06044059,-0.03262748,-0.03307878,0.04839985,0.12933369,-0.06095991,0.00177673,0.01114508,-0.01493887,0.04051126,0.03586031,0.00849286,-0.01379629,-0.01892495,-0.02433989,0.04772356,-0.06772929,-0.07106011,-0.00325616,-0.00054103,0.11636737,0.0517615,0.00945766,-0.01902721,0.07507917,0.01720624,-0.06709956,-0.18702252,0.05916593,-0.00373871,-0.04588987,-0.07182783,-0.03417557,0.02613527,-0.03586403,0.03871895,-0.00252161,-0.05474372,-0.01220684,-0.03634129,-0.07831015,0.04096062,-0.0986101,0.04456661,-0.02124757,-0.06258886,-0.00041168,0.04680679,0.05796031,-0.01117805,-0.04349311,-0.02532484,-0.01166849,-0.00218114,0.00469623,-0.0031188,0.02327032,0.02421872,0.02181142,-0.00488982,0.03646833,-0.02457412,0.01538325,-0.09078454,-0.04420772,0.0437912,0.04070933,-0.00485892,0.03403766,-0.03337909,-0.06634064,-0.00144636,-0.01155762,-0.02302903,0.05448509,-0.02598764,0.03263767,0.06038204,-0.0139614,0.06381195,0.06298689,-0.04125686,0.05547978,-0.0099368,-0.06505518,0.00803638,-0.01361323,0.01178172,0.06281839,-0.00500974,-0.22904657,-0.00443795,-0.0174838,0.02022642,-0.01063083,0.00576748,0.02744494,-0.05439069,-0.02611425,-0.0578565,-0.00066562,-0.01436462,0.01729345,-0.01225326,-0.00296068,-0.00214158,0.00368967,-0.0417754,0.02271639,-0.07574134,-0.00046396,-0.0117412,0.16584484,0.01256152,-0.00011624,-0.02173656,-0.01011082,0.01263555,0.03173809,0.03649262,0.01174227,0.03505958,0.13801625,-0.00537961,-0.02080382,-0.00812222,-0.02138848,0.04296315,0.00643183,-0.04573158,-0.01046267,-0.0254337,-0.05583695,0.00842242,0.05740245,-0.05077173,-0.00692678,-0.01353244,-0.04287219,0.06530332,-0.02852383,0.01270271,0.02845846,-0.00683381,-0.01578957,-0.00185178,-0.0566865,-0.00932198,-0.05298342,-0.04017863,0.01980636,0.02638043,0.031343,0.05741549,-0.02548145],"last_embed":{"hash":"107c0up","tokens":293}}},"text":null,"length":0,"last_read":{"hash":"107c0up","at":1750999889697},"key":"Projects/Freelance/Carmel Boutique/__home.md#Carmel Boutique - WordPress Store","lines":[1,43],"size":1176,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"107c0up","at":1750999889697}},
"smart_blocks:Projects/Freelance/Carmel Boutique/__home.md#Carmel Boutique - WordPress Store#Technical Specifications": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.01389013,-0.02855054,0.01875945,-0.04403656,-0.01849134,0.01299922,-0.07031485,0.01985842,-0.02003029,0.02792186,0.02806158,-0.01646462,0.00513128,0.03152221,0.03482432,-0.00264264,0.08028996,0.01808521,-0.06382147,0.01535219,0.07812043,-0.01417734,-0.05176814,-0.03788024,-0.04322733,0.00509467,0.01694625,-0.0188527,-0.0365219,-0.14371599,-0.06377978,0.03168083,0.03532834,0.01689867,0.10040338,0.03671712,-0.05245582,0.03038619,-0.06433159,0.04977157,0.03328831,0.00313962,-0.00871734,0.01801317,0.03921368,-0.04604137,0.02574394,0.01429093,-0.05465062,-0.00657477,0.01911861,-0.02625173,-0.00749344,0.02389504,0.02189591,0.12132209,0.01900069,0.0353469,0.00844705,0.04802637,0.08526323,-0.00496887,-0.21838859,0.06554592,-0.01438279,0.05379847,-0.05129198,0.01865994,-0.02263041,0.05987107,0.00616833,-0.01158835,0.04052318,0.09797667,0.03452282,0.00995045,0.06640177,-0.07906201,-0.01242016,0.01291578,0.04726899,0.01767424,-0.06865468,-0.02755321,-0.04282169,-0.03427749,-0.02449254,-0.00936159,0.038341,-0.02574162,-0.05471,-0.10809955,-0.00456834,-0.08873841,-0.05852486,-0.01274395,0.00690161,-0.0441397,0.00974481,0.12957911,0.04367372,0.02765715,0.04797206,-0.08459389,0.06992558,-0.00578801,0.01247763,0.01538569,-0.01310075,0.01394961,-0.0375602,-0.04233828,0.06322095,-0.10640832,-0.0300349,-0.00202447,-0.07967301,0.01794397,0.05257326,0.00092916,0.0235547,0.00981426,0.04892517,-0.0186022,0.00434298,-0.00430023,0.00868175,-0.00237987,0.04433303,0.08815295,0.02128191,0.06334519,-0.03008105,0.02593041,0.04616035,0.00233609,-0.03153292,0.02029623,0.02148402,-0.01086013,-0.08349638,-0.03161435,-0.01517777,-0.08510096,-0.04418349,0.09659766,0.00530035,0.02734659,-0.01062212,0.03544619,-0.08486527,0.07255391,0.00773736,-0.04852412,-0.02536622,0.08039527,0.10129534,0.04407484,-0.03346546,0.00401937,0.05222142,-0.01523514,0.02312214,0.11211663,-0.06187903,-0.15636343,-0.03517485,0.02251894,-0.00178185,-0.03857989,0.00218109,0.00051518,-0.05220205,0.03412169,0.0862714,0.00028334,-0.00658572,0.0673253,0.05407605,0.03873188,-0.05191636,0.03391245,-0.01016681,0.0168251,0.00761276,-0.08871971,0.01398251,0.01864288,0.04571715,0.02381443,-0.02550023,-0.0495565,-0.00926266,-0.00614133,0.0217954,0.01983828,-0.01642888,0.0397508,0.04846152,-0.029068,0.05760173,0.03849836,-0.06672271,0.06692944,0.00149596,0.059316,-0.0122548,0.0016446,0.04979288,-0.07073803,-0.05335504,0.06427463,0.04491806,-0.00874619,0.0093837,0.01702523,0.01536288,0.11085837,0.07513398,0.06121923,-0.01880864,0.00153637,-0.08697052,-0.21852925,0.03136773,-0.02400645,-0.01823495,-0.0375288,-0.06056304,0.05154478,0.02071613,-0.04380776,0.02225969,0.11050214,-0.06980552,0.01942728,-0.02747886,-0.0366117,0.03344588,0.03807124,-0.01921664,-0.0050779,-0.01782984,-0.00729842,0.0220414,-0.0235986,-0.09044431,0.01718571,-0.00119016,0.09889603,0.06437633,0.06167012,-0.01616076,0.09013562,0.00078685,-0.05146243,-0.16212098,0.07189213,0.015526,-0.04162413,-0.05428541,-0.01368862,0.00289494,-0.02005482,0.05641269,-0.01068567,-0.05007333,-0.01280467,-0.03759931,-0.03766591,0.03555506,-0.11787345,0.06800176,-0.0339691,-0.03857369,-0.02390098,0.00886693,0.05312923,-0.02846397,-0.04283947,-0.02868724,-0.0306967,0.01377566,-0.0129549,0.00527517,0.00133472,0.00444649,0.01245438,-0.01025244,0.04092837,0.00064909,0.04348379,-0.09062977,0.00419699,0.0259062,0.05450457,0.01602446,0.04511867,-0.03849471,-0.06758352,0.00560341,0.0064698,-0.01891554,0.03120583,0.00471134,0.0645039,0.04077001,0.00022402,0.0626586,0.02756372,-0.07319643,0.07178017,-0.00460053,-0.06896269,-0.00057022,-0.01585337,0.01262928,0.05436332,0.04409792,-0.23724042,-0.01053874,-0.00894319,0.02355968,-0.0206855,0.00588809,0.03016317,-0.06713884,-0.04876152,-0.04509581,0.04272503,-0.01939284,0.00391393,-0.00676332,0.01069346,-0.01442544,0.01978291,-0.02567156,0.03593788,-0.08036838,-0.02248861,-0.00853555,0.1879503,0.02059355,-0.01518467,-0.0541261,-0.0056341,0.02179126,0.04190444,0.04852248,-0.00510784,0.028152,0.13683484,-0.00530936,-0.04162391,0.00561927,-0.02372961,0.03880086,0.0303687,-0.04435105,-0.02216406,-0.01124236,-0.08901073,0.0156859,0.04673102,-0.04549363,-0.01821851,0.00222629,-0.04254168,0.048215,-0.00109671,0.01733429,-0.00351591,-0.02192504,0.00479154,-0.00314703,-0.0437074,-0.00519058,-0.05659179,-0.08586415,0.03543351,0.01437456,0.02235682,0.04851018,-0.02075792],"last_embed":{"hash":"1isy7sq","tokens":75}}},"text":null,"length":0,"last_read":{"hash":"1isy7sq","at":1750999889787},"key":"Projects/Freelance/Carmel Boutique/__home.md#Carmel Boutique - WordPress Store#Technical Specifications","lines":[19,24],"size":207,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1isy7sq","at":1750999889787}},
