
"smart_sources:Projects/Piecework/node_modules/escalade/readme.md": {"path":"Projects/Piecework/node_modules/escalade/readme.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06400008,-0.03634981,0.04091146,-0.08865894,-0.00030284,-0.00651311,-0.10880815,-0.02916874,-0.01687378,-0.00129217,-0.01628265,-0.00189957,0.04578739,0.02693353,0.07735544,0.00149801,-0.02179509,0.09136997,-0.06630855,0.02758267,0.03242123,-0.05396045,0.03121378,-0.01620786,0.01170971,0.07169535,0.00433248,-0.02536556,-0.00622857,-0.21904519,0.01126123,0.00576837,0.04426642,0.00396555,0.00895693,-0.02037571,-0.00601966,-0.01566148,-0.02293594,-0.02813424,-0.00783773,0.04188478,-0.03979308,-0.03050505,0.00490464,-0.00933236,-0.00911201,0.00056612,-0.02720928,-0.03101094,-0.00242423,-0.04718864,0.00342374,-0.0096662,0.01823868,0.06398064,0.02252951,0.01122354,0.07111222,0.09302501,0.03104837,0.00218413,-0.19229387,0.07843458,0.01050204,0.05837212,-0.06420178,-0.02975781,-0.02864561,0.02764966,0.02629492,0.00285277,-0.02523921,0.09449068,0.03523119,-0.05323307,0.02351929,-0.01488797,-0.04932041,-0.04341292,-0.04773521,0.00462816,-0.06234046,0.00926156,0.04113844,0.00815156,0.0411882,-0.00861159,0.07359643,0.0285092,-0.03847355,-0.10787663,0.01428538,0.04273097,-0.06211408,-0.04458203,0.07794885,0.05007828,-0.07529801,0.1028681,-0.01615789,0.02816183,-0.00347698,-0.01984092,-0.00446664,-0.00499076,-0.01278783,-0.05835582,-0.00638835,0.00809884,-0.02517926,-0.01932154,0.00603684,-0.07351476,0.00193833,0.01564069,0.00491731,-0.0172589,-0.03152801,0.03176352,0.02272096,0.07261679,0.03465181,-0.08072276,0.02623069,0.00341095,0.01504287,0.04976021,0.00789921,0.07558338,-0.02240167,0.04895458,-0.06348996,-0.00444702,-0.03837243,0.05253378,-0.04414672,-0.02134768,-0.01024823,-0.04370414,-0.00497084,-0.09068017,0.02716872,-0.06725311,-0.04307683,0.05801435,-0.03610568,0.0355352,-0.04268142,-0.0473301,-0.0255213,0.07507159,-0.04202455,-0.0517419,-0.03404927,0.01229665,0.04348709,0.03621541,-0.116323,-0.0349273,-0.00674483,-0.02827993,-0.0301519,0.09864686,0.01243496,-0.11957751,-0.05500644,0.0643656,0.05640956,-0.05941289,0.01823216,0.03713217,-0.00457621,-0.07084814,0.09447675,-0.06357419,-0.11168577,-0.0171873,0.01770968,0.05640354,0.01488393,-0.08562718,0.00979773,0.03672219,0.05317701,-0.07167127,0.01801439,-0.06884888,0.00201425,0.03058215,-0.06007134,0.02288735,-0.04250928,-0.03837,-0.0505939,-0.00936435,-0.05382277,0.0065537,0.06271083,-0.00682963,0.08036147,0.02083955,0.0190175,0.05444625,-0.08319017,0.01997024,-0.00426458,-0.02399572,0.02654136,0.00234068,-0.08792299,0.01214232,0.1158605,0.04058376,-0.02208727,-0.01561379,-0.0005094,0.03423233,0.02225668,0.1039039,-0.02852488,-0.00136855,-0.03293879,-0.21420917,0.00400909,0.00673516,-0.03687884,-0.00699606,-0.03432839,0.0863988,-0.04176006,-0.03913632,0.031198,0.10880381,0.02894506,-0.01527039,0.07078645,-0.02834019,0.05550436,0.03143322,-0.04542803,-0.00214484,0.03689388,0.02870837,-0.01082141,-0.03643823,-0.06681035,0.02606077,-0.00751596,0.13887712,0.01461793,0.05503246,0.02534805,0.01371843,0.03356183,0.00906499,-0.08012467,0.00817732,-0.00942924,0.03890904,0.01122471,0.00127706,0.0078203,-0.03245601,0.00381976,0.06978367,-0.08757724,-0.01932969,-0.04022925,-0.00753245,0.01862003,0.03448915,0.01960846,0.03093327,0.01426962,0.02929227,0.04130158,0.03015172,0.02225103,-0.03838508,0.03327775,0.01847456,0.01047485,-0.02446134,0.02324737,-0.00360641,-0.05146772,0.05453565,0.02241391,0.02349782,0.02827923,0.08933464,-0.06814106,0.02957164,0.04611484,-0.01487505,-0.01030855,-0.02437587,0.01422989,-0.05384153,0.0108397,0.05873705,0.02407722,-0.01148166,-0.0226011,0.00164418,0.00421495,0.00693805,-0.01903986,-0.02159449,-0.01294821,-0.00302851,-0.001171,-0.03824776,0.02047976,-0.00259859,0.01010331,0.00584503,0.01608158,-0.23633057,-0.01859921,0.01892589,-0.02150257,-0.00099753,0.05696358,0.05999699,-0.04207249,-0.02814288,-0.04341889,0.0034781,0.0609711,0.00242398,-0.03813059,0.03208398,0.03452609,0.04174732,0.01399483,0.14752629,-0.09533481,0.0177291,-0.01273255,0.24460103,-0.0019793,0.00709515,0.03785149,-0.0267536,0.09883446,0.03428919,0.03712869,-0.01824072,0.04413716,0.068354,-0.00664809,-0.0195727,0.01114944,-0.0166969,0.03711809,0.07187133,-0.00356865,-0.00737557,0.03500197,-0.03499433,-0.03941765,0.0811106,-0.10493749,0.01110154,-0.03026848,0.0534569,0.05744204,-0.06321312,0.04126989,0.0483509,-0.00388353,0.01784429,0.01615239,-0.04807138,0.02492177,-0.07108381,-0.04768059,-0.03687781,-0.02157621,0.05677611,0.03398468,0.04128564],"last_embed":{"hash":"1ed7iyp","tokens":429}}},"last_read":{"hash":"1ed7iyp","at":1751288792839},"class_name":"SmartSource","last_import":{"mtime":1751244530248,"size":6993,"at":1751288765474,"hash":"1ed7iyp"},"blocks":{"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)":[1,212],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#{1}":[3,17],"#---frontmatter---":[12,16],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Install":[18,24],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Install#{1}":[20,24],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes":[25,43],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes#{1}":[27,28],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes##\"async\"":[29,35],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes##\"async\"#{1}":[30,35],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes##\"sync\"":[36,43],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes##\"sync\"#{1}":[37,43],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Usage":[44,120],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Usage#{1}":[46,120],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API":[121,156],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)":[123,156],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#{1}":[124,130],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#input":[131,139],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#input#{1}":[132,139],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#callback":[140,156],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#callback#{1}":[141,156],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Benchmarks":[157,185],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Benchmarks#{1}":[159,185],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Deno":[186,202],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Deno#{1}":[188,202],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Related":[203,208],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Related#{1}":[205,205],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Related#{2}":[206,206],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Related#{3}":[207,208],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#License":[209,212],"#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#License#{1}":[211,212]},"outlinks":[{"title":"![codecov","target":"https://badgen.now.sh/codecov/c/github/lukeed/escalade","line":1},{"title":"![CI","target":"https://github.com/lukeed/escalade/workflows/CI/badge.svg","line":1},{"title":"![licenses","target":"https://licenses.dev/b/npm/escalade","line":1},{"title":"fast","target":"#benchmarks","line":3},{"title":"escalade","target":"https://en.wikipedia.org/wiki/Escalade","line":5},{"title":"Deno Usage","target":"#deno","line":14},{"title":"Deno support","target":"http://deno.land/x/escalade","line":14},{"title":"CommonJS","target":"https://unpkg.com/escalade/dist/index.js","line":32},{"title":"ES Module","target":"https://unpkg.com/escalade/dist/index.mjs","line":32},{"title":"`util.promisify`","target":"https://nodejs.org/api/util.html#util_util_promisify_original","line":34},{"title":"CommonJS","target":"https://unpkg.com/escalade/sync/index.js","line":39},{"title":"ES Module","target":"https://unpkg.com/escalade/sync/index.mjs","line":39},{"title":"API","target":"#api","line":190},{"title":"two modes","target":"#modes","line":190},{"title":"premove","target":"https://github.com/lukeed/premove","line":205},{"title":"totalist","target":"https://github.com/lukeed/totalist","line":206},{"title":"mk-dirs","target":"https://github.com/lukeed/mk-dirs","line":207},{"title":"Luke Edwards","target":"https://lukeed.com","line":211}],"last_embed":{"hash":"1ed7iyp","at":1751288789097}},"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06605172,-0.034002,0.03900509,-0.08644054,0.00359532,-0.00827424,-0.11455636,-0.03499199,-0.02162712,0.00097099,-0.01257162,-0.00248257,0.04027305,0.02600258,0.0735532,0.00165596,-0.01765466,0.08183292,-0.06112634,0.03351562,0.03130648,-0.05612483,0.04165433,-0.01381521,0.00649329,0.07133499,0.00636533,-0.02303436,-0.00776949,-0.21823841,0.00823271,-0.00507152,0.03818419,0.00426082,0.01100476,-0.01864715,0.00094249,-0.02096521,-0.02653023,-0.021097,-0.00935131,0.04751679,-0.04727595,-0.02662076,-0.00099523,-0.00894087,-0.01220586,0.00623602,-0.02963246,-0.02993231,-0.01331322,-0.0484092,0.00420356,-0.01551567,0.02540528,0.06380634,0.01766338,0.0110329,0.07092647,0.09511562,0.02460967,0.00546154,-0.19414343,0.07461885,0.01236639,0.06135194,-0.06825697,-0.03365229,-0.02133118,0.0132697,0.03007062,0.00310998,-0.02127265,0.10064925,0.02899726,-0.0472303,0.02218904,-0.01739966,-0.05007595,-0.03991057,-0.05079415,0.00567016,-0.05742346,0.00776162,0.03485956,-0.00145347,0.04489355,-0.00548902,0.06832485,0.02354046,-0.04933947,-0.10459217,0.01291285,0.04037914,-0.05627077,-0.04209423,0.07201836,0.05403791,-0.07275606,0.10067375,-0.00716095,0.0312686,-0.00801397,-0.01276516,0.00048274,-0.00658889,-0.00727667,-0.059818,-0.00392359,0.00602478,-0.02949894,-0.02421835,0.00814762,-0.06659196,0.00607352,0.01106563,0.00544483,-0.02834276,-0.02463023,0.0342347,0.02846627,0.07584209,0.02951259,-0.07851858,0.02757445,0.01042411,0.01255256,0.05011024,0.0032224,0.08239962,-0.01951856,0.05573579,-0.06255355,-0.00369266,-0.03217293,0.04870436,-0.04302839,-0.02095199,-0.01486171,-0.03768745,-0.00675965,-0.09938603,0.02536368,-0.06211193,-0.04453407,0.06210124,-0.03193922,0.04947051,-0.04447108,-0.04913097,-0.01888527,0.0757331,-0.04692362,-0.04928562,-0.03407157,0.0130331,0.03751574,0.03625558,-0.11396697,-0.03663082,-0.00251872,-0.01868063,-0.02620052,0.10292218,0.01217249,-0.12045129,-0.05946862,0.07068628,0.05778495,-0.0617107,0.01777111,0.0325733,-0.00247943,-0.06573175,0.09216261,-0.059784,-0.10023397,-0.02249928,0.01673483,0.04788676,0.0179164,-0.0940946,0.00251695,0.03409562,0.048579,-0.067771,0.02331207,-0.05967326,0.00566247,0.03461755,-0.0649217,0.01999167,-0.0359702,-0.03418914,-0.04813999,-0.01622011,-0.05831296,0.01043234,0.06272914,-0.01308427,0.10359866,0.02795521,0.01364429,0.05035694,-0.08437005,0.0177987,-0.00083724,-0.02489786,0.02859296,-0.0046614,-0.08766794,0.01081226,0.11062983,0.05006649,-0.02000293,-0.01770924,-0.00051689,0.03089337,0.02675692,0.10293961,-0.02908815,-0.01072743,-0.04120355,-0.21353875,0.00761535,0.00930567,-0.03546027,-0.00836175,-0.03032768,0.08466611,-0.0452054,-0.03429849,0.03109903,0.11189479,0.02651123,-0.01379665,0.0689543,-0.0347394,0.04790527,0.02262841,-0.04421747,-0.00180845,0.03537599,0.02637566,-0.00674615,-0.04242484,-0.0640033,0.01799063,-0.0111666,0.13985725,0.01063081,0.05382235,0.01516831,0.01220916,0.02930471,0.02018443,-0.08415678,0.0047389,0.0056808,0.03825381,0.01659575,0.00459862,0.01173447,-0.0312879,0.00006819,0.06655812,-0.09292816,-0.01796028,-0.04219015,-0.00578233,0.02599209,0.03474248,0.02229402,0.02187242,0.01581963,0.03167801,0.04101283,0.02471262,0.02351412,-0.03448315,0.03017785,0.02408981,0.01051333,-0.0211642,0.02042885,-0.01120567,-0.05148009,0.05813363,0.02383734,0.01836259,0.02736199,0.08425529,-0.07051545,0.03316588,0.04737536,-0.01907208,-0.00329053,-0.02605636,0.02571947,-0.04640489,0.0073799,0.05860074,0.0252646,-0.00914613,-0.01438519,0.00094508,0.00822421,-0.00146731,-0.01453049,-0.02354782,-0.01430992,0.00229202,-0.005654,-0.04517865,0.01701215,-0.00608179,0.00024977,0.01155816,0.01336437,-0.2421173,-0.02253553,0.01620982,-0.02046526,0.00169518,0.05830021,0.05952536,-0.03406679,-0.03553613,-0.0397478,-0.00240292,0.06248641,0.00091573,-0.03619157,0.03136478,0.03864593,0.04223568,0.01291665,0.14252873,-0.09501069,0.02562026,-0.00489555,0.24688685,-0.0047039,0.00538299,0.04670547,-0.02079462,0.09889355,0.03875561,0.03230604,-0.02458084,0.04558984,0.06284375,-0.00478762,-0.01856601,0.01037564,-0.01306208,0.03699659,0.06625295,-0.00194981,-0.00859963,0.03659104,-0.02635526,-0.03887516,0.08783176,-0.10158782,0.0032337,-0.03655056,0.05142885,0.05517552,-0.06268982,0.0374931,0.04886154,-0.00753409,0.02376191,0.01712483,-0.04865439,0.0219615,-0.0781165,-0.04696834,-0.02808691,-0.03111926,0.05052485,0.03898469,0.04525537],"last_embed":{"hash":"1ed7iyp","tokens":486}}},"text":null,"length":0,"last_read":{"hash":"1ed7iyp","at":1751288789303},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)","lines":[1,212],"size":6924,"outlinks":[{"title":"![codecov","target":"https://badgen.now.sh/codecov/c/github/lukeed/escalade","line":1},{"title":"![CI","target":"https://github.com/lukeed/escalade/workflows/CI/badge.svg","line":1},{"title":"![licenses","target":"https://licenses.dev/b/npm/escalade","line":1},{"title":"fast","target":"#benchmarks","line":3},{"title":"escalade","target":"https://en.wikipedia.org/wiki/Escalade","line":5},{"title":"Deno Usage","target":"#deno","line":14},{"title":"Deno support","target":"http://deno.land/x/escalade","line":14},{"title":"CommonJS","target":"https://unpkg.com/escalade/dist/index.js","line":32},{"title":"ES Module","target":"https://unpkg.com/escalade/dist/index.mjs","line":32},{"title":"`util.promisify`","target":"https://nodejs.org/api/util.html#util_util_promisify_original","line":34},{"title":"CommonJS","target":"https://unpkg.com/escalade/sync/index.js","line":39},{"title":"ES Module","target":"https://unpkg.com/escalade/sync/index.mjs","line":39},{"title":"API","target":"#api","line":190},{"title":"two modes","target":"#modes","line":190},{"title":"premove","target":"https://github.com/lukeed/premove","line":205},{"title":"totalist","target":"https://github.com/lukeed/totalist","line":206},{"title":"mk-dirs","target":"https://github.com/lukeed/mk-dirs","line":207},{"title":"Luke Edwards","target":"https://lukeed.com","line":211}],"class_name":"SmartBlock","last_embed":{"hash":"1ed7iyp","at":1751288789303}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05676918,-0.03941762,0.03512593,-0.08831468,0.01035916,-0.0126027,-0.08266596,-0.02394869,-0.02118058,-0.00326241,-0.01537487,0.0019042,0.04358596,0.01579967,0.0620552,-0.00081647,-0.01990758,0.08749355,-0.06347774,0.01722762,0.03315164,-0.05651829,0.02637066,-0.02168685,0.01621799,0.06928379,0.00168765,-0.02077641,-0.02464553,-0.20588104,0.0017783,0.00037709,0.06739087,0.00712823,0.00319193,-0.027399,-0.01031256,-0.00942965,-0.02919651,-0.0319557,0.00252594,0.04278016,-0.03700325,-0.02393341,0.00115317,-0.01523323,-0.02412245,0.00525855,-0.04408704,-0.01632891,-0.01332728,-0.05065384,-0.00304824,0.00009325,0.00910327,0.05764473,0.02410327,0.00051319,0.06451147,0.09619932,0.03637498,0.0050036,-0.19794746,0.08137723,-0.00237637,0.05581983,-0.06535235,-0.03982616,-0.02577664,0.031055,0.03119813,-0.00200282,-0.01442887,0.09613004,0.03127499,-0.04779987,0.02062919,-0.01421934,-0.04811286,-0.05197287,-0.04831521,0.00694021,-0.06104346,0.00473076,0.04142767,0.01646877,0.04377277,-0.00361956,0.08163413,0.03335974,-0.04047018,-0.10928015,0.00718177,0.03141181,-0.06983111,-0.04366181,0.07735278,0.0533499,-0.08270121,0.10967626,-0.01674707,0.02890277,-0.01176753,-0.01975033,0.00855986,0.00824865,-0.01012688,-0.05461034,0.00466454,0.00611283,-0.03455211,-0.01513297,0.01883838,-0.06819601,-0.01263511,0.02902316,0.00732475,-0.02680998,-0.03815567,0.03647321,0.01876737,0.06642478,0.02652197,-0.07391918,0.02584249,0.01301714,0.01899622,0.06245542,0.00116045,0.0754991,-0.02470079,0.05460428,-0.06105886,0.00307131,-0.03396892,0.04776855,-0.04165551,-0.01274078,-0.0067299,-0.03447283,-0.01752329,-0.08946136,0.02234176,-0.06808747,-0.04623094,0.04854643,-0.03226063,0.03601154,-0.04044077,-0.0487069,-0.0247737,0.07438491,-0.03119539,-0.05729073,-0.03795081,0.0158874,0.05119168,0.04700814,-0.11230369,-0.04091515,-0.00173901,-0.02818506,-0.03428945,0.11484577,0.00844587,-0.12424561,-0.04685262,0.0597815,0.06094672,-0.04765126,0.01049153,0.03338629,-0.00550841,-0.06370392,0.08270483,-0.06281851,-0.1070524,0.01470849,0.01469773,0.06951346,0.01703151,-0.08858407,0.01174822,0.04362894,0.05535119,-0.06590199,0.02257511,-0.06638507,-0.00572012,0.04334515,-0.08071046,0.02814907,-0.05262946,-0.04835507,-0.03741743,-0.0164998,-0.04324531,-0.00210588,0.06820063,-0.00541336,0.08072933,0.0298975,0.02211784,0.05747512,-0.07449275,0.00697277,-0.00028105,-0.02686133,0.0083727,0.01112198,-0.07514829,0.02308937,0.1148968,0.02708207,-0.0136478,-0.02004921,-0.00241571,0.03155983,0.00989728,0.09539323,-0.04004526,0.00155638,-0.01089455,-0.22535601,-0.00511955,0.00822403,-0.03955458,-0.01360923,-0.03821181,0.09393267,-0.03233545,-0.03546413,0.02451268,0.11306138,0.04025543,-0.02285911,0.07682017,-0.02960111,0.05556136,0.02355894,-0.04923302,0.01211878,0.03469163,0.03789925,-0.00721845,-0.01878422,-0.07007498,0.02593323,-0.00514535,0.13917376,0.0058655,0.05570561,0.02390175,0.01843254,0.02744648,0.00694234,-0.0795444,0.01294899,-0.01051415,0.03773742,0.0009017,0.00440845,0.00294981,-0.03481951,-0.00012282,0.06299037,-0.09305287,-0.02187444,-0.0369737,-0.01316323,0.00959894,0.03208717,0.02678135,0.03145134,0.00606574,0.03477737,0.05596616,0.04978762,0.00916755,-0.03083878,0.03488151,0.01636053,0.01661673,-0.01876501,0.01936316,-0.00734535,-0.04995574,0.05377312,0.01586577,0.01346818,0.03104523,0.09223563,-0.07039403,0.03075669,0.05747944,-0.01338625,-0.01920679,-0.02579951,0.02399697,-0.05361025,-0.01028867,0.06300975,0.02897791,-0.01787947,-0.01423896,0.00702914,0.00118133,0.01237977,-0.01955565,-0.01837375,-0.00603759,0.00114792,-0.00676927,-0.03180121,0.02649155,-0.02010957,0.01524241,0.00641332,0.02056826,-0.24492447,-0.02767108,0.00960588,-0.02615082,0.00109108,0.06348089,0.07108103,-0.04011646,-0.03951318,-0.04543915,0.00654049,0.05669517,0.00816802,-0.03392398,0.03804368,0.02774698,0.04752134,-0.00100662,0.15814206,-0.07510523,0.01594462,-0.01003547,0.23944305,0.00859056,0.01676302,0.02580075,-0.02358679,0.09488697,0.02710576,0.02492672,-0.01044051,0.03565773,0.07358743,0.00183723,-0.01820667,0.02133792,-0.01688799,0.01545146,0.05073469,-0.0046766,-0.01258467,0.0285794,-0.02696513,-0.0574028,0.08012493,-0.0931474,0.00439291,-0.04279222,0.04694131,0.05339376,-0.0537546,0.03172423,0.03832966,-0.00209357,0.01299382,0.01880961,-0.04361672,0.02691362,-0.07610115,-0.05685313,-0.03338789,-0.01862881,0.04553636,0.04170878,0.03952664],"last_embed":{"hash":"13fkxma","tokens":398}}},"text":null,"length":0,"last_read":{"hash":"13fkxma","at":1751288789538},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#{1}","lines":[3,17],"size":710,"outlinks":[{"title":"fast","target":"#benchmarks","line":1},{"title":"escalade","target":"https://en.wikipedia.org/wiki/Escalade","line":3},{"title":"Deno Usage","target":"#deno","line":12},{"title":"Deno support","target":"http://deno.land/x/escalade","line":12}],"class_name":"SmartBlock","last_embed":{"hash":"13fkxma","at":1751288789538}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07616177,-0.04336108,0.00683707,-0.04614854,0.01092433,-0.00407486,-0.07902744,-0.0072568,0.00442695,0.00988931,-0.01235793,-0.00329909,0.02072166,0.03227476,0.04928763,0.00034567,-0.02669929,0.03808155,-0.02910173,-0.00721301,0.04054029,-0.00311861,0.03764657,-0.04473665,-0.00479413,0.07329014,-0.01951171,-0.00976636,0.01364873,-0.19779149,-0.00940051,-0.02239441,0.02615585,0.01692543,-0.02361054,-0.03594043,0.01169906,-0.00863445,-0.0290594,0.02635475,-0.01284606,0.03457604,-0.07402673,-0.04221379,0.00400762,-0.04063725,-0.00900337,0.00293688,-0.0285221,-0.01549786,-0.00699783,-0.05643201,-0.00732568,-0.01898315,0.02662281,0.05830953,0.01483458,0.05779109,0.05067557,0.09019639,0.02050068,0.0141176,-0.21210983,0.06873292,0.02094935,0.05063045,-0.05309021,-0.01816063,0.01807208,0.02428692,0.01728101,0.02909358,-0.00612403,0.06595505,0.00814877,-0.01941518,0.00408048,-0.01133084,-0.03834003,-0.03195993,-0.05335267,-0.00378748,-0.02384874,-0.0000711,0.02382967,0.00924378,0.02508689,-0.02986751,0.04942513,0.00046149,-0.09093839,-0.08731727,0.01519114,0.04626832,-0.06019396,-0.00921515,0.05332773,0.04527261,-0.06002005,0.11007114,-0.02981374,0.04240965,-0.02000487,0.01056644,0.03803635,-0.0011721,-0.02215104,-0.09138016,0.00823904,0.01566589,-0.05087953,-0.00044282,-0.00150503,-0.10008228,-0.02106727,0.0623014,-0.02733449,-0.01881197,-0.01475694,0.04567178,0.024035,0.07732,0.02017113,-0.03761296,0.01282402,0.03210061,0.02412789,0.01976359,0.00644538,0.05596048,-0.01669211,0.10123243,-0.02158894,0.00824422,-0.03164258,0.02085896,-0.0050319,-0.01692104,0.01194731,-0.01906685,0.00227201,-0.04352242,0.04338247,-0.0800189,0.01663255,0.05724316,-0.01461656,0.05546221,-0.03184549,-0.04925436,-0.0234761,0.0337654,-0.06204618,0.00221495,-0.01442045,0.00360004,0.05284729,0.05776088,-0.11124229,-0.0244903,-0.01347803,-0.01539493,-0.0077653,0.12421094,0.00284092,-0.09775648,-0.03279598,0.04255365,0.02903149,-0.02417838,-0.02200282,0.0352782,-0.01830499,-0.00982506,0.06181609,-0.01238625,-0.09459744,-0.03681548,0.01406502,0.04360653,-0.01202312,-0.07599235,0.00517354,0.04939389,0.03378123,-0.01159128,0.03348457,-0.04646773,0.00254214,0.01716479,-0.05647376,0.01499654,0.00507094,-0.00331496,-0.03929368,-0.01002929,-0.06131603,-0.00365565,0.07430324,-0.03978362,0.11107468,0.06910004,0.01356653,0.03650934,-0.06483252,0.01195878,-0.0440913,-0.00971614,0.0019146,-0.02958762,-0.10500217,0.03512213,0.09832186,0.07959283,-0.01191585,-0.02512013,0.02038731,0.06405695,0.00470981,0.06237933,-0.03953687,0.06035,-0.03211916,-0.22887649,-0.00261096,0.01741124,-0.02694697,0.025272,-0.02970923,0.02379117,-0.04413779,-0.08610172,0.0234918,0.16061945,0.03130397,-0.03822931,0.03213499,-0.03232832,0.05088082,-0.02650277,-0.06172263,-0.00368499,0.02211954,0.04047751,0.00510314,-0.06533342,-0.05235958,0.01515755,-0.02164103,0.15450323,0.00363111,0.01063936,0.00472376,0.02370332,0.00721157,0.02188857,-0.1117622,-0.01623313,0.0453829,0.02995428,0.02001374,0.02643138,0.0318434,-0.0199312,-0.00245548,0.05985018,-0.11680988,0.00442178,-0.051019,-0.04866735,0.00949998,-0.00456825,0.02474131,0.04238427,0.02860027,0.04250681,0.03289456,0.01962195,0.02847063,-0.03648674,-0.0339459,0.0386408,-0.00661153,0.00255498,0.01691354,-0.0161318,-0.07431748,0.05461058,0.02915086,-0.00085781,0.01497879,0.07874222,-0.03521687,-0.00756056,0.08933509,0.02428746,-0.00353165,-0.00117436,0.00550999,-0.06304819,0.02811514,0.05759217,0.03706733,-0.01765835,0.0139868,0.02242822,-0.03456388,-0.02485171,-0.0193122,-0.01144948,-0.0314119,0.01687841,-0.02680509,-0.06082011,-0.01508375,-0.00217059,-0.00767146,0.0744001,0.01219618,-0.24958004,-0.00607878,0.0364903,-0.03925373,-0.0388009,0.0453385,0.03111342,-0.0614451,-0.09063827,-0.02586475,-0.03079162,0.07580747,0.00610181,-0.01122837,0.03621224,0.06005224,0.07138186,-0.05142921,0.1165331,-0.11645132,0.05651723,0.04125165,0.24880964,-0.03192143,0.01547639,0.0632133,-0.00645152,0.10470428,0.04854053,0.04850695,-0.01389094,0.01343495,0.05332823,0.00734909,-0.05564783,0.01372626,0.00615777,0.02060079,0.04988255,0.02366186,0.00156263,0.06919844,0.00521525,-0.05519762,0.06712499,-0.0948288,-0.02225682,-0.07355269,0.03302435,0.01725533,-0.07291771,0.00049691,0.0330759,0.04915808,0.01206864,0.03973878,-0.02483875,0.02686752,-0.07580775,-0.05119212,0.00413819,-0.03029108,0.00303493,0.01182564,0.00406998],"last_embed":{"hash":"ziezo9","tokens":487}}},"text":null,"length":0,"last_read":{"hash":"ziezo9","at":1751288789828},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes","lines":[25,43],"size":705,"outlinks":[{"title":"CommonJS","target":"https://unpkg.com/escalade/dist/index.js","line":8},{"title":"ES Module","target":"https://unpkg.com/escalade/dist/index.mjs","line":8},{"title":"`util.promisify`","target":"https://nodejs.org/api/util.html#util_util_promisify_original","line":10},{"title":"CommonJS","target":"https://unpkg.com/escalade/sync/index.js","line":15},{"title":"ES Module","target":"https://unpkg.com/escalade/sync/index.mjs","line":15}],"class_name":"SmartBlock","last_embed":{"hash":"ziezo9","at":1751288789828}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes##\"async\"": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05627533,-0.02998825,0.01169769,-0.05766163,0.03172586,-0.01174791,-0.06618058,0.00466612,-0.00433486,0.01930222,-0.02209238,-0.01072294,0.03001208,0.02963945,0.05700847,0.00247606,-0.00864716,0.04531045,-0.03118533,0.00250512,0.03708971,0.00159598,0.04353014,-0.04204039,0.00907522,0.07159377,-0.01001312,-0.01591002,0.00699934,-0.18594469,-0.01860307,-0.03029867,0.05266934,0.01515691,-0.00473416,-0.03737284,0.00314846,-0.00692885,-0.03256163,0.0209335,-0.00123304,0.02715276,-0.07568507,-0.02736494,0.02193267,-0.04459273,-0.01750909,0.00366919,-0.02577912,-0.02213922,-0.0103065,-0.05763851,-0.00107738,-0.01965256,0.02332762,0.03751855,0.01526602,0.04448747,0.07248794,0.08039507,0.02088788,0.00928425,-0.21167068,0.07084554,0.01075314,0.04932693,-0.07075129,-0.01616585,0.01471775,0.03419244,0.02324018,0.02450302,-0.00776525,0.06868927,0.0045861,-0.02137599,0.00741643,-0.0048518,-0.02935053,-0.03128954,-0.05900121,0.00383858,-0.03264553,-0.00502644,0.02198319,0.01396725,0.03331011,-0.01285719,0.06008331,0.0179075,-0.07387391,-0.10409405,-0.00291561,0.06495847,-0.07269751,-0.02385404,0.0602996,0.0272525,-0.06744636,0.12209249,-0.02219549,0.04225861,-0.01869737,-0.00884006,0.0332874,-0.00243103,-0.01538618,-0.08912537,0.00134998,0.00592863,-0.05597823,0.01689013,0.00505969,-0.0982187,-0.01037069,0.07150636,-0.02716983,-0.00171864,-0.027677,0.05425492,0.01999216,0.07245205,0.00135245,-0.04547843,0.0032571,0.04500363,0.02148995,0.0379182,0.00272947,0.07172725,-0.02997348,0.11093057,-0.035316,0.01454489,-0.03387095,0.02731207,-0.01343216,-0.00874291,-0.00655545,-0.02497094,-0.00661427,-0.02768398,0.0302135,-0.08793382,0.00520076,0.02119221,-0.02789585,0.04893062,-0.02362011,-0.05547566,-0.02975048,0.04363756,-0.06461506,0.00856463,-0.01427009,-0.01466717,0.06341169,0.06651487,-0.10144785,-0.02913644,-0.01291041,-0.02673637,-0.00112892,0.12702888,0.01522167,-0.08368326,-0.0309018,0.03707032,0.04121649,-0.02052203,-0.02278363,0.03724771,-0.02948718,-0.00616354,0.05603246,-0.01740564,-0.09191675,-0.03089176,0.0135985,0.04305858,-0.0122781,-0.0715493,0.01613586,0.06506356,0.03468832,-0.01779713,0.04145082,-0.06283036,-0.0097082,0.01855986,-0.04983867,0.04026897,-0.01545082,-0.02168398,-0.04091016,0.00281568,-0.04773968,-0.01722986,0.07446387,-0.0373125,0.10573626,0.05741818,0.01513272,0.04426786,-0.06319447,0.01107072,-0.0256795,-0.01600578,-0.00684664,-0.01656739,-0.09595516,0.03692505,0.10561375,0.06733078,0.00356298,-0.03539022,0.03553919,0.0504537,0.00555347,0.04942407,-0.04815937,0.04183021,-0.02391197,-0.23776112,0.00241024,0.003811,-0.03092015,0.01614398,-0.02466244,0.02147116,-0.03265332,-0.07526615,0.02715937,0.1657408,0.03403022,-0.03686207,0.03701831,-0.03331894,0.07637931,-0.01662845,-0.05118361,-0.00012319,0.03752813,0.03709216,-0.0002991,-0.05123232,-0.05733549,0.00807025,-0.01739136,0.15238215,0.02031787,0.02353347,0.01110743,0.01889857,0.0114032,0.01060497,-0.11275487,0.00386295,0.03328667,0.0275328,0.02967915,0.019788,0.02814365,-0.02207106,-0.01136133,0.06664777,-0.1110741,0.01720828,-0.04806264,-0.05769913,0.0095828,-0.00754832,0.02934292,0.0387036,0.01989194,0.0622086,0.04436779,0.02587186,0.02085189,-0.04049999,-0.02730239,0.03809506,0.00583104,0.01910038,-0.00097536,-0.01321458,-0.09277793,0.05839206,0.04466774,-0.00847074,-0.00134492,0.08792402,-0.03668482,-0.0292894,0.08210863,0.03471465,-0.01926609,0.00818082,0.00574937,-0.07640881,0.01805675,0.06892522,0.03814036,-0.02681891,0.01028263,0.02067213,-0.04231174,-0.01370857,-0.0156048,-0.03220995,-0.02569984,0.01335921,-0.02729424,-0.04925541,-0.00803755,-0.01378296,0.0129658,0.06642996,0.00178295,-0.24971801,0.0070145,0.03621791,-0.03987614,-0.03473592,0.03532425,0.03227826,-0.07283188,-0.07694635,-0.03771968,-0.03890164,0.072532,0.00973233,-0.02720971,0.03303255,0.05217773,0.06592973,-0.03923353,0.12264944,-0.09357414,0.03844972,0.02313359,0.24295568,-0.0251006,0.01518327,0.04821275,-0.01551216,0.08404827,0.05185306,0.04872365,-0.0017121,0.00974292,0.0705314,0.01544304,-0.06046458,0.02038927,0.01560876,0.02704267,0.02729588,-0.00006012,-0.00496185,0.05692288,0.00081777,-0.05562648,0.04216986,-0.10466309,-0.01098677,-0.08095838,0.03778338,0.02256368,-0.06865485,-0.00431745,0.00843856,0.03599161,-0.00919226,0.04191659,-0.02395055,0.00935517,-0.07731823,-0.06110177,-0.01481623,-0.0357918,0.01950691,0.02418789,0.01231022],"last_embed":{"hash":"3j2f19","tokens":347}}},"text":null,"length":0,"last_read":{"hash":"3j2f19","at":1751288790055},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes##\"async\"","lines":[29,35],"size":355,"outlinks":[{"title":"CommonJS","target":"https://unpkg.com/escalade/dist/index.js","line":4},{"title":"ES Module","target":"https://unpkg.com/escalade/dist/index.mjs","line":4},{"title":"`util.promisify`","target":"https://nodejs.org/api/util.html#util_util_promisify_original","line":6}],"class_name":"SmartBlock","last_embed":{"hash":"3j2f19","at":1751288790055}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes##\"async\"#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05350161,-0.02702859,0.0110106,-0.0597474,0.03115928,-0.01286583,-0.06844059,0.00278677,-0.00554376,0.01780422,-0.02515668,-0.01099762,0.03086962,0.02904879,0.05805065,0.00409693,-0.00802515,0.0463926,-0.03061526,0.00234359,0.03728896,0.00153736,0.04423685,-0.04048551,0.01051916,0.07110889,-0.01156473,-0.01426269,0.00401312,-0.18680987,-0.01903012,-0.03360933,0.05346317,0.01631422,-0.0055256,-0.03667077,0.00288143,-0.0062233,-0.02947896,0.02017249,-0.00222088,0.02755106,-0.07365341,-0.02788077,0.02265711,-0.04524608,-0.01706982,0.0033758,-0.02742472,-0.02044756,-0.01000728,-0.05862378,-0.00079227,-0.01847465,0.02291005,0.03887456,0.01282909,0.04186686,0.07094347,0.07921915,0.0216806,0.01151512,-0.21289814,0.06961645,0.01267301,0.05051986,-0.06972614,-0.01618341,0.01516456,0.03512507,0.02158335,0.02642165,-0.00810894,0.06918167,0.00277718,-0.02275827,0.00865734,-0.00295617,-0.02963194,-0.02986201,-0.06065051,0.0040806,-0.03464601,-0.00632948,0.02348127,0.01378632,0.03376146,-0.01216541,0.0603731,0.01890593,-0.07267451,-0.10186221,-0.00252433,0.06509778,-0.07312283,-0.024025,0.06236829,0.02574149,-0.06797208,0.12135324,-0.01945496,0.04361862,-0.0186696,-0.00988285,0.03407032,-0.00021513,-0.01543965,-0.0869638,0.00242049,0.0056148,-0.05693959,0.01469283,0.00470656,-0.0956268,-0.01022248,0.07356794,-0.02761367,-0.00130902,-0.02932597,0.05546376,0.0193862,0.06958863,0.0026493,-0.04690658,0.00282229,0.04536581,0.02161778,0.03933467,0.00679507,0.07057966,-0.02928944,0.11089235,-0.03796456,0.01108349,-0.03235892,0.02632544,-0.01374113,-0.01019418,-0.00463584,-0.0218096,-0.00881045,-0.02725004,0.03037225,-0.08845031,0.00402427,0.02160372,-0.02612881,0.04425828,-0.02397197,-0.05483919,-0.02886821,0.04378936,-0.06216335,0.01072036,-0.01270996,-0.01388178,0.0644765,0.06707919,-0.10076199,-0.02735645,-0.0120359,-0.02713976,-0.00142039,0.12927268,0.01377015,-0.08425743,-0.03118338,0.03528311,0.03996236,-0.02259264,-0.02293141,0.03735713,-0.02807739,-0.00496248,0.05336123,-0.01773002,-0.09271308,-0.03195415,0.01147817,0.04392464,-0.01127033,-0.07309981,0.01600739,0.06746598,0.0341356,-0.01664613,0.04265424,-0.0649593,-0.00809952,0.01616381,-0.04908225,0.04065939,-0.01564438,-0.02313432,-0.0393642,0.00304691,-0.0462889,-0.01712316,0.07453022,-0.03788492,0.10332491,0.0576087,0.01514623,0.04254733,-0.06199467,0.01105774,-0.02714024,-0.01662475,-0.00801086,-0.01694207,-0.09572858,0.03507746,0.10548109,0.06808665,0.00594942,-0.03561345,0.03625362,0.04716949,0.00422761,0.04765402,-0.04843593,0.04433252,-0.02570656,-0.23950222,0.00157474,0.00456051,-0.03268241,0.01503139,-0.0269488,0.02108432,-0.02994656,-0.07407314,0.02587943,0.16641468,0.03306501,-0.03615806,0.03729377,-0.03301137,0.07871658,-0.01881786,-0.04987038,0.0002758,0.03719706,0.0365856,0.00196012,-0.05120292,-0.05651855,0.00902036,-0.01943606,0.1520053,0.0200242,0.0204425,0.01052236,0.01960567,0.01258732,0.01003165,-0.11207787,0.00480084,0.02990988,0.02748118,0.02973916,0.01892658,0.02912974,-0.02097701,-0.01017967,0.06744766,-0.11283639,0.01514185,-0.04706888,-0.05921776,0.00824379,-0.01061764,0.02776146,0.03750537,0.01967248,0.06266007,0.04456649,0.02718166,0.01945051,-0.04118535,-0.0295371,0.03656198,0.0062246,0.01884013,-0.00191596,-0.01320273,-0.09514677,0.05781695,0.04286736,-0.00739126,-0.00022927,0.08850912,-0.03647917,-0.02870349,0.08007545,0.03445756,-0.02126358,0.00987044,0.0068702,-0.07448341,0.01493459,0.06743623,0.04017496,-0.0268417,0.01162492,0.01922417,-0.04263793,-0.01434426,-0.01459863,-0.03380496,-0.02474051,0.01551149,-0.02644029,-0.04784869,-0.00909113,-0.01367281,0.01508939,0.06758936,0.0022865,-0.24895649,0.00760266,0.03919128,-0.03840742,-0.03535446,0.03397942,0.03335761,-0.07446382,-0.07699438,-0.03820654,-0.0395203,0.07197352,0.01128207,-0.02578155,0.03409244,0.05144434,0.06725261,-0.03841415,0.12174349,-0.09506387,0.03610206,0.02274152,0.24254368,-0.02419818,0.01299463,0.04996811,-0.01524119,0.08304525,0.05340682,0.04988949,0.00143145,0.01036567,0.07336847,0.01647819,-0.05818416,0.02083476,0.01528882,0.0265333,0.03068999,0.00078923,-0.00540827,0.05867798,-0.00170457,-0.0576081,0.04258871,-0.10301024,-0.01145446,-0.08093384,0.03879585,0.02303857,-0.06918871,-0.00465503,0.00811498,0.0353871,-0.00839655,0.04353275,-0.02386019,0.00797996,-0.07790128,-0.06029196,-0.01227416,-0.03546125,0.01963326,0.02353156,0.01079231],"last_embed":{"hash":"j5ba5t","tokens":344}}},"text":null,"length":0,"last_read":{"hash":"j5ba5t","at":1751288790208},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes##\"async\"#{1}","lines":[30,35],"size":342,"outlinks":[{"title":"CommonJS","target":"https://unpkg.com/escalade/dist/index.js","line":3},{"title":"ES Module","target":"https://unpkg.com/escalade/dist/index.mjs","line":3},{"title":"`util.promisify`","target":"https://nodejs.org/api/util.html#util_util_promisify_original","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"j5ba5t","at":1751288790208}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes##\"sync\"": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07767094,-0.04122308,-0.0014399,-0.04663132,0.01206859,-0.00842077,-0.06619185,0.01059135,0.00523486,0.01155265,0.0027277,-0.01190349,0.01911751,0.02723049,0.0546267,0.0048939,-0.01404584,0.04691298,-0.01284418,-0.01421723,0.03200311,-0.00909793,0.02820982,-0.04316502,-0.01511916,0.06773323,-0.02343232,-0.01377257,0.01295769,-0.18849827,-0.01747849,-0.02540358,0.0393549,0.02208694,-0.00904197,-0.02661888,0.00080721,-0.00891035,-0.03411239,0.03270487,-0.01491616,0.03676021,-0.0838747,-0.03247672,0.01153214,-0.04082808,0.00493134,-0.0083405,-0.03490714,-0.00267286,-0.0070597,-0.05627612,0.00076296,-0.01320496,0.00913991,0.07061971,0.01809978,0.05334867,0.06145353,0.0798765,0.01281724,0.02446969,-0.20908746,0.06760832,0.01804683,0.05610084,-0.05054794,-0.0124526,0.02566704,0.02991819,0.01331473,0.03409668,-0.00599199,0.05417008,0.01254178,-0.01383204,0.01012734,-0.00835353,-0.04160668,-0.0446786,-0.05226181,0.00456782,-0.02968989,-0.00582394,0.0335642,0.02140258,0.02009708,-0.02510887,0.05880413,0.00225954,-0.09040172,-0.08346111,0.01888716,0.03617528,-0.06343585,-0.01404871,0.06141204,0.03828811,-0.08162181,0.12264903,-0.03277302,0.0435903,-0.02525831,-0.00454015,0.03494483,0.00500561,-0.0238218,-0.08416636,0.00825556,0.01107365,-0.04007133,0.00971559,-0.00255016,-0.10080253,-0.01827209,0.07932448,-0.01949742,-0.01077988,-0.040283,0.0587972,0.01554306,0.08803546,0.02489618,-0.02558676,0.01655233,0.03120265,0.02157009,0.02816872,0.00331479,0.04443591,-0.00730135,0.10737704,-0.01272714,0.00598748,-0.03107733,0.00914216,0.00267431,-0.01073597,0.01723698,-0.02596431,0.00378184,-0.03193679,0.05967791,-0.08112128,0.01214323,0.04061595,-0.02052833,0.06176348,-0.02321839,-0.06002743,-0.02200867,0.03421855,-0.06016335,-0.00268025,-0.01874302,-0.00395223,0.06007761,0.06510537,-0.11876293,-0.01031787,-0.00618389,-0.02035221,0.00853417,0.12577401,0.00691999,-0.10082612,-0.02734227,0.04250605,0.03132367,-0.02696149,-0.02402662,0.04533206,-0.01618043,-0.00290063,0.06117936,-0.02350145,-0.10113114,-0.03036068,0.00476192,0.04057291,-0.01294584,-0.0879272,0.01076574,0.05633677,0.03797556,-0.00454575,0.02046734,-0.0629468,0.00042813,0.02097248,-0.05706543,0.00833933,-0.0154901,-0.00540625,-0.03461687,-0.00693352,-0.06507938,-0.01263438,0.07200184,-0.03253109,0.10293975,0.05553075,0.00834795,0.04500457,-0.06128574,0.00914479,-0.03714234,-0.0063982,-0.00947009,-0.03387773,-0.09546143,0.03694985,0.10673147,0.06544176,-0.00749427,-0.00913001,0.01250785,0.07379773,0.00130492,0.05279114,-0.05397118,0.06138137,-0.0235032,-0.23346554,0.00087544,0.01405509,-0.03332804,0.01473754,-0.03419477,0.03155522,-0.04479482,-0.07418054,0.02833057,0.15685301,0.0401056,-0.0425629,0.01553539,-0.03005135,0.06048271,-0.02589035,-0.05633103,0.00909304,0.02540639,0.04011114,-0.00902163,-0.0607578,-0.0454814,0.03308145,-0.03346432,0.15705056,0.00771249,0.00494068,0.01494376,0.02041104,0.01069271,0.0189038,-0.11278664,-0.01221883,0.03693763,0.0330876,0.03476848,0.02409475,0.03628485,-0.02971462,-0.00574747,0.06044369,-0.11492109,0.00171878,-0.04931347,-0.05024603,0.00031572,-0.01108657,0.01881915,0.04473895,0.02332862,0.05224064,0.05295394,0.02525272,0.02243933,-0.03447244,-0.0287945,0.03073189,0.00177689,-0.00277935,0.01189409,-0.01502187,-0.07563073,0.05685355,0.03600382,0.00672278,0.02362201,0.08074317,-0.02464763,-0.00413778,0.08523493,0.03281796,-0.01247141,0.00522923,-0.00485285,-0.06298175,0.01492991,0.05477104,0.04218331,-0.01880326,0.01222858,0.02830982,-0.03419578,-0.01575315,-0.02080133,-0.01936987,-0.02245894,0.00866004,-0.02080003,-0.05212394,-0.01290297,-0.01073819,-0.00304797,0.06779426,0.01428582,-0.24603847,-0.0144043,0.0204162,-0.03013325,-0.04749207,0.04362055,0.03898163,-0.07309303,-0.09780806,-0.02499075,-0.01998636,0.06875005,0.00720337,-0.02292394,0.0267307,0.04888122,0.05916242,-0.06690478,0.11600284,-0.10777897,0.05053134,0.03169221,0.23552869,-0.02868483,0.00473999,0.0495593,-0.01646991,0.09912413,0.05291867,0.05510295,-0.0237533,0.01086141,0.05501514,0.01650189,-0.05257633,0.01957659,0.01696662,0.02288265,0.05390766,0.02875708,0.01103375,0.06775746,0.00556571,-0.07016909,0.05665892,-0.09907896,-0.03256448,-0.07269509,0.02863893,0.01152871,-0.06791072,0.00344049,0.02402933,0.04412462,0.00848748,0.03879201,-0.03247819,0.02107821,-0.07772137,-0.05965022,0.00115076,-0.03492869,0.00408824,0.00625671,-0.01606357],"last_embed":{"hash":"1bg6y0","tokens":303}}},"text":null,"length":0,"last_read":{"hash":"1bg6y0","at":1751288790330},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes##\"sync\"","lines":[36,43],"size":288,"outlinks":[{"title":"CommonJS","target":"https://unpkg.com/escalade/sync/index.js","line":4},{"title":"ES Module","target":"https://unpkg.com/escalade/sync/index.mjs","line":4}],"class_name":"SmartBlock","last_embed":{"hash":"1bg6y0","at":1751288790330}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes##\"sync\"#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0785012,-0.04132318,-0.00017302,-0.04611496,0.00924375,-0.00844445,-0.06287333,0.01178603,0.01038393,0.00954875,0.00546615,-0.01061795,0.01507568,0.0284809,0.05317584,0.00482726,-0.01414681,0.0466019,-0.01375641,-0.015055,0.0319117,-0.00814395,0.02905818,-0.04387005,-0.01575899,0.06718007,-0.02550726,-0.01322907,0.01216113,-0.18821366,-0.01691643,-0.02415581,0.03957186,0.02275348,-0.01435872,-0.02266892,0.00166195,-0.00590765,-0.03185046,0.0354765,-0.01643345,0.03708897,-0.08423097,-0.03314792,0.00773522,-0.0420636,0.00595107,-0.00891859,-0.03882646,0.00038809,-0.00311098,-0.05721283,-0.00085546,-0.01525277,0.00749039,0.07436316,0.01794739,0.05438643,0.06135349,0.08238247,0.01019986,0.02542336,-0.20983899,0.06631216,0.01827581,0.05689892,-0.04890423,-0.0116815,0.02535323,0.03247531,0.01110931,0.03446257,-0.00613829,0.05250959,0.01374299,-0.01388763,0.00741906,-0.00823376,-0.04334404,-0.04713704,-0.0510512,0.00521266,-0.02597595,-0.00617134,0.03184201,0.02063836,0.02075359,-0.02884364,0.05892936,-0.00021642,-0.09130372,-0.08422887,0.01996223,0.03178637,-0.06468306,-0.01418826,0.06150139,0.0405235,-0.08054164,0.12264735,-0.03571602,0.04071249,-0.02413753,-0.00335625,0.03669628,0.00446925,-0.0239655,-0.08375112,0.01102572,0.0112921,-0.04017438,0.00704468,-0.00277123,-0.10147851,-0.01863205,0.07841414,-0.01589857,-0.01026159,-0.04058687,0.05852707,0.01822698,0.09000675,0.02705092,-0.02335394,0.01506967,0.02957334,0.02121808,0.02673827,0.00237023,0.04281127,-0.00531419,0.10515186,-0.01110558,0.00661474,-0.03015624,0.00974143,0.00698049,-0.01016458,0.0189948,-0.02622344,0.00588887,-0.02864715,0.06003483,-0.08156738,0.01125239,0.04322084,-0.01992073,0.06265049,-0.02170984,-0.06031783,-0.02322133,0.03359422,-0.05847439,-0.00217862,-0.01863762,-0.00214987,0.0605041,0.06408874,-0.11985034,-0.01040251,-0.00684444,-0.01821627,0.00951905,0.12438387,0.0035841,-0.1031663,-0.02865512,0.04105394,0.03177305,-0.02615263,-0.02465102,0.04821198,-0.01570727,-0.00156261,0.06284726,-0.02284603,-0.10057554,-0.02721931,0.00374967,0.04005335,-0.01309476,-0.08764196,0.00864598,0.05596345,0.0392386,-0.00176483,0.01988218,-0.0615229,0.00099019,0.02234828,-0.05896343,0.007123,-0.01396031,-0.00348916,-0.03325184,-0.00727313,-0.0689885,-0.01408781,0.0708726,-0.03277595,0.10232267,0.05438027,0.00821368,0.04564412,-0.05872718,0.00974063,-0.03726332,-0.00991228,-0.01075977,-0.03652633,-0.09761352,0.03738858,0.10457692,0.06496388,-0.00804729,-0.00824361,0.00879948,0.07634754,-0.00061031,0.04843363,-0.05277594,0.06042549,-0.01979125,-0.23519589,0.00195308,0.01332021,-0.03212574,0.01525259,-0.03687502,0.03133517,-0.0436867,-0.07453769,0.02843473,0.15500093,0.04092514,-0.04135081,0.01439903,-0.02851508,0.06013807,-0.0290295,-0.0564617,0.00988937,0.02519211,0.04119409,-0.00873913,-0.05655403,-0.04530498,0.03536684,-0.03298971,0.15651985,0.00933319,0.00605732,0.01596415,0.02137743,0.00831775,0.0187865,-0.11025891,-0.01399658,0.03389967,0.03678444,0.0350558,0.02500715,0.03452301,-0.03115308,-0.00658079,0.05809138,-0.11666483,0.00215767,-0.05177762,-0.04909046,-0.00368605,-0.01450353,0.01865017,0.04731635,0.02532726,0.05257013,0.05337647,0.02724697,0.02338276,-0.03432575,-0.02965915,0.02719607,0.00068907,-0.00675651,0.01380706,-0.0159049,-0.07566565,0.05551937,0.03533106,0.00579318,0.02748368,0.0778194,-0.02234701,-0.00399732,0.08506493,0.0351942,-0.01391611,0.00442226,-0.00631535,-0.06264986,0.01695211,0.05370875,0.0425561,-0.0172752,0.01439555,0.02960804,-0.03459559,-0.01552478,-0.0231971,-0.01789451,-0.0215371,0.00975442,-0.02220009,-0.05605756,-0.01238906,-0.00913464,-0.00390514,0.06544871,0.01264046,-0.24637771,-0.01436361,0.02013151,-0.02950827,-0.04909191,0.04804735,0.03818941,-0.07401089,-0.09975287,-0.02453846,-0.01926683,0.06646817,0.00888087,-0.02257709,0.02645792,0.04793476,0.05812372,-0.068405,0.11643188,-0.10880505,0.05212273,0.02977928,0.23392691,-0.02756552,0.00553016,0.04812515,-0.01357809,0.1016774,0.05169462,0.05486718,-0.02507361,0.01213365,0.05380945,0.01568671,-0.05218865,0.01915235,0.01738806,0.02150196,0.05458431,0.02892035,0.01293507,0.06784449,0.00759264,-0.07118869,0.05865052,-0.09722365,-0.03362291,-0.07066614,0.02637878,0.01037901,-0.06688385,0.00652274,0.02333598,0.04154755,0.01096726,0.04042673,-0.03176222,0.02148605,-0.07707416,-0.05836546,0.00187686,-0.0322056,0.0027337,0.00609406,-0.01794016],"last_embed":{"hash":"124j8i1","tokens":300}}},"text":null,"length":0,"last_read":{"hash":"124j8i1","at":1751288790458},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Modes##\"sync\"#{1}","lines":[37,43],"size":276,"outlinks":[{"title":"CommonJS","target":"https://unpkg.com/escalade/sync/index.js","line":3},{"title":"ES Module","target":"https://unpkg.com/escalade/sync/index.mjs","line":3}],"class_name":"SmartBlock","last_embed":{"hash":"124j8i1","at":1751288790458}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Usage": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0576117,-0.03720278,0.03512394,-0.05242634,0.03917612,-0.02109098,-0.05871383,0.00403461,0.00185979,0.01144198,-0.03995173,-0.04866663,0.04312585,0.01702934,0.064808,-0.00463299,-0.04650547,0.03529665,-0.04525771,-0.03199242,0.02733281,0.00377603,0.02399759,-0.0494615,0.00595405,0.08689672,0.01900291,-0.01881687,0.01319564,-0.16832012,-0.01093106,-0.02368526,0.04082832,0.0301146,-0.01547434,-0.02622409,-0.01725471,-0.00618842,-0.03559876,0.02265291,0.01861874,0.00814998,-0.06576341,-0.0161618,0.01312708,-0.03613256,-0.0351235,0.00575013,-0.02461265,-0.00102869,-0.00629274,-0.0604163,0.00248468,-0.01843946,0.03125925,0.05424711,0.03309956,0.05137423,0.07344587,0.09640551,0.02242832,0.00188082,-0.20398074,0.07193761,0.0077463,0.04253922,-0.06292776,-0.01659507,0.02557572,0.04610165,0.01018748,0.02055743,-0.00048084,0.06929821,-0.00190155,-0.02381549,0.00772143,-0.03842533,-0.02964993,-0.03868096,-0.06762046,0.01225928,-0.02107086,-0.01131347,0.04177853,0.02947151,0.04042443,0.00026034,0.05422285,0.03441175,-0.04760925,-0.11696825,-0.0110921,0.06233909,-0.06930073,-0.04748818,0.0659621,0.03853118,-0.0715902,0.12756723,-0.0526511,0.03765858,-0.01146823,-0.01903774,0.027989,0.00613235,-0.01657054,-0.07343291,-0.01020022,0.00220115,-0.04429434,0.00016034,-0.02475324,-0.09101932,-0.02105623,0.04256753,-0.01881613,-0.01709656,-0.04555596,0.026255,0.03138772,0.07114743,0.00863541,-0.01695299,-0.00826948,0.03802403,0.03322802,0.02068887,-0.02698404,0.06694027,-0.02920422,0.06212565,-0.05781945,0.00221458,-0.03941751,0.06376868,-0.00379073,-0.00894484,-0.0139894,0.0181188,0.01341601,-0.04340837,0.0532161,-0.09506353,-0.00140791,0.00296849,-0.03467585,0.06442025,-0.03855603,-0.07494107,-0.00256139,0.05459854,-0.05486648,-0.01445357,-0.02440163,-0.00264624,0.06892115,0.08690523,-0.07652317,-0.01067388,-0.01098827,-0.03965827,-0.01236242,0.11247748,0.03124641,-0.09347508,-0.02611523,0.0368766,0.03764483,-0.02794889,-0.00511332,0.0329995,-0.05906399,0.01107348,0.04499569,-0.0112218,-0.05843858,0.00279278,0.01842963,0.06218647,-0.0066771,-0.05054173,-0.02562423,0.04331687,0.05877101,-0.02578272,0.01970318,-0.05709708,-0.01631306,0.02876329,-0.05398783,0.07283872,-0.02748375,-0.01150526,-0.04593644,0.00766948,-0.05125966,-0.03383162,0.07477044,-0.03318642,0.12209631,0.0768204,0.02665814,0.03677031,-0.06910045,0.01828568,-0.01043849,0.0019345,-0.00611574,-0.03486084,-0.10239328,0.01755314,0.09752196,0.0512504,0.0138437,-0.0452283,0.00955896,0.04440827,0.05235037,0.05754284,-0.05933398,0.04242671,-0.04570063,-0.25158942,0.01791061,0.02800635,-0.01612381,0.00254979,-0.03851275,0.02435058,-0.05133402,-0.05973415,0.05354212,0.15222742,0.01383284,-0.0167045,0.02404636,-0.01834907,0.05269045,-0.02592646,-0.04708018,-0.00752644,0.05222544,0.04740816,-0.01677069,-0.01066402,-0.05820205,0.00928636,-0.0184888,0.15662023,0.03152649,0.02929945,-0.01766664,0.02002915,-0.00091467,0.00304381,-0.09654688,-0.01186569,0.01853714,0.02137852,0.02483336,0.03588204,0.01865514,-0.04439492,0.01327852,0.04620331,-0.11220784,0.02702993,-0.05608088,-0.0477919,-0.0302157,-0.01802106,0.03315167,0.04422333,0.03055858,0.07810181,0.06419553,0.04152604,0.0102753,-0.03904372,-0.01212697,0.02765494,-0.01961149,0.01854921,0.03511859,0.00926601,-0.06787235,0.03735276,0.03999875,0.00968384,0.01462656,0.06706036,-0.02466315,-0.03068377,0.07343214,0.03189202,-0.00267174,-0.00295829,0.01340769,-0.07940835,-0.00472258,0.06013271,0.03275241,-0.04950669,-0.00116589,0.03489062,-0.03050286,-0.00912542,-0.00669236,-0.02674654,-0.03940778,0.03193804,-0.05241355,-0.05896311,-0.01325798,-0.01158908,0.01319803,0.04078613,0.02877823,-0.24723516,0.01471143,0.04406553,-0.02752387,-0.02695225,0.04312547,0.01526742,-0.06619103,-0.04367555,-0.02836157,-0.01383692,0.05666718,-0.00279907,-0.06002773,0.03272641,0.05703745,0.0711419,-0.03681645,0.13858536,-0.08369876,0.01882283,0.03140077,0.24595849,-0.02839437,0.0248032,0.03338907,-0.00161242,0.08285321,0.04719916,0.039845,-0.00096821,0.01098598,0.08782037,0.00970452,-0.05911056,0.02299114,0.02731519,0.00303376,0.02667828,-0.01425669,-0.01690483,0.03462252,-0.04126306,-0.02050686,0.03911164,-0.08458479,-0.00162628,-0.10234902,0.02965747,0.01374609,-0.03379931,0.00113098,0.0059252,0.04100915,0.0024352,0.01077446,-0.02773124,0.01825849,-0.07816907,-0.06188337,-0.03181732,-0.02390239,0.04127769,0.01078368,0.00826025],"last_embed":{"hash":"6nuiwp","tokens":426}}},"text":null,"length":0,"last_read":{"hash":"6nuiwp","at":1751288790571},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Usage","lines":[44,120],"size":1716,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"6nuiwp","at":1751288790571}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Usage#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05697642,-0.03720763,0.03346054,-0.05315179,0.03927414,-0.01998222,-0.05480208,0.00375514,0.00289834,0.00953805,-0.0406275,-0.04892868,0.04324214,0.0158988,0.06488863,-0.0028756,-0.0467263,0.03490661,-0.04419705,-0.03077768,0.02762014,0.00409954,0.02505944,-0.04973899,0.00684093,0.08475393,0.01783925,-0.01874802,0.01224894,-0.1671063,-0.0132569,-0.02045008,0.04197372,0.03223763,-0.01862192,-0.02748968,-0.01781123,-0.00562765,-0.03441653,0.02432849,0.01942693,0.0099986,-0.06466974,-0.01729117,0.01256312,-0.03398342,-0.03626553,0.00420029,-0.02576863,0.00008397,-0.00383812,-0.05993363,0.0031175,-0.01752811,0.03257714,0.05407981,0.0306959,0.0523768,0.07406957,0.09552781,0.02175887,0.00148145,-0.20531382,0.07145321,0.00866573,0.04221009,-0.06242733,-0.01943558,0.02340071,0.04511217,0.01143652,0.01948555,-0.00103892,0.06863726,-0.00276665,-0.02450167,0.0072974,-0.03729082,-0.03036073,-0.04096339,-0.0677342,0.01320225,-0.02157619,-0.01228131,0.04063972,0.03007896,0.03964179,0.00031635,0.05408788,0.03455805,-0.04725493,-0.1162819,-0.00752261,0.0635354,-0.07053205,-0.04736324,0.06475876,0.04055529,-0.07359494,0.12734647,-0.05300717,0.03697203,-0.01120835,-0.02043733,0.02807137,0.00869788,-0.01438717,-0.07213039,-0.00856786,0.00302718,-0.04281382,-0.00028007,-0.02772728,-0.09079456,-0.02036236,0.04252877,-0.02039351,-0.01735713,-0.04427402,0.02574982,0.03193592,0.06924583,0.00655877,-0.01584717,-0.00854066,0.03808815,0.03155858,0.02057273,-0.02560847,0.0668853,-0.02919108,0.06198823,-0.05756228,0.00159292,-0.03956468,0.06322981,-0.00168429,-0.01068114,-0.01445814,0.01974564,0.01243899,-0.04365432,0.05591033,-0.09387539,-0.00140188,0.00405438,-0.03326591,0.06485189,-0.03606331,-0.07634653,-0.00201112,0.05556045,-0.05629406,-0.01401872,-0.02527289,-0.00176571,0.06793271,0.08779562,-0.0774778,-0.01005891,-0.01303299,-0.04045324,-0.01419239,0.11316579,0.02962407,-0.09410545,-0.02410323,0.03667076,0.03696563,-0.0280905,-0.00406381,0.03112585,-0.05656564,0.01157234,0.04465238,-0.0105116,-0.05800868,0.00288326,0.01779528,0.06258413,-0.00807545,-0.0505151,-0.02750657,0.04129854,0.06071665,-0.02509435,0.0219043,-0.05656303,-0.01446923,0.02893807,-0.05526234,0.07457688,-0.02941343,-0.01246991,-0.04631546,0.00631775,-0.05194476,-0.03443427,0.0750792,-0.03105172,0.12159118,0.07801149,0.02436445,0.03507095,-0.06865927,0.0201609,-0.00940942,0.00198945,-0.00536585,-0.03529559,-0.10137378,0.01891946,0.09683014,0.05195585,0.01384386,-0.0473142,0.00892614,0.04332521,0.05200294,0.05621162,-0.05884761,0.04107028,-0.04573195,-0.25221187,0.02049661,0.02784708,-0.0160007,0.00232772,-0.03972816,0.02384002,-0.05164514,-0.06207717,0.05138137,0.15167186,0.01210858,-0.01679856,0.02684378,-0.01829069,0.05288199,-0.02610845,-0.04652731,-0.00957558,0.05264613,0.04765939,-0.01520055,-0.01073865,-0.05911048,0.01056297,-0.01670838,0.15730599,0.03040522,0.03135925,-0.01747173,0.0214585,-0.00070093,0.00198531,-0.09737919,-0.01395487,0.01838156,0.02065722,0.02436865,0.03582115,0.01965233,-0.04363877,0.01299549,0.04481021,-0.11266027,0.02798378,-0.05727189,-0.0485901,-0.03131587,-0.02121587,0.03230128,0.04515998,0.03009199,0.07874341,0.06572984,0.04215257,0.01010944,-0.03870535,-0.01198815,0.02626558,-0.01769581,0.02018769,0.03379478,0.01114104,-0.0699923,0.03667567,0.03944156,0.00845672,0.01716588,0.06571747,-0.02464998,-0.0303304,0.07373478,0.03109777,-0.00334664,-0.00281794,0.01278442,-0.0789792,-0.00689572,0.05857418,0.03304178,-0.04996669,-0.00186831,0.0374532,-0.02975002,-0.00946018,-0.00764887,-0.02545821,-0.03857736,0.03277701,-0.05438035,-0.0596723,-0.01360738,-0.01248331,0.01083564,0.04120231,0.02916926,-0.24785371,0.01659315,0.04483188,-0.02645879,-0.02608919,0.04233471,0.01644348,-0.06505505,-0.042723,-0.02622622,-0.01435311,0.05613749,-0.00430418,-0.06165883,0.03404061,0.05627366,0.07289274,-0.0380341,0.13689028,-0.08311188,0.01966673,0.03191519,0.24493048,-0.02835743,0.02456454,0.03347746,-0.002474,0.0825884,0.0447548,0.03840189,-0.00212623,0.01038293,0.08937708,0.00898916,-0.05768412,0.02404677,0.02899826,0.00259496,0.0255345,-0.01530315,-0.0161981,0.03531963,-0.04318046,-0.02020789,0.0417271,-0.08412429,-0.00151665,-0.10212118,0.03203325,0.01408492,-0.0333412,0.00104104,0.0060474,0.04040646,0.0032488,0.00982157,-0.02802208,0.02030191,-0.077897,-0.06208341,-0.03204631,-0.02014586,0.04082847,0.00909509,0.00989511],"last_embed":{"hash":"mhe20c","tokens":426}}},"text":null,"length":0,"last_read":{"hash":"mhe20c","at":1751288790805},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Usage#{1}","lines":[46,120],"size":1706,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"mhe20c","at":1751288790805}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05173213,-0.03479302,0.01373433,-0.03965491,0.00958238,-0.01506978,-0.08013579,-0.03467334,-0.01844065,-0.04123458,0.01505227,-0.0019495,0.04132086,-0.00340982,0.03816295,0.02068031,-0.03686877,0.09436832,-0.05245589,0.01066181,0.0076011,-0.02766306,0.04119322,-0.02334787,-0.00474285,0.08027571,-0.00996881,-0.03455105,-0.02383798,-0.20294322,-0.00591242,-0.01677872,0.02913157,0.02557795,0.00461663,-0.03448385,0.02540043,0.01699159,-0.00815847,-0.01101831,0.01217699,0.02929291,-0.04833638,-0.04081867,-0.00108802,-0.0091506,-0.03045172,0.03251725,-0.01622495,-0.0398298,-0.02193724,-0.04811655,-0.01666151,-0.03751744,0.01502039,0.08837097,0.05386068,0.05425052,0.01724189,0.12131669,0.0371807,0.00161337,-0.19354989,0.10642457,0.00454196,0.03949839,-0.05065363,-0.03755255,0.0024149,0.01298235,-0.00244792,0.02304606,-0.00862459,0.10682088,0.02183172,-0.06573211,0.05572449,-0.01077933,-0.05000246,-0.03262253,-0.04885121,0.00628711,-0.0550167,-0.01560605,0.04074685,0.02451122,0.03844376,-0.00219403,0.07452656,0.02087219,-0.0587238,-0.10235082,-0.01944793,0.043288,-0.08186574,-0.03754189,0.03005295,0.06919216,-0.03895238,0.1079047,-0.05581271,0.0477847,-0.0052288,-0.01800577,0.00248968,0.00197775,-0.00443815,-0.0605691,0.00363991,0.01388487,-0.01647001,-0.01766584,0.01571316,-0.08723661,-0.04115077,0.05429013,0.00710084,-0.00804451,-0.05668878,0.02878027,0.04482587,0.06951125,0.02689222,-0.06622245,0.00630392,0.01542141,0.04734067,0.02548028,0.02634053,0.06915397,-0.0210522,0.06007674,-0.03153245,-0.00773613,-0.039669,0.0624264,-0.0382591,-0.00652949,-0.00895226,-0.03715279,0.01166664,-0.08917177,0.01436149,-0.05398195,-0.04222878,0.04901174,-0.01451209,0.05877716,-0.05357336,-0.05071934,-0.0354912,0.0279591,-0.03181885,-0.03646512,-0.05772662,0.02431748,0.06144605,0.02629057,-0.09260873,-0.03129508,-0.01667601,-0.04841102,-0.04544521,0.11491387,0.01207409,-0.09816594,-0.03222035,0.06623072,0.05126544,-0.03027216,-0.00838573,-0.00103028,0.01168368,-0.06731844,0.06551629,-0.02257154,-0.10779206,0.00281133,0.01053749,0.06612927,-0.02857994,-0.05182597,0.01008414,0.04948547,0.04227022,-0.03696217,-0.01142917,0.00110954,0.01014689,0.02109662,-0.05796262,-0.0044121,0.01041661,-0.05845436,-0.02366719,-0.04805357,-0.0413433,0.00117843,0.07727817,-0.00407506,0.11153872,0.04391562,0.01445468,0.06276917,-0.05945166,-0.01260331,-0.03772082,-0.01000987,0.00881433,0.00615192,-0.07817528,0.0220534,0.09447715,0.03249327,-0.03686422,-0.03743311,0.0018013,0.04546258,0.04285764,0.09178389,-0.04363222,-0.00067346,-0.01216924,-0.23746172,-0.00212086,-0.00011323,-0.01854569,0.00955657,-0.04556789,0.04405842,-0.00935804,-0.06360821,0.0044286,0.12909326,0.04855198,0.00488091,0.0662569,-0.03784729,0.05911519,-0.01678486,-0.03835507,-0.01219731,0.06383704,-0.00361728,-0.0457569,-0.0287504,-0.0624592,0.02098158,-0.01812676,0.15207683,0.01526115,0.05207411,0.0129221,0.0195328,0.03326048,0.01115845,-0.11953612,-0.02337451,0.00477437,0.03004356,0.01168711,0.01734996,0.00842719,-0.02392742,-0.01944952,0.06979364,-0.07393722,-0.03218269,-0.03683832,-0.03166851,-0.0202666,0.02429226,0.01762061,0.05140267,0.01282247,0.04622242,0.05591019,0.04246045,0.02494502,0.01233591,0.02111896,0.02984739,-0.01138077,0.0099195,0.0457537,-0.00072752,-0.01326668,0.06597195,-0.00817232,0.01087657,0.01876448,0.06569272,-0.06249481,0.03746764,0.09046506,-0.0184964,-0.02041823,-0.03524074,0.01410622,-0.05337831,-0.02094871,0.04101232,0.0129667,-0.01293272,-0.01243422,0.05727515,-0.02652208,0.0021238,-0.01351535,0.02659869,-0.02114196,0.00121231,0.00842734,-0.0292795,0.00573566,-0.02729204,-0.04123369,0.03495225,0.02414411,-0.24849378,-0.0097707,0.02208221,-0.0573624,-0.03356956,0.04535542,0.04758419,-0.03673978,-0.06817903,-0.0199501,0.00775279,0.06697688,0.00686998,-0.03067103,0.00673976,0.03466479,0.08180708,-0.0228835,0.13586213,-0.09628689,0.0186014,0.03338676,0.24856621,-0.01398535,0.03353771,0.04803538,-0.01472739,0.08340074,0.05183434,0.0436285,-0.00882715,0.0230499,0.04723942,0.0019786,-0.00546428,-0.00079001,0.02139033,0.03454457,0.05893106,0.01093992,-0.02802843,0.03275131,-0.00686616,-0.06383736,0.05929611,-0.06890268,-0.03463743,-0.04455157,0.00521071,0.04727236,-0.08219017,-0.00623719,0.02853312,0.03077686,0.00436714,0.00766997,-0.03964562,0.01849385,-0.09613588,-0.03264307,0.02128967,-0.02584784,0.06033872,0.03643602,0.01347332],"last_embed":{"hash":"1kcinna","tokens":486}}},"text":null,"length":0,"last_read":{"hash":"1kcinna","at":1751288791003},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API","lines":[121,156],"size":1577,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1kcinna","at":1751288791003}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05378376,-0.03642768,0.01282047,-0.03743192,0.00972597,-0.01389519,-0.08034671,-0.03517176,-0.01523226,-0.04312165,0.01611003,-0.00102689,0.04185436,-0.00425047,0.03451738,0.01748815,-0.0375332,0.09090725,-0.05173505,0.00900764,0.00920632,-0.02764399,0.04096121,-0.02367685,-0.00522339,0.07894584,-0.01145405,-0.0354618,-0.02297586,-0.2031066,-0.00577667,-0.02094153,0.02662378,0.02558807,0.00402181,-0.03069541,0.02444074,0.01781703,-0.00693185,-0.01246096,0.01296563,0.03333927,-0.04729244,-0.04223388,-0.00422862,-0.00854676,-0.02893663,0.03167426,-0.01648402,-0.04137021,-0.02199456,-0.04963407,-0.01670282,-0.038119,0.01414028,0.09043556,0.05183154,0.05618921,0.01488554,0.12014066,0.0349707,0.00137627,-0.19344619,0.10588007,0.00553495,0.03970121,-0.04957194,-0.03885586,0.00164181,0.013544,-0.00146601,0.02139197,-0.00818068,0.10820602,0.02210149,-0.06706123,0.05341395,-0.01225885,-0.04919101,-0.03308682,-0.04692297,0.0082789,-0.05509477,-0.01591937,0.03879787,0.0240704,0.03787491,-0.00343539,0.07225189,0.02124285,-0.05928616,-0.10177125,-0.0236997,0.04380943,-0.07928107,-0.03716184,0.02620042,0.06738468,-0.03793015,0.10786774,-0.05691042,0.05003301,-0.00574627,-0.01718692,0.00345404,0.00144423,-0.00318997,-0.05873163,0.00341949,0.01524319,-0.01563076,-0.02055791,0.01804464,-0.08929875,-0.04282576,0.05459446,0.0066502,-0.00703683,-0.05825162,0.02889234,0.04319351,0.06917313,0.03024095,-0.06739423,0.00451212,0.01782489,0.05011784,0.02482822,0.02909635,0.06738374,-0.0200113,0.05835007,-0.03259143,-0.01056533,-0.03851862,0.06408464,-0.03690886,-0.00784718,-0.00954875,-0.03876959,0.01614143,-0.09014639,0.0165207,-0.05348654,-0.04389669,0.04968887,-0.01337313,0.06048097,-0.05600648,-0.05143232,-0.0396199,0.02647269,-0.0310865,-0.03645299,-0.06020988,0.02231592,0.06244102,0.02483166,-0.0956972,-0.03165615,-0.01673934,-0.04244038,-0.04501423,0.11159637,0.01011164,-0.093467,-0.03265763,0.06531125,0.05121128,-0.02972688,-0.00986285,-0.00131254,0.01370362,-0.06904306,0.06243807,-0.02362867,-0.10765065,0.00375726,0.00842413,0.06662703,-0.03038094,-0.04984833,0.01166069,0.04687415,0.04207231,-0.03598606,-0.01412352,0.0024137,0.01124048,0.02197525,-0.05865802,-0.006162,0.00996849,-0.05712055,-0.02101921,-0.0477383,-0.04044395,0.00187365,0.07744094,-0.00469859,0.11244036,0.04590225,0.01234811,0.06442652,-0.06168893,-0.01162977,-0.03722282,-0.01067228,0.00977468,0.0063392,-0.07768782,0.01951841,0.09274215,0.0298089,-0.03651023,-0.03566944,-0.00215215,0.04562278,0.04253753,0.08935671,-0.04134968,-0.00183449,-0.00911426,-0.23413284,-0.00060106,-0.00272081,-0.02001647,0.008163,-0.04588328,0.04517223,-0.00798132,-0.06575573,0.00330125,0.12924851,0.04714462,0.00601298,0.06409546,-0.03827202,0.05847383,-0.01412064,-0.0375877,-0.00752402,0.06414735,-0.00617132,-0.04710383,-0.03089161,-0.0611328,0.01842644,-0.02036566,0.15386884,0.01648078,0.05441424,0.01377752,0.01914298,0.03471882,0.00974865,-0.11959508,-0.02062228,0.00379986,0.02732634,0.01152044,0.01708236,0.00727935,-0.02466206,-0.01904257,0.06866039,-0.07373327,-0.03251099,-0.03834944,-0.02978889,-0.01933478,0.02325703,0.01820804,0.05170288,0.01400169,0.04562247,0.05443729,0.04335654,0.02251566,0.01131698,0.02011806,0.03088819,-0.01272897,0.00794404,0.0422482,-0.00250456,-0.01234247,0.06719375,-0.00845718,0.01220513,0.02070747,0.06568829,-0.06242513,0.03880161,0.09181283,-0.01905175,-0.02266485,-0.03496991,0.01572406,-0.04970065,-0.02213129,0.03974041,0.01180346,-0.01029393,-0.01303797,0.06114918,-0.02463207,0.00250735,-0.01356693,0.02927824,-0.01808906,0.00246343,0.00972392,-0.03062277,0.00571538,-0.02530229,-0.04344209,0.03352517,0.02729436,-0.24910453,-0.00784538,0.02427874,-0.05857161,-0.0358488,0.04751348,0.04562999,-0.03714257,-0.06796131,-0.02261399,0.00813681,0.06548055,0.00800592,-0.03052585,0.00554205,0.03592667,0.084813,-0.02416438,0.13498218,-0.09620153,0.01939875,0.03588738,0.24996465,-0.01422858,0.03488307,0.04584995,-0.01351869,0.0858148,0.0521681,0.04418525,-0.00655857,0.02433619,0.04677967,0.00089171,-0.00406162,0.00274365,0.02129671,0.03613688,0.06042971,0.01243016,-0.02660391,0.03336804,-0.00784552,-0.06362146,0.06125066,-0.06836275,-0.03527368,-0.04471602,0.00355877,0.04795059,-0.08446498,-0.00774282,0.03045914,0.03348584,0.00482024,0.005992,-0.03968715,0.01793875,-0.09330566,-0.02991185,0.02239858,-0.02564606,0.06318557,0.04062686,0.01126616],"last_embed":{"hash":"1r0pk6v","tokens":485}}},"text":null,"length":0,"last_read":{"hash":"1r0pk6v","at":1751288791225},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)","lines":[123,156],"size":1569,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1r0pk6v","at":1751288791225}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07704234,-0.03700403,0.00658961,-0.0114064,0.00208779,-0.01884261,-0.06417602,-0.01166286,0.00374728,-0.03277036,0.02136428,-0.008355,0.0462785,0.00533721,0.03484759,0.01063645,-0.00501272,0.04235784,-0.03892488,0.00533226,0.01025306,-0.02130177,0.02469609,-0.0159438,-0.00202342,0.06786352,-0.01768696,-0.03693115,-0.00087367,-0.18971211,-0.00841075,-0.04424149,-0.00128954,0.02613666,-0.0112107,-0.01424011,0.01747866,0.00497266,-0.00169547,0.03791983,0.00301134,0.0259889,-0.07122561,-0.04577414,0.011978,-0.03524862,-0.01843485,0.02908717,-0.00376333,-0.02808035,-0.00843691,-0.05347792,-0.01545519,-0.02354629,0.02650682,0.10574482,0.01953461,0.08256436,0.04565429,0.102368,0.0270882,-0.00442507,-0.20153035,0.09001694,0.01199047,0.04232088,-0.05119435,-0.02209667,0.02849971,0.02157436,0.04198114,0.03867519,0.00086426,0.08384898,0.01546306,-0.04291331,0.05403901,0.01932846,-0.03317081,-0.01669817,-0.0554755,-0.00737692,-0.04382363,-0.01307185,0.02903509,0.00245103,0.03246209,0.00217471,0.08437756,0.01861716,-0.0636164,-0.0652466,-0.03384594,0.05745083,-0.08676734,-0.02927808,0.02481316,0.04976159,-0.0380061,0.11179198,-0.04684401,0.06291775,-0.01239762,0.00308148,0.02799669,-0.01337006,-0.01583111,-0.07489489,0.00494394,0.01603922,-0.02251798,-0.00237334,0.03049266,-0.11799472,-0.03185809,0.06497483,-0.00639985,-0.00653573,-0.05494037,0.04344847,0.01958693,0.09105861,0.03624554,-0.05468331,-0.01143589,0.01180179,0.04297,0.02802888,0.01493999,0.0598038,-0.02055752,0.08207693,-0.01895424,-0.01648935,-0.02048477,0.05188592,-0.0216835,-0.01298039,-0.00105045,-0.04433426,0.01989446,-0.06164821,0.04054664,-0.0563497,-0.02700136,0.0303731,0.00485326,0.06531478,-0.05629906,-0.06507522,-0.0383858,0.014411,-0.05150704,-0.01554508,-0.03178884,-0.00055256,0.06606783,0.03314859,-0.10421451,-0.00420248,-0.00622115,-0.03207763,-0.02359061,0.09914128,0.00259012,-0.08153271,-0.01753892,0.0496722,0.0344792,-0.01678307,-0.05292803,0.00902829,-0.00182346,-0.06143121,0.06608285,-0.02206914,-0.09186636,-0.02805371,0.02475772,0.06623136,-0.0375065,-0.05680186,0.01948827,0.0644729,0.03974496,-0.01609141,0.00830691,-0.00834288,0.01678812,0.01220971,-0.04945866,-0.03423897,-0.0105063,-0.03008159,-0.00939739,-0.03420312,-0.0604501,-0.02009957,0.08143892,0.00219614,0.09773719,0.03922404,0.00987913,0.04509321,-0.06484617,0.00545123,-0.04392815,-0.00039714,-0.00967082,-0.00342491,-0.08496311,0.02841602,0.10265451,0.03725991,-0.03155009,-0.01730075,0.01364528,0.08696219,-0.00053947,0.05400406,-0.03745289,0.03871028,0.00329636,-0.23587331,0.00261813,0.00826276,-0.02695335,0.03639231,-0.04502025,0.02365093,-0.02047331,-0.06332392,0.00042421,0.14774463,0.05553056,-0.0048589,0.03135071,-0.03980999,0.06254309,-0.02475412,-0.02683842,0.0083805,0.03490593,0.00257175,-0.03699661,-0.06184037,-0.05273687,0.02505226,-0.03934617,0.15427825,0.02734605,0.03255254,0.00793907,0.02307489,0.02086091,0.00602271,-0.13602093,-0.01397359,0.02199308,0.03366687,0.03462053,0.0045202,0.01286409,-0.01315673,-0.01058009,0.06022589,-0.10410935,-0.01072209,-0.0524726,-0.03121999,-0.01955742,-0.00996067,0.0258726,0.04878135,0.00806862,0.04593283,0.04070028,0.04143899,0.02860874,0.00519793,0.01467223,0.03854188,-0.01345576,0.00530415,0.03927455,-0.02843995,-0.02560901,0.07646679,0.0215214,0.02166941,0.00354604,0.07550973,-0.04486951,0.01621101,0.10185359,0.00168807,-0.01082478,-0.01980446,0.0055421,-0.04219535,-0.05708242,0.05627619,0.03502832,-0.00959352,0.00436598,0.05576929,-0.05781143,-0.00478525,-0.02389716,0.00995076,-0.00474893,-0.01793857,0.00283026,-0.03027282,0.00835258,-0.01116551,-0.05480323,0.05301886,0.04004079,-0.23922698,-0.01243829,0.02456312,-0.06326499,-0.06727972,0.03632031,0.02827165,-0.06971017,-0.112083,-0.02393948,-0.01608255,0.06222032,-0.00528848,-0.02848693,-0.01153437,0.04697223,0.08266728,-0.06076762,0.12760584,-0.10004818,0.01591484,0.05102862,0.239608,-0.01599957,0.02006141,0.05111651,-0.01411426,0.08362719,0.06921906,0.03993982,-0.01515916,0.01123622,0.02585295,0.02673436,-0.03757022,0.00323783,0.01729249,0.05103735,0.05316493,0.01906524,0.00419251,0.05379963,0.01765819,-0.05596261,0.05980703,-0.06880449,-0.05724701,-0.05747921,0.00187136,0.03268754,-0.10486097,-0.03668097,0.02555516,0.043905,0.0048439,0.02313329,-0.04472597,0.01032719,-0.09214053,-0.02061622,0.02271942,-0.0334444,0.04198654,0.01456229,-0.00702694],"last_embed":{"hash":"c8vlze","tokens":304}}},"text":null,"length":0,"last_read":{"hash":"c8vlze","at":1751288791462},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#{1}","lines":[124,130],"size":384,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"c8vlze","at":1751288791462}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#input": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06747635,-0.05991389,0.04379179,-0.07723124,0.01442989,-0.01958896,-0.06231385,-0.01819077,-0.02025122,0.00631494,-0.02057071,0.00055516,0.03516493,0.03442907,0.04862819,-0.00297868,-0.01074246,0.04684441,-0.03625945,-0.0094262,0.03165994,-0.0016222,0.04194931,-0.05975106,-0.00852871,0.08493008,0.00587261,-0.0144739,-0.01260103,-0.17938757,-0.01826308,-0.02659971,0.07794038,0.00689364,-0.00369602,-0.02101283,0.00238439,0.00563388,-0.03431924,-0.02193537,0.01190234,0.03016663,-0.07251199,-0.01092544,0.01415568,-0.04208254,-0.00662562,0.02268902,-0.02421332,-0.02696375,-0.00648459,-0.05417908,0.00456088,-0.03014824,0.02260536,0.03279643,0.04683361,0.04349256,0.0656429,0.08432681,0.02531164,0.02968737,-0.20890334,0.07575374,0.01863407,0.03608952,-0.05768559,-0.01038633,-0.00115075,0.03903102,0.01656058,0.01231065,-0.03634507,0.07413903,0.03222963,-0.0473482,0.03122314,-0.02438226,-0.03199297,-0.02180584,-0.04031993,0.03101272,-0.04383933,0.01121204,0.02500466,0.03363868,0.04406908,0.001955,0.05516298,0.02829013,-0.04766692,-0.13386856,-0.03031962,0.06136536,-0.07139948,-0.0460507,0.06232273,0.03905571,-0.06431215,0.13419342,-0.05547637,0.03420116,-0.01879208,-0.02878128,0.02943737,0.01738713,0.01218296,-0.06683926,0.00103934,-0.00762354,-0.03670014,0.00643386,0.00310285,-0.08961064,-0.02588138,0.07299072,-0.01774152,0.01582192,-0.05705076,0.04669337,0.02719495,0.06601553,0.00537766,-0.05581016,-0.00563207,0.00980586,0.04055972,0.03501834,-0.00169864,0.06214624,-0.01252182,0.06029392,-0.06304511,0.00373877,-0.03623023,0.05520668,-0.01854553,-0.00185931,-0.03342598,-0.04179387,0.01982832,-0.05440164,0.0278075,-0.0910431,-0.00365621,0.00535009,-0.05420497,0.02470746,-0.04151309,-0.07633242,-0.02516729,0.04611186,-0.05739686,-0.01111204,-0.02933517,0.00925693,0.07118691,0.0470389,-0.1118509,-0.02416123,-0.00966311,-0.03354875,-0.01780098,0.12701647,0.02357514,-0.08678385,-0.04618586,0.03904181,0.05715252,-0.0140678,-0.01384431,0.05421874,-0.02671555,-0.02262193,0.03904807,-0.03328376,-0.09959804,-0.01635151,0.00694534,0.05665144,-0.00831251,-0.05490575,0.01777984,0.04991868,0.05017837,-0.02479083,0.0125037,-0.06745725,-0.02313353,0.04987776,-0.05677482,0.05060919,-0.02750018,-0.04672607,-0.03338933,-0.00761302,-0.05023127,-0.01246204,0.07886285,-0.02435967,0.08983673,0.03749545,0.01670645,0.08027045,-0.07410789,-0.00830592,-0.01513992,-0.00179105,-0.00675374,-0.01779224,-0.08523878,0.01734431,0.11641071,0.0200161,0.02013852,-0.05125751,0.007175,0.04496045,0.02908221,0.08027261,-0.04386671,0.01039125,-0.01121721,-0.22178759,-0.00875998,0.02106331,-0.02590034,0.01138,-0.03306367,0.06012651,-0.03700246,-0.06733581,0.01969419,0.15791671,0.03652902,-0.02098205,0.05039138,-0.01280629,0.07037245,-0.00807569,-0.04876237,-0.00022714,0.05805904,0.03179635,-0.04012484,-0.04353695,-0.05642335,0.01780415,-0.00183946,0.15031484,0.03293283,0.06282313,0.01304703,0.01595528,0.00728412,-0.01086721,-0.1084892,0.01478948,-0.01143307,0.0159947,0.01578753,0.00924915,0.0141444,-0.03912686,-0.01464096,0.05962584,-0.08298068,0.00073767,-0.04350305,-0.0606698,-0.00915104,0.03321952,0.0244002,0.03148177,0.01538382,0.05568388,0.06986886,0.05488924,0.00486395,-0.02656235,0.01415351,0.03518092,-0.024274,0.02317677,0.01425161,0.00346845,-0.04376482,0.05931038,0.0200845,-0.00131546,0.00700195,0.08817314,-0.03403635,0.00697463,0.07417543,0.04045364,-0.02033848,-0.00621339,0.00868824,-0.08401096,0.00604451,0.05922209,0.01926278,-0.03723284,-0.01627053,0.04460846,-0.02678015,-0.00794286,-0.01727665,-0.00318231,-0.04000071,0.00470673,-0.02123068,-0.02665761,-0.00523375,-0.04647996,0.00966665,0.02612739,0.01254367,-0.23877849,0.0109051,0.05984326,-0.04211815,-0.02598491,0.05358773,0.04812077,-0.04248607,-0.05571555,-0.02547363,0.00435743,0.05181709,0.00786041,-0.04692942,0.01004208,0.03280708,0.05980914,-0.03085942,0.15641537,-0.08526947,0.02565824,0.04239515,0.24425869,-0.01050568,0.03790123,-0.00017117,-0.02915522,0.08352971,0.05454133,0.04547482,0.00239003,0.00889884,0.08479103,0.00046863,-0.01546293,0.02570772,0.04020926,0.04962147,0.02403282,-0.00343155,-0.03382287,0.05193483,-0.02365565,-0.06730827,0.023347,-0.07315317,-0.0243097,-0.06185566,0.01400578,0.03430828,-0.04213842,0.01414689,0.01204548,0.0373344,-0.01350077,0.01194624,-0.02039151,-0.0113896,-0.09040545,-0.05381475,-0.00743027,-0.03782484,0.06339443,0.01985558,0.00629383],"last_embed":{"hash":"4zjndw","tokens":269}}},"text":null,"length":0,"last_read":{"hash":"4zjndw","at":1751288791571},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#input","lines":[131,139],"size":298,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"4zjndw","at":1751288791571}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#input#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06964426,-0.05843564,0.04324058,-0.07538402,0.01279215,-0.01806892,-0.06150485,-0.02381448,-0.01879023,0.00661755,-0.01881813,0.00220578,0.03388268,0.03555129,0.04717324,-0.002842,-0.00732542,0.04661826,-0.03868208,-0.00765688,0.0324649,-0.00046696,0.04334614,-0.06288757,-0.00882711,0.08447858,0.00673664,-0.01401653,-0.01247855,-0.17894091,-0.01704012,-0.03106853,0.07430787,0.00836675,-0.00677425,-0.02190462,0.0037352,0.00758479,-0.03659056,-0.02231232,0.01159992,0.03046212,-0.07082142,-0.01038802,0.01318068,-0.04370705,-0.00568073,0.02495044,-0.0237303,-0.02705033,-0.00911986,-0.05258111,0.00455059,-0.03276097,0.02255442,0.03323382,0.04465621,0.04437673,0.06155955,0.08273086,0.02751463,0.03123226,-0.20800681,0.07677991,0.02020762,0.03378996,-0.05602567,-0.0109688,-0.00165043,0.03948459,0.01543668,0.01121687,-0.03988041,0.07439338,0.0324974,-0.05066589,0.03032167,-0.02288657,-0.03182927,-0.01913366,-0.03986954,0.03135319,-0.04558057,0.0123507,0.02208999,0.03542181,0.0431563,0.00311652,0.05102475,0.025047,-0.04541566,-0.13406138,-0.03197299,0.06334276,-0.07059995,-0.04798284,0.05948522,0.03871928,-0.06290732,0.13291079,-0.05886452,0.03391347,-0.01616819,-0.02671775,0.03305421,0.02071661,0.01559004,-0.06363486,0.00412551,-0.00765945,-0.03434179,0.00249777,0.00134044,-0.08791001,-0.02451195,0.07267446,-0.01919298,0.01867496,-0.05753434,0.04756823,0.02733097,0.06268754,0.00320575,-0.05823918,-0.00791276,0.00944672,0.04186065,0.03494682,0.00039319,0.06262706,-0.00793661,0.05992452,-0.06551696,0.00331056,-0.03915827,0.05513586,-0.01679193,-0.00171871,-0.03537486,-0.04385194,0.01960346,-0.054874,0.02719572,-0.08996599,-0.00564995,0.005732,-0.05878101,0.02206819,-0.04020003,-0.07616387,-0.02499121,0.04290091,-0.05885106,-0.01518645,-0.02919179,0.00719568,0.06939554,0.04507812,-0.11192717,-0.02598065,-0.01477168,-0.03316253,-0.02086815,0.12593071,0.02453537,-0.08589917,-0.04248239,0.03938191,0.05729133,-0.01275952,-0.01089458,0.05278328,-0.02625075,-0.02272014,0.04193255,-0.03360375,-0.09938274,-0.01968933,0.00721757,0.0567364,-0.00811349,-0.05477215,0.01580395,0.04976637,0.05261153,-0.02238503,0.01004859,-0.06610723,-0.02004811,0.04963321,-0.05540118,0.05052634,-0.02877854,-0.04907837,-0.03378779,-0.00724189,-0.05201416,-0.01043015,0.07763901,-0.02383981,0.08935943,0.03725958,0.01658739,0.08229928,-0.07327932,-0.01200413,-0.01407519,0.00027146,-0.00704621,-0.02099334,-0.08549033,0.01506049,0.11626662,0.01851509,0.01999867,-0.05285837,0.00554897,0.04251379,0.02980444,0.08207247,-0.04231795,0.00718616,-0.01142368,-0.22230995,-0.00808915,0.02204997,-0.02779412,0.01154822,-0.03550596,0.06036138,-0.03344626,-0.06781814,0.01617203,0.15837313,0.03632418,-0.02045103,0.05008195,-0.00967787,0.07110973,-0.00756574,-0.04975026,0.00060036,0.06054414,0.03390862,-0.04261517,-0.04572579,-0.05314542,0.01665566,0.00104141,0.15067567,0.03547375,0.06366708,0.0135386,0.0152542,0.00697848,-0.01013021,-0.10752463,0.01458444,-0.01429074,0.01710557,0.01440173,0.0074321,0.01608546,-0.03708559,-0.01362219,0.05850914,-0.08065712,0.00024734,-0.04611999,-0.06022707,-0.01054927,0.0361276,0.02226448,0.03223771,0.01462879,0.05397284,0.07053089,0.05727186,0.00313353,-0.02765384,0.01502644,0.03628632,-0.02595573,0.02156063,0.01275444,0.00556815,-0.03809854,0.05707441,0.02117695,-0.00166907,0.00883566,0.08741357,-0.03396016,0.0059483,0.07664496,0.03919234,-0.01766349,-0.00637068,0.00741662,-0.0844641,0.00572007,0.05800889,0.01740849,-0.03516987,-0.01600911,0.04484572,-0.02825298,-0.0060526,-0.01796035,-0.00183382,-0.04046512,0.0068536,-0.0201256,-0.02536899,-0.00536625,-0.04830146,0.00768524,0.02970257,0.01249447,-0.24032043,0.011007,0.06201262,-0.04132063,-0.02607224,0.05562855,0.05204147,-0.04264832,-0.05685956,-0.02104492,0.00364977,0.05246726,0.00637239,-0.04427722,0.00859139,0.0329247,0.06186965,-0.02688003,0.15569074,-0.08593359,0.0236606,0.04065796,0.24197938,-0.01008598,0.04053346,-0.0023397,-0.02985134,0.08233824,0.05549455,0.04414982,0.00351057,0.0082213,0.08686477,-0.00111028,-0.01091328,0.02725266,0.04519703,0.05449787,0.02400121,-0.00445385,-0.03321713,0.05462449,-0.02276961,-0.06879789,0.02413262,-0.07213254,-0.0245435,-0.0606411,0.01177921,0.03494501,-0.04461846,0.01528299,0.01177885,0.0386958,-0.01419902,0.01263472,-0.01987352,-0.01382041,-0.09102811,-0.05342004,-0.00443778,-0.03601362,0.06423424,0.02079628,0.00682506],"last_embed":{"hash":"1ijmn43","tokens":266}}},"text":null,"length":0,"last_read":{"hash":"1ijmn43","at":1751288791684},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#input#{1}","lines":[132,139],"size":287,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1ijmn43","at":1751288791684}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#callback": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0481184,-0.02343576,0.01036833,-0.04146745,0.00903778,-0.01351878,-0.05705762,-0.03550126,-0.02780768,-0.03643234,0.00224577,0.01864179,0.0335534,-0.00609197,0.03531656,0.02718853,-0.03352013,0.11320966,-0.06554346,0.00830229,0.01053096,-0.02257391,0.04870206,-0.02322591,0.00530207,0.09072459,0.01007956,-0.02695484,-0.03499863,-0.20703451,-0.00325096,-0.02324944,0.02111524,0.01937318,0.00881963,-0.03443868,0.00925658,0.02968599,-0.02371539,-0.00096815,0.01409865,0.0312015,-0.03348837,-0.03118031,-0.00749886,0.0002251,-0.04594538,0.00727984,-0.01591187,-0.03421622,-0.03630399,-0.04365606,-0.02291631,-0.04281605,-0.00029193,0.0654835,0.06202217,0.04841901,0.02666707,0.11625835,0.04099771,-0.01246013,-0.17559136,0.10326906,-0.01779787,0.02748288,-0.04645269,-0.03311188,-0.00456719,0.04003163,-0.01294687,0.00837967,-0.03161954,0.11170965,0.02619722,-0.06883058,0.03260139,-0.02109955,-0.04451042,-0.03746673,-0.05791562,-0.00757936,-0.05040797,-0.01492337,0.04820013,0.02889739,0.05654101,-0.01467049,0.07726452,0.03483424,-0.05273718,-0.11983983,-0.00391881,0.05604211,-0.07618212,-0.03576472,0.05115139,0.06420583,-0.03652311,0.10531886,-0.05125127,0.03147594,-0.01918432,-0.01222168,-0.02143333,0.00451733,-0.00396291,-0.06157159,0.00643887,0.01602538,-0.01029106,0.00581658,0.03256638,-0.06357195,-0.02764306,0.06511435,0.01891207,-0.0299794,-0.06883935,0.02181596,0.05636615,0.03982858,0.01298274,-0.07458474,0.01582897,0.01407071,0.05157338,0.04534746,0.00987874,0.07287678,-0.0039902,0.05491268,-0.04643964,0.00540233,-0.04198577,0.04242664,-0.03066933,0.00679938,-0.01384209,-0.02509384,0.00066172,-0.09403429,-0.01197819,-0.06349929,-0.02712804,0.05661046,-0.03935443,0.0565961,-0.04547051,-0.04439529,-0.02456324,0.03349362,-0.01714749,-0.04111563,-0.06476188,0.02238629,0.04554525,0.03966304,-0.08284929,-0.05097382,-0.02967225,-0.04017378,-0.05157061,0.11995951,-0.00084012,-0.08583864,-0.03604299,0.07414195,0.05761901,-0.04038521,0.01164962,0.0032056,0.00144957,-0.07177574,0.07819457,-0.02222463,-0.12406903,-0.00411875,0.01745154,0.06038006,-0.01584022,-0.06357516,0.01626379,0.04113388,0.03926954,-0.05776165,-0.01830614,-0.02052672,0.01952381,0.01513155,-0.05688734,0.01512938,-0.00592132,-0.06341388,-0.04376439,-0.03544896,-0.02853273,0.01121701,0.07555749,-0.01051568,0.13692734,0.04115466,0.02759916,0.06485325,-0.05446617,-0.01719251,-0.03565743,-0.02327074,0.00527505,-0.00494988,-0.07139523,0.02179532,0.08947697,0.05323739,-0.03206779,-0.05288416,-0.00719218,0.00730058,0.05073041,0.06976242,-0.04683206,-0.01723624,-0.01837082,-0.24081886,0.00269802,0.00853233,-0.03448877,-0.00185005,-0.04240222,0.05676111,-0.01236711,-0.04133605,0.00364827,0.10053746,0.03309248,0.00688436,0.07484484,-0.03193864,0.05143022,-0.00934685,-0.03960013,-0.02078683,0.06168573,-0.00764721,-0.04436622,-0.02145119,-0.07078701,0.01347114,0.00218659,0.15351216,0.02521105,0.07054549,0.03805979,-0.00213558,0.02289526,0.01720865,-0.0905537,-0.01035275,-0.00977329,0.04578987,-0.00320418,0.0093458,0.00941283,-0.01064681,-0.01623303,0.07489643,-0.08183991,-0.01017554,-0.04852923,-0.03087564,-0.01315623,0.0335225,0.01617686,0.05625439,0.03087297,0.0561313,0.05210007,0.03977722,0.02189103,0.00244258,0.0120995,0.03268592,0.00491339,0.00414997,0.03350222,-0.00564748,-0.01273872,0.06829058,-0.00239704,0.01600551,0.02064722,0.06221346,-0.07538622,0.02520141,0.08049069,-0.00506046,-0.04083573,-0.0257123,0.01016725,-0.05398091,-0.0127596,0.03214685,0.00894982,-0.01066406,-0.00653136,0.04163747,-0.01806773,0.00885456,-0.02710446,-0.00301265,-0.00606425,0.01561995,-0.00602806,-0.03517548,0.01093665,-0.02599625,0.00159835,0.03841044,0.02289837,-0.23410997,-0.00059553,0.01200206,-0.0515634,-0.01625424,0.06558112,0.05816265,-0.03742212,-0.05186177,-0.014688,-0.00015143,0.08935779,0.00411508,-0.02996814,0.02800327,0.02809122,0.09009857,-0.00039625,0.13280661,-0.0701943,0.01841477,0.00274972,0.24832591,-0.03199394,0.04747297,0.05442487,-0.01583594,0.09581889,0.02162157,0.02097171,0.00412144,0.01892209,0.06959779,-0.02079644,-0.01165691,-0.01027199,0.01845824,0.03127431,0.05755808,-0.01851109,-0.02602981,0.02676677,-0.02661853,-0.05103505,0.06333474,-0.08662591,-0.01055845,-0.05441145,0.02706395,0.06534117,-0.07555885,0.01718855,0.01885839,0.01123093,-0.00159925,0.00619242,-0.05034063,0.02390776,-0.08428496,-0.0529037,-0.01341362,-0.03013093,0.06673506,0.04240831,0.03154281],"last_embed":{"hash":"dli5tw","tokens":418}}},"text":null,"length":0,"last_read":{"hash":"dli5tw","at":1751288791844},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#callback","lines":[140,156],"size":855,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"dli5tw","at":1751288791844}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#callback#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05062077,-0.02723398,0.01164754,-0.04138679,0.00734919,-0.01592974,-0.05082824,-0.03286462,-0.02955781,-0.0387472,0.00051776,0.01938301,0.03091012,-0.00543591,0.03098746,0.02436856,-0.02622438,0.10925362,-0.06463709,0.01096642,0.01050484,-0.02554734,0.04881862,-0.02770348,0.00430836,0.08898182,0.01283178,-0.02509671,-0.03935837,-0.20624721,-0.00321999,-0.02341843,0.02298656,0.02021777,0.00939882,-0.03486386,0.00988469,0.02791123,-0.02139562,-0.00048339,0.01204861,0.0331254,-0.0292412,-0.0310599,-0.00915389,-0.00238852,-0.04521177,0.00851249,-0.01533863,-0.03455988,-0.03589466,-0.04338427,-0.02278808,-0.04341846,-0.00132804,0.06572175,0.06367227,0.04859364,0.02637368,0.11549201,0.04415279,-0.01112262,-0.17636758,0.10466575,-0.01688361,0.02481746,-0.04835049,-0.03287328,-0.00509684,0.03988893,-0.01303108,0.0064513,-0.03659547,0.11181676,0.02915315,-0.07227807,0.0330347,-0.02358025,-0.04626684,-0.03890439,-0.06040898,-0.00728335,-0.0502269,-0.01133907,0.04786842,0.03187402,0.06024107,-0.01401281,0.07985727,0.03557462,-0.05502127,-0.11844189,-0.00171484,0.05795066,-0.07610374,-0.03640844,0.05174688,0.06449238,-0.03371482,0.10664358,-0.05549159,0.02909309,-0.0168283,-0.01435388,-0.02023116,0.00611536,-0.00295573,-0.0587505,0.00401487,0.0166052,-0.01258211,0.00634484,0.0338009,-0.06336554,-0.03042845,0.06379478,0.01636882,-0.02913498,-0.06592052,0.02244704,0.05913943,0.03608952,0.01451499,-0.07597369,0.01704207,0.01251515,0.05162552,0.04407644,0.01362612,0.07452853,-0.00342459,0.05173721,-0.04905775,0.00374094,-0.04383257,0.04249225,-0.02945254,0.00720729,-0.01482447,-0.02225409,0.00336826,-0.09272391,-0.01366073,-0.06065996,-0.02776683,0.05633608,-0.04171189,0.05216644,-0.04454493,-0.04162045,-0.02515849,0.0334335,-0.01725435,-0.04239465,-0.06288169,0.02909951,0.04759572,0.03748983,-0.08127362,-0.04569069,-0.02803932,-0.03621002,-0.05473298,0.1216481,-0.00599935,-0.08539273,-0.03560907,0.07528815,0.05840372,-0.04244391,0.00872629,0.00316723,0.00136389,-0.0716567,0.07675058,-0.02517218,-0.12634628,-0.00631475,0.01389496,0.06070129,-0.01717634,-0.06330989,0.01734656,0.03804142,0.0410302,-0.05642666,-0.01930729,-0.02302115,0.02007705,0.01770687,-0.05697629,0.0161264,-0.00626891,-0.06417525,-0.04414281,-0.03202721,-0.02652139,0.01215449,0.07482789,-0.01207909,0.13344139,0.04034782,0.02969566,0.0637436,-0.05436235,-0.01871709,-0.03139439,-0.02111032,0.00345178,-0.00829686,-0.06958233,0.02383724,0.08887124,0.05067094,-0.03050134,-0.0581173,-0.00722019,0.00566226,0.04967747,0.06684234,-0.05133079,-0.01909315,-0.02049747,-0.23708713,0.00577835,0.00563743,-0.03515713,-0.00117893,-0.04175277,0.05895733,-0.01099336,-0.04183663,0.00267048,0.09981336,0.03213015,0.00618089,0.07519621,-0.03105231,0.0526145,-0.00558117,-0.04097079,-0.02173335,0.0647638,-0.00454408,-0.04544521,-0.02174826,-0.06843425,0.01710217,0.00395317,0.15136062,0.02646788,0.07632198,0.04053223,-0.0018653,0.02435159,0.01459932,-0.09037307,-0.01478071,-0.01543133,0.04591309,-0.00626788,0.0074357,0.00849678,-0.01452017,-0.01388525,0.06906295,-0.07917508,-0.01132454,-0.0529665,-0.03153655,-0.01425314,0.03145457,0.01429134,0.05295574,0.027765,0.05555398,0.05234371,0.0419733,0.02542152,0.00596142,0.01337496,0.03356706,0.00417583,0.00313265,0.03370417,-0.00158821,-0.01337036,0.06309199,-0.00528096,0.01671678,0.02233084,0.06273606,-0.07291181,0.02666182,0.0806789,-0.00276532,-0.04446427,-0.02499189,0.00747231,-0.04987539,-0.01614261,0.03394264,0.00617336,-0.01386167,-0.00594068,0.04432465,-0.01702905,0.01284867,-0.02613757,-0.00322427,-0.00698037,0.01118485,-0.00889531,-0.03693062,0.01414058,-0.02767984,0.00267732,0.03875014,0.02544674,-0.23435192,0.00155791,0.01400797,-0.05130223,-0.01331929,0.06781085,0.05990243,-0.03752593,-0.05015257,-0.01404222,0.00304285,0.08491061,-0.00068446,-0.03318449,0.02674805,0.02667686,0.09137169,0.00454188,0.13733403,-0.06381868,0.02029063,0.00560038,0.24738574,-0.0345181,0.05362848,0.05344269,-0.01838212,0.09806002,0.02262071,0.02127091,0.00641617,0.01884789,0.07241774,-0.02426271,-0.01318067,-0.00901471,0.01670836,0.03252119,0.05614417,-0.02204064,-0.02291962,0.02454489,-0.03203422,-0.0508167,0.06111658,-0.08184467,-0.00711937,-0.05527873,0.02724497,0.06630126,-0.07477646,0.02199836,0.01989896,0.00985625,-0.00435237,0.00432828,-0.04824528,0.02356979,-0.08789842,-0.05474124,-0.01266396,-0.0319819,0.06939698,0.04552875,0.03450093],"last_embed":{"hash":"w80wdk","tokens":415}}},"text":null,"length":0,"last_read":{"hash":"w80wdk","at":1751288792034},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#API#escalade(input, callback)#callback#{1}","lines":[141,156],"size":841,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"w80wdk","at":1751288792034}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Benchmarks": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08680152,-0.03859011,0.04859717,-0.04538786,0.03421851,-0.01003135,-0.06759066,-0.00591932,0.03038049,0.02037599,0.00433024,-0.0362623,0.05246359,0.02197332,0.03838234,-0.00959838,-0.01473351,0.01822272,-0.02408896,-0.01629316,0.02417237,-0.02096217,0.02794786,-0.04968151,0.03528903,0.05464891,-0.00395978,-0.02468829,-0.00301227,-0.21587555,-0.0149858,-0.02702363,0.09478721,0.02376796,0.02833036,-0.02100959,0.0047601,0.01325783,-0.0081557,0.02742327,-0.0017346,0.02021907,-0.0781276,-0.0103066,-0.0032205,-0.01812845,-0.04307298,0.01020187,-0.012455,0.00174459,-0.01519773,-0.06503352,0.01648424,0.0072188,0.02890609,0.05072136,0.04192925,0.02950226,0.0764243,0.06125499,0.01154501,-0.00144856,-0.21306406,0.04675362,0.03284498,0.04388053,-0.05584786,-0.01941224,-0.00027974,0.05377629,0.00287892,0.04064454,0.02418629,0.05534322,0.02133288,-0.00216398,0.02230085,-0.02178805,-0.05158975,-0.06136752,-0.04206012,-0.00916162,-0.01447673,0.00639428,0.03677192,0.00716269,0.06211737,0.01254989,0.08132603,0.07819334,-0.03499223,-0.06420823,-0.00608201,0.03696088,-0.07193214,-0.00969727,0.04052545,0.07036618,-0.0613252,0.12641567,-0.04705751,0.0424186,-0.01215817,-0.0140347,0.04096264,-0.02282859,-0.01401811,-0.06493334,-0.01465212,0.00347936,-0.02825909,0.02179818,0.03409057,-0.10975789,0.00601587,0.07129852,0.00552594,-0.0165823,-0.05186709,0.0565079,0.02872811,0.05073756,0.04020363,-0.06587743,0.00622275,0.02797069,0.05124609,0.04979226,-0.01433615,0.06628148,-0.01872253,0.06751879,-0.05585672,0.01446181,-0.04447692,0.0432107,-0.01651992,0.01756479,-0.02122894,-0.0197721,-0.03107887,-0.02527941,0.05480079,-0.09040327,-0.01489204,0.06765728,-0.01877513,0.06015789,-0.03725924,-0.08724809,-0.03410428,0.04514379,-0.05933833,-0.01612423,-0.03020447,-0.00473079,0.08382605,0.0481994,-0.10577677,-0.03422538,0.00303208,-0.04300494,0.00165568,0.12841803,0.04491317,-0.08478869,-0.03182703,0.04618323,0.03702628,-0.02982466,-0.00602692,0.03196649,-0.03894115,-0.0175231,0.04605938,-0.05475382,-0.07719173,0.00493374,0.03149044,0.02233418,0.00989292,-0.04795702,-0.00867305,0.07052047,0.04961876,-0.03180809,0.03612128,-0.05535178,0.00031954,0.04862701,-0.06572045,-0.00669767,-0.00405585,0.02177699,-0.05187816,-0.02932345,-0.0488901,0.0112349,0.06295209,-0.02002417,0.11331608,0.03173115,0.01890814,0.05636331,-0.04271419,0.00773419,-0.04081342,-0.01681447,-0.01644211,-0.00641898,-0.05768603,0.01593843,0.10046874,0.03520248,0.00170082,-0.03264855,0.01353672,0.05265414,-0.00662731,0.07887596,-0.06329163,0.01719514,-0.02175798,-0.22275558,-0.03097876,0.01233164,0.0191082,0.02534772,-0.00750744,0.0337042,-0.04244962,-0.03522165,0.05481341,0.13149561,0.00719872,-0.02705475,0.02292011,-0.02264998,0.07391614,-0.01890951,-0.05369217,0.00832387,0.04357261,0.05347774,-0.02304961,-0.06289709,-0.02878975,0.01590477,-0.04585429,0.16773655,0.00147851,-0.00557145,-0.01446934,0.01113699,-0.01888032,-0.02021524,-0.068772,0.01262446,0.01113331,0.0424673,0.0224755,0.0355275,0.02084023,-0.0795842,-0.02155806,0.06837022,-0.0996078,-0.00923608,-0.01854555,-0.02191185,-0.01714796,0.00056322,0.00453855,0.05310276,0.01051753,0.07385237,0.02607114,0.01074788,0.00544976,-0.07895476,-0.0227866,0.00351172,0.00360595,-0.02111063,0.01405186,-0.01662785,-0.04804423,0.05722271,0.0429902,0.0212972,0.01564172,0.03787942,-0.06787518,0.02571512,0.05652229,0.04130038,0.00075276,-0.01409222,-0.01653141,-0.08097817,-0.01095829,0.04712504,0.01482898,-0.01278501,-0.02159531,0.01823319,-0.04310858,-0.01377632,-0.01229003,0.01074418,-0.02594172,0.02179357,-0.02225764,-0.03687021,0.0173371,-0.01132969,-0.0013203,0.05323128,0.02567927,-0.25450876,-0.00855248,0.03184962,-0.0468825,-0.01909608,0.02334094,0.02955094,-0.05794553,-0.03816859,-0.01878978,-0.06044126,0.07045322,-0.00714372,-0.04038502,0.01638344,0.05040078,0.03960891,-0.05918176,0.14139365,-0.08205628,0.03516605,0.01898963,0.23618965,-0.02297581,-0.00483527,0.04857453,-0.01985511,0.07704988,0.07140179,0.00535755,-0.01984758,0.0078479,0.06549234,0.00036096,-0.06883109,0.0563364,0.02930166,0.03171754,0.01499598,-0.0141871,0.00566579,0.04227372,-0.00078389,-0.04495336,0.06430888,-0.09334243,-0.04069798,-0.08682738,0.02825518,0.03479335,-0.09246569,0.00679993,0.01902482,0.0416216,0.0163701,0.05557138,-0.04819956,0.01421513,-0.07552744,-0.06857454,-0.02757712,-0.04553167,0.02212411,-0.00221537,-0.0065725],"last_embed":{"hash":"4xn1kl","tokens":466}}},"text":null,"length":0,"last_read":{"hash":"4xn1kl","at":1751288792193},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Benchmarks","lines":[157,185],"size":1003,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"4xn1kl","at":1751288792193}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Benchmarks#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08741082,-0.03913023,0.04775184,-0.04488793,0.0339225,-0.00900984,-0.06935946,-0.00547049,0.03029128,0.02097343,0.00289924,-0.03492057,0.05167111,0.02252852,0.03935139,-0.00919691,-0.0155964,0.01904079,-0.0222857,-0.01673335,0.02451573,-0.02035427,0.02809971,-0.04962169,0.03512754,0.05529397,-0.00352444,-0.02475467,-0.00506966,-0.21518517,-0.01432887,-0.02713453,0.09388889,0.02322666,0.02697623,-0.02253981,0.00567193,0.01499266,-0.0085111,0.02721255,-0.00174179,0.02069952,-0.07822285,-0.01274922,-0.00280942,-0.01870354,-0.04221074,0.01008185,-0.01088908,0.00196772,-0.01500895,-0.06441536,0.017828,0.00484521,0.0290165,0.05052093,0.0403416,0.03017858,0.07633132,0.06013473,0.01167616,-0.00113413,-0.21309145,0.0491403,0.0315915,0.04497626,-0.05562053,-0.02000233,-0.00120254,0.05556736,0.00314333,0.04177369,0.02193463,0.0555262,0.02238019,-0.00261138,0.02161204,-0.02103811,-0.05301834,-0.06152027,-0.04310787,-0.01068537,-0.01525589,0.00549751,0.03547794,0.00838913,0.06194843,0.01184359,0.08040693,0.07930186,-0.03596811,-0.06411543,-0.0042768,0.03823683,-0.0721213,-0.01004909,0.04087076,0.07108686,-0.06264299,0.12515579,-0.04667105,0.04451453,-0.01057827,-0.0147943,0.04124934,-0.02395781,-0.01583355,-0.06414815,-0.01496114,0.0027889,-0.02821422,0.02170737,0.0336695,-0.10908509,0.00676885,0.07087985,0.00447496,-0.01553779,-0.05270737,0.0561526,0.03051563,0.05155352,0.03810837,-0.06430162,0.00601929,0.02872417,0.05034507,0.05029196,-0.01519226,0.06659874,-0.02034721,0.06783829,-0.05642059,0.01707646,-0.04468541,0.04346781,-0.01644619,0.01719269,-0.02098164,-0.01948212,-0.03215771,-0.024645,0.05469957,-0.09094186,-0.01272838,0.06507441,-0.01949634,0.05984043,-0.03746184,-0.08623669,-0.03438148,0.04367198,-0.05909532,-0.01543682,-0.03163084,-0.00568724,0.08356524,0.04949284,-0.10520352,-0.03455073,0.0016872,-0.0419108,0.00181446,0.12932628,0.04680259,-0.08404107,-0.03258354,0.04491774,0.03748151,-0.02935131,-0.00759317,0.03117996,-0.03892875,-0.01923679,0.04728156,-0.05487288,-0.07672317,0.00339747,0.03122905,0.02167764,0.00842141,-0.04907485,-0.00734166,0.06931341,0.05068974,-0.03318453,0.03688554,-0.05669416,-0.00028269,0.04740004,-0.06541999,-0.00505046,-0.00453818,0.01992319,-0.0519691,-0.02938475,-0.04962952,0.01220909,0.06341614,-0.01959105,0.11393785,0.03354135,0.01838214,0.05758359,-0.04293826,0.00617926,-0.04160048,-0.01502291,-0.01656326,-0.00582323,-0.05901738,0.01671183,0.09979353,0.03550606,0.00066161,-0.03307072,0.01303108,0.05202283,-0.00628146,0.07833252,-0.06318818,0.01710339,-0.02118899,-0.22326089,-0.02972918,0.01127768,0.01855423,0.02549713,-0.00863359,0.03399267,-0.04293898,-0.03578014,0.05366201,0.13095506,0.00598469,-0.02755615,0.0217598,-0.02390346,0.07393064,-0.01916349,-0.05266242,0.0079112,0.04358822,0.05357442,-0.02227613,-0.06458801,-0.02919657,0.01735536,-0.04512547,0.16883281,0.0025389,-0.00205655,-0.01412683,0.01309669,-0.01755877,-0.02002292,-0.0700052,0.01190259,0.01234277,0.04248533,0.02264962,0.03687805,0.02120098,-0.07925693,-0.02287604,0.06851488,-0.10060243,-0.00691222,-0.01805676,-0.02207558,-0.01818737,0.00025792,0.00305342,0.05533499,0.011391,0.07195503,0.02731676,0.01113882,0.00555772,-0.07803653,-0.02158016,0.00228187,0.00333999,-0.02033688,0.01316233,-0.01811967,-0.04926218,0.05842549,0.0435971,0.02273,0.01640397,0.03838006,-0.06615291,0.02369479,0.05511731,0.04002232,-0.00088565,-0.01290186,-0.01498496,-0.08313183,-0.00954163,0.04814238,0.01505999,-0.01407474,-0.02093305,0.01749443,-0.04060055,-0.01467185,-0.01297779,0.00956064,-0.0253672,0.01867245,-0.02123208,-0.03680665,0.01799718,-0.01342171,0.00028238,0.05421495,0.02573854,-0.2543745,-0.00775885,0.03119479,-0.04598653,-0.01931588,0.02333714,0.02939626,-0.05807523,-0.03904583,-0.01849123,-0.06009113,0.07192743,-0.00691665,-0.03785343,0.01602205,0.05032687,0.03871093,-0.05740206,0.14195979,-0.08103766,0.03557331,0.01807676,0.23608872,-0.02224309,-0.00358337,0.04903329,-0.02103998,0.07942427,0.07129487,0.00538521,-0.01979162,0.0078224,0.0661232,-0.00021095,-0.06964748,0.05635601,0.02912669,0.03248077,0.01464175,-0.01348178,0.00522374,0.04252761,0.00040091,-0.04460074,0.06369169,-0.09252008,-0.04111544,-0.0861279,0.02764362,0.03490178,-0.09027664,0.00811027,0.01877099,0.04001677,0.0152762,0.05453989,-0.04840409,0.0132297,-0.07636119,-0.06773379,-0.02606723,-0.04490409,0.02122208,-0.00325608,-0.00709895],"last_embed":{"hash":"4k24vi","tokens":466}}},"text":null,"length":0,"last_read":{"hash":"4k24vi","at":1751288792376},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Benchmarks#{1}","lines":[159,185],"size":988,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"4k24vi","at":1751288792376}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Deno": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07987247,-0.04779721,0.03008086,-0.0537627,0.00980729,0.00509934,-0.04325598,-0.01322801,0.00641796,0.01053864,-0.03472738,-0.02532309,0.05279623,0.03009959,0.05459525,-0.02307107,-0.0122515,0.02577985,-0.02724296,0.00673233,0.03281837,0.01024792,0.02312313,-0.02354743,-0.00487781,0.0849655,0.00117107,-0.00084425,0.00873222,-0.19475801,0.00100666,-0.02136577,0.00844531,0.00625682,-0.00912433,-0.02218244,-0.0142321,-0.0103355,-0.0432248,0.00544916,0.00835872,0.01469252,-0.04798013,-0.03472898,0.02339692,-0.04056593,-0.00775348,0.02880518,-0.01168361,-0.02301103,-0.00237032,-0.05435162,0.0162022,-0.005137,0.02911757,0.08241348,0.0046342,0.06691319,0.07761382,0.07390483,0.02724316,0.00662218,-0.20529723,0.0801965,0.01091875,0.08073705,-0.06974685,-0.00451829,0.00250746,0.01499403,0.03385006,0.02501332,-0.01777711,0.04718563,0.04806402,-0.01799019,0.0104035,-0.00703152,-0.01738603,-0.05852325,-0.04466852,0.00307399,-0.00732723,0.00827426,-0.00403917,0.02407643,0.0386221,-0.01595612,0.03764313,0.02740485,-0.05670334,-0.08636975,0.04318537,0.05959155,-0.07303899,-0.01705337,0.05286809,0.03376202,-0.05740971,0.11009219,-0.02131316,0.03492055,-0.03827412,-0.00182782,0.00218123,-0.00770963,-0.01757087,-0.09668101,-0.00737973,0.01222663,-0.03677996,0.02103273,0.0170447,-0.12241357,-0.02253759,0.08097486,-0.02054292,-0.02208779,-0.0277211,0.03016126,0.00010616,0.0740763,0.01370544,-0.03606515,0.01108094,0.03361055,0.0417664,0.01471584,0.00823964,0.05991616,-0.02639521,0.0929609,-0.03544667,0.00262751,-0.03857182,0.03208191,-0.0159082,-0.02652941,-0.02243282,-0.04218694,-0.03450662,-0.0542914,0.05934582,-0.07420781,0.02332716,0.03774222,-0.02219696,0.04967634,-0.03256491,-0.06220427,-0.04324893,0.0536819,-0.06030764,0.00008606,-0.02010226,0.00122312,0.06045103,0.05913426,-0.10239124,-0.01356608,-0.00465996,-0.04757347,0.00959763,0.12824485,0.01743009,-0.10236781,-0.04945285,0.0576327,0.02572154,-0.00734145,0.00800268,0.03788361,-0.01281225,0.01815787,0.06557038,-0.015947,-0.08545079,-0.03507301,-0.00102512,0.03740682,-0.01435897,-0.09798722,0.01258603,0.04033315,0.04443252,-0.04172355,0.02121411,-0.0605902,0.01688619,0.02437748,-0.04308701,0.03137295,-0.01080349,0.00472691,-0.04116246,0.01247168,-0.04834699,-0.01161434,0.05836779,-0.02920048,0.07521612,0.05663542,-0.00621719,0.05138687,-0.09126149,0.0096296,-0.03087931,-0.01550271,0.01156429,-0.02482921,-0.07875451,0.06327071,0.10545354,0.08392493,-0.02102416,-0.01320851,0.02957369,0.07425684,0.00089943,0.0642117,-0.0334979,0.04607025,-0.02628126,-0.23365906,-0.00349779,0.00072199,-0.01283624,-0.02040597,-0.02635102,0.0551721,-0.03181923,-0.0660753,0.05260669,0.16862248,0.04695885,-0.03176702,0.01214611,-0.02539196,0.06028835,-0.00707521,-0.03635856,0.00815506,0.02330597,0.04274335,0.00738208,-0.06855501,-0.04027422,0.01351918,-0.02686107,0.14941405,-0.00387906,0.05301393,0.01447584,0.04483898,0.03096122,-0.00137813,-0.12449885,-0.00609234,0.02757192,0.02803231,0.03749104,0.02036419,0.00304288,-0.00947279,0.00433419,0.06521525,-0.10650516,0.02487576,-0.06199624,-0.03572525,-0.01244535,0.00020726,-0.00247467,0.05275671,0.03123026,0.04294745,0.05276748,-0.00500154,0.0218425,-0.04391104,-0.00596017,0.05215117,0.00508867,-0.02223947,0.03259866,-0.01019569,-0.06746005,0.04249888,0.04472111,-0.00501131,0.00435085,0.087511,-0.02518957,-0.00557639,0.06534877,0.01129323,-0.00808942,-0.01956538,0.01168649,-0.077058,0.01067008,0.06704407,0.03534441,-0.01302555,0.00190023,0.01093977,-0.02121081,-0.03547607,0.00577315,-0.0165846,-0.03230833,-0.00227105,-0.00914197,-0.03607674,-0.00506387,-0.00599448,0.0023469,0.05182432,0.00830187,-0.23669785,-0.03728231,0.03292831,-0.04515674,-0.05130148,0.03590957,0.03312356,-0.08229893,-0.08717655,-0.0627241,-0.02742586,0.07526147,-0.00467047,-0.02891154,0.02560023,0.06477208,0.0505333,-0.05107258,0.11887598,-0.12549897,0.03050798,0.01875708,0.23379619,-0.01491003,0.01035573,0.06161867,-0.01723906,0.09374067,0.03123392,0.06693023,-0.01887451,0.00004002,0.05812862,0.00639661,-0.03624972,0.01203568,0.0005903,0.03234581,0.04468863,0.0077521,-0.0180364,0.06436251,-0.0125373,-0.05980162,0.05185539,-0.1001932,-0.06384306,-0.06751189,0.02196174,0.03384271,-0.05806484,-0.01996746,0.02501394,0.04184154,0.00123832,0.01666501,-0.00771217,0.00487756,-0.06995536,-0.03585211,0.01511,-0.04791893,0.01926363,0.00181785,-0.00507326],"last_embed":{"hash":"19682e4","tokens":321}}},"text":null,"length":0,"last_read":{"hash":"19682e4","at":1751288792564},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Deno","lines":[186,202],"size":419,"outlinks":[{"title":"API","target":"#api","line":5},{"title":"two modes","target":"#modes","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"19682e4","at":1751288792564}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Deno#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07849022,-0.04908503,0.02825514,-0.05328099,0.00974678,0.00361593,-0.04378593,-0.01201014,0.00687001,0.01056766,-0.03376751,-0.02567714,0.05470296,0.02999676,0.05330527,-0.02436764,-0.01323345,0.02429818,-0.02697693,0.00686953,0.03344415,0.01018916,0.02254943,-0.02128246,-0.00469853,0.085708,0.00226301,-0.00138813,0.00677777,-0.1961814,-0.00000697,-0.02305031,0.008685,0.00677085,-0.00851635,-0.02122882,-0.01462655,-0.00897404,-0.04379501,0.00752838,0.00805718,0.01419466,-0.04755165,-0.03441335,0.02356929,-0.04064601,-0.00949123,0.02781699,-0.01138809,-0.0230238,-0.00130538,-0.05352539,0.01575162,-0.00208414,0.02834412,0.08371884,0.00526945,0.06653852,0.07668334,0.07389224,0.02540153,0.00834373,-0.20544934,0.08067208,0.00874853,0.07999497,-0.07130806,-0.00427452,0.00126469,0.01587826,0.033452,0.02556086,-0.01806213,0.04582151,0.04924597,-0.01600254,0.01222835,-0.00898109,-0.01748028,-0.05938318,-0.04386076,0.00292758,-0.00488429,0.00752893,-0.00500071,0.02541186,0.03863167,-0.01538675,0.03763542,0.02881938,-0.05797149,-0.08519392,0.04506545,0.06021098,-0.07371465,-0.02080073,0.05376685,0.03360693,-0.05796317,0.11005063,-0.02157777,0.03707519,-0.03771644,-0.00054595,0.00383034,-0.00736466,-0.01739935,-0.09662506,-0.00567907,0.01219645,-0.03726481,0.02248277,0.01786326,-0.12326359,-0.01782405,0.08259197,-0.0214546,-0.02447359,-0.02869005,0.02768834,-0.00044227,0.07298484,0.01443632,-0.03428189,0.01184972,0.03384176,0.04276466,0.01388951,0.0079547,0.06080787,-0.02687462,0.09169398,-0.03710314,0.00096978,-0.04114429,0.03258193,-0.01467723,-0.0263631,-0.02458274,-0.04261234,-0.03558676,-0.05321486,0.06117059,-0.07285038,0.02417922,0.03896363,-0.02172457,0.05049938,-0.03411712,-0.0641235,-0.04313237,0.05516439,-0.06031192,0.00039096,-0.0196912,-0.00002191,0.06014547,0.05819872,-0.10261913,-0.01258227,-0.00485468,-0.04860187,0.00985841,0.12721488,0.01698655,-0.10238738,-0.04899865,0.05861535,0.026355,-0.00864249,0.00810669,0.03645237,-0.01458963,0.01922843,0.06513138,-0.01775707,-0.08256829,-0.03389924,-0.0010844,0.03883173,-0.01660392,-0.09789911,0.01226039,0.04213373,0.04451771,-0.04176302,0.02139115,-0.05781909,0.01835051,0.02741385,-0.0450627,0.03138373,-0.01017984,0.00576259,-0.04100525,0.01262064,-0.04952593,-0.01136071,0.05684321,-0.02974113,0.07356964,0.05682823,-0.00638994,0.0510246,-0.09212722,0.00974936,-0.03238746,-0.01778616,0.01115334,-0.02505276,-0.08026525,0.0641177,0.10545168,0.08300592,-0.02292149,-0.01037916,0.0283142,0.07465857,0.00083891,0.06251844,-0.03394587,0.04624763,-0.02557755,-0.23421992,-0.00317365,0.0021442,-0.0140823,-0.0209211,-0.02396678,0.05716862,-0.02960719,-0.06779028,0.05136219,0.16948628,0.04824551,-0.0331745,0.0127217,-0.0235335,0.06002322,-0.00720648,-0.03581625,0.00700999,0.02363396,0.04336433,0.0092736,-0.06799492,-0.03865505,0.01385023,-0.02458608,0.15001917,-0.00593215,0.05281556,0.01427976,0.04544552,0.03135042,-0.00101461,-0.12374629,-0.00828689,0.02742841,0.02734896,0.03844913,0.01887814,0.00475067,-0.00869739,0.00311069,0.0651185,-0.10616829,0.02492319,-0.06189286,-0.03304077,-0.00940443,0.00081883,-0.00176569,0.05279509,0.03158202,0.04281668,0.05503016,-0.00528913,0.02140319,-0.04474279,-0.00516786,0.05030463,0.00422874,-0.02192153,0.02972728,-0.01065379,-0.0682137,0.04145069,0.04443142,-0.00442411,0.00468134,0.08580118,-0.02430762,-0.00775442,0.06480751,0.01150785,-0.00730784,-0.01884661,0.01233492,-0.07477756,0.00825324,0.06701131,0.03473347,-0.01361211,0.00317503,0.01110053,-0.02012936,-0.03686577,0.0087129,-0.01715444,-0.0320919,-0.00236409,-0.00855498,-0.03913537,-0.00626984,-0.00495676,-0.00045302,0.05171774,0.0080296,-0.23561658,-0.04037535,0.03344646,-0.0432336,-0.05177175,0.03331311,0.03110296,-0.08173218,-0.08801017,-0.06287099,-0.0264067,0.07587197,-0.00875643,-0.02813735,0.02350223,0.06682721,0.04948271,-0.05077173,0.11867447,-0.12454729,0.03143118,0.01624703,0.2331302,-0.01407115,0.00798912,0.0620988,-0.01684594,0.09317502,0.03088253,0.06687586,-0.01956486,0.00009765,0.0576992,0.00655533,-0.03394246,0.01432982,0.00059491,0.03171038,0.04499614,0.00794715,-0.01771152,0.06192974,-0.01371065,-0.0606431,0.05210001,-0.0995978,-0.06512009,-0.06785557,0.02140691,0.03309105,-0.05523941,-0.0203758,0.02644265,0.04396689,0.00109756,0.01589882,-0.00740064,0.00211341,-0.06964426,-0.0355872,0.01568668,-0.04805228,0.01960453,0.00350878,-0.00443592],"last_embed":{"hash":"1j0d8ce","tokens":320}}},"text":null,"length":0,"last_read":{"hash":"1j0d8ce","at":1751288792733},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Deno#{1}","lines":[188,202],"size":410,"outlinks":[{"title":"API","target":"#api","line":3},{"title":"two modes","target":"#modes","line":3}],"class_name":"SmartBlock","last_embed":{"hash":"1j0d8ce","at":1751288792733}},
"smart_blocks:Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Related": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0325513,-0.04458552,0.05280386,-0.05771783,0.0176436,-0.0396873,-0.0942983,-0.01265922,0.01345914,0.0106148,-0.02866001,0.00812653,0.04893053,0.03130285,0.04729826,-0.00624938,0.00492019,0.08405767,-0.06648706,-0.03730377,0.0330501,-0.03190552,-0.00091938,-0.0420099,0.03749753,0.06342651,-0.00931469,-0.03545443,-0.02434083,-0.1908126,-0.0022109,-0.01570049,0.07922759,0.03264353,0.02598285,-0.04393847,0.00729775,0.01633764,-0.10025147,-0.00599356,0.02605867,0.050567,-0.03353103,0.00146337,0.02057012,-0.01268876,-0.00871628,-0.00260702,-0.0078567,-0.0224234,-0.01991178,-0.04509998,0.00130701,0.02774512,0.02060776,0.0238824,0.04585897,0.03106079,0.0824082,0.07710683,0.04677229,0.05831572,-0.1677485,0.06188634,0.03143404,0.04539371,-0.05106327,-0.02648583,-0.01485615,0.07528827,0.0100616,0.01655063,-0.010522,0.04756885,0.02903338,-0.0511054,-0.00770201,-0.03825187,-0.0277826,-0.03688223,-0.08948334,-0.00553148,-0.05254307,0.01103876,0.04247263,-0.00294178,-0.0000513,-0.0127053,0.10823375,0.01717341,-0.06057445,-0.10736128,0.04464336,0.03121353,-0.07420929,-0.02950668,0.05217909,0.01429114,-0.05937273,0.1241805,-0.05436644,0.051806,0.00974111,-0.03736841,-0.01365913,0.01711476,-0.01157207,-0.05291744,-0.01038395,0.00428221,-0.03429873,-0.00980277,0.02286385,-0.10824796,-0.00773077,0.00809031,0.02137018,0.03791532,-0.03649738,0.06120955,-0.00882526,0.03006533,0.00881614,-0.04834504,0.0170768,0.02070835,0.00376656,0.03423344,0.02385153,0.1203904,-0.03596197,0.07240056,-0.05012254,0.00046832,-0.01318248,0.02600207,-0.00311251,-0.03498562,-0.00422632,-0.03418176,0.0120748,-0.03824398,0.02109791,-0.06839772,-0.02601655,0.03467205,-0.03165537,0.04494955,-0.0363692,-0.03956293,-0.03767051,0.0613203,0.00041365,-0.04997248,-0.02322385,-0.01269642,0.08825824,0.05192842,-0.10005375,0.00089641,-0.01772851,-0.04247669,-0.01054419,0.12827669,0.01600656,-0.07910296,-0.01395766,0.05405696,0.04596518,-0.01791065,-0.00876797,0.04689269,-0.04369577,-0.04117458,0.05137762,-0.04515023,-0.08620099,0.00597508,0.03143813,0.05299398,-0.03451086,-0.06700367,-0.00400348,0.0521709,0.04234229,-0.01831951,0.02984886,-0.04681663,-0.01416908,0.06615441,-0.05657952,0.02070691,0.00368351,-0.06890272,-0.03089217,-0.01029416,-0.03181278,-0.03260516,0.05618891,0.00176774,0.07013584,0.03062022,0.0203868,0.06191296,-0.08354335,-0.02478478,-0.01164672,-0.04096721,0.00881289,0.00737706,-0.0534882,0.0218931,0.08680148,0.00052445,-0.04180985,-0.03443838,0.03318752,0.06089808,0.03497002,0.0353059,-0.07214152,-0.00057309,-0.026199,-0.21295781,0.00918853,0.00578655,-0.03317775,-0.00839318,-0.04274062,0.01937568,-0.05133,-0.06934089,0.05358606,0.11049782,-0.00462007,-0.04714059,0.07034118,-0.04409968,0.05000328,-0.01130471,-0.05687178,0.00531371,0.05590525,0.0603756,-0.00099021,0.00176538,-0.04910966,0.0127959,-0.01166609,0.16323,0.02162586,0.05520697,0.04948952,0.05284898,0.07466955,0.01211328,-0.09352575,-0.00881568,0.01967639,-0.01883362,-0.01069895,0.04206932,0.00639291,-0.03061181,0.00426437,0.03253887,-0.11950466,0.02922858,-0.0385389,-0.0847398,-0.0116232,0.04462294,0.0605126,0.03564876,-0.00608267,0.0595669,0.0586901,0.04524648,0.01126089,-0.04477165,0.00289463,0.01762306,-0.01385918,0.01176003,0.01318399,-0.020826,-0.09702229,0.04411021,0.0251984,0.00247664,0.02364909,0.05238792,-0.04761928,-0.0017875,0.05410056,0.02944447,-0.06175059,-0.03655676,0.00491307,-0.08302632,0.02585461,0.03134074,-0.01190124,-0.02338677,-0.00554956,0.01819579,0.01871773,-0.00949135,-0.01744349,-0.01291363,-0.03673554,-0.01265329,-0.018659,-0.0267472,0.02310237,-0.01805237,0.06780589,0.06722449,0.02260856,-0.23791945,-0.02199394,-0.00765838,-0.02800164,0.01108774,0.07644464,0.01346526,-0.03405197,0.02198791,-0.04180952,-0.0159789,0.07021518,0.01570901,-0.05591378,0.03358253,-0.00242869,0.06490284,-0.00895399,0.13227651,-0.05938042,0.03244473,-0.0134949,0.25842053,-0.00932949,-0.00391311,0.00105096,-0.06050075,0.04442249,0.0420849,0.04992926,0.0187357,0.03019402,0.09534679,-0.00510525,-0.02702836,0.03037098,-0.01678057,0.0182492,0.03004748,0.00082785,-0.025971,0.01292923,-0.03506408,-0.0146276,0.06254627,-0.10640893,-0.00975638,-0.09926938,0.04714848,0.06235442,-0.05891931,0.02260135,0.00972907,0.03700746,0.01505588,0.00312912,-0.03926767,0.00496675,-0.08781353,-0.06571043,-0.02636787,-0.02746846,0.03766829,0.04363487,0.01595572],"last_embed":{"hash":"z7yy6b","tokens":304}}},"text":null,"length":0,"last_read":{"hash":"z7yy6b","at":1751288792839},"key":"Projects/Piecework/node_modules/escalade/readme.md#escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)#Related","lines":[203,208],"size":365,"outlinks":[{"title":"premove","target":"https://github.com/lukeed/premove","line":3},{"title":"totalist","target":"https://github.com/lukeed/totalist","line":4},{"title":"mk-dirs","target":"https://github.com/lukeed/mk-dirs","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"z7yy6b","at":1751288792839}},
