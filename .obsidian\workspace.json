{"main": {"id": "9da924696d57676c", "type": "split", "children": [{"id": "1018ac3950e41353", "type": "tabs", "children": [{"id": "4fd5d2afb2f0a027", "type": "leaf", "state": {"type": "graph", "state": {}, "icon": "lucide-git-fork", "title": "Graph view"}}]}], "direction": "vertical"}, "left": {"id": "f0718a4eb646f295", "type": "split", "children": [{"id": "ef3dc3323c5a8b97", "type": "tabs", "children": [{"id": "b5ff1a6a30a8243c", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "057225250fdc682a", "type": "leaf", "state": {"type": "search", "state": {"query": "tag:#notstarted", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "a0eb5f69dc462906", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}, {"id": "4295553f32b5452c", "type": "leaf", "state": {"type": "recent-files", "state": {}, "icon": "clock", "title": "Recent Files"}}]}], "direction": "horizontal", "width": 298.5}, "right": {"id": "2aa137be2599699e", "type": "split", "children": [{"id": "b8ef5e7ecba3aa2e", "type": "tabs", "children": [{"id": "ba5b21aca78afe95", "type": "leaf", "state": {"type": "backlink", "state": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": false}, "icon": "links-coming-in", "title": "Backlinks"}}, {"id": "7196af0f1922b49d", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "Projects/Music/__rules.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from __rules"}}, {"id": "da7b64e8a621b351", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "8f495841e415a6a7", "type": "leaf", "state": {"type": "outline", "state": {"file": "__start.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of __start"}}, {"id": "1b02b1026122223e", "type": "leaf", "state": {"type": "smart-connections-view", "state": {}, "icon": "smart-connections", "title": "Smart Connections"}}, {"id": "7e5e513bbf292855", "type": "leaf", "state": {"type": "git-view", "state": {}, "icon": "git-pull-request", "title": "Source Control"}}]}], "direction": "horizontal", "width": 266.5}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false, "smart-connections:Open: Smart Chat": false, "smart-connections:Open: View Smart Connections": false, "obsidian-kanban:Create new board": false, "obsidian-git:Open Git source control": false}}, "active": "4fd5d2afb2f0a027", "lastOpenFiles": ["Projects/Agency Work/Google Maps/__home.md", "Projects/Freelance/__home.md", "Projects/Freelance/__logs.md", "Projects/Agency Work/__home.md", "Google Business Profile Management/__home.md", "Google Business Profile Management", "Clients/__home.md", "<PERSON><PERSON> and Friends/__home.md", "<PERSON><PERSON> and Friends", "Projects/__home.md", "__memo.md", "__start.md", "__rules.md", "Tasks/2025-06-26-daily.md", "Projects/Music/__rules.md", "__reset.md", "Tasks/archive/2025-06-25-daily.md", "Projects/Lecturing/__logs.md", "Projects/Agency Work/Google Business Profile Management/Muchies/tasks.md", "Projects/Agency Work/Google Business Profile Management/Muchies/archive/improvements-history.md", "Google Business Profile Management/Muchies/__home.md", "Google Business Profile Management/MARSHAS Fitness/__home.md", "Mwila/__home.md", "Clients/__rules.md", "Performance/__rules.md", "Performance/2025/__rules.md", "Team/__rules.md", "Tasks/__rules.md", "Google Business Profile Management/Muchies", "Untitled.canvas", "Projects/Agency Work/Google Business Profile Management/Muchies/archive", "Projects/Music/Production/DubAvenue/archive", "Projects/Agency Work/Google Business Profile Management/Muchies", "Projects/Agency Work/Google Business Profile Management", "Google Business Profile Management/MARSHAS Fitness", "Projects/Agency Work/A<PERSON><PERSON>nk", "<PERSON><PERSON><PERSON>"]}