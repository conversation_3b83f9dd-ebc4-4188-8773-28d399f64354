{"main": {"id": "9da924696d57676c", "type": "split", "children": [{"id": "5469ad24d9f89896", "type": "tabs", "children": [{"id": "6df8468f3635fe46", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Projects/Agency Work/__rules.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "__rules"}}, {"id": "d6a245bc7f0685a7", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Team/__rules.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "__rules"}}], "currentTab": 1}], "direction": "vertical"}, "left": {"id": "f0718a4eb646f295", "type": "split", "children": [{"id": "ef3dc3323c5a8b97", "type": "tabs", "children": [{"id": "b5ff1a6a30a8243c", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "057225250fdc682a", "type": "leaf", "state": {"type": "search", "state": {"query": "tag:#notstarted", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "a0eb5f69dc462906", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}, {"id": "4295553f32b5452c", "type": "leaf", "state": {"type": "recent-files", "state": {}, "icon": "clock", "title": "Recent Files"}}]}], "direction": "horizontal", "width": 200}, "right": {"id": "2aa137be2599699e", "type": "split", "children": [{"id": "b8ef5e7ecba3aa2e", "type": "tabs", "children": [{"id": "ba5b21aca78afe95", "type": "leaf", "state": {"type": "backlink", "state": {"file": "__start.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": false}, "icon": "links-coming-in", "title": "Backlinks for __start"}}, {"id": "7196af0f1922b49d", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "__start.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from __start"}}, {"id": "da7b64e8a621b351", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "8f495841e415a6a7", "type": "leaf", "state": {"type": "outline", "state": {"file": "__start.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of __start"}}, {"id": "1b02b1026122223e", "type": "leaf", "state": {"type": "smart-connections-view", "state": {}, "icon": "smart-connections", "title": "Smart Connections"}}, {"id": "7e5e513bbf292855", "type": "leaf", "state": {"type": "git-view", "state": {}, "icon": "git-pull-request", "title": "Source Control"}}], "currentTab": 5}], "direction": "horizontal", "width": 238.5}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false, "smart-connections:Open: Smart Chat": false, "smart-connections:Open: View Smart Connections": false, "obsidian-kanban:Create new board": false, "obsidian-git:Open Git source control": false}}, "active": "d6a245bc7f0685a7", "lastOpenFiles": ["Team/__home.md", "Team/__logs.md", "Clients/__home.md", "Clients/__logs.md", "__memo.md", "__rules.md", "__start.md", "Performance/__rules.md", "Performance/__logs.md", "Performance/__home.md", "Tasks/2025-06-26-daily.md", "Tasks/__rules.md", "Tasks/__logs.md", "__logs.md", "Projects/__rules.md", "Projects/Agency Work/__rules.md", "Performance/2025/__rules.md", "Untitled", "Team", "Clients", "Projects/Agency Work/__home.md", "Projects/Agency Work/Google Maps/__home.md", "Projects/__home.md", "Tasks/archive", "Tasks", "Daily", "Performance/2025/June/Untitled", "Performance/2025/June", "Performance/2025", "Performance", "Projects/Agency Work/Google Maps/__kanban.md", "Projects/Agency Work/Untitled.md", "2025-06-26.md", "Agency Work.md", "create a link.md"]}