
"smart_sources:Projects/Piecework/node_modules/typescript/SECURITY.md": {"path":"Projects/Piecework/node_modules/typescript/SECURITY.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03164747,-0.04009308,-0.00012043,-0.07117564,-0.00765013,-0.00482604,-0.04533121,-0.01599289,-0.0651154,0.04786021,0.03598128,0.00959494,0.0613243,-0.00843184,0.01401627,0.04149078,0.01180438,-0.0060599,-0.01961,0.01467525,0.01375252,-0.04935753,-0.01658052,-0.06670441,0.00800517,0.08183383,-0.01191644,-0.04820608,-0.04616645,-0.18881147,-0.04322107,-0.01023729,-0.00476062,0.01000603,0.08954622,-0.04577707,-0.04314911,-0.00078228,-0.08220535,0.00367201,0.02122407,0.04367774,0.02096793,-0.03830104,0.02331996,-0.09308247,-0.02826095,-0.02192158,0.01952747,-0.03838885,-0.01920143,-0.04291206,0.01088341,0.06532641,0.02928335,0.00396287,0.02005438,0.04232755,-0.00377434,0.00473928,0.03939129,0.02644141,-0.18402593,0.08985135,0.08253842,0.10098357,-0.02890575,-0.02814526,0.02335359,-0.03986794,-0.02720144,0.03647522,-0.04001136,0.04543303,0.01143519,0.03719154,0.00080233,-0.01678729,0.01375266,-0.01618919,-0.03845808,0.02026474,-0.00936129,0.06286393,-0.07663615,-0.00994187,0.07255175,0.03515281,0.09007734,-0.02084738,-0.0247872,-0.05814647,0.09296649,0.02665587,-0.00287458,-0.03473315,0.00283046,0.0188664,-0.10234212,0.14544369,-0.05347782,0.02486252,-0.00558796,0.00068781,0.03903706,0.02034688,0.03623672,-0.06872377,-0.00162262,-0.01230854,0.0358041,0.01833733,-0.04814371,-0.0685362,0.01771801,0.00119289,-0.01822658,-0.0010345,-0.03459299,0.02200558,0.02996594,0.0144491,0.05676463,-0.01019781,0.04270243,0.0118663,0.02882948,0.03433178,0.06201543,0.08185668,0.01166703,0.03876903,0.00396921,0.02987517,0.03347165,-0.01897279,-0.04774155,-0.00425364,0.05325553,-0.0460554,-0.01380136,-0.00709125,0.04419115,-0.02716431,-0.03771232,0.09463847,-0.02193158,0.05820596,-0.02531144,-0.00196221,-0.00233784,0.0623669,-0.07194392,-0.0440983,0.02942807,-0.0474445,0.02039363,0.01597817,-0.04895011,-0.04283494,0.00592985,-0.03463459,-0.03911416,0.16372187,0.03769588,-0.09203561,0.03655559,0.05173393,-0.00928787,-0.07900625,-0.01466581,0.04472106,0.00630285,-0.04207085,0.00945825,-0.02692254,-0.06203794,-0.04027062,0.0498987,0.03368635,-0.03798253,-0.03407682,-0.06668136,0.00676131,0.05538702,-0.05281332,0.03086273,-0.00292472,-0.00053291,-0.04555866,-0.07982446,-0.02988284,-0.0322247,-0.03499249,-0.00315258,-0.05625924,-0.03123964,-0.01734429,0.02179328,-0.02892627,0.0708215,0.02918247,-0.0243987,0.03852955,-0.06654918,0.05992532,-0.02034354,-0.01622144,0.01456293,0.02729748,-0.03891915,0.0117521,0.04568736,0.05673681,-0.02978613,0.00149348,0.0208259,0.09477657,0.01142161,0.06455989,-0.02736713,0.08715156,-0.10283321,-0.18733804,-0.00925123,-0.01823812,-0.02555899,-0.02577065,-0.0595304,-0.00199119,-0.06770018,-0.04699076,0.00706581,0.20390466,0.07131441,-0.01539567,-0.02297805,0.03297659,0.01767354,0.00831495,-0.05863879,-0.00966605,0.00229631,-0.0192896,0.00073959,-0.10052825,-0.02578397,-0.01380712,-0.04895754,0.16117316,0.03212076,0.04385545,0.0172479,-0.01341087,-0.00139342,0.04876067,-0.15628888,0.02578755,0.00367805,-0.02104596,0.04641598,0.01697227,-0.03333303,-0.04728734,0.01968149,-0.03501732,-0.04044095,0.02804266,-0.04352311,-0.06522641,0.04137125,-0.01557916,0.06841353,0.01682974,0.03689975,0.0420783,0.11730503,-0.03350612,0.01844209,-0.02370179,-0.0117429,0.01703662,0.06561689,0.03162974,-0.02280338,0.00197195,-0.09959487,0.08288638,0.00888377,-0.0125125,-0.00515795,0.07883707,-0.04241868,0.01278605,0.1259383,0.00551633,0.01180952,-0.02975785,0.02692027,-0.00510466,-0.03228468,-0.05128456,-0.03897646,-0.01459786,-0.04868496,0.02492913,-0.00531413,0.0372342,0.02875483,0.00982391,0.02120491,0.0713845,-0.05056043,-0.01442425,-0.06931821,-0.0279977,-0.05389171,0.06622714,0.01857567,-0.21126877,0.01321246,0.05766708,-0.03350182,-0.0778366,-0.04646982,0.06470776,-0.04956643,-0.00117912,-0.03461576,-0.00043171,0.07041843,0.03367762,-0.03758699,0.0078342,-0.01927847,0.0661906,-0.03968853,0.04124619,-0.01412948,0.00972788,0.02023328,0.21837693,0.03067404,0.02062587,0.03450589,0.03646899,0.06997257,0.06931259,0.04378952,0.03698296,-0.01429616,0.04887615,-0.01080451,-0.02428328,0.00113096,0.02935524,0.02871489,0.04122756,0.0275344,-0.02693437,-0.02152488,0.00266243,-0.00868357,0.09385699,-0.0749101,0.0057907,-0.07576817,0.03321484,0.00664853,-0.08025259,0.01166509,0.00601872,0.01061738,-0.01915365,-0.01131778,-0.00628601,-0.01548627,-0.06978642,0.02408839,0.02184881,-0.03013543,-0.00936856,-0.00398263,-0.01023959],"last_embed":{"hash":"585dwf","tokens":510}}},"last_read":{"hash":"585dwf","at":1751288835990},"class_name":"SmartSource","last_import":{"mtime":1751244567626,"size":2656,"at":1751288766190,"hash":"585dwf"},"blocks":{"#":[1,2],"##Security":[3,8],"##Security#{1}":[5,8],"##Reporting Security Issues":[9,32],"##Reporting Security Issues#{1}":[11,32],"##Preferred Languages":[33,36],"##Preferred Languages#{1}":[35,36],"##Policy":[37,42],"##Policy#{1}":[39,42]},"outlinks":[{"title":"AspNet","target":"https://github.com/aspnet","line":5},{"title":"Azure","target":"https://github.com/Azure","line":5},{"title":"DotNet","target":"https://github.com/dotnet","line":5},{"title":"Microsoft","target":"https://github.com/Microsoft","line":5},{"title":"Xamarin","target":"https://github.com/xamarin","line":5},{"title":"Microsoft's definition of a security vulnerability","target":"https://aka.ms/security.md/definition","line":7},{"title":"https://msrc.microsoft.com/create-report","target":"https://aka.ms/security.md/msrc/create-report","line":13},{"title":"Microsoft Security Response Center PGP Key page","target":"https://aka.ms/security.md/msrc/pgp","line":15},{"title":"<EMAIL>","target":"mailto:<EMAIL>","line":15},{"title":"microsoft.com/msrc","target":"https://www.microsoft.com/msrc","line":17},{"title":"Microsoft Bug Bounty Program","target":"https://aka.ms/security.md/msrc/bounty","line":31},{"title":"Coordinated Vulnerability Disclosure","target":"https://aka.ms/security.md/cvd","line":39}],"last_embed":{"hash":"585dwf","at":1751288835531}},"smart_blocks:Projects/Piecework/node_modules/typescript/SECURITY.md##Security": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02751206,-0.06652921,-0.00614579,-0.04832005,0.01485782,-0.00665821,-0.00402034,-0.02516616,-0.06545101,0.04188711,0.02829537,0.00270145,0.08602095,-0.01131493,0.01952253,0.02705476,0.01359441,0.02004815,-0.04791144,0.03167296,0.03859974,-0.02499683,0.00455584,-0.03547278,0.04518969,0.09161545,-0.00783642,-0.05498825,-0.0427117,-0.15350945,-0.0475959,0.00147089,-0.02089142,0.02181464,0.08627772,-0.04519087,-0.04077642,-0.03521661,-0.0791417,0.03778942,0.01948131,0.04006179,0.02017551,-0.03107456,0.00075677,-0.09225997,-0.04181176,0.00248321,0.00373063,-0.02044898,0.0012695,-0.01885699,0.01042672,0.06272417,0.01160893,0.00948599,0.00945313,0.04922093,0.01819296,0.00863532,0.04964969,-0.00528131,-0.17938484,0.10131611,0.0711392,0.11744771,-0.02032499,-0.04783773,0.03191523,-0.02196995,0.01030835,0.01988308,-0.04079287,0.05276822,0.0126994,0.02909708,0.00148865,-0.03747661,0.01054514,-0.01067468,-0.0221658,0.00938271,-0.01798274,0.08692429,-0.05964987,0.01644013,0.06182175,0.0146218,0.0963406,-0.00800608,-0.04139856,-0.0713869,0.09214785,0.02125212,0.01069507,-0.04956474,0.00881994,0.02348679,-0.05741788,0.16536325,-0.04541093,0.01254715,-0.02214276,0.01167001,0.05571662,0.02483661,0.02151493,-0.07041933,-0.0125846,-0.00208528,0.03679506,0.04350866,-0.05375261,-0.04345808,0.02874199,-0.02109788,-0.04261442,0.00078755,-0.02180627,0.02236673,0.02715732,-0.00682858,0.08920701,-0.00159269,0.02391401,0.00517218,0.02091406,0.02614196,0.03475412,0.06096751,0.0039957,0.02112121,0.01112155,0.03465394,0.03488732,-0.00109264,-0.03093718,-0.01388778,0.01140352,-0.02759699,-0.01333484,-0.00246698,0.07351386,-0.01998552,-0.02947308,0.07953095,-0.01470104,0.05911183,-0.03394426,-0.00003568,-0.0235346,0.08583374,-0.06500962,-0.01202027,0.0322274,-0.0554796,0.03606248,0.00280542,-0.06151915,-0.04471238,0.00721921,-0.04406379,-0.04231202,0.14782344,0.0192368,-0.07564798,0.02095542,0.03824876,-0.02233702,-0.05887697,-0.0200143,0.01923211,-0.04172552,-0.02205923,0.00830122,-0.00345729,-0.06365925,-0.0489558,0.03788635,0.02330301,-0.03590993,-0.04469353,-0.07554621,0.02860188,0.06632198,-0.04265914,0.02817392,-0.00679204,-0.00423836,-0.04751605,-0.07465957,0.00107842,-0.03344218,-0.04270972,0.00545645,-0.0374584,-0.02942889,-0.02524322,0.01345825,-0.02635483,0.02794882,0.05723898,-0.01325965,0.01575956,-0.06672961,0.04165497,-0.03487599,-0.02297064,-0.01061687,0.02903468,-0.06179871,0.03404694,0.03397826,0.04025536,-0.03205344,0.00728686,0.0350154,0.09793384,0.01546364,0.04154024,-0.03434454,0.05677985,-0.1137936,-0.17676355,-0.0198297,-0.04851554,-0.05046665,0.00317864,-0.05662493,-0.03109694,-0.06253993,-0.06333733,-0.00418484,0.1935664,0.06234996,0.01469485,-0.0336733,-0.00280476,0.04326249,0.00287835,-0.04461046,-0.02849802,-0.00507665,-0.0125882,0.03057379,-0.08565234,-0.02457463,-0.02916311,-0.02332931,0.16160288,0.03403777,0.08849832,0.01732372,-0.00083301,-0.01069527,0.0456247,-0.14152497,0.01056273,0.00992694,-0.01546285,0.01181688,-0.00784171,-0.05746945,-0.03407693,0.00828346,-0.03295306,-0.06696998,0.0557526,-0.03640984,-0.08988652,0.03374623,-0.01047357,0.06955342,0.01561674,0.04264274,0.02771491,0.119428,-0.06383403,0.02634524,-0.01565344,-0.00716597,0.01426806,0.07766437,0.02887327,-0.02447025,-0.0168419,-0.10796247,0.10741971,-0.02451438,-0.01628501,-0.04396244,0.07056247,-0.05915875,0.02306855,0.11419076,0.00755995,-0.01035939,-0.03216483,0.01545564,0.00106231,-0.03344354,-0.05258283,-0.02708898,-0.02009965,-0.05454829,0.02260819,0.00863068,0.02311101,0.01266222,-0.00540973,0.0501561,0.0364812,-0.05125124,-0.00762131,-0.04628659,-0.02466814,-0.06072902,0.08306717,0.01236892,-0.20222969,0.00581445,0.05444051,-0.01011341,-0.0774378,-0.06052842,0.08605409,-0.03688723,-0.00722074,-0.01068083,0.02287205,0.0521368,0.04482843,-0.04821123,0.00794927,-0.01584984,0.10378412,-0.06143251,0.05575377,0.00622709,0.0085808,0.03049052,0.23795941,0.02385137,0.04289729,0.03287417,0.0219741,0.06551553,0.05090827,0.04625482,0.03776592,0.00089146,0.03419494,-0.01086173,-0.03726645,-0.01553315,0.0602364,0.01745126,0.04435479,0.0260176,-0.02991605,-0.04445417,-0.02479108,-0.00740071,0.10395672,-0.08709501,-0.00701478,-0.0919691,0.02323663,0.01954655,-0.06616021,-0.00296564,0.00591782,-0.00267142,-0.00022253,-0.0056961,-0.02115994,-0.01972853,-0.06815305,0.01822894,0.01448846,-0.01906027,-0.0220944,0.00960349,-0.00900819],"last_embed":{"hash":"10bl9oh","tokens":197}}},"text":null,"length":0,"last_read":{"hash":"10bl9oh","at":1751288835732},"key":"Projects/Piecework/node_modules/typescript/SECURITY.md##Security","lines":[3,8],"size":622,"outlinks":[{"title":"AspNet","target":"https://github.com/aspnet","line":3},{"title":"Azure","target":"https://github.com/Azure","line":3},{"title":"DotNet","target":"https://github.com/dotnet","line":3},{"title":"Microsoft","target":"https://github.com/Microsoft","line":3},{"title":"Xamarin","target":"https://github.com/xamarin","line":3},{"title":"Microsoft's definition of a security vulnerability","target":"https://aka.ms/security.md/definition","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"10bl9oh","at":1751288835732}},
"smart_blocks:Projects/Piecework/node_modules/typescript/SECURITY.md##Security#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02592547,-0.06466863,-0.00545695,-0.04928439,0.01504686,-0.00755294,-0.00556701,-0.02572819,-0.06628378,0.04155742,0.02899245,0.00634229,0.08926862,-0.01206639,0.01934098,0.02603812,0.0157933,0.01786365,-0.04964853,0.0318304,0.03396958,-0.02374481,0.00226572,-0.03373911,0.04583397,0.09407407,-0.00953181,-0.05554935,-0.04413679,-0.15355946,-0.04652098,0.00025069,-0.0205574,0.02183489,0.08780626,-0.04319918,-0.0398914,-0.03595582,-0.07798313,0.03572193,0.01938126,0.04167523,0.02317689,-0.03031267,-0.00159419,-0.0917196,-0.04267326,0.00333398,0.00687031,-0.01773336,0.00413607,-0.01782857,0.01053372,0.06499868,0.00921399,0.00948484,0.00772589,0.05029484,0.01854212,0.01037436,0.04578241,-0.00927734,-0.17860141,0.09995234,0.06706729,0.12059027,-0.02071592,-0.05030395,0.03378078,-0.02393152,0.00918572,0.01806671,-0.04198729,0.05358943,0.01296999,0.02846833,0.00287639,-0.03878357,0.00915456,-0.01048697,-0.02246566,0.00830566,-0.01993123,0.08893224,-0.05969725,0.01719848,0.06512988,0.01287532,0.09595639,-0.00913319,-0.03985681,-0.07357736,0.08979031,0.02101624,0.01017834,-0.04940844,0.00670902,0.0246118,-0.05700132,0.16314812,-0.04566041,0.01320602,-0.02442361,0.00949946,0.05383808,0.02344516,0.02144186,-0.06639596,-0.01168372,0.00003482,0.03488239,0.0435131,-0.05439449,-0.04033313,0.02822028,-0.01796134,-0.04603999,-0.00020647,-0.024393,0.02121724,0.0289358,-0.00776339,0.09109979,-0.00183233,0.02236704,0.00752381,0.01857706,0.02456429,0.03218672,0.05899126,0.00043447,0.01699463,0.00800123,0.0366049,0.03434749,-0.00088299,-0.03117603,-0.01471877,0.00921492,-0.02259452,-0.01252412,-0.00215468,0.07478896,-0.01939326,-0.03039571,0.07974729,-0.01342383,0.06156227,-0.03529644,-0.00093653,-0.02010028,0.08728524,-0.06595861,-0.01216903,0.03177184,-0.05599912,0.03453378,0.00093294,-0.06092772,-0.04520902,0.00738722,-0.04385379,-0.0421571,0.15058126,0.01763725,-0.07142127,0.01877095,0.04150995,-0.02082772,-0.05179463,-0.02012002,0.02123729,-0.04099738,-0.02037402,0.00845679,-0.00113995,-0.06604925,-0.04626024,0.04107127,0.02244194,-0.03580001,-0.0413659,-0.07557815,0.03017487,0.06681209,-0.04493404,0.02922663,-0.00515889,-0.00649691,-0.04869238,-0.07346781,0.00388013,-0.03306321,-0.04365357,0.0085725,-0.03842719,-0.02853862,-0.0237365,0.01380673,-0.02826375,0.02760497,0.05866537,-0.01324867,0.01206544,-0.06294958,0.03769436,-0.03775567,-0.02279023,-0.00933323,0.02504367,-0.06114716,0.03463888,0.0326525,0.03968461,-0.03024351,0.01088643,0.03235744,0.09964534,0.01553778,0.04357942,-0.03399645,0.05705975,-0.11159673,-0.1808832,-0.02110711,-0.04832614,-0.04953794,0.00281392,-0.05789917,-0.0309585,-0.06273764,-0.06563758,-0.00430486,0.19113293,0.05978528,0.01661719,-0.03115182,-0.0039472,0.04316047,0.00260951,-0.03744368,-0.03162735,-0.00835883,-0.01821825,0.03018697,-0.08489745,-0.02361897,-0.02891473,-0.02219345,0.16128606,0.03034939,0.08577988,0.01375901,-0.00151311,-0.01216278,0.0456781,-0.14437465,0.01425968,0.01284111,-0.01491561,0.00990967,-0.00581538,-0.05990472,-0.03449659,0.00824742,-0.0308669,-0.0667781,0.0578931,-0.03422404,-0.09067397,0.03443846,-0.01067117,0.06916717,0.01481206,0.04278677,0.02849272,0.11921846,-0.06432669,0.02514919,-0.01632027,-0.00327441,0.01399222,0.0775699,0.03162307,-0.02544579,-0.01810127,-0.10782282,0.11018847,-0.02199334,-0.01675084,-0.04267551,0.06912216,-0.05696523,0.02105979,0.11339152,0.00558245,-0.00844792,-0.03148653,0.01750091,0.00095408,-0.03488114,-0.0519378,-0.02747053,-0.02098715,-0.0528048,0.02178325,0.0109125,0.02523764,0.00944928,-0.00525266,0.04775945,0.03511653,-0.05223285,-0.00846784,-0.04527935,-0.02529546,-0.05794613,0.08566831,0.01317293,-0.20317538,0.0041169,0.05350139,-0.00724463,-0.07906693,-0.06113596,0.08414502,-0.0350979,-0.00675168,-0.00890829,0.02174083,0.05254456,0.04495558,-0.05309846,0.00843293,-0.01448453,0.10660218,-0.05990247,0.05953425,0.00517008,0.00986477,0.03029919,0.23900951,0.02589418,0.04074177,0.03451671,0.02415474,0.06300586,0.04864536,0.04560053,0.04033092,0.0008081,0.0381005,-0.01045595,-0.03913579,-0.01291108,0.05659546,0.01770493,0.04279412,0.02533355,-0.02922632,-0.04538009,-0.02866523,-0.01214848,0.10532815,-0.08686885,-0.00838965,-0.09085419,0.02264718,0.02124991,-0.0635296,-0.0030689,0.00478797,-0.00186709,0.00151152,-0.01103661,-0.02163453,-0.01858862,-0.06681749,0.01459827,0.01554215,-0.02070057,-0.02360087,0.0085372,-0.00747168],"last_embed":{"hash":"1ns1wpm","tokens":196}}},"text":null,"length":0,"last_read":{"hash":"1ns1wpm","at":1751288835801},"key":"Projects/Piecework/node_modules/typescript/SECURITY.md##Security#{1}","lines":[5,8],"size":609,"outlinks":[{"title":"AspNet","target":"https://github.com/aspnet","line":1},{"title":"Azure","target":"https://github.com/Azure","line":1},{"title":"DotNet","target":"https://github.com/dotnet","line":1},{"title":"Microsoft","target":"https://github.com/Microsoft","line":1},{"title":"Xamarin","target":"https://github.com/xamarin","line":1},{"title":"Microsoft's definition of a security vulnerability","target":"https://aka.ms/security.md/definition","line":3}],"class_name":"SmartBlock","last_embed":{"hash":"1ns1wpm","at":1751288835801}},
"smart_blocks:Projects/Piecework/node_modules/typescript/SECURITY.md##Reporting Security Issues": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00410405,-0.01080736,-0.0082039,-0.08128784,-0.00904734,-0.01661975,-0.04555237,0.02895106,-0.03999765,0.04972757,0.0498563,0.0118017,0.0261886,0.00118712,-0.00161832,0.04518752,-0.01502832,-0.0235927,0.00503991,0.01327315,-0.00990958,-0.06358483,-0.00236559,-0.0440136,-0.01522658,0.0553039,-0.03441386,-0.03388289,-0.05831536,-0.21169411,-0.05364318,-0.01503513,0.00246382,-0.0300389,0.08291878,-0.06698731,-0.05174444,0.0413349,-0.06987023,-0.0150487,0.00120974,0.06551656,0.00023965,-0.02999699,0.0313481,-0.0598334,-0.02387688,-0.06167469,0.03105919,-0.05711198,-0.01869053,-0.07765285,-0.00300602,0.07055321,0.02837168,0.02009352,0.02795596,0.00353823,0.00316968,-0.00247075,0.05476519,0.04156519,-0.19140078,0.05327111,0.07148106,0.07478658,-0.02108803,-0.02047898,0.01164978,-0.03184865,-0.06329411,0.02241004,-0.04250868,0.0676552,0.01835369,0.01117962,-0.01666412,-0.00065551,0.00891553,-0.01094101,-0.04382917,0.06233056,0.00025575,0.02262358,-0.08140194,-0.05865299,0.06955925,0.03141844,0.10655398,-0.0138727,0.01266324,-0.04155267,0.0737525,0.03691919,-0.00746896,-0.00075597,0.00251518,0.01193077,-0.14057806,0.14430666,-0.07967292,0.02985242,0.01525945,-0.0171862,0.03350937,-0.00018677,0.04563527,-0.05401463,-0.00875208,-0.00786652,0.03115663,-0.01496576,-0.03084684,-0.09514541,0.00262571,0.05127292,0.02884317,0.01552409,-0.07552122,0.03215234,0.03856731,0.03663669,0.03158879,-0.01165364,0.07260395,0.01091791,0.02144587,0.05958354,0.04670569,0.07583381,0.02384258,0.01847362,-0.02637115,0.00669391,0.01357186,-0.06119618,-0.03498437,0.01638982,0.09422874,-0.01862244,-0.008585,-0.02100743,0.00003381,-0.0532723,-0.03926324,0.11736209,-0.01803641,0.03946688,-0.01113183,-0.04100109,-0.00100597,0.02616868,-0.07946328,-0.08021394,-0.00484628,-0.00441234,-0.00011546,0.02616366,-0.023418,0.00543411,0.02588697,-0.01407603,-0.01855954,0.1347809,0.05209651,-0.10692916,0.02864817,0.05787975,0.01046038,-0.08054634,-0.02261144,0.05795928,0.02878815,-0.06900497,0.02296302,-0.03364718,-0.03894423,-0.02055939,0.04974496,0.05711669,-0.0400906,-0.0116729,-0.03637685,0.0097983,0.05780643,-0.05323524,0.00543404,-0.00578457,0.00930647,-0.01241543,-0.0681953,-0.0475244,-0.01535084,-0.0062375,-0.0208714,-0.05698347,-0.00440384,-0.01847555,0.02625744,-0.01104117,0.08540913,0.00850729,-0.0268055,0.03222594,-0.0247237,0.07035945,-0.00260931,-0.03203673,0.04204796,0.0263416,-0.02945314,-0.00514637,0.05107427,0.06702396,-0.06346648,-0.0250195,-0.01614875,0.09071368,-0.0042077,0.04056608,-0.03282023,0.10581347,-0.07928157,-0.20309678,-0.02907858,0.02230284,0.01451063,-0.04065714,-0.06424309,0.03395757,-0.04425939,-0.01892521,0.07081449,0.17370914,0.08175958,-0.0430756,-0.04778213,0.08207341,0.02154926,-0.00830275,-0.08066311,0.02402176,-0.01102553,-0.03542043,-0.04173113,-0.06200786,-0.00170953,0.00936487,-0.03844173,0.14310215,0.04536713,-0.01148858,-0.00247142,-0.03417841,0.01970968,0.04950636,-0.13644958,0.02887428,0.00265626,-0.04672753,0.05417483,0.01951825,-0.02025394,-0.05511521,0.01861206,-0.05303372,-0.01921559,-0.00688448,-0.02796309,-0.01381017,0.01753624,0.013847,0.06264398,0.01264648,0.02347936,0.05347379,0.09775818,0.02719214,-0.00409859,-0.02656619,0.00055077,0.01712729,0.05448319,0.0348154,-0.01873335,0.02057783,-0.07523052,0.08930487,0.04575551,-0.00573325,0.03000626,0.09561876,-0.03177093,0.02020833,0.15543863,-0.0041805,0.03149211,-0.00293181,0.02986893,0.00566897,-0.06757623,-0.01499257,-0.04952439,0.00963189,-0.05792242,0.04920277,0.00559916,0.02289423,0.06223772,0.01676721,-0.01104182,0.08053323,-0.0567395,-0.02607347,-0.05123208,-0.03569297,-0.02635819,0.01758006,0.0392331,-0.20032761,-0.0209883,0.04905229,-0.07149564,-0.0530975,-0.0161867,0.04002082,-0.05400708,-0.01189663,-0.03352375,0.00405737,0.05711295,0.01527588,-0.00911824,0.00298003,0.00014014,0.02672566,-0.02845721,0.01482924,-0.06115778,0.00845014,0.01550905,0.2040251,0.03647346,-0.04695445,0.03438773,0.04149494,0.06207384,0.05420346,0.02033562,0.02116515,-0.03471081,0.05554274,-0.01813307,-0.00742968,0.02230969,0.00848088,0.0306631,0.02352438,0.01805115,-0.02855718,0.00336162,0.03254764,-0.04043382,0.07932465,-0.05950115,0.02082268,-0.04543992,0.0326312,-0.00906516,-0.08365792,-0.01038992,-0.00779108,0.00415351,-0.01149515,-0.00486924,0.01780762,-0.01799095,-0.06661993,0.04057458,0.00771246,-0.04231398,0.00374747,-0.00595047,-0.03560582],"last_embed":{"hash":"1cfea7h","tokens":437}}},"text":null,"length":0,"last_read":{"hash":"1cfea7h","at":1751288835860},"key":"Projects/Piecework/node_modules/typescript/SECURITY.md##Reporting Security Issues","lines":[9,32],"size":1749,"outlinks":[{"title":"https://msrc.microsoft.com/create-report","target":"https://aka.ms/security.md/msrc/create-report","line":5},{"title":"Microsoft Security Response Center PGP Key page","target":"https://aka.ms/security.md/msrc/pgp","line":7},{"title":"<EMAIL>","target":"mailto:<EMAIL>","line":7},{"title":"microsoft.com/msrc","target":"https://www.microsoft.com/msrc","line":9},{"title":"Microsoft Bug Bounty Program","target":"https://aka.ms/security.md/msrc/bounty","line":23}],"class_name":"SmartBlock","last_embed":{"hash":"1cfea7h","at":1751288835860}},
"smart_blocks:Projects/Piecework/node_modules/typescript/SECURITY.md##Reporting Security Issues#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00265451,-0.0074326,-0.01047136,-0.08105502,-0.00556913,-0.0162141,-0.04441828,0.03010546,-0.03788659,0.05068047,0.05169227,0.01609893,0.02901614,0.00284645,-0.00238685,0.04753901,-0.01581204,-0.02601931,0.00607871,0.01600343,-0.01262929,-0.06304188,-0.00064749,-0.03935033,-0.0142205,0.05459091,-0.03355568,-0.0324517,-0.05978945,-0.21306813,-0.0560564,-0.01688851,0.00221085,-0.03007437,0.08331908,-0.06663162,-0.05289041,0.03974686,-0.06945878,-0.0176049,0.00054038,0.06530532,0.00434447,-0.03274095,0.02826272,-0.05989879,-0.02277018,-0.06156873,0.03364048,-0.0604224,-0.01671424,-0.07682357,-0.00425205,0.07268307,0.02716681,0.02089636,0.0274199,0.0034266,0.00500558,-0.00326041,0.05683441,0.03671329,-0.19323827,0.05178471,0.07179004,0.07465264,-0.02025858,-0.0215402,0.01155842,-0.03030067,-0.06509784,0.02123509,-0.04162657,0.06666897,0.02030486,0.0114578,-0.02060297,0.00020973,0.0100995,-0.01122996,-0.04590117,0.06270859,-0.00266501,0.0231506,-0.0790099,-0.0579354,0.06769126,0.03201867,0.10962132,-0.01535305,0.01379131,-0.03782938,0.07230651,0.03485787,-0.00707024,0.00033219,-0.0002425,0.01351281,-0.14413306,0.14414693,-0.07755993,0.02843857,0.01345965,-0.01821273,0.03384791,-0.00247713,0.04433806,-0.05463537,-0.00873651,-0.00592949,0.03148834,-0.01451801,-0.0271513,-0.09637161,0.00184104,0.05111986,0.03068332,0.01705018,-0.07515139,0.03256673,0.03809261,0.03468139,0.03165035,-0.01077026,0.07137079,0.0114812,0.02116268,0.05944001,0.04787097,0.07648835,0.02537447,0.01337475,-0.02928546,0.00542817,0.0127695,-0.06246374,-0.0361231,0.01765399,0.09376012,-0.01830761,-0.0094334,-0.02172636,-0.00117866,-0.05028012,-0.04038271,0.11285822,-0.01693143,0.03810583,-0.01189565,-0.0411389,-0.00228754,0.02571279,-0.07883316,-0.083299,-0.00432932,-0.00320516,-0.0005396,0.02561429,-0.02277859,0.00857423,0.02865566,-0.01446566,-0.01607924,0.13453828,0.04922738,-0.10371628,0.02842914,0.05769522,0.01154898,-0.07964008,-0.02260404,0.05634002,0.03029502,-0.06830127,0.02228284,-0.0340385,-0.03494503,-0.01967547,0.04856386,0.06124637,-0.04192963,-0.01142647,-0.03701321,0.00999796,0.06039149,-0.05239362,0.00463374,-0.00494513,0.01061465,-0.01483835,-0.06829949,-0.04826457,-0.01427432,-0.00369954,-0.02081955,-0.05875439,-0.00254185,-0.01750826,0.02597616,-0.01168794,0.08616236,0.00865225,-0.02501182,0.02781972,-0.02426592,0.06715333,-0.00261726,-0.03136681,0.0433652,0.02227208,-0.03165799,-0.00440096,0.04893402,0.06411799,-0.06253275,-0.02518455,-0.01423786,0.09288786,-0.00506452,0.04175127,-0.03286167,0.10573946,-0.07671469,-0.20452601,-0.03121647,0.02193688,0.01802745,-0.04103534,-0.06512777,0.03438338,-0.04446377,-0.0169955,0.07177345,0.17027625,0.08534696,-0.04216287,-0.04686521,0.08430051,0.01944324,-0.00456274,-0.07927208,0.02433033,-0.01109636,-0.03727641,-0.04402763,-0.05977493,-0.00115561,0.00787101,-0.03716904,0.14141871,0.04454514,-0.01302094,-0.00466535,-0.03690651,0.02053832,0.05041374,-0.13719866,0.03057432,0.00429062,-0.04650063,0.05143292,0.01880509,-0.01836997,-0.05344989,0.02001902,-0.05368109,-0.01939893,-0.00671958,-0.02896748,-0.01191059,0.01585136,0.01513421,0.0616036,0.0120439,0.02369774,0.05552598,0.09775624,0.02913107,-0.00439247,-0.02833969,0.00116964,0.01717193,0.05115918,0.03443304,-0.0189715,0.02352992,-0.07498793,0.08939413,0.04697523,-0.0051044,0.03260995,0.09856668,-0.03158168,0.01832939,0.15744513,-0.00364152,0.03313757,-0.0043584,0.02967766,0.00809203,-0.07195579,-0.01565148,-0.05123888,0.01054769,-0.0546197,0.04986417,0.00606788,0.02122351,0.06383103,0.0157571,-0.0118659,0.07926021,-0.05495837,-0.02691349,-0.05053208,-0.03770525,-0.02596078,0.01586659,0.04270307,-0.19841622,-0.02251248,0.0501809,-0.07209738,-0.05286185,-0.01594573,0.04017074,-0.05363323,-0.01213142,-0.03362354,0.00467708,0.05573711,0.01536841,-0.01253864,0.00325573,0.00093297,0.02914157,-0.02838514,0.01259645,-0.0627713,0.00952574,0.01415739,0.20177102,0.03718049,-0.05058374,0.03188667,0.04321336,0.06285194,0.05216594,0.01951734,0.02301053,-0.03649947,0.05565533,-0.0153396,-0.00795769,0.02230171,0.00597986,0.03258861,0.02269421,0.01604383,-0.02780786,0.00427277,0.03535999,-0.03899002,0.07754108,-0.05778256,0.02233323,-0.04378387,0.03284586,-0.00742581,-0.08478227,-0.01220404,-0.00968066,0.00221703,-0.00916639,-0.00644885,0.01567838,-0.0193382,-0.06604073,0.03501642,0.00708075,-0.04207402,0.00192423,-0.00602049,-0.03604844],"last_embed":{"hash":"eu0uos","tokens":436}}},"text":null,"length":0,"last_read":{"hash":"eu0uos","at":1751288835990},"key":"Projects/Piecework/node_modules/typescript/SECURITY.md##Reporting Security Issues#{1}","lines":[11,32],"size":1719,"outlinks":[{"title":"https://msrc.microsoft.com/create-report","target":"https://aka.ms/security.md/msrc/create-report","line":3},{"title":"Microsoft Security Response Center PGP Key page","target":"https://aka.ms/security.md/msrc/pgp","line":5},{"title":"<EMAIL>","target":"mailto:<EMAIL>","line":5},{"title":"microsoft.com/msrc","target":"https://www.microsoft.com/msrc","line":7},{"title":"Microsoft Bug Bounty Program","target":"https://aka.ms/security.md/msrc/bounty","line":21}],"class_name":"SmartBlock","last_embed":{"hash":"eu0uos","at":1751288835990}},
