# Clients - Template

## Overview
Client management and project tracking.

## Client Structure (Auto-Updated)
Client information will be populated from __start.md:

| Client | Project | Status | Payment | Next Action |
|--------|---------|--------|---------|-------------|
| [Auto-populated from __start.md] | | | | |

## Client Management

### Active Clients
- Current project status tracking
- Payment status monitoring
- Communication history
- Next action items

### Client Coordination
- Project milestone tracking
- Deliverable management
- Feedback integration
- Relationship management

## Auto-Update Rules
Client data automatically updates based on:
- Project status changes
- Payment status updates
- Task completion milestones
- Communication logs

#clients #management #projects #template
