# AfyaLink Project Rules

## Project-Specific Rules
- **DEPLOYMENT MANDATORY**: Every code change must be deployed immediately to Vercel production
- **BUILD VERIFICATION**: Always run `npm run build` before deployment to catch issues
- **SEO-FIRST**: All design changes must maintain or improve SEO performance
- **Sanity Integration**: All content changes must sync with Sanity CMS
- **Production URL**: https://afyalink-zambia-mim2mdxj6-zambiancivilservantgmailcoms-projects.vercel.app

## Development Workflow
1. Make code changes in C:\Users\<USER>\afyalink-zambia
2. Test with `npm run build`
3. Deploy with `npm run deploy` or `vercel --prod`
4. Verify deployment URL is accessible
5. Update project logs with deployment status

## Technical Standards
- **Design System**: Follow the AfyaLink SEO-First style guide from memo
- **Color Palette**: Trust Green (#2E7D32), Professional Blue (#1E40AF), Action Orange (#F59E0B)
- **Typography**: Inter font family with semantic heading classes
- **Components**: Use established component classes (btn-primary, content-card, etc.)
- **NHIMA Badges**: Proper color coding for coverage status

## Content Strategy (3-Pillar Approach)
- **Pillar 1**: Factual directory content (facilities, costs, NHIMA coverage)
- **Pillar 2**: Educational guides (NHIMA explanations, healthcare guides)
- **Pillar 3**: High-intent thematic content (comprehensive health topics)

## Quality Gates
- All builds must pass without errors
- All deployments must be verified working
- All design changes must align with style guide
- All content must support SEO strategy

#afyalink #rules #deployment #seo #design #quality
