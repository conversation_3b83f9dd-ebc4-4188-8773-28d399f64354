# Lecturing - Project Tasks

## LGU Administrative Tasks {#invoicing-curriculum}

### #invoicing
**Invoice Management**
- [ ] Prepare December 2025 invoice for LGU (immediate priority)
- [ ] Inquire on invoices for previous semester (follow up on outstanding payments)
- [ ] Review payment schedule and terms
- [ ] Update invoice tracking system
- [ ] Coordinate with LGU finance department

### #curriculum-development
**Programming Foundations Course**
- [ ] Go over the Programming Foundations course curriculum (currently in planning stage)
- [ ] Plan curriculum development timeline for next semester
- [ ] Identify areas for improvement and updates
- [ ] Research modern programming teaching methodologies
- [ ] Prepare course materials and resources

## Academic Career Development

### #career-progression
**Road to Head of IT**
- [ ] Assess current position and requirements for advancement
- [ ] Identify skills and qualifications needed
- [ ] Plan professional development activities
- [ ] Network with department leadership
- [ ] Document achievements and contributions

### #course-development
**Additional Course Planning**
- [ ] Plan Application Development Course curriculum
- [ ] Research industry-relevant programming languages
- [ ] Develop practical project-based learning modules
- [ ] Create assessment and evaluation frameworks
- [ ] Coordinate with IT department on course offerings

## Administrative & Professional

### #documentation
**Academic Documentation**
- [ ] Update CV and academic portfolio
- [ ] Document teaching methodologies and outcomes
- [ ] Prepare course evaluation reports
- [ ] Maintain student progress records
- [ ] Create professional development log

### #networking
**Professional Relationships**
- [ ] Engage with LGU IT department colleagues
- [ ] Participate in academic conferences and workshops
- [ ] Build relationships with industry professionals
- [ ] Mentor junior faculty and students
- [ ] Collaborate on research opportunities

## Success Metrics
- Timely invoice processing and payment
- Course curriculum completion and approval
- Student satisfaction and learning outcomes
- Professional development milestones
- Career advancement progress

#lecturing #lgu #invoicing #curriculum #programming #career #academic
