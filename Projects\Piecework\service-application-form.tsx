import React from 'react';

const ServiceApplicationForm = () => {
  return (
    <div className="bg-gray-100 min-h-screen py-16">
      <div className="container mx-auto px-6">
        <div className="max-w-2xl mx-auto bg-white p-8 rounded-lg shadow">
          <h2 className="text-3xl font-bold text-gray-800 mb-8">Request a Service</h2>
          <form>
            <div className="mb-6">
              <label htmlFor="name" className="block text-gray-800 font-bold mb-2">Full Name</label>
              <input type="text" id="name" className="w-full px-4 py-3 bg-gray-200 rounded-lg focus:outline-none focus:bg-white" />
            </div>
            <div className="mb-6">
              <label htmlFor="phone" className="block text-gray-800 font-bold mb-2">Phone Number</label>
              <input type="tel" id="phone" className="w-full px-4 py-3 bg-gray-200 rounded-lg focus:outline-none focus:bg-white" />
            </div>
            <div className="mb-6">
              <label htmlFor="address" className="block text-gray-800 font-bold mb-2">Address</label>
              <input type="text" id="address" className="w-full px-4 py-3 bg-gray-200 rounded-lg focus:outline-none focus:bg-white" />
            </div>
            <div className="mb-6">
              <label htmlFor="service" className="block text-gray-800 font-bold mb-2">What service do you need?</label>
              <textarea id="service" rows="4" className="w-full px-4 py-3 bg-gray-200 rounded-lg focus:outline-none focus:bg-white"></textarea>
            </div>
            <div className="text-center">
              <button type="submit" className="bg-blue-500 text-white font-bold py-4 px-8 rounded-lg hover:bg-blue-600 transition-colors">
                Submit Request
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ServiceApplicationForm;

