## Google Maps 
- We just aquired Muchies and MARSHA's Fitness as new clients for business profiles 
- Muchies iPub and Grill is a restaurant near home and is 
- Mr <PERSON> is the current manager and has quite a good grasp on the matter
- I am working with <PERSON><PERSON><PERSON><PERSON> as the Creative Lead and <PERSON> (<PERSON>) as the Marketing Tech Lead.
- Here are some notes from <PERSON><PERSON><PERSON><PERSON> on potential improvements we observed:
	- <PERSON><PERSON>’s
		As discussed after a walk through, <PERSON>ie’s has a capacity to sit 66 people. Great venue and decor, even better lighting and fair pricing.
	- What we observed:
		- We want them to keep bathroom to the highest standards (door for toilet, have a toilet seat, toilet paper stocked, air freshener, toilet wiper). Have periodic checks to the bathroom (opening - 11am, afternoon - 4pm, evening - pm, night - 9pm) 
		- Gather content and come up with a rollout plan for posting content (happy hour, buckets, beat the traffic)
		- Enhance drink menu
		- Come up with a simple menu with at least 3 options (chips and chicken, sausage or pork chops) attached with a QR code 
		- Establish braai price and what it includes 
	- Here were our suggestions:
		- Rearrange outside furniture to colour coordinate
		- Move circular wooded table to outside in the kitchen in the Centre
	- <PERSON><PERSON><PERSON><PERSON> came up with some menues with all the items but there are still things that need to be added like the price for shots. 
	- <PERSON><PERSON><PERSON><PERSON> is gonna confirm on the prices
	  

## Afyalink
- Got some notes for the instructions of the application setup
  
  ### **TO: Warp AI Terminal**

### **FROM: Co-Founder, Project AfyaLink Zambia**

### **SUBJECT: Technical Specifications for MVP Build**

**Objective:** Generate the code and project structure for the AfyaLink Zambia MVP website.

---

### **1. Core Technology Stack**

- **Framework:** Next.js (version 14.2.x, using App Router)
    
- **Language:** TypeScript
    
- **Styling:** Tailwind CSS (version 3.4.x)
    
- **CMS:** Sanity.io (version 3.x)
    
- **Deployment:** Vercel
    

---

### **2. Sanity.io Data Models**

Initialize a new Sanity project and define the following schemas. These models are essential for managing content without code changes.

**a. `careWorker` Schema**

- **Purpose:** To store Simon's professional details. This will later be used for all care workers in the agency model.
    
- **Fields:**
    
    JavaScript
    
    ```
    defineType({
      name: 'careWorker',
      title: 'Care Worker',
      type: 'document',
      fields: [
        {name: 'name', title: 'Full Name', type: 'string'},
        {name: 'slug', title: 'Slug', type: 'slug', options: {source: 'name'}},
        {name: 'title', title: 'Professional Title', type: 'string', description: 'e.g., Registered Nurse'},
        {name: 'profileImage', title: 'Profile Image', type: 'image', options: {hotspot: true}},
        {name: 'bio', title: 'Biography', type: 'text'},
        {name: 'services', title: 'Services Offered', type: 'array', of: [{type: 'string'}]},
        {name: 'googleCalendarUrl', title: 'Google Calendar Booking URL', type: 'url'},
      ],
    })
    ```
    

**b. `nhimaFacility` Schema**

- **Purpose:** To store data for our programmatic SEO pages about NHIMA-accredited facilities.
    
- **Fields:**
    
    JavaScript
    
    ```
    defineType({
      name: 'nhimaFacility',
      title: 'NHIMA Facility',
      type: 'document',
      fields: [
        {name: 'name', title: 'Facility Name', type: 'string'},
        {name: 'facilityType', title: 'Facility Type', type: 'string', options: {
          list: ['Hospital', 'Clinic', 'Pharmacy', 'Diagnostic Centre'],
          layout: 'radio'
        }},
        {name: 'location', title: 'City/Town', type: 'string', initialValue: 'Lusaka'},
        {name: 'address', title: 'Address', type: 'string'},
        {name: 'notes', title: 'Notes', type: 'text', description: 'e.g., 24/7 service, specific services covered'},
      ],
    })
    ```
    

**c. `medicationPriceGuide` Schema**

- **Purpose:** To store data for the medication price guide page.
    
- **Fields:**
    
    JavaScript
    
    ```
    defineType({
      name: 'medicationPriceGuide',
      title: 'Medication Price Guide',
      type: 'document',
      fields: [
        {name: 'medicationName', title: 'Medication Name', type: 'string'},
        {name: 'strength', title: 'Strength', type: 'string', description: 'e.g., 500mg'},
        {name: 'estimatedPrice', title: 'Estimated Price (ZMW)', type: 'number'},
        {name: 'pharmacyChain', title: 'Pharmacy Chain / Type', type: 'string', description: 'e.g., Clicks, MedLink, Independent'},
        {name: 'lastUpdated', title: 'Last Updated', type: 'date'},
      ],
    })
    ```
    

---

### **3. Next.js Project Structure (App Router)**

Create the following file and directory structure inside the `/app` directory.

```
/app
|-- /about
|   |-- page.tsx         // About Simon page
|-- /book-now
|   |-- page.tsx         // Embedded Google Calendar booking page
|-- /guides
|   |-- /medication-prices
|   |   |-- page.tsx     // Page for the medication price guide
|   |-- /nhima-facilities
|   |   |-- page.tsx     // Page for the NHIMA facilities guide
|-- /services
|   |-- page.tsx         // Services and pricing page
|-- layout.tsx           // Root layout (with header, footer)
|-- page.tsx             // Homepage
/lib
|-- sanity.ts            // Sanity client configuration
```

---

### **4. SEO Strategy: Programmatic Content**

Our primary SEO strategy is to answer specific user questions programmatically. The `nhima-facilities` page is our first implementation of this.

**Execution:**

1. The `/guides/nhima-facilities/page.tsx` will fetch **all** documents from the `nhimaFacility` schema in Sanity.
    
2. The page will render a title like: "Comprehensive List of NHIMA Accredited Hospitals, Clinics, and Pharmacies in Zambia".
    
3. On this page, implement client-side controls (e.g., buttons, dropdowns) to filter the list by `facilityType` and `location`.
    
4. When a user filters, dynamically update the page's `<h1>` and a descriptive paragraph to reflect the selection. This creates the "custom variations" for search engine indexing and user experience.
    
    - **Example:** User filters for "Pharmacy" in "Lusaka".
        
    - The `<h1>` changes to: "NHIMA Accredited Pharmacies in Lusaka".
        
    - A text block appears: "Below is a list of pharmacies in Lusaka that accept NHIMA for medication and services..."
        

This approach allows us to target hundreds of long-tail search variations from a single, manageable data source (e.g., "nhima hospitals in kitwe", "nhima clinics lusaka", "pharmacies that accept nhima").

---

### **5. Build Instructions**

**Step 1: Project Initialization**

Bash

```
npx create-next-app@latest afyalink-zambia --typescript --tailwind --eslint --app
cd afyalink-zambia
```

**Step 2: Sanity Integration**

- Set up a new project on `sanity.io`.
    
- In your Next.js project, run `npx sanity@latest init` and connect it to your Sanity project.
    
- Populate the `schemas` directory with the models defined in section 2.
    
- Populate the `lib/sanity.ts` file with your project ID and dataset details to create a Sanity client.
    

**Step 3: Component & Page Development**

1. **Homepage (`/app/page.tsx`):**
    
    - Fetch `careWorker` data for Simon.
        
    - Display his name, title, and a compelling headline.
        
    - Include a clear Call-to-Action (CTA) button linking to `/book-now`.
        
2. **Booking Page (`/app/book-now/page.tsx`):**
    
    - Fetch Simon's `googleCalendarUrl` from Sanity.
        
    - Use an `<iframe>` to embed the Google Calendar booking page. Make it responsive.
        
3. **NHIMA Facilities Guide (`/app/guides/nhima-facilities/page.tsx`):**
    
    - Use `groq` to fetch all `nhimaFacility` documents.
        
    - Render the data in a clean, searchable table or list.
        
    - Implement the filtering logic described in section 4.
        
4. **Medication Price Guide (`/app/guides/medication-prices/page.tsx`):**
    
    - Fetch all `medicationPriceGuide` documents.
        
    - Display in a table. Add a clear disclaimer that prices are estimates.
        

**Step 4: Metadata and SEO**

- For each page, use the `generateMetadata` function from Next.js to dynamically set titles and descriptions.
    
- **Example for the NHIMA page:**
    
    TypeScript
    
    ```
    import { Metadata } from 'next'
    
    export const metadata: Metadata = {
      title: 'NHIMA Accredited Facilities in Zambia | AfyaLink',
      description: 'Find a complete list of hospitals, clinics, and pharmacies in Lusaka, Kitwe, and across Zambia that accept NHIMA.',
    }
    ```
    

**Step 5: Deploy**

- Push the repository to GitHub.
    
- Connect the GitHub repository to a new project on Vercel.
    
- Add your Sanity environment variables to the Vercel project settings. The site will deploy automatically on every push.



These notes were something I need for augment to develop which it did

`__code`  C:\Users\<USER>\afyalink-zambia

