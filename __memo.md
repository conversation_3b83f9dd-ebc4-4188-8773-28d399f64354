Muchies Sundowners: Event & Strategy GuideThis document outlines the vision, marketing strategy, execution plan, and essential business recommendations for the "Sundowners at Muchies" event. The goal is to create a recurring, profitable, and community-building event that establishes Muchies Pub and Grill as a go-to Sunday destination.1. The Vision: "The Sunday Cure"This isn't just another Sunday special. This is the antidote to the "Sunday scaries" and the winter chill. It’s a curated experience to wind down the weekend and recharge for the week ahead. It's about creating a "third place"—not home, not work—where people feel they belong.The Vibe:Cozy: We are actively fighting the cold. This is a warm, inviting haven.Connected: A place for Lusaka's young professionals, creatives, and entrepreneurs to connect with friends and meet new people.Chill: Relaxed, no pressure. Good music, good food, and good company.2. Marketing Lingo & The "Hook"This is the language to use in all promotional materials.Event Title:Sundowners at Muchies: The Sunday CureKey Marketing Phrases:"Beat the winter chill with warm vibes and cool company.""Your new Sunday ritual is here. Unwind and connect at Muchies.""Don't let the weekend end. Extend the vibe at Lusaka's newest hidden gem.""We're bringing the heat this Sunday. Join us for classic board games, sizzling braai, and sunset beats from DJ <PERSON>."Addressing the Location:"Tucked away in a serene corner of the complex...""Find your new favorite spot, conveniently located near Pick n Pay.""Enjoy the peaceful sunset views from our terrace."3. The Customer Experience: Making Guests Feel SpecialThe goal is to make every guest feel like an insider who has discovered something special.The Welcome: The Aunt and Nephew (Stephen) should act as hosts. A personal "Welcome, we're so glad you could make it" is powerful.Tackling the Cold:Action: Procure braziers or small fire pits for warmth and ambiance.Action: Have a stack of simple, clean blankets available.Offer: A "Winter Warmer" drink special like a hot toddy or coffee with a kick.The Flow of the Evening:4:00 PM - 5:00 PM (The Golden Hour): Welcome guests. "Golden Hour Warmer" - free shots for ladies. Encourage games of Chasha.5:00 PM - 6:00 PM (The Social Hour): The braai should be sizzling. Promote the bucket raffle.6:00 PM - 7:00 PM (The Sunset Set): DJ TonyDollarSign plays. This is the musical main event.The Details:Free WiFi: Display the password clearly on tables.Games: Have Chasha, playing cards, and Jenga available to break the ice.4. Actionable 6-Day Marketing & Execution PlanDay 1 (Mon): Finalize PlanFinalize all details. Create a stylish digital flyer (Canva is great for this).Include: Date, Time, Address, Key Offers, DJ Name, and the crucial line: "Find us on Google Maps: Muchies Pub and Grill."Day 2 (Tues): Initial PromotionPost the flyer on personal and business WhatsApp statuses and Instagram/Facebook stories.Send directly to your core network.Day 3 (Wed): Widen the NetPost in relevant Lusaka-based Facebook groups.Ask your inner circle to share the flyer.Day 4 (Thur): Hyper-Local BlitzCrucial Step: Stephen must visit the neighboring businesses in the complex (salons, barbershop, stores).The Pitch: "Hi, I'm Stephen from Muchies next door. We're new and hosting a special Sundowner event this Sunday. As a neighbor, we'd love for you and your staff to join us—your first drink is on us." This builds crucial local goodwill.Day 5 (Fri): Build HypeRe-post on all channels: "The weekend is here! We've got the perfect way to end it. See you Sunday!"Post a picture of the Chasha board or the view from the terrace.Day 6 (Sat): Final ReminderPost a "See you tomorrow!" message with a countdown sticker.Confirm with DJ. Prep the venue, pallet furniture, and braai area.Day 7 (Sun): Event Day!Post "It's happening today!"During the event, post live stories and videos.Leverage Your Agency: Place small cards on tables with a QR code.Top: "Enjoying the vibe? Leave us a review on Google!" (links to Google Maps).Bottom: "Love our seamless menu? This digital menu is powered by [Your Agency Name]."At the exit, Stephen should personally thank guests: "Thanks for coming, we hope to see you next Sunday!"5. Talking to the Owners: The Pricing StrategyThis conversation is vital for their long-term success. Approach it as a supportive partner helping them build a sustainable business.The Issue:A bottle of Gordon's Gin (750ml) is K200. A single shot (25ml) is K35.A 750ml bottle contains 30 standard 25ml shots.If they sold all 30 shots individually at K35 each, they would make: 30 shots * K35/shot = K1,050.This means they are currently losing K850 in potential revenue for every bottle sold whole.How to Frame the Conversation:Start positively: "I've been looking at the menu to see how we can make this event as successful as possible, and I noticed something I think we can optimize to really boost your profits. Can we look at the Gordon's pricing together?"1. Break Down the Math (The "Shot Logic"):"Right now, a bottle sells for K200. A shot sells for K35.""There are 30 shots in a bottle. If you sell just 6 shots (200 / 35 = 5.7), you've already made back the price of the entire bottle. The remaining 24 shots are pure profit.""By selling the whole bottle for K200, you're essentially giving away 24 shots for free that you could have sold for K840 (24 * 35)."2. Explain Customer Psychology:"Customers who do this math in their head (and many do) will feel the pricing is illogical. It can make them question the fairness of other prices on the menu.""Fair, consistent pricing builds trust. When prices make sense, customers feel respected and are more likely to become regulars."3. Propose a Solution (The 3-Tier Pricing Model):Shot Price (The Base): K35. This is your highest margin item. Keep it. It's for customers who only want one drink.Bottle Price (The "Session" Price): This should offer a small discount for buying in bulk, but it should never be cheaper than the sum of its parts. A fair price would be between K600 - K800.K600 offers a great deal (a K450 saving over buying shots individually) but still makes great profit.K750 offers a more modest discount.Recommendation: Start at K650. It feels like a good deal and positions Muchies as having fair value.Cocktail Price (The "Value-Add"): A gin and tonic should be priced higher than a shot, but not excessively.Example: K35 (gin) + K10 (mixer cost) + K15 (profit/labor) = K60 per G&T. This makes sense and is profitable.The Key Takeaway for Them: "We want customers to feel they are getting a fair deal whether they buy one shot or a whole bottle. This new structure ensures that you are profitable on every sale and the customer feels the pricing is transparent and fair. This is how you build a loyal customer base that trusts you."

## Google Business Profile Management

- Rename the google Business profile management to pinpoint 
  
  ### **PinPoint Agency Overview**

#### **Who We Are?**

We are **PinPoint**, Lusaka's local visibility experts. In partnership with the creative professionals at **Investini**, we are your complete digital presence team.

Our mission is simple: We make local businesses in Lusaka visible to the thousands of customers searching for them online every day. Many great businesses (restaurants, mechanics, lodges, shops) are practically invisible on Google Maps and Search. When a potential customer is in Ibex Hill looking for a service, they pull out their phone, and if a business isn't on the map, it doesn't exist.

PinPoint handles the technical setup, data analytics, and web services, while our creative partner, Investini, manages all visual branding—from photography to professional design. Together, we bridge the gap between local businesses and their digital customers.

#### **Our Partnership in Action**

Our services are delivered through a seamless partnership with clearly defined roles:

- **PinPoint (Technical & Analytics - Your Role):** You will manage the technical Google Profile setup, verification, API integration, performance reporting, and website development services.
    
- **Investini (Creative & Branding - Partner's Role):** Your partner will manage all visual aspects, including photography, graphic design for posts, company profiles, business cards, and other visual branding materials.
    

#### **What We Offer?**

Our services are designed to take a client from being invisible to being a dominant player in their local area.

**Phase 1: Foundational Setup (The Free Offering)**

This is our "Get on the Map" service, designed for rapid client acquisition with zero friction for our first 100 clients.

- **Technical Setup (PinPoint):** Business Profile creation, verification, core data optimization (name, address, phone, map pin), and essential information entry.
    
- **Initial Visual Identity (Provided by Investini):**
    
    - **Starter Photography:** A professional photoshoot providing 3-5 essential photos (the storefront, the interior, a key product/service).
        
    - **Starter Graphic Design:** A professionally designed cover photo that establishes their brand.
        

**Phase 2: Monthly Management & Growth (The Paid Subscriptions)**

Once a business is on the map, we offer paid packages that generate recurring revenue for our partnership.

- **Services Offered in these packages:**
    
    - **Reputation Management (PinPoint):** Monitoring all customer reviews and providing response strategies.
        
    - **Content & Engagement (Joint Effort):**
        
        - **Investini** creates the graphics/selects photos for regular "Google Posts" (e.g., specials, events).
            
        - **PinPoint** publishes the posts and manages the Q&A section.
            
    - **Performance Reporting (PinPoint):** Providing simple, easy-to-understand monthly reports and giving actionable insights based on the data.
        
- **Pricing Tiers:**
    
    - **"Growth" Package:** Includes Reputation Monitoring, 2 Monthly Posts, and a Performance Snapshot.
        
        - **Standard Price:** **ZMW 2,500 / month**
            
        - **⭐ Introductory Offer (First 3 Months): ZMW 2,000 / month**
            
    - **"Visibility" Package (Most Recommended):** Includes all Growth services, plus full Review & Q&A management, 4-5 monthly posts, and monthly photo updates from Investini.
        
        - **Standard Price:** **ZMW 4,750 / month**
            
        - **⭐ Introductory Offer (First 3 Months): ZMW 3,750 / month**
            

**Phase 3: Premium Growth Services (High-Value Upsells)**

For clients who are seeing a great return and want to dominate their market, offering significant project-based revenue.

- **Services Offered:**
    
    - **Advanced Local SEO (PinPoint):** In-depth competitor analysis and profile optimization.
        
    - **Website Development (PinPoint):** Creating modern, professional websites for clients.
        
    - **Google Ads Management (PinPoint):** Creating and managing targeted local ad campaigns.
        
    - **Advanced Media & Print (Investini Upsell Opportunity):** Full-scale professional photoshoots, company profiles, business cards, posters, and other print materials.
        
- **Pricing:**
    
    - **"Dominance" Package:** Includes all Visibility services plus Google Ads management and a quarterly SEO audit.
        
        - **Standard Price:** **ZMW 8,000+ / month** (plus ad budget)
            
        - **⭐ Introductory Offer (First 3 Months): ZMW 6,500 / month** (plus ad budget)
            

**Phase 4: Strategic Consulting & Market Forecasting**

This is our most premium, high-value service for entrepreneurs, real estate developers, and established businesses looking to expand. We act as "digital meteorologists" for the local market.

- **Service Description:** We move beyond managing existing businesses to providing data-driven advice on new ventures. Using Google's data tools and our own network insights, we analyze search demand, competitive density, and consumer behavior patterns across Lusaka. We don't just put you on the map; we help you decide where to place the pin in the first place.
    
- **Deliverable:** A comprehensive **"Local Market Opportunity Report"** with a clear recommendation on a new business or location's viability.
    
- **Pricing:**
    
    - This is a **Project-Based Fee**, not a monthly subscription.
        
    - **Starts at ZMW 30,000** _(Approximately $1,200)_ per report, with the final price depending on the scope of the research.
## Dub avenue

- We re-recorded dollas and dianna louis (title changed to grattitude) with the change of the last line at the end of the verse for both songs
- The LABS library from Spitfire audio expired, which has kinda slowed things down, I need to either purchase it or something
- I need to get the compostion of dianna louis in order, maybe first focus on the mix bus
- We have no visual identity and that keeps alot of the music demos and their presentation poor 

## LGU 
- I need to prepare my December invoice for the school
- I need to inquire on my invoices for this previous semester 
- I need to go over the course curriculum for the Programming foundations (planning stage)

## __task.ai

This is a fairly new project where I have created a started. Its about creating a universal agent that helps organize tasks that I released on github yesterday. The github is `__online`: https://github.com/Mwimwii/__task.ai 

- I noticed that its very difficult to get the agent to navigate to the `__start.md` which misses the whole point of the convenience. I need to build some sort of front end or onboarding that helps the user build the `__start.md` and the `__memo.md` for the first time.
- Adding to the `__memo.md` is rather also difficult and becomes cumbersome to refresh all the time.
- Adding notes in general is kind tough, not for the lazy, there should some iterative reward for the user whenever they take notes 


## Lubundas wedding rehearsals

- My friend Lubunda is getting married on Aug 2 2025 and the rehearsals start today at 2pm to 5pm


## Personal addiction solving
- I love smoking up weed but sometimes I can become a bit too trigger happy with rolling a fat blunt first thing I do when I wake up.
- I dont need to be sedated every single time, it should be used sparingly, before this turns into an addiction, I need to show some restraint and actually plan some of these things nicely
- Theres a party thats gonna happen this evening and I have about a gram, I need to be able to compose my self till then, maybe smoke up after 1 or 2 deliverables of the day that require immediate attention like afya link and then I could smoke up

## Afyalink

-Excellent. This is exactly the kind of strategic consolidation we need. By combining all our previous discussions, we can create a single, unified blueprint for the MVP. This document will serve as our master plan.

Here is the consolidated strategy update, combining the content pillars with the detailed data points on NHIMA coverage and price history.

---

### **AfyaLink Zambia: The Unified MVP Content & SEO Blueprint**

**Our Core Mission:** To become Zambia's most trusted digital health resource by providing clear, direct, and comprehensive answers to the real-world questions patients are asking.

Our strategy is built on three interconnected content pillars, designed to capture user intent at every stage of their healthcare journey.

---

### **Pillar 1: Factual & Directory Content (The "Lists")**

**Purpose:** To provide structured, factual data that users can access quickly. This is the foundation of our site and will be powered by Programmatic SEO (pSEO) to create hundreds of specific, indexable pages.

**A. NHIMA-Accredited Facilities**

- **User Question:** "Which hospitals/clinics/pharmacies in my city accept NHIMA?"
    
- **URL Structure:** `/nhima/[city]/[facilityType]` (e.g., `/nhima/lusaka/hospitals`)
    
- **Content:** A generated page listing all relevant facilities for that category and location.
    

**B. Procedure Costs & NHIMA Coverage**

- **User Question:** "How much does an MRI cost in Lusaka, and will NHIMA cover it?"
    
- **URL Structure:** `/costs/[procedure-slug]/[city]` (e.g., `/costs/mri-scan/lusaka`)
    
- **Content:** A generated page listing facilities that perform the procedure. Each entry will clearly display:
    
    - The estimated cash price.
        
    - A highly visible **"NHIMA Status" badge** (e.g., "Covered," "Not Covered," "Co-payment Required").
        
    - A toggle switch to "Show only NHIMA-covered options."
        
    - Detailed `nhimaCoverageNotes` (e.g., "Pre-authorization required").
        

**C. Detailed Facility Profiles**

- **User Question:** "What services can I get at Coptic Hospital, and what do they cost?"
    
- **URL Structure:** `/facility/[facility-slug]` (e.g., `/facility/coptic-hospital-lusaka`)
    
- **Content:** A definitive profile page for a single facility, showing its address, contact info, and a comprehensive list of its procedures with both the cash price and NHIMA status listed side-by-side.
    

---

### **Pillar 2: Educational & Guide Content (The "Explainers")**

**Purpose:** To answer the foundational "what is" and "how-to" questions, establishing our authority and building deep trust with our audience.

- **Key Example:** **"The Ultimate Guide to NHIMA in Zambia"**
    
- **URL:** `/guides/what-is-nhima`
    
- **Content:** A single, comprehensive article covering everything a user needs to know.
    
- **Crucial Integration:** This guide will contain a new, detailed section titled **"Which Procedures and Services Does NHIMA Cover?"** This section will provide a general overview and then **internally link** directly to our Pillar 1 pages. For example: _"...for specific costs, you can see our list of NHIMA-covered MRI scans in Lusaka."_
    

---

### **Pillar 3: High-Intent Thematic Content (The "Hubs")**

**Purpose:** To capture users asking broad, complex, and often life-changing questions. These pages act as central hubs, organizing information and linking out to our more specific content.

- **Key Example:** **"A Guide to Cancer Diagnosis and Treatment in Zambia"**
    
- **URL:** `/guides/cancer-treatment-zambia`
    
- **Content:** This hub page will provide a holistic overview of the cancer treatment journey in Zambia.
    
- **Crucial Integration (The "Hub-and-Spoke" Model):** This page will systematically link to our Pillar 1 content, creating a powerful network of related information.
    
    - It will discuss diagnostic costs and link to `/costs/biopsy/lusaka`.
        
    - It will list treatment facilities and link to their `/facility/cancer-diseases-hospital` profiles.
        
    - It will discuss treatment costs and link to `/costs/chemotherapy-session/lusaka`, where users will instantly see the price _and_ NHIMA coverage status.
        
    - It will talk about insurance and link to our "Ultimate Guide to NHIMA."
        

---

### **Cross-Cutting Technical Solution: Handling Price Volatility**

To address the critical need to track price changes over time without over-engineering the MVP, we will enhance our Sanity schemas.

- **Method:** We will use a `priceHistory` field within our `procedureCost` schema.
    
- **Implementation:** When a price is updated, the new value goes into the `currentPrice` field, and the old price is added as an object `{date: 'YYYY-MM-DD', price: ZMW_XXXX}` to the `priceHistory` array.
    
- **Benefit:** This simple, robust solution allows us to display price trends on our pages, adding immense value and trustworthiness for users, and can be implemented immediately within Sanity.
    

### **Final, Unified Data Model for `procedureCost`**

This schema combines all our requirements into one powerful data model.

JavaScript

```
defineType({
  name: 'procedureCost',
  title: 'Procedure Cost',
  type: 'document',
  fields: [
    { name: 'procedureName', title: 'Procedure Name', type: 'string' },
    { name: 'slug', title: 'Slug', type: 'slug', options: { source: 'procedureName' } },
    { name: 'facility', title: 'Facility Name', type: 'reference', to: { type: 'facility' } },
    { name: 'city', title: 'City', type: 'string' },
    { name: 'currentPrice', title: 'Current Estimated Price (ZMW)', type: 'number' },
    { name: 'nhimaAccepted', title: 'Is NHIMA Accepted for this?', type: 'boolean', initialValue: false },
    { name: 'nhimaCoverageNotes', title: 'NHIMA Coverage Notes', type: 'text' },
    { name: 'priceHistory', title: 'Price History', type: 'array',
      of: [{
        type: 'object',
        fields: [
          { name: 'price', title: 'Price', type: 'number' },
          { name: 'date', title: 'Date Recorded', type: 'date' }
        ]
      }]
    },
    { name: 'lastUpdated', title: 'Last Updated', type: 'date' },
  ],
})
```

This unified strategy ensures that every piece of content we create works together. The pillars support each other, creating a content flywheel that will build authority, drive traffic, and, most importantly, provide unparalleled value to patients in Zambia. This is our blueprint.



   ### **B. AfyaLink Zambia: The SEO-First Style Guide**

1. Upgrade the project's dependencies to use the latest stable release of Next.js 15 and its dependencies (`react@rc`, `react-dom@rc`).
    
2. The primary benefit for us in Next.js 15 is the maturation of the **React Compiler**. It is enabled by default in Next.js 15. This compiler will automatically optimize our React components, reducing re-renders and improving load times, which is a significant win for both Core Web Vitals (SEO) and user experience.
    
3. Ensure our Tailwind CSS installation is on the latest version (`v4.x` if stable, otherwise latest `v3.x`) and configured for the Next.js App Router.
   

This guide synthesizes the best elements of the provided examples into a cohesive identity for AfyaLink.

#### **1. Design Philosophy**

- **SEO First (Clarity & Speed):** We will use high-contrast text, a logical heading hierarchy (`h1`, `h2`, etc.), and minimal, highly-optimized imagery. Pages must be lightweight and load instantly. Every visual element must be accessible and easily understood by search engine crawlers.
    
- **UX Second (Trust & Simplicity):** The user journey must be frictionless. We'll use a color palette that inspires trust and calmness. Navigation will be intuitive, and forms will be simple and easy to complete. We will build trust through transparency and professionalism.
    
- **UI Third (Clean & Professional):** The aesthetic is clean, modern, and welcoming. We will use ample whitespace to reduce cognitive load and focus the user's attention on key information and calls-to-action. We avoid distracting animations or overly trendy designs.
    

#### **2. Color Palette**

This palette combines the trustworthy green of `Senior Buddies`, the professional blue of `Caregiver`, and a warm, actionable accent for calls-to-action.

|Role|Color Name|HEX Code|Tailwind Class|Usage|
|---|---|---|---|---|
|**Primary**|Trust Green|`#2E7D32`|`primary`|Headings, icons, key brand elements.|
|**Secondary**|Professional Blue|`#1E40AF`|`secondary`|Secondary buttons, supportive text, links.|
|**Accent (CTA)**|Action Orange|`#F59E0B`|`accent-cta`|All primary call-to-action buttons ("Book Now").|
|**Background**|Light Neutral|`#F8FAFC`|`background`|Main page background color for a soft, off-white feel.|
|**Text**|Dark Slate|`#1E293B`|`text-primary`|Main body text for high readability.|
|**Text Muted**|Medium Slate|`#64748B`|`text-muted`|Secondary text, descriptions, placeholders.|
|**Feedback (Success)**|Success Green|`#16A34A`|`feedback-success`|Success messages, "NHIMA Covered" badges.|
|**Feedback (Warning)**|Warning Amber|`#FACC15`|`feedback-warning`|"Co-payment Required" badges, warnings.|
|**Feedback (Error)**|Error Red|`#DC2626`|`feedback-error`|Error messages, "Not Covered" badges.|

Export to Sheets

#### **3. Typography**

- **Font:** We will use **Inter** from Google Fonts. It's a highly readable, variable font that is excellent for both headings and body text, ensuring performance and consistency.
    
- **Headings (`<h1>`, `<h2>`, etc.):** Font `Inter`, semi-bold, using `text-primary` color.
    
- **Body Text (`<p>`):** Font `Inter`, regular weight, using `text-primary` color. Size `16px` or `18px` for optimal readability.
    

---

### **C. Tailwind CSS Configuration for AI Agent**

**File: `tailwind.config.ts`** Copy and use this configuration directly in the project.

TypeScript

```
import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: '#2E7D32', // Trust Green
        secondary: '#1E40AF', // Professional Blue
        'accent-cta': '#F59E0B', // Action Orange
        background: '#F8FAFC', // Light Neutral
        'text-primary': '#1E293B', // Dark Slate for text
        'text-muted': '#64748B', // Medium Slate for secondary text
        'feedback-success': '#16A34A', // Green for success, "Covered"
        'feedback-warning': '#FACC15', // Amber for warnings, "Co-payment"
        'feedback-error': '#DC2626',   // Red for errors, "Not Covered"
        border: '#CBD5E1', // For input borders, cards
      },
      fontFamily: {
        // Set Inter as the default sans-serif font
        sans: ['Inter', 'sans-serif'],
      },
      borderRadius: {
        'lg': '0.75rem',
        'md': '0.5rem',
        'sm': '0.25rem',
      },
      // Optional: Add some subtle box-shadows for cards
      boxShadow: {
        'card': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'), // Highly recommended for better form styling
  ],
}
export default config
```

---

### **D. Component Style Reference**

Here is how the Tailwind config should be applied to key components. This is a guide for building the UI Kit.

**1. Buttons:**

- **Primary CTA:**
    
    - **Classes:** `bg-accent-cta text-white font-bold py-3 px-6 rounded-md hover:bg-amber-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-cta`
        
- **Secondary:**
    
    - **Classes:** `bg-transparent border border-secondary text-secondary font-bold py-3 px-6 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary`
        

**2. Form Inputs:**

- **Classes:** `block w-full rounded-md border-border shadow-sm focus:border-primary focus:ring-primary sm:text-sm`
    

**3. Headings:**

- **`<h1>`:** `text-4xl md:text-5xl font-extrabold text-primary tracking-tight`
    
- **`<h2>`:** `text-3xl font-bold text-primary`
    
- **`<h3>`:** `text-xl font-semibold text-primary`
    

**4. NHIMA Status Badges:**

- **Covered:** `inline-flex items-center rounded-full bg-feedback-success/10 px-3 py-1 text-sm font-medium text-feedback-success`
    
- **Not Covered:** `inline-flex items-center rounded-full bg-feedback-error/10 px-3 py-1 text-sm font-medium text-feedback-error`
    
- **Co-payment:** `inline-flex items-center rounded-full bg-feedback-warning/10 px-3 py-1 text-sm font-medium text-yellow-800`
    

**5. Cards (for services, procedures, etc.):**

- **Classes:** `bg-white border border-border rounded-lg p-6 shadow-card`
    

### **E. Redesign and Implementation Notes**

- **Homepage:** We should adopt the structure from `Senior Buddies` and `Caregiver`. A clear hero section with a headline and a simple contact/booking form (or button), followed by sections for "Our Services" (with icons), "Why Choose Us" (with stats), and "Meet Simon."
    
- **Programmatic Pages:** The pages for procedures and facilities should be data-first. Use cards to list each item clearly. The primary focus is presenting the information (price, NHIMA status) effectively, not on heavy design.
    
- **The Dashboard (`Zendenta` inspiration):** For the future agency portal, we will use a more muted version of this style guide. The `secondary` blue will become the primary color, and the `feedback` colors will be used heavily for statuses, just like in the example