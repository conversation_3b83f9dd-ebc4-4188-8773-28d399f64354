
"smart_sources:Projects/Piecework/node_modules/@types/node/README.md": {"path":"Projects/Piecework/node_modules/@types/node/README.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03137083,-0.03222185,0.03898208,-0.04526724,0.04151364,-0.0053999,-0.10403695,-0.00845298,-0.02975121,-0.01752322,-0.02130768,-0.06335174,0.02891748,0.02610907,0.05356886,0.02911852,-0.02560533,0.03398921,-0.01247525,0.03945655,0.05754638,-0.02492175,0.01924194,-0.00503886,0.03810185,0.06084946,0.0168206,-0.01876557,0.04326236,-0.1919308,-0.03588494,0.03864859,-0.0307728,0.01634336,0.05988136,-0.02723376,-0.02231614,-0.04675276,-0.04265086,0.07177293,0.00814838,0.00821825,-0.00294596,-0.00372857,0.02327303,-0.00932107,-0.0092609,-0.03894164,-0.00811531,-0.08870915,-0.02846319,-0.05524834,0.00752021,0.00615414,0.02870225,0.04823121,0.05913139,0.07931683,0.04537158,0.05044968,0.04586077,0.00978799,-0.18535514,0.0277871,0.05965362,0.08251775,-0.06263442,-0.03520768,0.0262461,-0.0095328,-0.00377718,0.04506919,-0.07092708,0.06371216,-0.01148218,-0.04227248,-0.00068597,-0.02772756,-0.03671269,-0.00921316,-0.07221996,0.01998692,-0.02255086,0.01736842,-0.04431448,-0.01548513,0.00824495,-0.03699833,0.02208966,0.02138544,-0.02336464,-0.03072755,0.00621991,0.09443006,0.00364553,-0.01089178,0.05355436,-0.01483017,-0.02643782,0.14209495,-0.04027728,-0.01067803,0.02519604,-0.0093265,-0.01071222,-0.00748324,-0.05185765,-0.0905496,-0.03325041,-0.03445286,-0.03306368,0.00144703,-0.04478658,-0.09830085,0.00735371,-0.03404804,0.03056827,0.03048049,-0.00628306,0.06158064,0.01170932,0.08601205,0.06998204,-0.02677296,0.01083885,0.02725914,0.01276749,0.01316669,0.04077188,0.06831284,-0.00815395,0.10470418,-0.0372954,-0.00763421,0.00981231,0.00613334,-0.05534184,-0.0300395,-0.02215611,-0.01779174,0.03876277,-0.00028229,0.03352651,-0.03917729,-0.07522307,0.06068411,-0.05641761,0.09260625,-0.02994863,0.04193084,-0.04853448,0.08998399,-0.09626673,-0.03897147,0.00196302,-0.02609828,0.01619246,0.02050563,-0.08560488,0.00749682,0.03704828,-0.07462396,-0.00729557,0.08933049,0.0011519,-0.05739442,-0.00955947,0.04867091,0.02347173,-0.08670201,-0.01755634,0.05721295,-0.05423981,-0.06784936,0.05261246,-0.00471484,-0.04419158,-0.09491678,0.01711381,-0.01678998,0.00085116,-0.01394477,-0.04468346,0.01254744,0.01650193,-0.03476309,-0.02188832,-0.01581257,0.00724729,-0.00051113,0.00918404,0.02739294,0.02119895,0.00138305,-0.06341051,0.02494794,-0.03977612,-0.01640152,0.03746066,-0.04529338,0.05456872,0.03669493,0.00225397,0.03547211,-0.08896096,0.04967246,-0.00937014,0.00693951,0.04609143,0.02254018,-0.10419089,0.00282899,0.06461195,0.11090278,-0.04289889,-0.01004057,0.06068007,0.07419972,0.04295326,0.055945,-0.02265588,-0.03883398,-0.10819624,-0.17756158,-0.00573574,0.00961969,0.00082945,0.00397716,-0.0417062,0.00260749,-0.04608229,-0.05125335,0.07239918,0.12187877,-0.00198002,-0.01476567,-0.0144858,-0.0040509,0.02235388,-0.01473536,-0.04154838,-0.03731098,0.01845001,-0.0023631,-0.02712075,-0.08976529,-0.04550258,-0.00936995,-0.02009203,0.15773489,0.04338037,0.06958355,0.01348964,0.0226003,0.04540997,0.03492602,-0.15700832,-0.01101604,0.01669899,-0.05317428,0.02242666,0.04391846,0.00871996,-0.03693467,0.04221717,-0.01576139,-0.06067029,0.04657252,-0.0175111,-0.07078791,0.01280078,0.00553943,0.03610094,-0.03148408,0.02016025,0.0283874,0.02799211,-0.05835201,0.02309385,-0.07178345,-0.02846638,0.00924249,0.03287298,0.01728961,-0.02319349,0.00794291,-0.09254132,0.04773803,-0.00653889,0.01461003,0.01706275,0.0173378,-0.05195005,0.0162897,0.0800269,-0.00765965,-0.03575153,0.00221388,0.02388879,-0.03129951,0.0327025,0.0265764,0.00349247,0.00329141,-0.04868824,0.01614454,0.03851388,-0.01946354,0.04371925,-0.00524115,-0.03419859,0.02673674,0.02284197,-0.04677063,0.01092274,0.01482515,-0.0272603,0.09632708,-0.00206319,-0.2293199,0.03848694,0.05009362,-0.05999277,0.01670063,-0.03584437,0.00470676,-0.05215133,-0.04322034,-0.01459563,0.03550448,0.04743592,0.02340805,-0.01159179,-0.04000674,0.03262145,0.08023846,-0.00241665,0.0778961,-0.06017492,0.07269873,0.01715848,0.23661558,-0.02475194,0.00634824,0.04623559,-0.05444035,0.08368401,0.06674965,0.07224131,-0.01639911,0.03682991,0.10589048,0.00602123,-0.03455852,0.040885,0.04695243,0.05575067,0.08909908,0.01390296,-0.02914675,-0.0281324,-0.04449509,0.01797639,0.08208473,-0.09061413,-0.04656623,-0.09312687,0.00205275,0.01922973,-0.08071581,0.03069682,0.02225239,-0.01712806,0.04789577,0.00192481,0.02545631,0.01497672,-0.07692911,0.05190118,-0.019647,-0.03779422,0.03462773,0.04334688,0.02578001],"last_embed":{"hash":"qs5x0q","tokens":417}}},"last_read":{"hash":"qs5x0q","at":1751288838515},"class_name":"SmartSource","last_import":{"mtime":1751244530076,"size":2329,"at":1751288766382,"hash":"qs5x0q"},"blocks":{"#Installation":[1,3],"#Installation#{1}":[2,3],"#Summary":[4,6],"#Summary#{1}":[5,6],"#Details":[7,13],"#Details#{1}":[8,9],"#Details##Additional Details":[10,13],"#Details##Additional Details#{1}":[11,13],"#Credits":[14,16],"#Credits#{1}":[15,16]},"outlinks":[{"title":"undici-types","target":"https://npmjs.com/package/undici-types","line":12},{"title":"Anna Henningsen","target":"https://github.com/addaleax","line":15},{"title":"Klaus Meinhardt","target":"https://github.com/ajafff","line":15},{"title":"Alvis HT Tang","target":"https://github.com/alvis","line":15},{"title":"Yagiz Nizipli","target":"https://github.com/anonrig","line":15},{"title":"Thanik Bhongbhibhat","target":"https://github.com/bhongy","line":15},{"title":"Benjamin Toueg","target":"https://github.com/btoueg","line":15},{"title":"Marcin Kopacz","target":"https://github.com/chyzwar","line":15},{"title":"Deividas Bakanas","target":"https://github.com/DeividasBakanas","line":15},{"title":"Sebastian Silbermann","target":"https://github.com/eps1lon","line":15},{"title":"ExE Boss","target":"https://github.com/ExE-Boss","line":15},{"title":"Eugene Y. Q. Shen","target":"https://github.com/eyqs","line":15},{"title":"Nikita Galkin","target":"https://github.com/galkin","line":15},{"title":"Hannes Magnusson","target":"https://github.com/Hannes-Magnusson-CK","line":15},{"title":"Huw","target":"https://github.com/hoo29","line":15},{"title":"Lishude","target":"https://github.com/islishude","line":15},{"title":"Alberto Schiabel","target":"https://github.com/jkomyno","line":15},{"title":"Kelvin Jin","target":"https://github.com/kjin","line":15},{"title":"Kyle Uehlein","target":"https://github.com/kuehlein","line":15},{"title":"Linus Unnebäck","target":"https://github.com/LinusU","line":15},{"title":"Matteo Collina","target":"https://github.com/mcollina","line":15},{"title":"Microsoft TypeScript","target":"https://github.com/Microsoft","line":15},{"title":"Mohsen Azimi","target":"https://github.com/mohsen1","line":15},{"title":"Mariusz Wiktorczyk","target":"https://github.com/mwiktorczyk","line":15},{"title":"NodeJS Contributors","target":"https://github.com/NodeJS","line":15},{"title":"Parambir Singh","target":"https://github.com/parambirs","line":15},{"title":"Piotr Błażejewicz","target":"https://github.com/peterblazejewicz","line":15},{"title":"Ilia Baryshnikov","target":"https://github.com/qwelias","line":15},{"title":"Andrew Makarov","target":"https://github.com/r3nya","line":15},{"title":"René","target":"https://github.com/Renegade334","line":15},{"title":"Samuel Ainsworth","target":"https://github.com/samuela","line":15},{"title":"Dmitry Semigradsky","target":"https://github.com/Semigradsky","line":15},{"title":"Chigozirim C.","target":"https://github.com/smac89","line":15},{"title":"Thomas den Hollander","target":"https://github.com/ThomasdenH","line":15},{"title":"David Junger","target":"https://github.com/touffy","line":15},{"title":"Trivikram Kamat","target":"https://github.com/trivikr","line":15},{"title":"Victor Perin","target":"https://github.com/victorperin","line":15},{"title":"wafuwafu13","target":"https://github.com/wafuwafu13","line":15},{"title":"Wilco Bakker","target":"https://github.com/WilcoBakker","line":15},{"title":"wwwy3y3","target":"https://github.com/wwwy3y3","line":15},{"title":"Junxiao Shi","target":"https://github.com/yoursunny","line":15}],"last_embed":{"hash":"qs5x0q","at":1751288837663}},"smart_blocks:Projects/Piecework/node_modules/@types/node/README.md#Details": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03051525,-0.05561937,0.08149998,-0.05391503,0.07334707,-0.01662541,-0.10562177,-0.03697576,-0.04227548,0.02140921,0.00250654,-0.05043735,0.01167446,0.02002869,0.03173725,0.04289108,-0.0105094,0.01420372,-0.00177274,0.02521994,0.06109248,0.00247601,0.0307925,0.00881089,0.02281903,0.04515686,0.00603083,-0.00931093,0.03557888,-0.17873348,-0.03275506,0.01041896,-0.04290886,0.01588216,0.07654677,-0.03141242,-0.02231648,-0.02467718,-0.03526109,0.03773681,0.0361064,0.05143885,-0.00204378,-0.01535929,0.02905848,-0.02212052,0.00069903,-0.00597651,0.01742143,-0.08219175,-0.03486298,-0.06370014,0.0434473,0.0343119,0.03993437,0.07044259,0.08990068,0.03192519,0.0317748,0.07367714,0.02683152,0.04387223,-0.17293148,0.01792987,0.056737,0.06939527,-0.05724122,-0.03840023,0.0018421,-0.0247003,-0.01564195,0.02684161,-0.04564788,0.06957911,0.01188088,-0.06905349,0.00981928,-0.02839967,-0.04069687,-0.00777493,-0.11612228,0.00835543,-0.00917238,0.00575072,-0.04776906,-0.0283035,0.00480291,0.00862652,0.03602719,0.00311279,-0.02977859,-0.02436701,0.03147293,0.1007587,-0.03448182,0.01740657,0.0547171,0.01084392,0.00547554,0.13678114,-0.09860551,-0.02088972,0.01713192,-0.01479461,-0.01993309,0.03058124,-0.08193028,-0.07636801,-0.01989865,0.00412592,-0.01754976,0.00726101,-0.05819243,-0.0934859,-0.00820652,0.00638371,0.03528773,0.05116596,-0.0514342,0.05774627,0.02010819,0.05215039,0.05623747,-0.00858186,0.03008166,0.0263404,-0.00102411,-0.02669313,0.04840097,0.10464233,-0.00018014,0.11755816,-0.05434703,-0.01196123,-0.00000767,-0.00795677,-0.00945289,-0.06518659,-0.02430248,-0.01937044,-0.01285455,-0.032235,0.01302669,-0.0561608,-0.04108621,0.05186078,-0.03080353,0.04878826,-0.00268012,0.015382,-0.06720267,0.0674061,-0.07232752,-0.02768299,0.00834125,-0.04803733,0.01259066,0.00971645,-0.07196418,0.02259873,0.04207807,-0.04808306,-0.01681045,0.0969852,0.02507639,-0.08827969,-0.02340135,0.0430901,0.04633803,-0.08362689,-0.01935915,0.04167153,0.01468868,-0.03361071,0.05635706,0.0109463,-0.0258829,-0.05688156,-0.00462109,-0.01583582,0.0123925,-0.02407026,-0.01256838,0.06394829,-0.01346427,-0.02183696,0.01298493,-0.01879622,0.01854666,0.02842047,0.03105816,0.00909373,-0.01871394,-0.00308467,-0.01850267,0.01112097,-0.03245861,-0.02124506,0.01199111,-0.04675187,0.08207955,0.05305918,-0.00838945,0.06552906,-0.10066032,0.06309904,-0.02625228,0.01821584,0.04664946,0.03944103,-0.08015946,-0.0182829,0.06358258,0.10931262,-0.07100189,-0.02956532,0.04660184,0.08348197,0.03572466,0.06183012,-0.05638405,-0.0390438,-0.07829706,-0.22704382,-0.00840474,0.01152708,-0.03186233,-0.03122235,0.00109201,0.0098653,-0.02718148,-0.01083127,0.09212317,0.13131662,0.01333534,-0.02909222,-0.01772014,-0.01204233,0.01033571,-0.04460351,-0.03486175,-0.05355254,-0.00374395,0.00629042,-0.03475107,-0.1075951,-0.04692233,0.01030988,-0.04359643,0.14843868,0.04602265,0.04011809,-0.01681255,0.01831328,0.04328956,0.02286547,-0.15943103,-0.01596536,0.02194192,-0.03741757,0.03128193,0.06601114,0.02513199,-0.01735258,0.00634978,-0.02054498,-0.07802733,0.03369549,0.00997067,-0.03785785,-0.00702501,-0.00906524,0.04740766,-0.01002952,0.02987369,0.03269471,0.07420649,-0.03667698,0.01318428,-0.06776816,-0.01492932,0.03330391,0.02755631,0.01942059,-0.00567962,-0.02813262,-0.06632023,-0.00507658,0.0023588,-0.0204454,0.0110539,0.01123617,-0.05814238,0.02527778,0.06257369,-0.00180448,-0.04388051,0.0149984,0.03180216,-0.0268103,0.03425068,0.03311934,0.0218273,0.01253873,-0.01553785,0.03009202,0.00103085,0.00286187,0.0282854,-0.03808044,-0.04403064,0.0361132,-0.01581927,-0.06300537,-0.00850253,-0.00335581,-0.03343364,0.14469171,0.01324997,-0.21943636,0.01540866,0.06066318,-0.0488005,-0.01704486,-0.03280688,0.04351447,-0.02344867,-0.02806609,-0.01122366,0.02666274,0.0227928,0.00416415,-0.01927767,-0.0410526,0.0569861,0.07675704,-0.00641171,0.08698599,-0.08032113,0.06586976,0.00884098,0.23373184,-0.00253491,-0.00976998,0.04525401,-0.06372999,0.05478268,0.04973671,0.0715378,0.01049321,0.03138883,0.09754372,0.01497155,-0.0451975,0.02354599,0.05123463,0.05456604,0.03448084,0.00850979,-0.02916856,-0.00754278,-0.03606338,-0.0192506,0.0742712,-0.06009272,-0.06047365,-0.10598882,0.00453347,-0.01552882,-0.0575229,0.01872208,0.04823248,-0.0144891,0.01549697,-0.01935893,0.01861247,0.01146826,-0.07890149,0.01446815,0.0049994,-0.01681898,0.0128752,0.03884018,0.01665235],"last_embed":{"hash":"1hbt4db","tokens":100}}},"text":null,"length":0,"last_read":{"hash":"1hbt4db","at":1751288838048},"key":"Projects/Piecework/node_modules/@types/node/README.md#Details","lines":[7,13],"size":260,"outlinks":[{"title":"undici-types","target":"https://npmjs.com/package/undici-types","line":6}],"class_name":"SmartBlock","last_embed":{"hash":"1hbt4db","at":1751288838048}},
"smart_blocks:Projects/Piecework/node_modules/@types/node/README.md#Credits": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02754738,-0.03244139,0.02596825,-0.0283911,0.01375002,-0.03566753,-0.05711002,-0.00340818,-0.034052,-0.0270593,0.00106595,-0.06617762,0.01117718,0.04139642,0.03558309,-0.01293895,-0.00177285,0.03826023,-0.01144036,0.02052064,0.065207,-0.01632165,0.04013444,0.01393304,0.04996569,0.08750772,0.00120334,-0.0249191,-0.00173901,-0.18454464,-0.03034857,0.04090377,-0.01784062,0.03824123,0.08183838,-0.02697043,0.00176962,-0.02253908,-0.069553,0.0698078,0.02489282,0.01904854,-0.00351073,0.02745971,-0.00466463,-0.01639897,-0.01921883,-0.03929309,-0.01331078,-0.0620072,-0.06314392,-0.04220113,0.02822261,-0.00228781,0.01598397,0.05852574,0.00945602,0.06798706,0.03451535,0.02378196,0.05696714,0.02639186,-0.20059717,0.05370963,0.06817784,0.047012,-0.05654035,-0.04544776,0.00380208,0.02852814,0.03218861,0.01649612,-0.04305618,0.05780746,0.00107992,-0.03093618,-0.00867566,-0.02680012,-0.0347997,-0.03166167,-0.08894987,0.02445551,-0.0036687,0.01055847,-0.0489653,0.02377288,0.04532275,-0.01460171,0.05706186,0.03704366,-0.04573715,-0.08137204,0.02702986,0.08737729,0.02510482,-0.03730632,0.02857079,0.02366569,-0.00255269,0.13312732,-0.03487862,0.02366609,0.01441676,0.01382806,0.00672169,0.01353509,0.00201643,-0.06072723,-0.02849682,-0.00915054,-0.03509931,-0.00188708,-0.05925336,-0.09002254,0.0081849,-0.02544994,0.02965716,0.02876971,0.00516167,0.09665145,0.00262011,0.0937437,0.01315384,-0.02567843,0.00946195,-0.00233425,0.05233572,0.03331208,0.0289959,0.07595224,-0.01453581,0.11693062,-0.05765345,-0.01594312,0.01551209,-0.01331448,-0.0234075,-0.05533367,-0.01091022,-0.04444797,0.03857986,-0.01162049,0.02700564,-0.03909822,-0.057368,0.07255541,-0.0603605,0.0576032,-0.03189419,0.04918161,-0.00388288,0.06630845,-0.06736352,-0.02048203,-0.00021283,-0.04326991,0.04116663,0.01615318,-0.06031516,0.00567665,0.04483634,-0.05330141,-0.04623019,0.07435768,0.01845953,-0.05595198,-0.01382191,0.085793,0.00653458,-0.08478556,-0.02055711,0.03219481,-0.02839454,-0.06353705,0.04734534,0.00931029,-0.08081668,-0.08394298,0.0197963,0.00718247,-0.01050245,-0.03464915,-0.05642997,-0.00485486,0.00615626,-0.02256144,0.01265402,-0.04667379,0.00696168,0.02326529,0.02000282,0.01828062,0.00953413,0.05589918,-0.06089668,-0.01158359,-0.04823249,0.0128366,0.01158958,-0.05218653,0.04218955,0.04876165,-0.04022742,0.08208168,-0.06056506,0.02302359,-0.03151095,-0.01169208,0.02461544,-0.00499147,-0.08865318,-0.0031018,0.02167367,0.07250521,-0.03157103,-0.03951016,0.03096149,0.04949816,0.01404025,0.0391201,-0.04286254,-0.05687874,-0.12409466,-0.19984493,0.00399282,0.03778948,-0.01822703,0.01149674,-0.03080202,-0.02431479,-0.02573408,-0.05656666,0.05963987,0.13019353,0.00406121,-0.01356492,-0.0635284,-0.00547774,0.02716913,-0.04367463,-0.05545507,-0.03855446,0.00362986,0.02722882,-0.01705008,-0.09759621,-0.03479241,-0.01185062,0.01288546,0.14850529,0.04914719,0.04524795,0.04201064,0.05373358,0.0126136,0.0848748,-0.11709675,-0.03675481,0.05663659,-0.00249042,0.01708369,0.03477166,-0.02061277,-0.04035639,0.02952995,-0.03141828,-0.0589885,0.02979861,-0.01856353,-0.08059921,-0.00326727,0.03616887,0.06025094,-0.02157373,0.02197236,0.00447884,0.06454108,-0.06760132,0.03718212,-0.0677602,-0.04503281,-0.03463977,0.03199479,0.008905,-0.00965918,0.01459751,-0.07432763,0.08354971,0.02805343,0.01622088,-0.00202185,0.00621549,-0.02803516,0.01628832,0.08992381,-0.01157344,-0.03306796,0.00495805,-0.00069122,-0.01182689,0.0031856,0.01060867,0.01157645,0.01864919,-0.04522142,0.01656017,0.04243854,-0.02340551,0.04269072,-0.01419562,-0.03674196,0.03253484,-0.0046501,-0.04544048,0.03039032,-0.02374422,0.01320643,0.0626464,-0.01832196,-0.24334003,0.03831138,0.02508805,-0.04562277,-0.01086812,-0.02154186,0.00312266,-0.07225151,-0.03192806,-0.00582122,0.05669403,0.06006743,0.00160992,-0.02630412,-0.05254579,0.01900592,0.07783393,-0.01399481,0.070482,-0.00742857,0.05698528,0.01399014,0.26703048,-0.06138334,0.02883134,0.04095049,-0.04929743,0.05408955,0.07228845,0.08036423,0.02216403,0.01789516,0.10716989,-0.00051769,-0.03658587,0.04326711,0.03316332,0.01466215,0.08311227,0.0062669,-0.02159641,-0.01693917,-0.05272787,0.00323494,0.10162564,-0.04769742,-0.08182288,-0.11543991,-0.0033341,0.06139077,-0.06418475,0.0306799,-0.00090171,0.03258768,0.02291514,0.01006519,0.02602796,-0.00599979,-0.06447215,0.03984073,-0.03425315,-0.03633405,0.03474985,0.05205741,0.00570771],"last_embed":{"hash":"18kgxon","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"18kgxon","at":1751288838128},"key":"Projects/Piecework/node_modules/@types/node/README.md#Credits","lines":[14,16],"size":1925,"outlinks":[{"title":"Anna Henningsen","target":"https://github.com/addaleax","line":2},{"title":"Klaus Meinhardt","target":"https://github.com/ajafff","line":2},{"title":"Alvis HT Tang","target":"https://github.com/alvis","line":2},{"title":"Yagiz Nizipli","target":"https://github.com/anonrig","line":2},{"title":"Thanik Bhongbhibhat","target":"https://github.com/bhongy","line":2},{"title":"Benjamin Toueg","target":"https://github.com/btoueg","line":2},{"title":"Marcin Kopacz","target":"https://github.com/chyzwar","line":2},{"title":"Deividas Bakanas","target":"https://github.com/DeividasBakanas","line":2},{"title":"Sebastian Silbermann","target":"https://github.com/eps1lon","line":2},{"title":"ExE Boss","target":"https://github.com/ExE-Boss","line":2},{"title":"Eugene Y. Q. Shen","target":"https://github.com/eyqs","line":2},{"title":"Nikita Galkin","target":"https://github.com/galkin","line":2},{"title":"Hannes Magnusson","target":"https://github.com/Hannes-Magnusson-CK","line":2},{"title":"Huw","target":"https://github.com/hoo29","line":2},{"title":"Lishude","target":"https://github.com/islishude","line":2},{"title":"Alberto Schiabel","target":"https://github.com/jkomyno","line":2},{"title":"Kelvin Jin","target":"https://github.com/kjin","line":2},{"title":"Kyle Uehlein","target":"https://github.com/kuehlein","line":2},{"title":"Linus Unnebäck","target":"https://github.com/LinusU","line":2},{"title":"Matteo Collina","target":"https://github.com/mcollina","line":2},{"title":"Microsoft TypeScript","target":"https://github.com/Microsoft","line":2},{"title":"Mohsen Azimi","target":"https://github.com/mohsen1","line":2},{"title":"Mariusz Wiktorczyk","target":"https://github.com/mwiktorczyk","line":2},{"title":"NodeJS Contributors","target":"https://github.com/NodeJS","line":2},{"title":"Parambir Singh","target":"https://github.com/parambirs","line":2},{"title":"Piotr Błażejewicz","target":"https://github.com/peterblazejewicz","line":2},{"title":"Ilia Baryshnikov","target":"https://github.com/qwelias","line":2},{"title":"Andrew Makarov","target":"https://github.com/r3nya","line":2},{"title":"René","target":"https://github.com/Renegade334","line":2},{"title":"Samuel Ainsworth","target":"https://github.com/samuela","line":2},{"title":"Dmitry Semigradsky","target":"https://github.com/Semigradsky","line":2},{"title":"Chigozirim C.","target":"https://github.com/smac89","line":2},{"title":"Thomas den Hollander","target":"https://github.com/ThomasdenH","line":2},{"title":"David Junger","target":"https://github.com/touffy","line":2},{"title":"Trivikram Kamat","target":"https://github.com/trivikr","line":2},{"title":"Victor Perin","target":"https://github.com/victorperin","line":2},{"title":"wafuwafu13","target":"https://github.com/wafuwafu13","line":2},{"title":"Wilco Bakker","target":"https://github.com/WilcoBakker","line":2},{"title":"wwwy3y3","target":"https://github.com/wwwy3y3","line":2},{"title":"Junxiao Shi","target":"https://github.com/yoursunny","line":2}],"class_name":"SmartBlock","last_embed":{"hash":"18kgxon","at":1751288838128}},
"smart_blocks:Projects/Piecework/node_modules/@types/node/README.md#Credits#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02887935,-0.03512058,0.01978325,-0.03267575,0.01168847,-0.02767576,-0.05749412,-0.0011659,-0.02767504,-0.02815903,-0.00387905,-0.06289344,0.00477807,0.03790714,0.02452891,-0.01480453,-0.01196253,0.04768548,-0.01855097,0.01825926,0.0622682,-0.02849528,0.03864843,0.00862462,0.05866996,0.09518703,-0.00226772,-0.0242444,0.00278569,-0.17960447,-0.03216032,0.04397143,-0.00701171,0.03331457,0.0855225,-0.03316171,-0.00466528,-0.01401239,-0.07167479,0.06236784,0.0276321,0.01248537,-0.00395197,0.02531343,-0.00805567,-0.01786309,-0.0309382,-0.04117716,-0.00923935,-0.05814526,-0.05287939,-0.0508222,0.02154048,0.00392459,0.01248451,0.04512167,0.00603325,0.06798455,0.03778255,0.03784833,0.05390451,0.01887307,-0.20572673,0.0522048,0.06746169,0.05019332,-0.05578354,-0.04012125,0.00322052,0.04076969,0.02296427,0.01649562,-0.04057292,0.05337367,0.00110836,-0.03557588,-0.00794493,-0.02806037,-0.0338161,-0.0297861,-0.08354224,0.01458112,-0.01126486,0.01856528,-0.0471208,0.02336924,0.04813175,-0.01980528,0.05366805,0.04160511,-0.04105954,-0.08368493,0.02530736,0.08166212,0.02355061,-0.03923645,0.0212091,0.02281877,-0.00467489,0.13855256,-0.03441385,0.01741278,0.019757,0.01381568,0.00996251,0.01829606,0.00294724,-0.06058481,-0.02871751,-0.00637365,-0.03548659,0.01123464,-0.06463505,-0.09870168,0.00647379,-0.0319775,0.0222581,0.02191081,0.00297293,0.09090854,-0.00248501,0.09092834,0.01147541,-0.02990273,0.00730245,-0.00052772,0.04267633,0.03176164,0.03458355,0.0743449,-0.01233877,0.11554137,-0.04842308,-0.01167571,0.01526524,-0.01698196,-0.01422753,-0.03881351,-0.00443371,-0.03434778,0.04490924,-0.0081673,0.02404131,-0.03420784,-0.0546937,0.07728045,-0.0664376,0.06046843,-0.02409232,0.05115951,-0.01033065,0.06494473,-0.06750037,-0.02591213,0.00691699,-0.03469164,0.03361306,0.01967058,-0.05674503,-0.00398598,0.05122672,-0.05497606,-0.05029966,0.08906949,0.02043613,-0.05958064,-0.00085768,0.0924168,0.01235725,-0.07921392,-0.03630622,0.02702024,-0.03447127,-0.0690967,0.04993863,0.00184589,-0.08005809,-0.07040922,0.02530288,0.01799261,-0.01567473,-0.02831336,-0.06535666,-0.01353762,0.01138928,-0.03018979,0.00740845,-0.03863685,-0.00396446,0.02283086,0.00544242,0.02045356,0.01412024,0.05641078,-0.05199806,-0.01240411,-0.04218983,0.00920482,0.00644488,-0.04019143,0.04017115,0.04929977,-0.0328203,0.08451753,-0.0576345,0.01733189,-0.02797536,-0.01039778,0.01978,-0.00595324,-0.09412171,0.00019076,0.01869975,0.07559091,-0.02938246,-0.03460027,0.02316068,0.04441135,0.01037147,0.04366355,-0.04944196,-0.05306877,-0.11671612,-0.19085434,0.00405819,0.04163809,-0.02165027,0.0169512,-0.04273629,-0.0257762,-0.03005161,-0.06954869,0.05546338,0.1298423,0.00381749,-0.0023046,-0.06594957,-0.00465762,0.02088303,-0.03330759,-0.06319745,-0.02807831,0.00728432,0.02595284,-0.02016261,-0.09447103,-0.0304744,-0.01709771,0.01850111,0.15342999,0.05039242,0.03937636,0.05029697,0.05169082,0.01382236,0.08306992,-0.11795295,-0.03295931,0.0510289,-0.01075146,0.00353965,0.04090824,-0.02377799,-0.04329326,0.03320243,-0.03585758,-0.05098123,0.02910392,-0.01759944,-0.08267901,-0.00395902,0.04221702,0.05755914,-0.0274749,0.02442996,0.00314867,0.05953242,-0.07259144,0.03353773,-0.06877416,-0.04277397,-0.03691445,0.03874495,0.00917689,-0.01872835,0.01884183,-0.06580225,0.09118754,0.02912669,0.02623641,-0.00727971,0.0070774,-0.02485629,0.01921978,0.09758403,-0.00892029,-0.03942888,0.01488556,-0.00646894,-0.01352108,0.00329398,0.01622179,0.00625061,0.0164164,-0.05278886,0.01620742,0.04293877,-0.01806859,0.04548065,0.00282116,-0.04192906,0.03867214,-0.01020959,-0.0502883,0.03131177,-0.01912102,0.02655656,0.0658359,-0.01514189,-0.24719417,0.03313407,0.02949293,-0.05492265,-0.00853395,-0.01985377,0.00686848,-0.07242061,-0.03578341,-0.00208203,0.05451787,0.06598981,0.01137072,-0.0198347,-0.04918333,0.01327048,0.08611394,-0.01874325,0.07832282,0.00393782,0.05668767,0.01675738,0.26471564,-0.0663666,0.03364553,0.03278929,-0.05399257,0.05438533,0.06788453,0.07289203,0.0241011,0.01369021,0.10387801,0.00906534,-0.03953696,0.04669001,0.0291226,0.00366098,0.07842308,0.01171628,-0.03097097,-0.03147253,-0.05002655,-0.00038201,0.1033842,-0.03717245,-0.07957955,-0.11530111,-0.0039192,0.05418934,-0.06354713,0.0345002,-0.00721047,0.02598321,0.01781849,0.01534779,0.02420656,-0.00118992,-0.06928064,0.03754796,-0.03369931,-0.02890742,0.0353761,0.04909031,0.00540891],"last_embed":{"hash":"1ar6u4e","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"1ar6u4e","at":1751288838515},"key":"Projects/Piecework/node_modules/@types/node/README.md#Credits#{1}","lines":[15,16],"size":1914,"outlinks":[{"title":"Anna Henningsen","target":"https://github.com/addaleax","line":1},{"title":"Klaus Meinhardt","target":"https://github.com/ajafff","line":1},{"title":"Alvis HT Tang","target":"https://github.com/alvis","line":1},{"title":"Yagiz Nizipli","target":"https://github.com/anonrig","line":1},{"title":"Thanik Bhongbhibhat","target":"https://github.com/bhongy","line":1},{"title":"Benjamin Toueg","target":"https://github.com/btoueg","line":1},{"title":"Marcin Kopacz","target":"https://github.com/chyzwar","line":1},{"title":"Deividas Bakanas","target":"https://github.com/DeividasBakanas","line":1},{"title":"Sebastian Silbermann","target":"https://github.com/eps1lon","line":1},{"title":"ExE Boss","target":"https://github.com/ExE-Boss","line":1},{"title":"Eugene Y. Q. Shen","target":"https://github.com/eyqs","line":1},{"title":"Nikita Galkin","target":"https://github.com/galkin","line":1},{"title":"Hannes Magnusson","target":"https://github.com/Hannes-Magnusson-CK","line":1},{"title":"Huw","target":"https://github.com/hoo29","line":1},{"title":"Lishude","target":"https://github.com/islishude","line":1},{"title":"Alberto Schiabel","target":"https://github.com/jkomyno","line":1},{"title":"Kelvin Jin","target":"https://github.com/kjin","line":1},{"title":"Kyle Uehlein","target":"https://github.com/kuehlein","line":1},{"title":"Linus Unnebäck","target":"https://github.com/LinusU","line":1},{"title":"Matteo Collina","target":"https://github.com/mcollina","line":1},{"title":"Microsoft TypeScript","target":"https://github.com/Microsoft","line":1},{"title":"Mohsen Azimi","target":"https://github.com/mohsen1","line":1},{"title":"Mariusz Wiktorczyk","target":"https://github.com/mwiktorczyk","line":1},{"title":"NodeJS Contributors","target":"https://github.com/NodeJS","line":1},{"title":"Parambir Singh","target":"https://github.com/parambirs","line":1},{"title":"Piotr Błażejewicz","target":"https://github.com/peterblazejewicz","line":1},{"title":"Ilia Baryshnikov","target":"https://github.com/qwelias","line":1},{"title":"Andrew Makarov","target":"https://github.com/r3nya","line":1},{"title":"René","target":"https://github.com/Renegade334","line":1},{"title":"Samuel Ainsworth","target":"https://github.com/samuela","line":1},{"title":"Dmitry Semigradsky","target":"https://github.com/Semigradsky","line":1},{"title":"Chigozirim C.","target":"https://github.com/smac89","line":1},{"title":"Thomas den Hollander","target":"https://github.com/ThomasdenH","line":1},{"title":"David Junger","target":"https://github.com/touffy","line":1},{"title":"Trivikram Kamat","target":"https://github.com/trivikr","line":1},{"title":"Victor Perin","target":"https://github.com/victorperin","line":1},{"title":"wafuwafu13","target":"https://github.com/wafuwafu13","line":1},{"title":"Wilco Bakker","target":"https://github.com/WilcoBakker","line":1},{"title":"wwwy3y3","target":"https://github.com/wwwy3y3","line":1},{"title":"Junxiao Shi","target":"https://github.com/yoursunny","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1ar6u4e","at":1751288838515}},
