
"smart_sources:Projects/Piecework/node_modules/react/README.md": {"path":"Projects/Piecework/node_modules/react/README.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0233954,-0.03714272,0.04591654,-0.00070453,-0.00498766,-0.02097605,-0.02758242,0.03633837,-0.00161465,0.02161357,0.01261331,-0.02533048,0.02430257,0.08458421,-0.02642567,0.00360533,-0.04684012,0.00597569,0.00916529,0.02142327,0.06436763,-0.00480117,0.011415,-0.03904837,0.00674694,0.07992067,-0.0228602,0.00215157,0.03171908,-0.16949232,-0.04709725,0.02434917,-0.0012529,0.04429159,-0.00763045,-0.02313923,0.01985666,-0.01850748,-0.0262534,0.06362653,-0.05533893,0.08780413,-0.02370963,-0.03689795,-0.02206715,0.0144732,-0.05272067,-0.03376158,-0.01951612,0.01882004,-0.04977739,-0.0272122,0.05757767,-0.02521667,0.05322376,0.08392941,0.02504923,0.06596578,-0.00794,0.03090094,0.04253805,0.00783053,-0.14919992,0.07453607,0.11572754,0.06041263,-0.06895787,-0.02398067,0.01971874,-0.00698982,-0.02956893,0.03678351,0.01537109,0.06045713,0.0430759,-0.06457672,-0.00166,-0.01648776,0.04363292,-0.03750211,-0.0671543,-0.04479585,-0.0567845,-0.00771121,0.01237574,-0.01949731,0.05608145,-0.01441267,0.07939824,0.06256215,-0.05488652,-0.04847674,0.01909451,0.06090263,-0.01581835,0.04770552,0.0494042,0.0481744,-0.02086195,0.14770445,-0.06074819,-0.00319949,-0.00644891,-0.00217741,-0.03129473,0.00808359,-0.03304785,-0.03394193,-0.01299266,0.00459886,-0.02249524,-0.01073257,-0.09343667,-0.07100547,0.0059519,-0.02826544,-0.00960029,0.02550556,-0.00301807,0.03782366,0.08352079,0.06740228,0.01204775,-0.02118964,0.00581344,0.01461434,0.07399216,0.01727241,-0.02274309,0.10472498,-0.00043919,0.0633967,-0.04045611,0.00040273,0.02754917,0.04389745,-0.01945313,-0.03395277,0.01108539,0.06614873,0.01764556,-0.02633392,-0.008437,-0.06281032,0.00846129,0.02880864,-0.03595234,0.08292744,-0.04566332,0.01917826,-0.00513269,0.06759306,-0.08927293,-0.03612926,-0.0496427,-0.07354066,-0.02945328,-0.05848785,-0.04767516,-0.00908715,-0.01837607,0.00484973,-0.00082128,-0.00474646,-0.02965364,-0.07521034,-0.03802322,0.09231244,0.04000848,-0.09706803,-0.04358613,0.03722308,0.00311473,-0.05628959,0.05879277,0.01745667,-0.03848571,0.00234424,0.02053347,0.02300528,0.05333024,-0.06081358,-0.06333713,0.00959206,0.00270289,-0.04627477,0.02727747,-0.02464334,0.02496828,0.03142936,0.0069797,-0.00089315,-0.03872982,0.03585022,-0.01213662,0.02299323,-0.04464,-0.0548848,0.02970833,-0.00002931,0.11110896,0.07680818,-0.01754256,0.05253931,-0.04781032,0.06339201,-0.00810755,0.01387665,0.0324343,-0.00909905,-0.11932947,-0.01233029,0.05836317,0.08181513,-0.05260334,-0.06357297,0.05505039,0.08781117,0.04961235,0.04267402,0.01212479,-0.00657323,-0.07084649,-0.20975108,0.0530538,0.02671446,-0.01422039,-0.06950909,-0.03040356,0.01257625,-0.02660206,-0.02809072,0.05140625,0.11623821,0.01269898,-0.00579618,-0.00858047,-0.03025227,-0.06156098,-0.01730924,-0.0440782,-0.08502087,-0.01165361,-0.03541898,-0.04386114,-0.03149929,-0.10391258,0.04667335,-0.0404614,0.14321695,0.04772872,0.0020108,-0.02401663,0.03753566,-0.00304752,0.01528545,-0.0950961,-0.031967,0.02952026,0.02491932,0.01118924,0.03778346,-0.00146905,-0.03578165,-0.08048965,0.00228214,-0.03472931,0.04176152,-0.03313038,-0.0763155,-0.0394944,-0.01844973,0.04364705,0.03916625,-0.01085827,0.03607697,0.08072487,-0.07050672,0.02253122,-0.00084487,-0.01375993,-0.01142165,0.00005013,0.00561665,0.03096602,0.00555244,-0.04236592,0.03164348,0.05885075,0.02754982,-0.03165087,0.00137027,-0.02082543,-0.02792113,0.10780914,0.00074876,0.0161354,-0.03233674,-0.03544227,-0.02042283,0.01106192,0.0503356,0.03318097,0.01115001,0.00271824,0.04393532,0.01852453,-0.00960729,-0.0123957,0.04865243,-0.06468827,-0.00822043,-0.02630058,-0.03214484,0.04452759,-0.01885617,-0.04255241,0.04540583,-0.00152263,-0.24279073,-0.00855483,0.02698753,-0.03715659,0.01296276,-0.03783727,0.03619098,-0.02161053,-0.03962735,-0.01369234,-0.01572399,0.04316998,-0.02993355,-0.00542323,0.05161206,0.03691384,0.09179667,0.03500132,0.07534309,-0.10860495,0.0568094,-0.00310668,0.24425314,-0.00367151,0.00177871,0.09188323,-0.02440665,0.0311552,0.07973674,0.09276958,-0.05026772,0.00605374,0.08961359,0.0048925,-0.07504658,-0.00560513,0.04571406,0.00463196,0.03862634,0.01268034,-0.00989568,0.00774282,0.01699118,0.01828035,0.06266871,-0.11282764,-0.07955588,-0.12500864,-0.01429607,-0.00624294,-0.04203949,0.01212546,0.0331347,-0.03324056,-0.01415366,0.02562088,-0.00281167,-0.01452089,-0.07243338,-0.00551489,0.03897892,-0.01813193,0.06340592,0.07557792,-0.01185434],"last_embed":{"hash":"1uih14b","tokens":330}}},"last_read":{"hash":"1uih14b","at":1751288801404},"class_name":"SmartSource","last_import":{"mtime":1751244542197,"size":1158,"at":1751288765474,"hash":"1uih14b"},"blocks":{"#`react`":[1,38],"#`react`#{1}":[3,8],"#`react`#Usage":[9,30],"#`react`#Usage#{1}":[11,30],"#`react`#Documentation":[31,34],"#`react`#Documentation#{1}":[33,34],"#`react`#API":[35,38],"#`react`#API#{1}":[37,38]},"outlinks":[{"title":"production build","target":"https://reactjs.org/docs/optimizing-performance.html#use-the-production-build","line":7}],"last_embed":{"hash":"1uih14b","at":1751288800995}},"smart_blocks:Projects/Piecework/node_modules/react/README.md#`react`": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02336185,-0.03745557,0.04877615,-0.00129047,-0.00374482,-0.0262151,-0.02637537,0.03494387,-0.00200967,0.01902209,0.01048121,-0.02669441,0.02792235,0.08810323,-0.02217911,0.00060372,-0.04055938,0.00420885,0.01129606,0.02339617,0.06706231,0.00108485,0.01248833,-0.03142453,0.00360059,0.08009048,-0.01987702,0.00081775,0.02772058,-0.16745256,-0.04651295,0.02092097,-0.00549632,0.04424975,-0.00374972,-0.02271003,0.02336059,-0.02013252,-0.02697968,0.06303034,-0.05420755,0.08661459,-0.02490006,-0.0324702,-0.02325562,0.01421158,-0.05021631,-0.034033,-0.02137746,0.01466487,-0.0589222,-0.02264958,0.05891614,-0.02448695,0.05249814,0.08459739,0.02682655,0.06797273,-0.00698036,0.02445155,0.04640092,0.00985156,-0.14909676,0.0736692,0.11385922,0.05814633,-0.06477813,-0.0279618,0.01939045,-0.0133724,-0.02642496,0.03539656,0.01143911,0.06139473,0.04470747,-0.06705994,-0.00184558,-0.01863355,0.03875359,-0.03565482,-0.07245671,-0.04321487,-0.05193424,-0.00717218,0.00589784,-0.01528807,0.05894043,-0.01719797,0.08344172,0.06041825,-0.05865703,-0.05035947,0.01809913,0.06413855,-0.01400026,0.03915376,0.04875236,0.0514268,-0.01559683,0.14617573,-0.06092247,0.00101452,-0.01266328,-0.00476503,-0.0305949,0.00719076,-0.03134294,-0.03545032,-0.01843221,0.00240856,-0.01930171,-0.01044807,-0.08995232,-0.06587958,0.00803977,-0.02115159,-0.00339809,0.02585634,-0.00337961,0.04304161,0.08096915,0.06818061,0.00749163,-0.02406998,0.00431283,0.01355331,0.07692534,0.02283313,-0.02317693,0.10806597,0.00178863,0.06285127,-0.04742184,0.00010574,0.02598851,0.04786864,-0.02326618,-0.04087669,0.00880165,0.05621924,0.01789847,-0.02741201,-0.00812376,-0.06487627,0.00712195,0.02832072,-0.03645531,0.07975004,-0.04843239,0.02129081,-0.0036465,0.06978789,-0.08675636,-0.03349454,-0.04840587,-0.07497351,-0.02480006,-0.05961312,-0.04586392,-0.00635825,-0.01917902,0.00415623,-0.00084033,-0.0085587,-0.02486884,-0.0735652,-0.04238276,0.08869889,0.03486985,-0.09771848,-0.03835364,0.03692864,0.00538786,-0.05398219,0.05615421,0.01882863,-0.03616763,-0.00264199,0.01525268,0.02076685,0.05206502,-0.06663887,-0.06152672,0.00832174,-0.00109761,-0.03916887,0.02541305,-0.03131919,0.02786938,0.03573551,0.01589134,-0.00515599,-0.03985905,0.03990415,-0.01225256,0.02278081,-0.04953221,-0.05221396,0.02798735,-0.00458533,0.10939246,0.07530385,-0.02436487,0.05482987,-0.04668061,0.0616083,-0.01123272,0.01419713,0.03306626,-0.01031894,-0.11353183,-0.01221731,0.05940395,0.08467754,-0.05280108,-0.06780323,0.05912385,0.08554608,0.04994758,0.04093878,0.01121353,-0.01348811,-0.07678639,-0.21263143,0.05505199,0.02509534,-0.01470706,-0.0685645,-0.02597894,0.01200472,-0.02522394,-0.023321,0.0528458,0.11831787,0.01754918,-0.00695157,-0.00695437,-0.02760775,-0.05576457,-0.02503925,-0.0414435,-0.08923104,-0.01226326,-0.03032073,-0.04174124,-0.03644592,-0.10087458,0.0502102,-0.03875549,0.1427193,0.0505299,0.00388407,-0.02308846,0.0381744,-0.00443061,0.023152,-0.09277659,-0.0365446,0.03059658,0.02621486,0.01781381,0.03443436,0.00146294,-0.03385244,-0.08308274,0.00243668,-0.03482597,0.03947765,-0.03582447,-0.0726978,-0.04230525,-0.01826269,0.04670784,0.04043674,-0.01039007,0.03783633,0.08130067,-0.07364399,0.02506962,-0.00127119,-0.01440919,-0.0136395,-0.00541398,0.00487439,0.03053477,0.00654443,-0.04307083,0.03337182,0.05679516,0.02167194,-0.024782,0.00003011,-0.02239694,-0.02699419,0.10438465,0.00025424,0.01990045,-0.03437365,-0.03540782,-0.01865485,0.01109877,0.04685043,0.03071798,0.01480041,0.00520866,0.03969812,0.0202514,-0.00866029,-0.00657456,0.04404128,-0.06269532,-0.01230181,-0.02270619,-0.0301444,0.04390483,-0.02242123,-0.04428002,0.04095567,-0.00527643,-0.2434703,-0.00505786,0.0197486,-0.03243842,0.01114646,-0.03814902,0.03482936,-0.02124423,-0.03854772,-0.01262665,-0.01438873,0.04304156,-0.03655554,-0.01120338,0.04434992,0.04065673,0.08887625,0.03605286,0.07480437,-0.1085638,0.06036068,-0.00636095,0.24690032,-0.00698921,0.00443154,0.08997784,-0.02050276,0.03184913,0.08027866,0.09953259,-0.04485657,0.00532646,0.09361804,0.00215769,-0.07613838,-0.00581688,0.04780371,0.00991311,0.04342245,0.01265005,-0.0080914,0.01110482,0.01400427,0.01717066,0.06244924,-0.11037652,-0.08360054,-0.13016878,-0.0158343,-0.00018609,-0.04125571,0.01360926,0.035151,-0.02955621,-0.01183144,0.02356269,-0.00207388,-0.01739749,-0.06806642,-0.00258381,0.0388425,-0.02004544,0.06374119,0.07339589,-0.01378747],"last_embed":{"hash":"1uih14b","tokens":329}}},"text":null,"length":0,"last_read":{"hash":"1uih14b","at":1751288801132},"key":"Projects/Piecework/node_modules/react/README.md#`react`","lines":[1,38],"size":1158,"outlinks":[{"title":"production build","target":"https://reactjs.org/docs/optimizing-performance.html#use-the-production-build","line":7}],"class_name":"SmartBlock","last_embed":{"hash":"1uih14b","at":1751288801132}},
"smart_blocks:Projects/Piecework/node_modules/react/README.md#`react`#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01682579,-0.02851001,0.06130101,0.00072343,0.0253893,-0.02301651,-0.03311788,0.02845764,-0.01298853,0.0154311,0.01413123,-0.01865514,0.02908852,0.07770563,-0.01547967,0.0082603,-0.0339971,0.00498716,0.02706641,0.01760356,0.07089139,0.02154155,0.00102057,-0.03465394,0.00159915,0.07842261,-0.0163145,0.00677812,0.02006029,-0.16387434,-0.05738211,0.01434888,-0.01451399,0.03394235,-0.00152769,-0.02268177,0.03092372,-0.01937816,-0.02942698,0.0613196,-0.04924703,0.08797695,-0.02992151,-0.0324084,-0.0186674,0.02678688,-0.04932351,-0.03989273,-0.0368082,0.0009023,-0.04841395,-0.01971867,0.05090652,-0.02140088,0.04075852,0.08037788,0.0267383,0.06559506,-0.0124163,0.03176703,0.04180671,0.00163619,-0.14213967,0.07953787,0.12361564,0.05229298,-0.05924174,-0.02813475,0.00851378,-0.0142764,-0.02922427,0.03197108,0.01202048,0.05775391,0.04363897,-0.05090311,0.00193015,-0.02699887,0.03432177,-0.02818399,-0.06663617,-0.04407788,-0.05215206,-0.00449895,0.00429675,-0.01113111,0.0478567,-0.02087704,0.07967287,0.05839326,-0.06101093,-0.0510652,0.01323673,0.06947658,-0.02687086,0.04183455,0.04829802,0.03931932,-0.00143506,0.15817514,-0.05849851,-0.00529112,0.00300706,-0.00442237,-0.02286278,0.00749555,-0.02860069,-0.02615506,-0.02131756,-0.00571355,-0.0272549,0.00054504,-0.0930484,-0.06904589,0.00545392,-0.03791179,-0.02032425,0.03849705,0.00264575,0.03470231,0.07276545,0.06855647,0.01957735,-0.02112851,-0.00215591,0.00948428,0.07932816,0.00958616,-0.01267456,0.11437806,0.00586749,0.06785406,-0.03466539,0.00462368,0.03097888,0.04254733,-0.03602478,-0.05254152,0.00935711,0.07918467,0.01275545,-0.00843927,-0.00565692,-0.05659118,0.00050722,0.02882397,-0.03977013,0.08168749,-0.06077009,0.04452869,-0.00767828,0.06693629,-0.077475,-0.02113385,-0.04069247,-0.07246381,-0.03729014,-0.0697901,-0.05019335,-0.00916556,-0.02108513,0.00397949,-0.01776591,0.0058388,-0.03820981,-0.08187529,-0.05062599,0.07482225,0.04867989,-0.08952571,-0.04947048,0.03540903,0.0141755,-0.05632864,0.0427453,0.02096306,-0.03037622,0.00496573,0.00969849,0.01954675,0.0570204,-0.05760073,-0.05472005,0.00603111,0.00121226,-0.03252863,0.02121984,-0.02468865,0.04854375,0.03327532,0.01226732,0.00996341,-0.02707203,0.0283931,-0.00135415,0.01633593,-0.02958446,-0.05400177,0.03745516,-0.00930022,0.10383762,0.08786254,-0.02433027,0.06155639,-0.04967814,0.0491465,-0.01576918,0.00473403,0.02852979,0.00978533,-0.11567317,-0.01795604,0.06342071,0.07455459,-0.04761583,-0.06192675,0.06566405,0.09289964,0.03969372,0.04631162,0.00979265,-0.01291195,-0.06899936,-0.21888033,0.06168729,0.0213019,-0.01609259,-0.06029687,-0.02406399,0.00337521,-0.02878529,-0.03308785,0.06059754,0.11505116,0.00575274,0.00017203,0.00195991,-0.02787961,-0.05712336,-0.02312434,-0.04794033,-0.09421513,-0.01481969,-0.0344023,-0.04103959,-0.03255898,-0.10012332,0.05146949,-0.02644166,0.13703905,0.0558644,0.01904769,-0.03412731,0.03679014,0.00440174,0.01532699,-0.10209159,-0.02889333,0.03973315,0.03158012,0.01064063,0.03413056,-0.00768197,-0.03430452,-0.07610697,-0.00101621,-0.04433528,0.04581045,-0.03125699,-0.06248786,-0.03285088,-0.02658488,0.04559989,0.03189706,-0.01037664,0.04055574,0.08818895,-0.07866788,0.02927956,-0.00593193,-0.01161026,-0.00975754,-0.00537461,0.01314641,0.0283159,0.00440097,-0.04731015,0.02196811,0.05342971,0.02508359,-0.02309726,-0.00683962,-0.03005601,-0.03199811,0.11201128,-0.00765708,0.00604076,-0.02554489,-0.03107613,-0.03226693,0.01784322,0.04471255,0.03320493,0.01695422,0.0062571,0.04712412,0.01553455,-0.00660948,-0.01103348,0.0413962,-0.0783672,-0.01291786,-0.01616481,-0.0287992,0.04668179,-0.04206157,-0.05262921,0.05528238,-0.00997336,-0.24376228,0.00239889,0.02764189,-0.03657715,0.00235195,-0.04042529,0.03767288,-0.03053244,-0.03519488,-0.00792653,-0.00829312,0.0344221,-0.03441035,0.00178072,0.04361578,0.03640918,0.11027021,0.02869131,0.06152386,-0.11284042,0.04612455,-0.00484016,0.24196792,-0.01330017,-0.00657936,0.08381776,-0.02145926,0.02914392,0.07276408,0.08852544,-0.03868138,0.01437087,0.09458936,-0.00089155,-0.08055469,0.00614283,0.05278664,0.01622289,0.04239072,0.01778911,-0.00371367,0.0041589,0.01031917,0.00996143,0.05360513,-0.10289865,-0.07805561,-0.13731901,-0.02855597,-0.0135115,-0.03941959,0.00703836,0.03076273,-0.03548316,-0.00566564,0.01912851,0.00219424,-0.01642728,-0.0652435,0.00132849,0.03129528,0.00591578,0.05506016,0.08195244,-0.0179334],"last_embed":{"hash":"hcj17y","tokens":163}}},"text":null,"length":0,"last_read":{"hash":"hcj17y","at":1751288801271},"key":"Projects/Piecework/node_modules/react/README.md#`react`#{1}","lines":[3,8],"size":667,"outlinks":[{"title":"production build","target":"https://reactjs.org/docs/optimizing-performance.html#use-the-production-build","line":5}],"class_name":"SmartBlock","last_embed":{"hash":"hcj17y","at":1751288801271}},
"smart_blocks:Projects/Piecework/node_modules/react/README.md#`react`#Usage": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05396256,-0.04578267,0.03003364,-0.03045795,-0.06307545,-0.01016189,-0.00788531,0.03646012,0.03399575,0.02528105,-0.00499641,-0.0487591,0.01090327,0.08260381,-0.03216311,-0.01063247,-0.07315037,0.06447324,-0.04238449,0.00247217,0.08388391,-0.04549152,0.01275737,-0.03059763,0.00753135,0.09436728,-0.03168606,-0.00644254,0.05656466,-0.14575385,-0.00015839,0.0004674,0.01271284,0.04434888,0.00808418,-0.03487044,-0.00908753,-0.01993367,-0.02438501,0.06727889,-0.02385664,0.04380384,-0.00692405,-0.04154816,-0.02791852,-0.0258175,-0.05651871,-0.00903373,0.03667004,0.02831893,-0.02493033,-0.04335718,0.05244043,-0.04741871,0.05253525,0.09216343,0.01316059,0.0495924,0.00406108,0.01556961,0.03152006,0.02495499,-0.15954125,0.04535785,0.05929876,0.06923448,-0.0662687,-0.00990677,0.05076635,0.0447551,-0.0381071,0.02719603,-0.00222751,0.08425377,0.04681244,-0.10163108,-0.02459582,-0.01238129,0.05161651,-0.03465016,-0.06346744,-0.05908391,-0.03009667,0.01512458,0.01944051,-0.03291378,0.04789986,0.01082616,0.05869576,0.06249576,-0.050178,-0.05148045,0.01788023,0.06193425,-0.01720846,0.02596689,0.0382034,0.0531323,-0.05171526,0.13553549,-0.07800878,0.02674715,-0.01989415,-0.0221482,-0.02870389,0.02303475,-0.0163565,-0.01946068,-0.01868028,0.01812254,0.01050563,-0.06680085,-0.06615707,-0.0854075,-0.0037518,0.00657215,0.01311459,0.01509751,-0.02469016,0.02492812,0.08528084,0.04270766,-0.0023556,-0.00116994,0.03109777,0.03765552,0.0473429,0.05973341,-0.05046374,0.07598581,-0.00486267,0.03355664,-0.0632757,-0.00360922,0.01496006,0.04383715,0.01677999,0.01877739,0.01829353,0.04330029,0.02100697,-0.05088964,-0.0174897,-0.09638874,0.01979051,0.03807511,-0.01730415,0.06427979,-0.01113597,-0.03414794,0.01106439,0.04873913,-0.09773182,-0.06176754,-0.05239487,-0.0422872,-0.00328136,-0.01130512,-0.00730073,-0.00683229,-0.03511263,0.02592,0.02631741,0.02507121,-0.00839584,-0.08536239,0.01223413,0.09881252,0.01341656,-0.09474642,-0.01042319,0.04858829,-0.03330408,-0.04421863,0.11515305,0.01116267,-0.03654108,-0.00821284,0.04719023,0.03218097,0.04973073,-0.04627285,-0.07605504,0.01417789,-0.02793791,-0.05290088,0.02015135,-0.01733388,-0.03502036,0.04253723,-0.00202421,-0.0037921,-0.04065516,-0.00959163,-0.02326001,0.02009949,-0.07998136,-0.04250728,0.01995296,0.01063714,0.13324718,0.04120844,-0.00507944,0.01091995,-0.01719569,0.07482001,-0.01357958,0.0322329,0.02739118,-0.02844372,-0.09811284,0.01158414,0.05330047,0.08676135,-0.0613024,-0.05922889,0.01028932,0.04594507,0.06827225,0.02082401,-0.00664698,-0.01855361,-0.06408045,-0.20350848,0.0309011,0.01373575,-0.01976916,-0.06255279,-0.01777299,0.0362154,-0.01438357,-0.03718589,0.02730118,0.10877778,0.01309122,-0.02755258,-0.02817491,-0.03709521,-0.07084085,0.00890043,-0.03532539,-0.04514726,-0.00276148,0.00324105,-0.06572407,-0.03466697,-0.09769849,0.03856781,-0.05406613,0.16310041,0.04376729,-0.03156296,-0.00754134,0.04751813,-0.01971059,0.00532529,-0.08576676,-0.01914736,0.01496354,0.01228348,0.00890236,0.04065695,0.00077915,-0.02452247,-0.05863828,0.01114111,-0.02356713,0.04567942,-0.02175128,-0.06843954,-0.05987585,-0.00624825,0.03620186,0.04634805,-0.00241636,0.01728244,0.07146004,-0.07414808,-0.01351155,0.01548304,-0.03125333,-0.02763479,0.00520562,-0.02025649,0.01595436,-0.01735526,-0.02171358,0.0433709,0.06781254,0.02362525,-0.03673535,0.01950456,-0.01370429,-0.02314267,0.07410963,0.03304025,0.0303676,-0.04787992,-0.02288301,-0.01084601,0.02260406,0.03833981,0.00352774,0.01492965,0.0096573,-0.01051926,0.03007191,-0.00684638,-0.01805798,0.0320676,-0.0101094,0.01967858,-0.06256703,-0.04062874,0.02218734,0.04071764,0.01029435,0.01649631,-0.0065451,-0.270769,0.00218054,0.00611622,0.00718872,0.01137001,-0.01315264,0.04381225,0.00969054,-0.04756218,-0.02295551,-0.01715343,0.05944116,-0.00491274,-0.00551177,0.03540857,0.03365997,0.05696137,0.03595134,0.10955713,-0.08906286,0.06491008,0.00886344,0.26246518,-0.00471708,0.05388995,0.07278603,-0.02889949,0.0398368,0.09131593,0.08735873,-0.063278,-0.0205844,0.08519313,-0.00265356,-0.03963976,-0.01795292,0.01647137,0.01063949,0.03226138,-0.00017983,-0.05382378,-0.00061874,0.00433115,0.0315357,0.08055931,-0.1220671,-0.07320497,-0.10977923,-0.00713289,0.01691687,-0.04664379,0.01549339,0.02246393,-0.02041203,-0.01788142,0.04513286,-0.02560109,0.00228902,-0.07236501,-0.00373326,0.03536722,-0.03418979,0.08842282,0.05136669,0.00872102],"last_embed":{"hash":"1qhktn0","tokens":158}}},"text":null,"length":0,"last_read":{"hash":"1qhktn0","at":1751288801339},"key":"Projects/Piecework/node_modules/react/README.md#`react`#Usage","lines":[9,30],"size":390,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1qhktn0","at":1751288801339}},
"smart_blocks:Projects/Piecework/node_modules/react/README.md#`react`#Usage#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05292736,-0.04924547,0.02874971,-0.03284644,-0.06288606,-0.00929789,-0.00197532,0.0370037,0.03424892,0.02479104,-0.00240906,-0.04750971,0.01349979,0.08404455,-0.03358465,-0.01244589,-0.07230428,0.06142703,-0.04080317,-0.00044728,0.08409555,-0.04469348,0.01517105,-0.0304131,0.00839083,0.09184737,-0.03386917,-0.00414855,0.05451813,-0.14514655,0.00279585,0.00179146,0.01189562,0.04700923,0.0028837,-0.03366871,-0.00657303,-0.01858876,-0.02241077,0.06892775,-0.02488962,0.04317604,-0.00555849,-0.04256395,-0.03098984,-0.02607522,-0.05702006,-0.0077904,0.03514976,0.03222507,-0.02100369,-0.04366056,0.05417068,-0.04696609,0.05308639,0.09270361,0.01357241,0.05079398,0.00480185,0.01622046,0.03118028,0.02424246,-0.16146424,0.04379309,0.05733439,0.06897572,-0.065729,-0.00858444,0.04980505,0.04209446,-0.04105652,0.02627885,-0.00157099,0.08303162,0.04606717,-0.10380305,-0.02264218,-0.01042101,0.0504055,-0.03844742,-0.06209293,-0.06073757,-0.0272968,0.01277485,0.01974935,-0.03016004,0.04647455,0.01018342,0.05952485,0.05906223,-0.05090392,-0.0509507,0.01844393,0.06223109,-0.01700934,0.02563944,0.04019456,0.05529463,-0.05242529,0.13541546,-0.07967343,0.0243549,-0.02098094,-0.02224699,-0.02730301,0.0255399,-0.01805458,-0.01896113,-0.01677618,0.01993866,0.01088153,-0.06493865,-0.06501287,-0.08521056,-0.00186601,0.00677018,0.01192868,0.01277209,-0.02467649,0.02515049,0.0835963,0.04005011,-0.00065212,0.00187645,0.0316249,0.03761441,0.04700021,0.06013251,-0.05269006,0.07310243,-0.00688396,0.03068098,-0.06602084,-0.00091134,0.0178184,0.04271369,0.02036398,0.01715489,0.01749594,0.04550484,0.02341302,-0.05002977,-0.01590192,-0.09669393,0.01969234,0.03920509,-0.01606987,0.06468061,-0.00996325,-0.03631849,0.01008351,0.04863887,-0.09738532,-0.06289078,-0.05310015,-0.04249137,-0.0018946,-0.01077366,-0.00770883,-0.00835758,-0.03743887,0.02618393,0.02613601,0.02256569,-0.01071227,-0.0809597,0.01108663,0.09907057,0.01387556,-0.09332301,-0.01100954,0.0497915,-0.03442536,-0.04119338,0.11261309,0.01129983,-0.03553598,-0.00667934,0.0468648,0.03177354,0.05130877,-0.04749994,-0.07561388,0.01254569,-0.02533505,-0.05479223,0.02005792,-0.01639537,-0.03432304,0.04315878,-0.00382784,-0.00206067,-0.03944331,-0.01176022,-0.02301412,0.02425943,-0.08517366,-0.04079686,0.02058046,0.00966995,0.13595621,0.0417929,-0.00382392,0.0131237,-0.01571644,0.07801108,-0.01317187,0.03338778,0.02709657,-0.03291144,-0.09918423,0.01309215,0.05362188,0.08574761,-0.06131598,-0.05882202,0.00855611,0.04492205,0.06967898,0.02093996,-0.00807588,-0.01942103,-0.06577664,-0.20356011,0.03178407,0.016681,-0.0194686,-0.06103016,-0.02049361,0.03643786,-0.01405444,-0.03491376,0.02979899,0.1035484,0.01026655,-0.02699408,-0.02883464,-0.03489809,-0.07184999,0.00854864,-0.03361955,-0.04631963,-0.00276858,0.00024843,-0.06617053,-0.03493658,-0.09743197,0.04002457,-0.0541584,0.16517337,0.04314844,-0.03309549,-0.00879566,0.04662774,-0.02058299,0.00390881,-0.08735693,-0.02250897,0.01236771,0.01305956,0.00829875,0.04360729,0.00046163,-0.02675818,-0.05892112,0.00994799,-0.02529687,0.0507803,-0.02055635,-0.06712762,-0.06411872,-0.00777418,0.03708982,0.04863124,-0.00034004,0.01693697,0.06736962,-0.0716963,-0.01586395,0.01623847,-0.0273647,-0.02712557,0.00646734,-0.02199596,0.01274182,-0.01663965,-0.02542536,0.04438269,0.06531034,0.02596798,-0.03498331,0.01860081,-0.01055744,-0.02732298,0.07421708,0.02935278,0.03087574,-0.04636857,-0.02367486,-0.01169889,0.02298966,0.03718546,0.00429092,0.01573575,0.01065928,-0.00979275,0.03352023,-0.00535392,-0.02099487,0.03371968,-0.00959985,0.02021706,-0.06472342,-0.03999471,0.01918403,0.03987576,0.01045399,0.0162498,-0.0086599,-0.27063712,0.00115871,0.00483376,0.01017483,0.01225237,-0.01526711,0.04228676,0.01210609,-0.04785487,-0.02181513,-0.01592478,0.05850931,-0.00184556,-0.00526652,0.03432139,0.03268576,0.05842737,0.03397184,0.1078127,-0.09062764,0.06614902,0.00746738,0.26158473,-0.00357048,0.05443056,0.07112504,-0.029462,0.04224276,0.09079273,0.0871992,-0.06397014,-0.0187618,0.08577362,-0.0048735,-0.03894688,-0.02035616,0.01532476,0.00885613,0.02992851,0.001124,-0.05300948,-0.00172007,0.00284573,0.03163214,0.08144891,-0.12080605,-0.07232385,-0.1090002,-0.00841242,0.01807153,-0.04667155,0.01678851,0.02151053,-0.02043158,-0.0152869,0.0437463,-0.02418428,0.00477281,-0.07229561,-0.0051373,0.03639479,-0.03383369,0.08793158,0.05073433,0.0083234],"last_embed":{"hash":"j0v9yf","tokens":157}}},"text":null,"length":0,"last_read":{"hash":"j0v9yf","at":1751288801404},"key":"Projects/Piecework/node_modules/react/README.md#`react`#Usage#{1}","lines":[11,30],"size":380,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"j0v9yf","at":1751288801404}},
