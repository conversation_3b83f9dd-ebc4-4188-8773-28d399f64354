# PriceBot Architecture

## 1. Chatbot (User-Facing)

- **Platform:** Web-based chat interface.
- **Technology:**
  - Frontend: React/Next.js
  - Backend: Node.js/Express
  - Real-time Communication: Socket.IO
- **Workflow:**
  1. User enters a product query.
  2. The backend sends the query to the WhatsApp Business API and SMS Gateway.
  3. As businesses reply, the backend parses the messages and sends the data to the frontend via Socket.IO.
  4. The frontend displays the prices and store information in a clean, real-time list.

## 2. Business Onboarding

- **Initial Outreach:** The chatbot will send an opt-in message to businesses that reply with a price.
- **Onboarding Form:** A simple web form for businesses to officially register.
  - **Data Collected:** Business Name, Location Coordinates, Contact Person, WhatsApp Number.
- **Business Portal:** A simple dashboard for businesses to manage their public listings and view price requests.

## 3. Public Catalog

- **Platform:** Web-based, searchable catalog.
- **Technology:**
  - Frontend: React/Next.js
  - Backend: Node.js/Express
  - Database: PostgreSQL/MongoDB
- **Features:**
  - Search for products by name or category.
  - Sort results by price or distance.
  - View store information, including location and contact details.

## 4. APIs

- **WhatsApp Business API:** Twilio or a similar provider.
- **SMS Gateway:** To reach businesses that are not on WhatsApp.
- **Geocoding API:** Google Maps or a similar provider to calculate distances.

