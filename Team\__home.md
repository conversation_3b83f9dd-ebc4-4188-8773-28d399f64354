# Team Coordination - Template

## Overview
Auto-updating team assignments and coordination tracking.

## Team Structure (Auto-Updated)
Team information will be populated from __start.md:

| Name | Role | Projects | Utilization | Status |
|------|------|----------|-------------|--------|
| [Auto-populated from __start.md] | | | | |

## Team Coordination

### Project Assignments
- Automatic assignment tracking from project files
- Workload distribution monitoring
- Collaboration tracking between team members

### Communication
- Team sync meeting schedules
- Project coordination requirements
- Cross-project collaboration needs

### Performance Tracking
- Individual contribution metrics
- Team collaboration effectiveness
- Skill development progress

## Auto-Update Rules
Team data automatically updates based on:
- Project assignments in project files
- Team member mentions in tasks
- Collaboration requirements in projects
- Workload distribution calculations

#team #coordination #assignments #template
