{"recentFiles": [{"basename": "__home", "path": "Team/__home.md"}, {"basename": "__home", "path": "Performance/__home.md"}, {"basename": "2025-06-26-daily", "path": "Tasks/2025-06-26-daily.md"}, {"basename": "__home", "path": "Projects/Agency Work/Afyalink/__home.md"}, {"basename": "__home", "path": "Projects/Agency Work/__home.md"}, {"basename": "__home", "path": "Google Business Profile Management/MARSHAS Fitness/__home.md"}, {"basename": "__home", "path": "Clients/__home.md"}, {"basename": "__home", "path": "Projects/Music/Production/DubAvenue/__home.md"}, {"basename": "__home", "path": "Projects/Music/Production/__home.md"}, {"basename": "__home", "path": "Projects/Music/__home.md"}, {"basename": "__home", "path": "Projects/Podcast/__home.md"}, {"basename": "__logs", "path": "Tasks/__logs.md"}, {"basename": "__logs", "path": "Projects/Podcast/__logs.md"}, {"basename": "__rules", "path": "Projects/__rules.md"}, {"basename": "__home", "path": "Projects/__home.md"}, {"basename": "__rules", "path": "Projects/Podcast/__rules.md"}, {"basename": "__start", "path": "__start.md"}, {"basename": "__home", "path": "Projects/Agency Work/Google Maps/__home.md"}, {"basename": "__rules", "path": "Projects/Agency Work/__rules.md"}, {"basename": "__rules", "path": "Team/__rules.md"}, {"basename": "__logs", "path": "Team/__logs.md"}, {"basename": "__memo", "path": "__memo.md"}, {"basename": "__rules", "path": "__rules.md"}, {"basename": "__logs", "path": "Clients/__logs.md"}, {"basename": "__rules", "path": "Performance/__rules.md"}, {"basename": "__logs", "path": "Performance/__logs.md"}, {"basename": "__rules", "path": "Tasks/__rules.md"}, {"basename": "__logs", "path": "__logs.md"}, {"basename": "__rules", "path": "Performance/2025/__rules.md"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-open", "omitBookmarks": false}